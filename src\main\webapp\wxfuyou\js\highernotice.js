handleLinkV();
var objdata = {
    startid: 0,
    endid: 0,
    pernum: 10,
    myscroll: null,
    unReadNum: 0,
    ossSize: "?x-oss-process=image/resize,m_fill,h_92,w_112"
};
require.config({
    baseUrl: '../', //指定js文件的基路径
    paths: {
        system: 'sys/system_zepto',
        moment: 'plugin/js/moment',
        mescroll: 'plugin/mescroll/mescroll.min'
    },
    waitSeconds: 0
});
require(['system', 'moment', 'mescroll'], function (s, moment, MeScroll) {
    var limit = 10,
        offset = 0,
        pageNum = 0,
        allNum = 0,
        preVal = "",
        areacode = null,
        prePageY,
        wHeght = $(window).height(),
        noData = false,
        $_con = $("#thelist"),
        $_search = $("#search-box");
    getUnumber();
    window.mescroll = new MeScroll("wrapper", {
        down: {//解析: down.callback默认调用mescroll.resetUpScroll(),而resetUpScroll会将page.num=1,再触发up.callback
            callback: function () {
                mescroll.resetUpScroll();
            }
        },
        up: {
            callback: function (page) {
                var strWhere = "tn.isdel=0";
                var val = $(".inp-txt").val();
                if (val)
                    strWhere += " and (td.title like '%" + val + "%' or td.summary like '% " + val + "%')";
                $.sm(function (re, err) {
                    if (!err && re) {
                        if (re.length > 0) {
                            listtable(re);
                            mescroll.endSuccess(re.length);
                        } else {
                            mescroll.endSuccess(re.length);
                        }
                    } else {
                        mescroll.endSuccess(0);
                    }
                }, ["highernotice.contant", page.size, page.size * (page.num-1), strWhere]);
            },
            autoShowLoading: false,
            isBounce: false, //此处禁止ios回弹,解析(务必认真阅读,特别是最后一点): http://www.mescroll.com/qa.html#q10
            noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
            empty: {
                warpId: 'thelist',
                icon: "../plugin/mescroll/mescroll-empty.png", //图标,默认null
                tip: "暂无相关数据~", //提示
                btntext: "刷新", //按钮,默认""
                btnClick: function () {//点击按钮的回调,默认null
                    mescroll.resetUpScroll();
                }
            },
            //clearEmptyId:"ulyeylist", //相当于同时设置了clearId和empty.warpId; 简化写法;默认null
            toTop: { //配置回到顶部按钮
                src: "../plugin/mescroll/mescroll-totop.png"
            }
        }
    });
    /*setTimeout(function () {
        objdata.myscroll = new scrollClass({
            wrapper: "wrapper",
            downAction: function () {
                pageNum=0;
                getUnumber();
                $.sm(function (re, err) {
                    if (!err && re) {
                        if(re.length>0){
                            $("#thelist").html('');
                            listtable(re);
                        }
                        objdata.myscroll.scroll.refresh();
                    } else {
                        parent.layer.msg("发送消息失败")
                    }
                }, ["highernotice.contant", limit,offset, "tn.isdel=0"]);
            },
            upAction: function () {
                pageNum++;
                $.sm(function (re, err) {
                    if (!err && re) {
                        if(re.length>0){
                            listtable(re);
                            objdata.myscroll._wrapper.find('.pullUpLabel').html('下拉加载更多');
                            objdata.myscroll.scroll.refresh();
                        }else {
                            objdata.myscroll._wrapper.find('.pullUpLabel').html('没有啦');
                            objdata.myscroll._wrapper.find('.pullUpIcon').hide();
                        }
                    } else {
                        parent.layer.msg("发送消息失败")
                    }
                }, ["highernotice.contant", limit, limit*pageNum, "tn.isdel=0"]);
            }
        });
        objdata.myscroll.init();
        setTimeout(function () {
            document.getElementById('wrapper').style.left = '0';
        }, 800);
    }, 200);*/
    $_con.off("click", ".baby-main").on("click", ".baby-main", function (ev) {
        var $this = $(this),
            isread = $this.attr("data-isread"),
            noticeId = $this.attr("data-id"),
            url = "wxchdcDetail.html?noticeid=" + noticeId + "&areacode=" + $this.attr("data-areacode") + "&mobile=" + $this.attr("data-mobile") + "&v=" + (+new Date()) + "&readstatus=" + isread + "&homeversion=" + Arg("v");
        if (isread === "noread") {
            setTimeout(function () {
                $this.attr("data-isread", "read");
                $this.find(".mark-read").prop("src", "images/" + statusIcon.read);
                objdata.unReadNum-- ? $_search.children().eq(1).html("未读" + '<i>' + objdata.unReadNum + '</i>') : $_search.children().eq(1).html("未读" + '<i>' + objdata.unReadNum + '</i>').hide();
            });
        }
        openwin("wxchdcDetail", url, "通知详情");
    });

    //    搜索
    //    ios自带键盘点击完成，输入框失去焦点
    $_search.off("input", ".inp-txt").on("input", ".inp-txt", function (event) {
        mescroll.resetUpScroll();
    });
});
function getData(limit, offset, sWhere) {
    $.sm(function (re, err) {
        if (!err && re) {
            listtable(re);
        } else {
            parent.layer.msg("发送消息失败")
        }
    }, ["highernotice.contant", limit, offset, sWhere]);//notice.selReceivedNotice
}
function listtable(re) {
    var html = [];
    for (var i = 0; i < re.length; i++) {
        var obj = re[i];
        html.push('<dl class="baby-main" data-id="' + obj.noticeid + '" data-isread="' + obj.isread + '" data-areacode="' + obj.areacode + '" data-mobile="' + obj.mobile + '">');
        if (obj.isread == "read") {
            html.push('<img src="images/read.png" class="mark-read">');
        } else {
            html.push('<img src="images/unread.png" class="mark-read">');
        }
        html.push('<dt>');
        html.push('<img src="' + obj.cover + objdata.ossSize + '">');
        html.push(' </dt>');
        html.push('<dd class="baby-txt">');
        html.push('<p class="highlilst-p1"><label class="title">' + obj.title + '</label>');
        html.push('<span class="time">' + formatTime(obj.publishtime) + '</span></p>');
        html.push('<p class="highlilst-p2">' + obj.summary + '</p>');
        html.push('</dd>');
        html.push('</dl>');
    }
    $("#thelist").append(html.join(""));
}
function getUnumber() {
    $.sm(function (re, err) {
        if (!err && re) {
            objdata.unReadNum = Number(re[0][0]);
            if (!isNaN(objdata.unReadNum) && objdata.unReadNum) {
                $("#search-box").children().eq(1).html("未读" + '<i>' + objdata.unReadNum + '</i>');
            }
        } else {
            parent.layer.msg("发送消息失败")
        }
    }, ["index.xiaoxiNum"]);
}
function formatTime(time) {
    //time 2017-10-25 15:30
    var today = new Date(),
        year = today.getFullYear(),
        month = today.getMonth() + 1,
        date = today.getDate(),
        arrTime = time.split(" ")[0].split("-");
    if (year != arrTime[0]) {
        return time;
    } else if (month == arrTime[1] && date == arrTime[2]) {
        return time.substring(11);
    } else {
        return time.substring(5);
    }
}
//处理link中的version
function handleLinkV() {
    var version = $.cookie('homeVersion'),
        version1 = Arg("v");
    if (!version || version != version1) {
        $.cookie("homeVersion", version1);
        var links = $("link");
        links.eq(0).prop("href", links.eq(0).prop("href") + "?v=" + version1);
        links.eq(1).prop("href", links.eq(1).prop("href") + "?v=" + version1);
    }
}
function pushHash(obj) {
    // objdata.arrwin.push(obj);
    window.history.pushState(null, obj.title, "#" + obj.key);
}
function openwin(key, src, title) {
    objdata.curzindex = 10;
    objdata.prevtitle = document.title;
    document.title = title || "";
    pushHash({
        key: key,
        title: title
    });
    if (!src) {
        src = key + '.html?v=' + Arg('v');
    }
    //	objdata.curzindex
    //	var pageii = layer.open({
    //		type: 1
    //		,anim:false
    //		,content:
    //		,style: 'position: absolute; left:0;background:#fff; top:0; width:100%; bottom:0;'
    //	});
    var str = '<div class="oneopenframe" style="border:none;z-index:' + objdata.curzindex + '">'
        + '<iframe id="frame' + key + '" src="' + src + '" style="width:100%;height:100%;border:none;position: absolute;top: 0;left: 0;"/></div>';
    objdata.curzindex += 1;
    $('body').append(str);
}
/*头部*/
.area-top{font-size: 14px;height: 91px;background: url(../images/topbg.png);background-size: 100% 100%;padding: 0 15px 20px 15px;}
.area-top .fullbtn{font-size: 12px;background: #e57c68;border-radius: 20px;padding: 2px 8px;color: #ffffff;}
.area-nav h5{align-items: center;font-size: 15px;color: #333333;height: 45px;border-bottom: 1px dashed #dddddd;}
.area-nav .tab-btn{margin-left: 10px;font-size: 13px;color: #5d8ff7;border-radius: 3px;background: #dfe9fe;height: 24px;line-height: 24px;padding: 0 10px;width: 50px;}
.remind-con{width:max-content;align-items: center;font-size: 11px;color: #83888f;border-radius: 3px;background: #edf3fb;height: 21px;line-height: 21px;padding: 0 5px;}		
.nav-list{font-size: 15px;color: #333333;margin: 10px 0;}
.nav-list .weui-flex__item{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;position: relative;}
.nav-list .weui-flex__item:after{content: "";height: 60%;width: 1px;background: #dddddd;position: absolute;right: 0;top: 20%;}
.nav-list .weui-flex__item:last-child:after{content: none;}	
/*宝宝的体检报告*/	
.phy-report{margin: 10px 8px 0;position: relative;}
.phy-report:before{content: "";width: 100%;height: 10px;background: #e8e8e8;position: absolute;top: -6px;border-radius: 10px;z-index: -1;}
.phy-report:after{content: "";height: 5px;background: #cbcbcb;position: absolute;top: -3px;left: 4px;right: 4px;border-radius: 10px;z-index: -1;}
.phy-report h5{font-size: 18px;color: #61a5ff;text-align: center;height: 50px;line-height: 54px;}
.phy-cell{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;justify-content: center;margin: 0 1px;border-radius: 4px;color: #ffffff;font-size: 14px;height: 46px;padding-top: 2px;}
.phy-cell.greenphy{background: #09c47c;}
.phy-cell.redphy{background: #f73c3c;}
.rule-mark{border: 1px solid #afdef1;background: #ecf8fd;font-size: 11px;color: #666666;position: absolute;z-index: 999;border-radius: 10px;padding: 0px 5px;box-shadow: 0 1px 8px #cbcbcb;height: 13px;}
.triangle-out-left{
	position: absolute;
  	border: 4px solid transparent;
  	border-right-color: #afdef1;
  	bottom: -4px;
  	left: -9px;
  	border-left:6px solid transparent;
  	transform:rotate(-30deg);
	-ms-transform:rotate(-30deg); 	/* IE 9 */
	-moz-transform:rotate(-30deg); 	/* Firefox */
	-webkit-transform:rotate(-30deg); /* Safari 和 Chrome */
	-o-transform:rotate(-30deg); 	/* Opera */
}
.triangle-in-left{
	position: absolute;
  	border: 4px solid transparent;
  	border-right-color: #ecf8fd;
 	bottom: -3px;
 	left: -7px;
  	border-left:6px solid transparent;
  	transform:rotate(-30deg);
	-ms-transform:rotate(-30deg); 	/* IE 9 */
	-moz-transform:rotate(-30deg); 	/* Firefox */
	-webkit-transform:rotate(-30deg); /* Safari 和 Chrome */
	-o-transform:rotate(-30deg); 	/* Opera */
}							
.icon_arrowup:before,.icon_arrowdown:before{width: 12px;display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
.heightnum{font-size: 13px;color: #ff5342;position: absolute;right: 30px;}
.heightnum:before{content: "";width: 16px;height: 2px;background: #ff5342;display: inline-block;position: absolute;top: 11px;right: -28px;}
.weightnum{font-size: 13px;color: #ff5342;position: absolute;}
.weightnum img{
	transform:rotate(90deg);
	-ms-transform:rotate(90deg); 	/* IE 9 */
	-moz-transform:rotate(90deg); 	/* Firefox */
	-webkit-transform:rotate(90deg); /* Safari 和 Chrome */
	-o-transform:rotate(90deg); 	/* Opera */
}
.weightnum:before{content: "";height: 7px;width: 2px;background: #ff5342;display: inline-block;position: absolute;bottom: -13px;right: 16px;}
.weight-mark .triangle-out-left{
	position: absolute;
    top: -6px;
	right: -7px;
	bottom: auto;
	left: auto;
	border-left: 6px solid transparent;
	transform:rotate(135deg);
	-ms-transform:rotate(135deg); 	/* IE 9 */
	-moz-transform:rotate(135deg); 	/* Firefox */
	-webkit-transform:rotate(135deg); /* Safari 和 Chrome */
	-o-transform:rotate(135deg); 	/* Opera */
}
.weight-mark .triangle-in-left{
	position: absolute;
	top: -5px;
	right: -6px;
	bottom: auto;
	left: auto;
	border-left: 6px solid transparent;
	transform:rotate(135deg);
	-ms-transform:rotate(135deg); 	/* IE 9 */
	-moz-transform:rotate(135deg); 	/* Firefox */
	-webkit-transform:rotate(135deg); /* Safari 和 Chrome */
	-o-transform:rotate(135deg); 	/* Opera */
}
/*身高体重登记*/
.phylist-title{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px solid #dddddd;height: 43px;font-size: 14px;color: #333333;padding: 0 15px;}
.phylist-cell{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px dashed #dddddd;height: 37px;font-size: 13px;margin: 0 15px;text-align: center;}
.phylist-cell>div{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;}
.phylist-cell>div:first-child{text-align: left;}
.phylist-cell>div:last-child{text-align: right;}
.phylist-cell:last-child{border-bottom: 1px solid #dddddd;}
.icon_star{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;height: 12px;margin: 0 3px;}
.icon_star.current:before{color: #ffb600;}
.level-list span{width: 100%;height: 5px;display: inline-block;background: red;margin: 0 1px;}
.level-list{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;text-align: center;margin: 0 0.5px;}
.level-list.current{color: #00d36d;}
.rule-cell{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;border: 1px dashed #e0e0e0;font-size: 15px;color: #cccccc;height: 37px;background: #f4f4f4;margin: 0 3px;}
.rule-cell.current{border: 1px dashed #ff0000;background: #fff0ed;color: #ff6e51;}
.type-title{align-items: center;height: 45px;border-bottom: 1px solid #dddddd;}
.type-title img{width: 20px;height: 20px;font-size: 15px;color: #333333;margin: 0 10px;}
.eyemark-img{width: 20px;height: 20px;background: #e0ecfc;border-radius: 50%;}
.earmark-img{width: 20px;height: 20px;background: #fcedd9;border-radius: 50%;}
.toothmark-img{width: 20px;height: 20px;background: #dcf3fa;border-radius: 50%;}
.checklist-div{font-size: 13px;color: #333333;margin: 5px 14px 0 10px;line-height: 26px;  padding-bottom: 10px;}
.checklist{ margin: 10px 0; overflow: hidden;}
.checklist img{width: 100px; float: left;margin: 5px; height: 100px;}
.checklist-div .weui-cell{padding: 10px 0;}
.checklist-div .weui-cell:before{border-top: none;}
/*五官检查登记*/
.check-cell .check-name{align-items: center;justify-content: flex-end;width: 70px;font-size: 13px;color: #61a5ff;height: 30px;}
.check-cell .check-text{word-break: break-all;display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;font-size: 14px;color: #333333;margin: 0 15px 0 10px;align-items: center;background: #f4f4f4;padding: 4px 13px;/*height: 29px;*/border-radius: 3px;}
/*其他*/
.phystat-cell{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;}
.phystat-cell span{-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;text-align: center;background: #e7e7e7;font-size: 12px;color: #999999;height: 15px;line-height: 15px;margin: 0 1px;}
.phystat-cell span:first-child{border-radius: 3px 0 0 3px;}
.phystat-cell span:last-child{border-radius: 0 3px 3px 0;}
.phystat-cell .phy-redtxt{background: #ff4747;color: #ffffff;}
.phystat-cell .phy-greentxt{background: #00d36d;color: #ffffff;}
.abdomenr-txt{padding-left:30%;color: #999999; font-size: 12px;display: block; line-height: 14px;}
.abdomen-list{font-size: 13px;color: #333333;margin: 5px 14px 0 10px;line-height: 26px; border-bottom: 1px solid rgb(221, 221, 221); padding-bottom: 10px;}
.abdomenr-txt p{line-height: 23px;}
.abdomenr-txt p img{width: 12px; margin-right: 5px; vertical-align: top; margin-top:5px;}
/*口腔*/
.toothlist{ background: #F8F5E5; margin: 15px; padding:0 15px;}
.toothlist div{border-bottom: 1px dashed #D9C769; padding: 8px 0;}
.toothlist p{color: #333333; font-size: 14px; line-height: 23px;}
.yuandianHL{vertical-align: top; margin-right:5px;width: 12px; margin-top: 5px;}
.toothlist p.yellowtxt{color: #FF9232;}
.toothlist p span{display: inline-block; margin-right: 10px;}
.toothlist div:last-child{border-bottom: none;}
/*备注*/
.remarks-ul{font-size: 14px;color: #888888;line-height: 24px;padding: 7px 0;border-bottom: 1px solid #dddddd;margin-bottom: 10px;}
.remarks-ul li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;}
.remarks-label{display: inline-block;margin-right: 5px;background: #61a5ff;font-size: 13px;color: #ffffff;width: 13px;height: 13px;line-height: 13px;text-align: center;border-radius: 50%;}
/*报告封面*/
.icon_tab{background: #dfe9fe;width: 27px;height: 27px;border-radius: 50%;}
.icon_tab:before{font-size: 16px;}
.report-cover{box-shadow: 0px 14px 20px -7px inset #D3C2C5;margin: 0 10px;background: url(../images/reporttop_bg.jpg)no-repeat;background-size: 100% 100%;border-top: 1px solid #dddddd;text-align: center;position: relative;height: 560px;margin-bottom: 12px;}
.report-cover .tab-txt{font-size: 17px;color: #666666;justify-content: space-between;background: #f4f4f4;height: 44px;margin: 7px 38px 0 38px;border-radius: 3px;padding: 0 15px;}
.report-info{background: #ffffff;position: relative;box-shadow: 0 0 15px 3px #f5f5f5;padding: 28px 30px 40px 30px;text-align: left;font-size: 14px;margin: 33px 10px 20px 10px;line-height: 33px;border-radius: 10px;}
.report-info li{display: -webkit-box;display: -webkit-flex;display: flex;}
.report-info li span{min-width: 70px;display: -webkit-box;display: -webkit-flex;display: flex;align-items:center;justify-content:space-between;}
.report-info li p{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;align-items: center;margin-left: 5px;border-bottom: 1px solid #cdcdcd;width: 150px;padding: 0 10px;color: #666666;}
.report-info .report-pho{width: 43px;height: 43px;border-radius: 50%;border: 4px solid #ffffff;position: absolute;top: -22px;left: 50%;margin-left: -22px;box-shadow: 0px -2px 8px -2px #d9d9d9;}
.report-btmtxt{font-size: 12px;color: #ffffff;align-items: flex-end;background: url(../images/reporttop_img.png)no-repeat;background-size: 100% 100%;width: 100%;height: 131px;position: absolute;bottom: 0;}

//微信sdkcommon
define(function (wx, zhtype) {
    var baiduak = "aZO941OOxNhp7NE9ifCwa1yk";
    var wxtool = {
        osshost: "http://tbfile.oss-cn-beijing.aliyuncs.com",
        isready: false,
        isios: navigator.userAgent.indexOf("iPhone") > -1,//navigator.userAgent.indexOf("iPhone") > -1,
        wx: null,
        zhtype: null,
        iswx: function () {
            var ua = window.navigator.userAgent.toLowerCase();
            if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                return true;
            } else {
                return false;
            }
        },
        jsApiList: ['checkJsApi', 'getNetworkType', 'previewImage', 'getLocation', 'chooseImage', 'getLocalImgData', 'uploadImage', 'downloadImage', 'startRecord', 'stopRecord', 'onVoiceRecordEnd', 'uploadVoice', 'playVoice', 'pauseVoice', 'stopVoice', 'onVoicePlayEnd', 'onMenuShareTimeline', 'onMenuShareAppMessage'],
        init: function (wx, zhtype, jsApiList) {
            this.wx = wx;
            this.zhtype = zhtype;
            if (jsApiList) {
                this.jsApiList = jsApiList;
            }
            return wx;
        },
        ready: function (cb) {
            var _this = this;
            if (_this.isready) {
                cb(true, this.wx);
            } else {
                if (!this.wx) {
                    require.config({
                        paths: {
                            wxsdk: 'http://res.wx.qq.com/open/js/jweixin-1.2.0'
                        }
                    });
                    require(['wxsdk'], function (wx) {
                        _this.wx = wx;
                        _this.initConfig(function (issucc, err) {
                            if (!issucc) {
                                cb(false, err);
                            } else {
                                cb(true, _this.wx);
                            }
                        });
                    })
                } else {
                    _this.initConfig(function (issucc, err, objerr) {
                        if (!issucc) {
                            cb(false, err, objerr);
                        } else {
                            cb(true, _this.wx);
                        }
                    });
                }
            }
        },
        initConfig: function (cb) {//初始化配置
            var _this = this;
            if (!this.wx || !this.zhtype) {
                cb && cb(false, '未初始化wxtool');
                return;
            }
            if (!this.iswx()) {
                cb && cb(false, '不是微信浏览器');
                return;
            }
            var wx = _this.wx;
            var zhtype = _this.zhtype;
            $.sm(function (re, err) {
                if (re) {
                    var wxinfo = JSON.parse(re);
                    wx.config({
                        debug: false,
                        appId: wxinfo['appId'],
                        timestamp: wxinfo['timestamp'],
                        nonceStr: wxinfo['nonceStr'],
                        signature: wxinfo['signature'],
                        jsApiList: _this.jsApiList
                    });
                    wx.ready(function () {
                        _this.isready = true;
                        cb && cb(true);
                    });
                    wx.error(function (res) {
                        cb && cb(false, "wxerror", res);
                        console.log("wx.error" + JSON.stringify(res));
                    });
                } else {
                    cb && cb(false, err);
                }
            }, ["wx_jssdk_config", zhtype, parent.location.href.split('#')[0]]);
        }, getPosition: function (cb) {//获取位置
            this.ready(function (issucc, err) {
                if (!issucc) {
                    cb(false, err);
                } else {
                    getPosition(cb);
                }
            });
        }, getLocalImaDataOneByOne: function (localIds, objLocalData, cb) {//一个个获取imgdata base64格式
            var _this = this;
            var localId = localIds.pop();
            _this.wx.getLocalImgData({
                localId: localId,
                success: function (res) {
                    var localData = res.localData;
                    objLocalData[localId] = localData;
                    if (!localIds.length) {
                        cb(objLocalData);
                    } else {
                        _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                    }
                }, fail: function (a, b) {
                    alert(a);
                    if (!localIds.length) {
                        cb(objLocalData);
                    } else {
                        _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                    }
                }
            });
        }
        , getAllLocalImgData: function (localIds, cb) {//获取所有的imgdata base64格式
            var _this = this;
            _this.ready(function (issucc, wx, objerror) {
                if (issucc) {
                    var objLocalData = {};
                    _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                }
            });
        }
        , uploadImageOneByOne: function (wx, arrLocalId, arrServerId, config, index) {//一个个上传到微信服务器 返回serverid
            var _this = this;
            var localId = arrLocalId[index];
            index++;
            config.startOne && config.startOne(index);
            wx.uploadImage({
                localId: localId,
                isShowProgressTips: 0, // 默认为1，显示进度提示
                success: function (res) {
                    arrServerId.push(res.serverId);
                    config.completeOne && config.completeOne(true, index, localId, res.serverId);
                    if (index >= arrLocalId.length) {
                        config.complete && config.complete(arrServerId);
                    } else {
                        _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, index);
                    }
                }, fail: function (a, b) {
                    arrServerId.push('');
                    config.completeOne && config.completeOne(false, index, localId);
                    if (index >= arrLocalId.length) {
                        config.complete && config.complete(arrServerId);
                    } else {
                        _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, index);
                    }
                }
            });
        }
        , uploadLocalImage: function (arrLocalId, config) {//上传多个图片
            var _this = this;
            _this.ready(function (issucc, wx) {
                if (issucc) {
                    var arrServerId = [];
                    if (!config) {
                        config = {};
                    } else {
                        if (typeof config == "function") {
                            config = {
                                complete: config
                            }
                        }
                    }
                    _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, 0);
                }
            });
        }
        , uploadOneLocalImage: function (localId, cb) {//上传单个图片
            var _this = this;
            _this.ready(function (issucc, wx) {
                if (issucc) {
                    wx.uploadImage({
                        localId: localId,
                        isShowProgressTips: 0, // 默认为1，显示进度提示
                        success: function (res) {
                            cb && cb(res.serverId);
                        }, fail: function (a, b) {
                            cb && cb();
                        }
                    });
                } else {
                    cb && cb();
                }
            });
        }, chooseImage: function (option) {//选择图片
            var _this = this;
            _this.ready(function (issucc, wx, objerror) {
                if (!issucc) {
                    option.fail && option.fail(wx, objerror);
                    return;
                }
                wx.chooseImage({
                    count: option.count ? option.count > 9 ? 9 : option.count : 9, // 默认9
                    sizeType: option.sizeType || ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: option.sourceType || ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                    success: function (res) {
                        //日志
                        if (res.errMsg == "chooseImage:ok") {
                            var localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                            if (_this.isios) {
                                //日志
                                var arrlocalid = JSON.parse(JSON.stringify(localIds));
                                _this.getAllLocalImgData(arrlocalid, function (objLocalImgData) {
                                    option.success(localIds, objLocalImgData);
                                });
                            } else {
                                option.success(localIds);
                            }
                            //option.success(localIds);
                        } else {
                            option.fail && option.fail(issucc, wx);
                        }
                    }
                });
            });
        }, upToOss: function (obj) {
            var _this = this;
            var dir = _this.zhtype + "/" + (obj.funtype || "test");
            if (obj.arrtoup.length <= 0)return;
            if (!_this.osssignature) {
                $.sm(function (re, err) {
                    if (re) {
                        _this.upexpire = re.expire;
                        _this.osssignature = {
                            'key': re.dir,
                            'policy': re.policy,
                            'OSSAccessKeyId': re.accessid,
                            'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                            'signature': re.signature
                        };
                    }
                }, ["oss.getsignature", dir], "", "", {
                    async: false
                });
            }
            _this.upToOssStart(obj);
        }, upToOssStart: function (obj) {
            this.upToOssOneByOne(obj.arrtoup, 0, obj);
        }, upToOssOneByOne: function (arrtoup, i, objconfig) {
            var _this = this;
            if (i == arrtoup.length) {
                objconfig.complete && objconfig.complete();
                return;
            }
            var oneobj = arrtoup[i];
            i++;
            objconfig.onestart && objconfig.onestart(oneobj, i);//第i张图片开始上传 回调
            var localId = oneobj.localId;
            _this.wx.uploadImage({
                localId: localId,
                isShowProgressTips: 0, // 默认为1，显示进度提示
                success: function (res) {
                    alert(res.serverId)
                    _this.wx.downloadImage({
                        serverId:  res.serverId, // 需要下载的图片的服务器端ID，由uploadImage接口获得
                        isShowProgressTips: 0, // 默认为1，显示进度提示
                        success: function (res) {
                            var localId = res.localId; // 返回图片下载后的本地ID
                            _this.wx.getLocalImgData({
                                localId: localId, // 图片的localID
                                success: function (res) {
                                    oneobj.imgData = res.localData;
                                    $.sm(function (re, err) {

                                    }, ["tool.log", res.localData]);
                                    alert("获取数据")
                                    _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                                        objconfig.completeOne && objconfig.completeOne(oneobj, i);
                                        _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                                    });
                                },fail:function (res) {
                                    objconfig.completeOne && objconfig.completeOne(oneobj, i);
                                    _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                                    alert(JSON.stringify(res));
                                    $.sm(function (re, err) {

                                    }, ["tool.log", res.errMsg]);
                                }
                            });
                        },fail:function (res) {
                            $.sm(function (re, err) {

                            }, ["tool.log", res.errMsg]);
                            console.log(res.errMsg)
                            objconfig.completeOne && objconfig.completeOne(oneobj,i);
                            _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                        }
                    });
                }, fail: function (res) {
                    console.log(res.errMsg)
                    objconfig.completeOne && objconfig.completeOne(oneobj,i);
                    _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                }
            });
            /*if (oneobj.imgData) {
                _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                    objconfig.completeOne && objconfig.completeOne(oneobj, i);
                    _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                });
            } else {
                this.getAllLocalImgData([localId], function (objImgData) {
                    oneobj.imgData = objImgData[localId];
                    _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                        objconfig.completeOne && objconfig.completeOne(oneobj, i);
                        _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                    });
                })
            }*/
        }, uploadwximg: function (arrlocalid, objconfig) {
            if (!objconfig) {
                objconfig = {};
            }
            var _this = this;
            _this.ready(function (issucc, wx) {
                if (issucc) {
                    var arrObj = [];
                    for (var i = 0; i < arrlocalid.length; i++) {
                        (function (i) {
                            wx.uploadImage({
                                localId: localId,
                                isShowProgressTips: 0, // 默认为1，显示进度提示
                                success: function (res) {
                                    arrServerId.push(res.serverId);
                                    config.completeOne && config.completeOne(true, index, localId, res.serverId);
                                    if (index >= arrLocalId.length) {
                                        config.complete && config.complete(arrServerId);
                                    } else {
                                        _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, index);
                                    }
                                }, fail: function (a, b) {
                                    arrServerId.push('');
                                    config.completeOne && config.completeOne(false, index, localId);
                                    if (index >= arrLocalId.length) {
                                        config.complete && config.complete(arrServerId);
                                    } else {
                                        _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, index);
                                    }
                                }
                            });
                        })(i);

                    }
                } else {
                    objconfig.error && objconfig.error("微信未准备好");
                }
            });
        }, upToOssOne: function (oneobj, objconfig, cb) {
            var _this = this;
            var base64 = '';
            var fileType = '';
            var fileafter = '';
            if (!oneobj.imgData) {
                cb && cb(oneobj);
                return;
            }
            if (this.isios) {
                oneobj.imgData = oneobj.imgData.replace('image/jgp', 'image/jpeg');
                base64 = oneobj.imgData.split(',')[1];
                fileType = oneobj.imgData.split(';')[0].split(':')[1];
                fileafter = '.jpeg';
            } else {
                base64 = oneobj.imgData;
                fileType = 'image/jpg';
                fileafter = '.jpg';
            }
            var blob = toBlob(base64, fileType);
            if (!(objconfig.needsize === false))
                oneobj.size = blob.size;
            var filename = makeFilename() + fileafter;
            var request = new FormData();
            request.append("OSSAccessKeyId", _this.osssignature.OSSAccessKeyId);//Bucket 拥有者的Access Key Id。
            request.append("policy", _this.osssignature.policy);//policy规定了请求的表单域的合法性
            request.append("Signature", _this.osssignature.signature);//根据Access Key Secret和policy计算的签名信息，OSS验证该签名信息从而验证该Post请求的合法性
            //---以上都是阿里的认证策略
            request.append("key", _this.osssignature.key + "/" + filename);//文件名字，可设置路径
            request.append("success_action_status", '200');// 让服务端返回200,不然，默认会返回204
            request.append('file', blob);//需要上传的文件 file
            //request.append("callback", "");//回调，非必选，可以在policy中自定义
            $.ajax({
                url: _this.osshost,  //上传阿里地址
                data: request,
                processData: false,//默认true，设置为 false，不需要进行序列化处理
                cache: false,//设置为false将不会从浏览器缓存中加载请求信息
                //async: false,//发送同步请求
                contentType: false,//避免服务器不能正常解析文件---------具体的可以查下这些参数的含义
                //dataType: 'JSONP',//不涉及跨域  写json即可
                type: 'post',
                success: function (callbackHost, request) { //callbackHost：success,request中就是 回调的一些信息，包括状态码什么的
                    /*$("body").append("<div style='width: 80%'><img src=" + _this.osshost + "/" + _this.osssignature.key + filename + "></div>");//动态向页面添加上传图片*/
                    oneobj.url = _this.osssignature.key + "/" + filename;
                    if (objconfig.needwh) {
                        _this.getImageWidth(_this.osshost + "/" + _this.osssignature.key + "/" + filename, function (w, h) {
                            oneobj.width = w;
                            oneobj.height = h;
                            cb && cb(oneobj, _this.osshost);
                        })
                    } else {
                        cb && cb(oneobj, _this.osshost);
                    }
                },
                error: function (returndata) {
                    cb && cb(oneobj);
                    alert("上传图片出错");
                    alert(JSON.stringify(returndata));
                }
            });
        }, getImageWidth: function (url, callback) {
            if (!url) {
                return callback(null, null, "图片地址为空");
            }
            var img = new Image();
            img.src = url;
            // 如果图片被缓存，则直接返回缓存数据
            if (img.complete) {
                callback(img.width, img.height);
            } else {
                // 完全加载完毕的事件
                img.onload = function () {
                    callback(img.width, img.height);
                }
                img.onerror = function () {
                    callback(null, null, "获取图片失败");
                }
            }
        }
    };

    //
    function toBlob(urlData, fileType) {
        var bytes = window.atob(urlData),
            n = bytes.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bytes.charCodeAt(n);
        }
        return new Blob([u8arr], {type: fileType});
    }

    //生成文件名字的key
    function makeFilename() {
        var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
        var maxPos = chars.length;
        var random_string = '';
        for (i = 0; i < 10; i++) {
            random_string += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return random_string + new Date().getTime();
    }

    function getPosition(cb) {
        wxtool.wx.getLocation({
            type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
            success: function (res) {
                console.log(JSON.stringify(res));
                var latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
                var longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
                // var speed = res.speed; // 速度，以米/每秒计
                // var accuracy = res.accuracy; // 位置精度
                //转换为百度坐标
                getBaiduPosition(longitude, latitude, cb);
            }, fail: function (res) {
                cb && cb(false);
            }
            , cancel: function (res) {
                cb && cb(false);
            }
        });
    }

    function getBaiduPosition(lng, lat, callback) {
        var url = "http://api.map.baidu.com/geoconv/v1/?coords=" + lng + "," + lat + "&from=1&to=5&ak=" + baiduak;
        $.ajax({
            url: url,
            type: 'GET',
            contentType: "application/json",
            dataType: 'jsonp',//这里要用jsonp的方式不然会报错
            success: function (data) {
                var lng = objdata.lng = data.result[0].x;//经度
                var lat = objdata.lat = data.result[0].y;//纬度
                getBaiduAdderss(lng, lat, callback);
            }
        });
    }

    function getBaiduAdderss(lng, lat, callback) {
        var url = "http://api.map.baidu.com/geocoder/v2/?callback=renderReverse&location=" + lat + "," + lng + "&output=json&pois=1&ak=" + baiduak;
        $.ajax({
            url: url,
            type: 'GET',
            contentType: "application/json",
            dataType: 'jsonp',//这里要用jsonp的方式不然会报错
            success: function (data) {
                objdata.address = data.result.formatted_address;
                objdata.country = data.result.addressComponent.country;
                var province = objdata.province = data.result.addressComponent.province;
                var city = objdata.city = data.result.addressComponent.city;
                var district = objdata.district = data.result.addressComponent.district;
                var street = data.result.addressComponent.street;
                if (province === city) {
                    province = '';
                }
                String(lng).length > 15 ? lng = String(lng).substring(0, 15) : null;
                String(lat).length > 15 ? lat = String(lat).substring(0, 15) : null;
                var obj = data.result;
                obj.lng = lng;
                obj.lat = lat;
                callback && callback(true, obj);
            }
        });
    }

    return wxtool;
});
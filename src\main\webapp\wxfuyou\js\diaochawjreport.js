layui.config({
    base: './js/'
}).extend({ //设定模块别名
    system: '../../../sys/system',
    promise: '../../../plugin/promise/promise'
});
var objdata = {
    strOssSize: "?x-oss-process=image/resize,m_fill,h_180,w_180",
}, from, rate;
layui.use(['system', 'form', 'rate'], function () {
    form = layui.form;
    rate = layui.rate;
    form.render();
    $("#yeyname").html(Arg("yeyname"));
    $("#classname").html(Arg("classname"));
    $("#stuname").html(Arg("stuname"));
    $("#frompname").html(Arg("frompname"));
    $("#stuname_pttype").html(Arg("stuname") + Arg("frompname"));
    timer();
    localStorage.setItem("token", Arg("token"));
    getWjPerson(function () {
        initData(Arg("wjid"));
    })
    initEvent();
});


function timer() {
    $("#duration_text").text('0秒');
    $("#duration").val('0秒');
    var begintime = new Date();
    setInterval(function () {
        var endtime = new Date();
        var seconds = endtime.getTime() - begintime.getTime();
        seconds = Math.floor(seconds / 1000);
        var hour = Math.floor(seconds / (60 * 60));
        var mm = Math.floor(seconds / 60) % 60;
        var dd = seconds % 60 % 60;
        var duration = "";
        if (hour > 0) {
            duration = hour + "小时" + mm + "分钟" + dd + "秒";
        } else {
            if (mm > 0) {
                duration = mm + "分钟" + dd + "秒";
            } else {
                duration = dd + "秒";
            }
        }
        $("#duration_text").text(duration);
        $("#duration").val(seconds);
    }, 1000)
}

function initData(id) {
    $.sm(function (re, err) {
        if (re[0].error || re[1].error) {
            return jQuery.getparent().layer.msg("系统错误");
        }
        if (re[0].isniming) {
            $("#isniming").show();
        }
        $("#wtitle").html(re[0].wtitle);
        $("#introduce").html(re[0].introduce);
        form.render();
        for (var i = 0; i < re[1].length; i++) {
            createHtmlByType(re[1][i]);
        }
    }, [["wenjuan.selectbyid", id],//id,wtitle,introduce,isniming,ispub
        ["wenjuanti.selectbywjid", id]])//id,wenjuanid,wentitype,wentititle,wentiitem,ismust,sort,zhibiaoid
}

function getWjPerson(cb) {
    $.sm(function (re, err, obj) {
        if (err) {
            return layer.msg(err);
        }
        if (obj.num == 0) {
            $("#wenjuan").show()
            if(Arg("from") == 'teacher'){//从教师端跳转来的
                $("#btnsave").hide();
                $(".nobefore").hide();
            }
            cb && cb();
        } else {
            $("#no-record").show();
        }
    }, ["wenjuantiperson.selectbyfromid", Arg("wjid"), Arg("yeyid"), Arg("fromid")])
}

function initEvent() {
    $("#btnsave").click(function () {
        var wjid = Arg("wjid");
        var reportid = Arg("reportid") || 0;
        var yeyid = Arg("yeyid");
        var yeyname = Arg("yeyname");
        var classno = Arg("classno");
        var classname = Arg("classname");
        var stuno = Arg("stuno");
        var stuname = Arg("stuname");
        var fromptype = Arg("fromptype");
        var frompname = Arg("frompname");
        var fromopenid = Arg("fromopenid");
        var fromid = Arg("fromid");
        var fromname = Arg("fromname");
        var duration = $("#duration").val();
        var arrpm = [];
        var arrptye = ["", "爸爸", "妈妈", "爷爷", "奶奶", "姥爷", "姥姥", "叔叔", "阿姨", "哥哥", "姐姐"];
        arrpm.push(["wenjuantiperson.insert", reportid, wjid, yeyid, yeyname, classno, classname, stuno, stuname, arrptye.indexOf(fromptype), fromopenid, fromid, fromname, duration]);
        var arrDom = $("#arrquestion").find(".question-outer");
        for (var i = 0; i < arrDom.length; i++) {
            var dom = arrDom.eq(i);
            var wtid = dom.data("wtid");
            var type = dom.data("type");
            var ismust = dom.data("ismust");
            var sort = dom.data("sort");
            switch (type) {
                case 'select':
                    var checkedDom = $("[name=radio_" + wtid + "]:checked");
                    if (ismust == 1 && checkedDom.length == 0) {
                        return layer.msg("第" + sort + "道题未选择");
                    }
                    var wentiitem = checkedDom.val();
                    var wentiresult = "";
                    var tikongDom = checkedDom.parents(".weui-flex_center").siblings(".tikong");
                    if (tikongDom.length) {
                        wentiresult = tikongDom.val();
                    }
                    arrpm.push(["wjwentiresult.insert", reportid, wjid, wtid, wentiitem, wentiresult, yeyid, yeyname, fromopenid, fromid]);
                    break;
                case 'multi':
                    var checkedDom = $("[name=checkbox_" + wtid + "]:checked");
                    if (ismust == 1 && checkedDom.length == 0) {
                        return layer.msg("第" + sort + "道题未选择");
                    }
                    for (var j = 0; j < checkedDom.length; j++) {
                        var wentiitem = checkedDom.eq(j).val();
                        var wentiresult = "";
                        var tikongDom = checkedDom.eq(j).parents(".weui-flex_center").siblings(".tikong");
                        if (tikongDom.length) {
                            wentiresult = tikongDom.val();
                        }
                        arrpm.push(["wjwentiresult.insert", reportid, wjid, wtid, wentiitem, wentiresult, yeyid, yeyname, fromopenid, fromid]);
                    }
                    break;
                case 'question':
                    var wentiitem = "";
                    var wentiresult = $("#question_" + wtid).val();
                    if (ismust == 1 && !wentiresult) {
                        return layer.msg("第" + sort + "道题未填");
                    }
                    arrpm.push(["wjwentiresult.insert", reportid, wjid, wtid, wentiitem, wentiresult, yeyid, yeyname, fromopenid, fromid]);
                    break
                case 'star':
                    var wentiitem = $("#input_" + wtid).val();
                    var wentiresult = "";
                    if (ismust == 1 && !wentiitem) {
                        return layer.msg("第" + sort + "道题未评分");
                    }
                    arrpm.push(["wjwentiresult.insert", reportid, wjid, wtid, wentiitem, wentiresult, yeyid, yeyname, fromopenid, fromid]);
                    break
            }
        }
        $.sm(function (re, err) {
            if (re[0].error) {
                return layer.msg("系统错误");
            }
            $("#wenjuan").hide();
            $("#no-record").show();
        }, arrpm, null, null, {trans: 1})
    })

    $("#btnclose").click(function(){
        history.back();
    })
}

/**
 * 根据题型生成对应的html
 * @param type
 * @param objQuestion
 */
function createHtmlByType(objQuestion) {
    var type = objQuestion.wentitype;
    var id = objQuestion.id;
    var wentititle = objQuestion.wentititle;
    var ismust = objQuestion.ismust;
    var sort = objQuestion.sort;
    var zhibiaoid = objQuestion.zhibiaoid || '';
    var wentiitem = JSON.parse(objQuestion.wentiitem || '[]');
    var strhtml = "";
    switch (type) {
        case 'select':
            strhtml = addSelectHtml(id, wentititle, ismust, sort, zhibiaoid, wentiitem);
            break;
        case 'multi':
            strhtml = addMultiHtml(id, wentititle, ismust, sort, zhibiaoid, wentiitem);
            break;
        case 'question':
            strhtml = addQuestionHtml(id, wentititle, ismust, sort, zhibiaoid);
            break
        case 'star':
            strhtml = addStarHtml(id, wentititle, ismust, sort, zhibiaoid);
            break
    }
    return strhtml;
}

/**
 * 单选题
 */
function addSelectHtml(id = 0, wentititle = '', ismust = 1, sort = 0, zhibiaoid = 0, wentiitem = []) {
    var arrHtml = [];
    arrHtml.push('<div class="question-outer" data-wtid="' + id + '" data-ismust="' + ismust + '" data-sort="' + sort + '" data-type="select" style="margin-top: 0;border-bottom: 1px solid rgba(221, 221, 221, 1);">');
    arrHtml.push('    <h5>');
    arrHtml.push('        <span><em>' + (ismust ? '*' : '') + '</em><label>' + sort + '.</label>' + wentititle + '</span>');
    arrHtml.push('    </h5>');
    arrHtml.push('    <div class="preview-cell">');
    arrHtml.push('        <ul class="set-radio-style3">');
    for (var i = 0; i < wentiitem.length; i++) {
        var name = wentiitem[i].name || '';
        var cantiankong = wentiitem[i].cantiankong || false;
        arrHtml.push('            <li>');
        arrHtml.push('                <span class="weui-flex_center">');
        arrHtml.push('                    <label><input type="radio" name="radio_' + id + '" value="' + name + '"/>' + name + '</label>');
        arrHtml.push('                </span>');
        if (cantiankong) {
            arrHtml.push('            <input type="text" class="tikong inbesti-txt">');
        }
        arrHtml.push('            </li>');
    }
    arrHtml.push('        </ul>');
    arrHtml.push('    </div>');
    arrHtml.push('</div>');
    $("#arrquestion").append(arrHtml.join(''));
}

/**
 * 多选题
 */
function addMultiHtml(id = 0, wentititle = '', ismust = 1, sort = 0, zhibiaoid = 0, wentiitem = []) {
    var arrHtml = [];
    arrHtml.push('<div class="question-outer" data-wtid="' + id + '" data-ismust="' + ismust + '" data-sort="' + sort + '" data-type="multi" style="margin-top: 0;border-bottom: 1px solid rgba(221, 221, 221, 1);">');
    arrHtml.push('    <h5>');
    arrHtml.push('        <span><em>' + (ismust ? '*' : '') + '</em><label>' + sort + '.</label>' + wentititle + '</span>');
    arrHtml.push('    </h5>');
    arrHtml.push('    <div class="preview-cell">');
    arrHtml.push('        <ul class="set-radio-style3">');
    for (var i = 0; i < wentiitem.length; i++) {
        var name = wentiitem[i].name || '';
        var cantiankong = wentiitem[i].cantiankong || false;
        arrHtml.push('            <li>');
        arrHtml.push('                <span class="weui-flex_center">');
        arrHtml.push('                    <label><input type="checkbox" name="checkbox_' + id + '" value="' + name + '"/>' + name + '</label>');
        arrHtml.push('                </span>');
        if (cantiankong) {
            arrHtml.push('            <input type="text" class="tikong inbesti-txt">');
        }
        arrHtml.push('            </li>');
    }
    arrHtml.push('        </ul>');
    arrHtml.push('    </div>');
    arrHtml.push('</div>');
    $("#arrquestion").append(arrHtml.join(''));
}

/**
 * 问答题
 */
function addQuestionHtml(id = 0, wentititle = '', ismust = 1, sort = 0, zhibiaoid = 0) {
    var arrHtml = [];
    arrHtml.push('<div class="question-outer" data-wtid="' + id + '" data-ismust="' + ismust + '" data-sort="' + sort + '" data-type="question" style="margin-top: 0;border-bottom: 1px solid rgba(221, 221, 221, 1);">');
    arrHtml.push('    <h5>');
    arrHtml.push('        <span><em>' + (ismust ? '*' : '') + '</em><label>' + sort + '.</label>' + wentititle + '</span>');
    arrHtml.push('    </h5>');
    arrHtml.push('    <div class="preview-cell">');
    arrHtml.push('      <ul>');
    arrHtml.push('          <li>');
    arrHtml.push('              <textarea id="question_' + id + '" rows="3" style="padding:5px;"></textarea>');
    arrHtml.push('          </li>');
    arrHtml.push('      </ul>');
    arrHtml.push('    </div>');
    arrHtml.push('</div>');
    $("#arrquestion").append(arrHtml.join(''));
}

/**
 * 评星题
 */
function addStarHtml(id = 0, wentititle = '', ismust = 1, sort = 0, zhibiaoid = 0) {
    var arrHtml = [];
    arrHtml.push('<div class="question-outer" data-wtid="' + id + '" data-ismust="' + ismust + '" data-sort="' + sort + '" data-type="star" style="margin-top: 0;border-bottom: 1px solid rgba(221, 221, 221, 1);">');
    arrHtml.push('    <h5>');
    arrHtml.push('        <span><em>' + (ismust ? '*' : '') + '</em><label>' + sort + '.</label>' + wentititle + '</span>');
    arrHtml.push('    </h5>');
    arrHtml.push('    <div class="preview-cell">');
    arrHtml.push('      <ul>');
    arrHtml.push('          <li>');
    arrHtml.push('              <div id="star_' + id + '" class="star"></div>');
    arrHtml.push('              <input id="input_' + id + '" type="hidden" value="0"/>');
    arrHtml.push('          </li>');
    arrHtml.push('      </ul>');
    arrHtml.push('    </div>');
    arrHtml.push('</div>');
    $("#arrquestion").append(arrHtml.join(''));
    rate.render({
        elem: '#star_' + id,  //绑定元素
        choose: function (value) {
            $("#input_" + id).val(value);
        }
    });
}



















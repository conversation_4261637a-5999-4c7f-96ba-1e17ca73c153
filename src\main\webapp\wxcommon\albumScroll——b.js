﻿define(['../../plugin/iscroll/js/iscroll','../wxcommon/scrollclass.js'],function () {
    var elem;
    var obj = {};
    /**
     * 查询数据
     * @param ismore
     * @param callback
     * @param isfirst
     */
    function querydata(ismore, callback) {
        var swhere = '';
        if (!objRe.isFirst) {
            if (ismore) {
                swhere += " and mtime < '" + objRe.starttime +"'";
            } else {
                swhere += " and mtime > '" + objRe.endtime +"'";
            }
        }
        objRe.dbParam.swhere = swhere;
        $.sm(function (re, err) {
            if (re) {
                callback(re);
                if(ismore){
                    objRe.arrdata = objRe.arrdata.concat(re);
                } else {
                    objRe.arrdata = re.concat(objRe.arrdata);
                }
            } else {
                callback([]);
            }
        }, ['weixin.material.getData', JSON.stringify(objRe.dbParam)]);
    }

    /**
     * @param arr[Arrary] 消息列表数据
     * @param noticeLen[Number|null] 页面已有消息条数
     * @param direction["up"|"down"|default down] 消息插入方向
     * */
    function makeHtml(arr, direction) {
        var html = '';
        if (arr.length) {
            objRe.isFirst = false;
            var lastHtml = '';
            var arrDay = [];
            var oneDay = {};
            for (var i = 0; i < arr.length; i++) {  //
                var obj = arr[i];
                var strDate = obj.mtime.split(" ")[0];//得到日期
                if(objRe.lastDate && (strDate == objRe.lastDate)){//如果和上次最后的日期一样
                    objRe.lastDate_old = objRe.lastDate;
                    lastHtml += makeonehtml(obj);
                } else {
                    if(strDate != objRe.curdate){//如果是新的日期 生成日期
                        objRe.curdate = strDate;
                        oneDay = {
                            strDay : strDate,
                            html:''
                        }
                        arrDay.push(oneDay);
                    } else {
                        oneDay = arrDay[arrDay.length-1];
                    }
                    oneDay.html += makeonehtml(obj);
                }
            }
            objRe.lastDate = objRe.curdate;
            //如果有上次的  先把上次的放上
            if(lastHtml){
                elem.find(".photo-list[data-date='"+ objRe.lastDate_old +"']").append(lastHtml);
            }
            //循环天数
            for(var j=0;j<arrDay.length;j++){
                html += '<div class="photo-pic">'+
                            '<p class="time-star">'+arrDay[j].strDay+'</p>'+
                            '<i class="iconfont"></i>'+
                            '<div class="photo-stat">'+
                                '<div class="photo-list" data-date="'+arrDay[j].strDay+'">'+
                                    arrDay[j].html +
                                '</div>'+
                            '</div>'+
                        '</div>';
            }
        }
        return html;
    }
    function makeonehtml(obj) {
        //objRe.data[obj.id] = obj;
        return '<div class="pho-medi">'+
                    '<div>'+
                        '<img  data-id="'+ obj.id +'" src="'+ ossPrefix + obj.murl + '?x-oss-process=image/resize,m_fill,w_300,limit_0,h_300' +'" class="medi-pho">'+
                    '</div>'+
                '</div>';
    }
    var objRe = {};
    var objReturn = {
        isFirst:true,
        init: function (objConfig) {
            var objCur = {
                isFirst:true
            };
            objRe = objCur;
            objCur.curdate = "";//当前处理日期
            objCur.lastDate = "";//最后一个日期
            objCur.lastDate_old = "";//上次的最后一个日期
            objCur.data = {};
            objCur.arrdata = [];
            objCur.dbParam = objConfig.dbParam;
            obj = objCur;
            //objCur.dbParam  = JSON.parse(JSON.stringify(objParam));
            elem = $("#" + objConfig.wrapperId);
            //查询班级数据，并显示
            querydata(true, function (re) {
                if (re.length > 0) {
                    elem.find(".thelist").html(makeHtml(re));
                    objCur.endtime = re[0].mtime;
                    objCur.starttime = re[re.length - 1].mtime;
                } else {
                    elem.find(".thelist").siblings('.pullUp').children('.pullUpLabel').html('暂无内容');
                }
                setTimeout(function () {
                    objCur.myscroll = new scrollClass({
                        wrapper: objConfig.wrapperId,
                        // onScrollMove:function (_this) {
                        //     var top = -(_this.y+objCur.myscroll.pullDownOffset);
                        //     if(top>174){
                        //         $("#head").show();
                        //     } else {
                        //         $("#head").hide();
                        //     }
                        // },
                        // onScrollEnd:function (_this) {
                        //     var top = -(_this.y+objCur.myscroll.pullDownOffset);
                        //     if(top>174){
                        //         $("#head").show();
                        //     } else {
                        //         $("#head").hide();
                        //     }
                        // },
                        downAction: function () {
                            querydata(false, function (re) {
                                if (re.length > 0) {
                                    elem.find(".thelist").prepend(makeHtml(re, "down"));
                                    objCur.endtime = re[0].mtime;
                                }
                                objCur.myscroll.scroll.refresh();
                            });
                        },
                        upAction: function () {
                            querydata(true, function (re) {
                                if (re.length > 0) {
                                    //上拉之前消息条数
                                    elem.find(".thelist").append(makeHtml(re, "up"));
                                    objCur.myscroll._wrapper.find('.pullUpLabel').html('下拉加载更多');
                                    objCur.starttime = re[re.length - 1].mtime;
                                    setTimeout(function () {
                                        objCur.myscroll.scroll.refresh();
                                    },1000);
                                    setTimeout(function () {
                                        objCur.myscroll.scroll.refresh();
                                    },3000);
                                } else {
                                    objCur.myscroll._wrapper.find('.pullUpLabel').html('没有啦');
                                    objCur.myscroll._wrapper.find('.pullUpIcon').hide();
                                }
                            });
                        }
                    });
                    objCur.myscroll.init();

                    setTimeout(function () {
                        objCur.myscroll.scroll.refresh();
                    },1000);
                    setTimeout(function () {
                        objCur.myscroll.scroll.refresh();
                    },3000);
                    setTimeout(function () {
                        elem.css('left',0);
                        //document.getElementById('wrapper').style.left = '0';
                    }, 10);
                }, 10);
            });
            this.initEvent(elem);
            return objCur;
        },initEvent:function (elem) {
            elem.find(".thelist").on('click','img',function () {
                var curid = $(this).data("id");
                var arr = [];
                var curindex = 0;
                var curi = 0;
                for(var i=0;i<objCur.arrdata.length;i++){
                    var obj = objCur.arrdata[i];
                    var id = obj.id;
                    if(id==curid){
                        curindex = i;
                    }
                    var objmedia = JSON.parse(obj.mdetail);
                    if(obj.mtype=='image'){
                        arr.push({
                            id:id,
                            src:  ossPrefix + obj.murl,
                            w: objmedia.w,
                            h: objmedia.h,
                            title:'<div>图片介绍'+id+'</div>'+
                            '<div><span>编辑</span><span style="float: right">删除</span></div>'
                        });
                    } else {
                        arr.push({
                            id:id,
                            html: '<div style="text-align: center">'+
                            '<video width="80%" height="80%" controls  autoplay="autoplay">'+
                            '<source src="'+ossPrefix + objmedia.videourl+'" type="video/mp4">'+
                            '您的浏览器不支持Video标签。'+
                            '</video>'
                            +'</div>',
                            title:'<div>图片介绍'+id+'</div>'+
                            '<div><span>编辑</span><span style="float: right">删除</span></div>'
                        })
                    }
                }
                /*elem.find(".thelist").find("img").each(function () {
                 var id = $(this).data("id");
                 var obj = objCur.data[id];
                 if(id==curid){
                 curindex = curi;
                 } else {
                 curi+=1;
                 }
                 var objmedia = JSON.parse(obj.mdetail);
                 if(obj.mtype=='image'){
                 arr.push({
                 id:id,
                 src:  ossPrefix + obj.murl,
                 w: objmedia.w,
                 h: objmedia.h,
                 title:'<div>图片介绍'+id+'</div>'+
                 '<div><span>编辑</span><span style="float: right">删除</span></div>'
                 });
                 } else {
                 arr.push({
                 id:id,
                 html: '<div style="text-align: center">'+
                 '<video width="80%" height="80%" controls  autoplay="autoplay">'+
                 '<source src="'+ossPrefix + objmedia.videourl+'" type="video/mp4">'+
                 '您的浏览器不支持Video标签。'+
                 '</video>'
                 +'</div>',
                 title:'<div>图片介绍'+id+'</div>'+
                 '<div><span>编辑</span><span style="float: right">删除</span></div>'
                 })
                 }
                 })*/
                require(['../wxcommon/photoswipe.js'],function (objswipe) {
                    var shareButtons;
                    if(objCur.dbParam.roletype=='yey'&&objCur.dbParam.datatype=='my'){//老师打开我的相册
                        //判断打开的是班级相册 还是宝宝相册
                        if(objClass.curLocation){
                            if(objClass.curLocation.type=='class') {
                                shareButtons = [
                                    {
                                        id:'moveto',
                                        label:'复制到'+ objClass.curLocation.classname +'相册',
                                        click:function (elem) {debugger
                                            $.sm(function (re,err) {
                                                if(re){
                                                    $.toast('复制成功');
                                                } else {
                                                    $.toast('复制失败');
                                                }
                                            },["weixin.material.move",JSON.stringify(objCur.dbParam),JSON.stringify({
                                                type:'copy',
                                                id:elem.id,
                                                classname:objClass.curLocation.classname,
                                                classno:objClass.curLocation.classno
                                            })]);
                                        }
                                    },{
                                        id:'copyto',
                                        label:'移动到'+ objClass.curLocation.classname +'相册',
                                        click:function (elem) {
                                            $.sm(function (re) {
                                                if(re){
                                                    $.toast('移动成功');
                                                } else {
                                                    $.toast('移动失败');
                                                }
                                            },["weixin.material.move",JSON.stringify(objCur.dbParam),JSON.stringify({
                                                type:'move',
                                                id:elem.id,
                                                classname:objClass.curLocation.classname,
                                                classno:objClass.curLocation.classno
                                            })]);
                                        }
                                    }
                                ]
                            } else {
                                shareButtons = [
                                    {
                                        id:'moveto',
                                        label:'移动到'+objClass.curLocation.stuname+'宝宝的相册',
                                        click:function (elem) {

                                        }}
                                ]
                            }
                        }
                    } else {

                    }
                    objswipe.init(arr,curindex,{
                        shareButtons:shareButtons
                    });
                })
            });
        }
    };
    return objReturn;
});
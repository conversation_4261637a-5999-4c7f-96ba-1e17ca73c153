<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<title>开学核检-达标状态</title>
	<link type="text/css" rel="stylesheet" href="css/reset.css">
	<link type="text/css" rel="stylesheet" href="css/weui.css"><!--../wxcommon/weui/weui.min.css-->
	<link type="text/css" rel="stylesheet" href="css/commonhome.css">
	<link type="text/css" rel="stylesheet" href="css/style.css?v=5">
	<link type="text/css" rel="stylesheet" href="css/icon.css">
	<link type="text/css" rel="stylesheet" href="verify.css">
	<link rel="stylesheet" href="../plugin/layer_mobile/layer.css">
	<style>
		body {
			background: #f7f7f7;
		}
		.wrapouter,.circle,.percent{
			position: absolute;
			width: 120px;
			height: 120px;
			border-radius: 50%;
		}
		.wrapouter{
			background-color: #eee;
			left: 50%;
			margin-left: -60px;
		}
		.circle{
			box-sizing: border-box;
			border:20px solid #eee;
			clip:rect(0,120px,120px,60px);
		}
		.clip-auto{
			clip:rect(auto, auto, auto, auto);
		}
		.percent{
			box-sizing: border-box;
			top:-20px;
			left:-20px;
		}
		.left{
			transition:transform ease;
			border:20px solid #fc784f;
			clip: rect(0,60px,120px,0);
		}
		.right{
			border:20px solid #fc784f;
			clip: rect(0,120px,120px,60px);
		}
		.wth0{
			width:0;
		}
		.centercontent{
			position: absolute;
			box-sizing: border-box;
			width: 100px;
			height: 100px;
			text-align: center;
			left: 10px;
			top: 10px;
			border-radius: 50%;
			background-color: #4dadf9;
			z-index: 1;
			padding: 5px 10px 0 10px;
		}
		/*弹框样式*/
		.frames {
			position: absolute;
			top: 0;
			bottom: 45px;
			width: 100%;
			-webkit-overflow-scrolling: touch;
			overflow-y: scroll;
		}
		.oneFrame {
			display: none;
			width: 100%;
			height: 100%;
			border: none;
			position: absolute;
			left: 0;
			top: 0;
		}
		.oneopenpage {
			display: none;
		}
		.oneopenpage, .oneopenpage_overlay {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			width: 100%;
			height: 100%;
		}
		.oneopenpage_overlay {
			background-color: rgba(0, 0, 0, .6);
			opacity: 0;
			-webkit-transition: opacity .3s;
			transition: opacity .3s
		}
		.oneopenpage_frame {
			width: 100%;
			height: 100%;
			position: absolute;
			z-index: 100;
			top: 0;
			bottom: 0;
			background-color: #ffffff;
			border: none;
			border-radius: 0;
			opacity: .6;
			-webkit-transition-duration: .3s;
			transition-duration: .3s;
			-webkit-transform: translate3d(100%, 0, 0);
			transform: translate3d(100%, 0, 0);
			-webkit-transition-property: opacity, -webkit-transform;
			transition-property: opacity, -webkit-transform;
			transition-property: transform, opacity;
			transition-property: transform, opacity, -webkit-transform;
		}
		.oneopenpage--visible {
			display: block;
		}
		.oneopenpage--visible .oneopenpage_overlay {
			opacity: 1;
		}
		.oneopenpage--visible .oneopenpage_frame {
			opacity: 1;
			-webkit-transform: translate3d(0, 0, 0);
			transform: translate3d(0, 0, 0)
		}
		.layui-m-layer-changeyey .layui-m-layercont {
			padding: 20px;
		}
		/*未关注样式*/
		.focus_bot{ position: absolute; bottom: 0; text-align:center; width: 100%}
		.focus_bot img{text-align: center}
		.focus_botimg img{width: 280px;vertical-align: bottom;left:10px;}
		.focus_bot_cen{margin: 10px auto 0px auto; width: 280px;}
		.focus_bot_cen img{width: 200px;}
		@media (device-height:480px) and (-webkit-min-device-pixel-ratio:2){/* 兼容iphone4/4s */
			.focus_botimg img{width: 220px;}
		}
		@media (device-height:568px) and (-webkit-min-device-pixel-ratio:2){/* 兼容iphone5 */
			.focus_bot_cen{margin: 10px auto 0px auto; width:250px;}
			.focus_botimg img{ width: 250px;}
		}
		@media (device-height:667px) and (-webkit-min-device-pixel-ratio:2){/* 兼容iphone6 */
			.focus_bot_cen{width:300px;}
			.focus_botimg img{ width: 300px;}
		}
		@media (device-height:736px) and (-webkit-min-device-pixel-ratio:2){/* 兼容iphone6 plus*/
			.focus_bot_cen{width:320px;}
			.focus_botimg img{ width: 320px;}
		}
		@media (device-height:812px) and (-webkit-min-device-pixel-ratio:2){/* 兼容iphoneX */
			.focus_botimg img{width: 320px;}
			.focus_bot_cen img{width: 320px;}
			.focus_bot_cen{margin: 10px auto 0px auto; width: 320px;}
		}
		@media (device-height:1024px) and (-webkit-min-device-pixel-ratio:2){/* 兼容ipad*/
			.focus_bot_cen{margin: 10px auto 0px auto; width: 500px;}
			.focus_bot_cen img{width: 334px;}
			.focus_botimg img{width: 500px;left:20px;}
		}
		@media only screen  and (min-device-height : 1024px)  and (max-device-height : 1366px){
			.focus_bot_cen{margin: 10px auto 0px auto; width: 500px;}
			.focus_bot_cen img{width: 334px;}
			.focus_botimg img{ width: 500px;left:20px;}
		}
		/*自定义*/
		.div-content,.weui-badge{display:none;}
	</style>
</head>
<body>
<section class="out-container">
	<div class="verify-wrapouter weui-flex_center">
		<div class="stat-item-num weui-flex_center">
			<div class="wrapouter">
				<div class="circle clip-auto">
					<div class="percent left" style="transform: rotate(201.6deg);"></div>
					<div class="percent right"></div>
				</div>
				<div class="centercontent">
					<p id="ptotalscore" style="padding-top: 6px;line-height: 1.5;border-bottom: 1px solid #ffffff;"><span id="hadcheck" style="font-size: 30px"></span>/<span id="allyey"></span></p>
					<p style="line-height: 1"><span id="strdate" style="font-size: 10px;color: #d3f2ff;">已核检园所/全部园所</span></p>
				</div>
			</div>
		</div>
		<div style="position: absolute;bottom: -70px;width: 100%;">
			<div class="verify-list weui-flex">
				<div style="min-width: 80px;">
					<p>未核检幼儿园</p>
					<span style="font-size: 22px;color: #4fa6ff;font-weight: bold;" id="spweiheyan"></span>
				</div>
				<div class="weui-flex" style="width: 100%;">
					<div class="verify-redcon weui-flex weui-flex__item">
						<div class="verify-lists weui-flex_center weui-flex__item">
							<p>未达标(自查)</p>
							<span style="color: #e03434;" id="bzcwdb">0</span>
						</div>
						<div class="verify-lists weui-flex_center weui-flex__item">
							<p>未达标(核查)</p>
							<span style="color: #e03434;" id="bhywdb">0</span>
						</div>
					</div>
					<div class="verify-greencon weui-flex weui-flex__item">
						<div class="verify-lists weui-flex_center weui-flex__item">
							<p>已达标(自查)</p>
							<span style="color: #0fd69c;" id="bzcdb">0</span>
						</div>
						<div class="verify-lists weui-flex_center weui-flex__item">
							<p>已达标(核查)</p>
							<span style="color: #0fd69c;" id="bhydb">0</span>
						</div>
					</div>
				</div>
			</div>
			<!--<div class="verify-list weui-flex">-->
				<!--<div style="min-width: 80px;">-->
					<!--<p>未核检</p>-->
					<!--<span style="font-size: 22px;color: #4fa6ff;font-weight: bold;" id="spweiheyan">0</span>-->
				<!--</div>-->
				<!--<div style="min-width: 80px;">-->
					<!--<p>核检未达标</p>-->
					<!--<span style="font-size: 22px;color: #e03434;font-weight: bold;" id="spweidabiao">0</span>-->
				<!--</div>-->
				<!--<div style="min-width: 80px;">-->
					<!--<p>核检已达标</p>-->
					<!--<span style="font-size: 22px;color: #0fd69c;font-weight: bold;" id="spdabiao">0</span>-->
				<!--</div>-->
			<!--</div>-->
		</div>
	</div>
	<div id="divverifyyeylist" style="margin-top: 96px;">
		<ul class="tab-common6 weui-flex" style="margin-top: -17px;" id="stutab">
			<li class="current">已核检达标(<b id="bdabiao">0</b>)</li>
			<li>已核检不达标(<b id="bweidabiao">0</b>)</li>
			<li>未核检幼儿园(<b id="bweiheyan">0</b>)</li>
		</ul>
		<!--已核检达标-->
		<div id="divdabiao" class="divdabiaostate" style="background: #ffffff;">
			<!--<div class="state-list">
				<h5>蓝天蓝天蓝天蓝天幼儿园</h5>
				<div class="success-state weui-flex" style="align-items: center;justify-content: space-between;padding: 0px 0 4px 0;">
					<div class="weui-flex__item" style="margin-right: 20px;">
						<p style="font-size: 12px;color: #999999;text-align: right;">达标项：<span class="state-txt">30</span>/43</p>
						<div class="progressbar_1" style="width: 100%;">
							<div class="bar" style="width: 50%;"></div>
						</div>
					</div>
					<a class="btn-state weui-flex_center">已达标<i class="iconfont icon_arrowcir-right weui-flex" style="height: 12px;margin-left: 5px;line-height: initial;"></i></a>
				</div>
			</div>
			<div class="state-list">
				<h5>蓝天蓝天蓝天蓝天幼儿园</h5>
				<div class="success-state weui-flex" style="align-items: center;justify-content: space-between;padding: 0px 0 4px 0;">
					<div class="weui-flex__item" style="margin-right: 20px;">
						<p style="font-size: 12px;color: #999999;text-align: right;">达标项：<span class="state-txt">30</span>/43</p>
						<div class="progressbar_1" style="width: 100%;">
							<div class="bar" style="width: 50%;"></div>
						</div>
					</div>
					<a class="btn-state weui-flex_center">已达标<i class="iconfont icon_arrowcir-right weui-flex" style="height: 12px;margin-left: 5px;line-height: initial;"></i></a>
				</div>
			</div>-->
		</div>

		<!--已核检不达标-->
		<div id="divweidabiao" class="divdabiaostate" style="background: #ffffff;display: none;">
			<!--<div class="state-list">
				<h5>蓝天蓝天蓝天蓝天幼儿园</h5>
				<div class="weui-flex" style="align-items: center;justify-content: space-between;padding: 0px 0 4px 0;">
					<div class="weui-flex__item" style="margin-right: 20px;">
						<p style="font-size: 12px;color: #999999;text-align: right;">达标项：<span class="state-txt">30</span>/43</p>
						<div class="progressbar_1" style="width: 100%;">
							<div class="bar" style="width: 50%;"></div>
						</div>
					</div>
					<a class="btn-state weui-flex_center">未达标<i class="iconfont icon_arrowcir-right weui-flex" style="height: 12px;margin-left: 5px;line-height: initial;"></i></a>
				</div>
			</div>-->
		</div>

		<!--未核检幼儿园-->
		<div id="divweiheyan" class="divdabiaostate" style="background: #ffffff;display: none;">
			<!--<div class="state-list">
				<h5>蓝天蓝天蓝天蓝天幼儿园</h5>
				<div class="weui-flex" style="align-items: center;justify-content: space-between;padding: 0px 0 4px 0;">
					<div class="weui-flex__item" style="margin-right: 20px;">
						<p style="font-size: 12px;color: #999999;text-align: right;">达标项：<span class="state-txt">0</span>/43</p>
						<div class="progressbar_1" style="width: 100%;">
							<div class="bar" style="width: 0%;"></div>
						</div>
					</div>
					<a class="btn-state weui-flex_center">未达标<i class="iconfont icon_arrowcir-right weui-flex" style="height: 12px;margin-left: 5px;line-height: initial;"></i></a>
				</div>
			</div>-->
		</div>

	</div>
</section>
<!--未关注chdc-->
<section style="position:absolute;height: 100%;background:#3bceff;text-align:center;width:100%;display: none;" id="divunsub">
	<div class="focus_bot">
		<div class="focus_bot_cen">
			<img src="images/focus_tit.png" style="margin: 20px auto 10px auto;" alt="">
			<div class="focus_botimg"><img src="images/focus_bot.png" style="position:relative;" alt=""></div>
		</div>
	</div>
</section>
<!--<script type="text/javascript" src="../sys/jquery.js"></script>-->
<script data-main="js/openverifylist" src="../sys/require.min.js"></script>
</body>
</html>

﻿define(function () {
    var emojidata = [["微笑","1.gif","/::\\)"],["撇嘴","2.gif","/::~"],["色","3.gif","/::B"],["发呆","4.gif","/::\\|"],["得意","5.gif","/:8-\\)"],["流泪","6.gif","/::<"],
        ["害羞","7.gif","/::$"],["闭嘴","8.gif","/::X"],["睡","9.gif","/::Z"],["大哭","10.gif","/::'\\("],["尴尬","11.gif","/::-\\|"],["发怒","12.gif","/::@"],
        ["调皮","13.gif","/::P"],["呲牙","14.gif","/::D"],["惊讶","15.gif","/::O"],["难过","16.gif","/::\\("],["酷","17.gif","/::\\+"],["冷汗","18.gif","\\[囧\\]"],
        ["抓狂","19.gif","/::Q"],["吐","20.gif","/::T"],["偷笑","21.gif","/:,@P"],["可爱","22.gif","/:,@-D"],["白眼","23.gif","/::d"],["傲慢","24.gif","/:,@o"],
        ["饥饿","25.gif","/::g"],["困","26.gif","/:\\|-\\)"],["惊恐","27.gif","/::!"],["流汗","28.gif","/::L"],["憨笑","29.gif","/::>"],["大兵","30.gif","/::,@"],
        ["奋斗","31.gif","/:,@f"],["咒骂","32.gif","/::-\\S"],["疑问","33.gif","/:\\?"],["嘘","34.gif","/:,@x"],["晕","35.gif","/:,@@"],["折磨","36.gif","/::8"],
        ["衰","37.gif","/:,@!"],["骷髅","38.gif","/:!!!"],["敲打","39.gif","/:xx"],
        ["再见","40.gif","/:bye"],["擦汗","41.gif","/:wipe"],["抠鼻","42.gif","/:dig"],["鼓掌","43.gif","/:handclap"],["糗大了","44.gif","/:&-\\("],["坏笑","45.gif","/:B-\\)"],
        ["左哼哼","46.gif","/:<@"],["右哼哼","47.gif","/:@>"],["哈欠","48.gif","/::-O"],["鄙视","49.gif","/:>-\\|"],["委屈","50.gif","/:P-\\("],["快哭了","51.gif","/::'\\|"],
        ["阴险","52.gif","/:X-\\)"],["亲亲","53.gif","/::\\*"],["吓","54.gif","/:@x"],["可怜","55.gif","/:8\\*"],["菜刀","56.gif","/:pd"],["西瓜","57.gif","/:<W>"],
        ["啤酒","58.gif","/:beer"],["篮球","59.gif","/:basketb"],["乒乓","60.gif","/:oo"],["咖啡","61.gif","/:coffee"],["饭","62.gif","/:eat"],["猪头","63.gif","/:pig"],
        ["玫瑰","64.gif","/:rose"],["凋谢","65.gif","/:fade"],["示爱","66.gif","/:showlove"],["爱心","67.gif","/:heart"],["心碎","68.gif","/:break"],["蛋糕","69.gif","/:cake"],
        ["闪电","70.gif","/:li"],["炸弹","71.gif","/:bome"],["刀","72.gif","/:kn"],["足球","73.gif","/:footb"],["瓢虫","74.gif","/:ladybug"],["便便","75.gif","/:shit"],
        ["月亮","76.gif","/:moon"],["太阳","77.gif","/:sun"],["礼物","78.gif","/:gift"],["拥抱","79.gif","/:hug"],["强","80.gif","/:strong"],["弱","81.gif","/:weak"],
        ["握手","82.gif","/:share"],["胜利","83.gif","/:v"],["抱拳","84.gif","/:@\\)"],["勾引","85.gif","/:jj"],["拳头","86.gif","/:@@"],["差劲","87.gif","/:bad"],
        ["爱你","88.gif","/:lvu"],["NO","89.gif","/:no"],["OK","90.gif","/:ok"],["爱情","91.gif","/:love"],["飞吻","92.gif","/:<L>"],["跳跳","93.gif","/:jump"],
        ["发抖","94.gif","/:shake"],["怄火","95.gif","/:<O>"],["转圈","96.gif","/:circle"],["磕头","97.gif","/磕头"],["回头","98.gif","/回头"],["跳绳","99.gif","/跳绳"],
        ["挥手","100.gif","/挥手"],["激动","101.gif","/激动"],["街舞","102.gif","/街舞"],["献吻","103.gif","/献吻"],["左太极","104.gif","/左太极"],["右太极","105.gif","/右太极"]];

    var obj = {
        emojidata:emojidata,
        initemoji:function (config) {
            $("head").append('<link type="text/css" rel="stylesheet" href="../plugin/swiper/swiper.min.css" />');
            var str = '';
            for (var i = 0; i < 5; i++) {
                str += '<div class="swiper-slide">';
                for (var j = 0; j < 20; j++) {
                    var index = i * 20 + j;
                    var arr = emojidata[index]
                    str += '<span class="oneemoji" index="' + index + '"><img src="../image/emoji/' + arr[1] + '" /></span>';
                }
                str += '<span class="oneemoji backface" index="' + index + '"><img src="../image/emoji/0.png" style="width: 30px;height: 30px"/></span>';
                str += '</div>';
            }
            $("#faceswiper").children('.swiper-wrapper').html(str).find('span').click(function () {
                if (!$(this).hasClass('backface')) {
                    var index = $(this).attr('index');
                    config.inputface && config.inputface(emojidata[index]);
                } else {
                    config.backface && config.backface();
                }
            });
            require(['swiper'],function () {
                var mySwiper = new Swiper('#faceswiper',{
                    direction:'horizontal',
                    loop:true,
                    // 如果需要分页器
                    pagination:'.swiper-pagination'
                });
            });
        },
        contentImgToEmoji:function (content) {
            for (var i = 0; i < emojidata.length; i++) {
                var regex = new RegExp('<img src="../image/emoji/' + emojidata[i][1] + '">',"g");
                content = content.replace(regex,'[' + emojidata[i][0] + ']');
            }
            return content;
        },contentEmojiToImg:function (content) {
            //[ "微笑", "1.gif" , "/::)" ]
            //.replace(/image\//g,'../image/');
            if (content) {
                for (var i = 0; i < emojidata.length; i++) {
                    if (emojidata[i][2])
                        content = content.replace(new RegExp(emojidata[i][2],"g"),'<img src="../image/emoji/' + emojidata[i][1] + '">').replace(new RegExp("\\[" + emojidata[i][0] + "\\]","g"),'<img src="../image/emoji/' + emojidata[i][1] + '">');
                }
                return content;
            } else {
                return "";
            }
        }
    }
    return obj;
});

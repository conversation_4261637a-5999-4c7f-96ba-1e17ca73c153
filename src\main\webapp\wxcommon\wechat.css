﻿/*聊天窗口的
*/
* {
    padding: 0;
    margin: 0;
}

.ltfooter {
    background: #F8F8F8;
    border-top: 1px solid #D2D2D4;
    position: fixed;
    bottom: 0;
    width: 100%;
}

/*  {
    -webkit-touch-callout:none;  
    -webkit-user-select:none; 
    -khtml-user-select:none; 
    -moz-user-select:none;
    -ms-user-select:none; 
    user-select:none;   
    
    
    -webkit-overflow-scrolling:touch;
	-webkit-text-size-adjust:none; 
}*/
.footertool {
    display: flex;
    padding: 0.1rem;
}

.centerdiv {
    background-color: #fff;
    border: 0 none;
    flex: 1 1 0;
    user-select: none;
    /*    height:0.5rem;
    */
    padding-left: 5px;
    float: left;
    border-radius: 3px;
    display: -webkit-box;
    -webkit-box-orient: vertical; /*排列方式flex-direction: column*/
    -webkit-box-pack: justify; /*box2的纵向排列方式 justify-content: space-between;*/
    -webkit-box-flex: 1; /*box2的伸缩比例 flex:auto;*/
    margin-right: 0.1rem;
    min-height: 0.5rem;
    width: 65%;
}

.txtinput {
    width: 100%;
    /*	border-bottom:1px solid red;
    */
    line-height: 0.5rem;
    font-size: 14px;
    border: none;
}

.txtinput:empty:before {
    content: attr(placeholder);
    color: #bbb;
}

.txtinput:focus:before {
    content: none;
}

.btnvoice {
    width: 90%;
    height: 100%;
    border-radius: 5px;
    margin: 5px;
    text-align: center;
    display: none;
}

.ltimg1 {
    float: left;
    display: inline-block
}

.ltimg1 img {
    width: 0.52rem;
    height: 0.52rem;
    margin-right: 0.14rem;
    vertical-align: middle;
    cursor: pointer
}

/**/

.chat-top {
    text-align: left;
    height: 0.8rem;
    background: #ececec;
    position: relative;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.chat-name {
    font-size: 0.3rem;
    height: 0.8rem;
    display: inline-block;
    vertical-align: top;
    line-height: 0.8rem;
    margin-left: 0.15rem;
}

.backto {
    cursor: pointer;
    width: 0.16rem;
    height: 0.25rem;
    vertical-align: top;
    margin-top: 0.28rem;
    margin-left: 0.24rem;
    float: left;
}

.back-icon {
    cursor: pointer;
    height: 0.8rem;
    width: 0.6rem;
    display: inline-block;
    float: left;
}

.chat-right {
    display: inline-block;
    float: right;
    margin-right: 0.24rem;
}

.arrow-re {
    width: 0.25rem;
    height: 0.16rem;
    vertical-align: top;
    margin-top: 0.42rem;
}

.chat-img {
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.1rem;
    margin-left: 0.1rem;
    cursor: pointer;
    border-radius: 50%;
}

.img-list {
    float: right;
    margin-right: 0.15rem;
    font-size: 0;
    display: inline-block;
    height: 0.8rem;
    padding-top: 0.2rem;
    vertical-align: top;
}

.img-list img {
    width: 0.4rem;
    height: 0.4rem;
    cursor: pointer;
    margin-left: 0.12rem;
    border-radius: 50%;
}

.tit-time {
    clear: both;
    width: 2.2rem;
    height: 0.4rem;
    background: #ddd;
    color: #fff;
    line-height: 0.4rem;
    text-align: center;
    margin: 0.3rem auto;
    font-size: 0.2rem;
}

@media only screen and (max-width: 360px) {
    .tit-time {
        width: 2.5rem;
    }
}

.talk {
    /*position: absolute;*/
    bottom: 1.2rem;
    top: 1rem;
    overflow: auto;
    left: 0;
    -webkit-overflow-scrolling: touch;
    overflow-y: scroll;
}

.talk dl {
    clear: both;
}

.talk dl img.touxiang, .talk dl img.touxiang {
    width: 0.7rem;
    height: 0.7rem;
    border-radius: 50%;
    cursor: pointer;
}

.talk dl p {
    text-align: left;
    font-size: 0.26rem;
    padding: 0.1rem 0.15rem 0.1rem 0.2rem;
    line-height: 0.4rem;
    white-space: pre-wrap;
    word-break: break-all;
}

.talk .tishi p img.voices {
    cursor: pointer;
    margin-right: 1rem;
    width: 0.23rem;
    height: 0.3rem;
    margin-top: 0.05rem;
    display: inline-block;
    vertical-align: top;
}

.talk .tishi p img.facial {
    width: 0.33rem;
    height: 0.3rem;
    vertical-align: top;
    margin-top: 0.05rem;
    float: left;
    margin-right: 0.06rem;
}

/*.talk .tishi img.chat-pic{width: 2.45rem;}*/

.talk-left .touxiang {
    margin-left: 0.24rem;
}

.names {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: absolute;
    top: -0.35rem;
    font-size: 0.24rem;
    color: #999;
    width: 3.8rem;
}

.talk-right .names {
    right: 0;
}

.chat-pic {
    border-radius: 4px; /*margin-bottom: -0.05rem;*/
}

.talk-left .triangle {
    width: 0;
    height: 0;
    border-top: 0.1rem solid transparent;
    border-right: 0.12rem solid #ddd;
    border-bottom: 0.1rem solid transparent;
    position: absolute;
    left: -0.13rem;
    top: 0.17rem;
}

.talk-left .triangle2 {
    width: 0;
    height: 0;
    border-top: 0.1rem solid transparent;
    border-right: 0.12rem solid #fff;
    border-bottom: 0.1rem solid transparent;
    position: absolute;
    left: -0.11rem;
    top: 0.17rem;
}

/*.talk-right .triangle {width: 0;height: 0;border-top:  0.1rem solid transparent;border-left:  0.12rem solid #ff632d;border-bottom:  0.1rem solid transparent;position: absolute;right:-0.12rem;top:0.17rem;}
*/
.talk-right .triangle2 {
    width: 0;
    height: 0;
    border-top: 0.1rem solid transparent;
    border-left: 0.1rem solid #ff632d;
    border-bottom: 0.1rem solid transparent;
    position: absolute;
    right: -0.08rem;
    top: 0.17rem;
}

dl.talk-left {
    text-align: left;
    height: auto;
    z-index: -9;
}

dl.talk-left .tishi {
    margin-bottom: 0.3rem;
    background: #fff;
    float: left;
    border-radius: 4px;
    position: relative;
    margin-left: 1.1rem;
    margin-top: -0.43rem;
    width: auto;
    word-wrap: break-word;
    max-width: 3.25rem;
    border: 1px solid #e5e4e5;
    min-height: 0.6rem;
    height: auto;
    font-size: 0;
}

.voi-time {
    position: absolute;
    bottom: 0;
    right: 0;
    display: inline-block;
    margin-right: -0.55rem;
    margin-bottom: 0.08rem;
    font-size: 0.2rem;
}

.voi-time i {
    font-style: normal;
    float: left;
    color: #b5b5b5;
}

.voi-time u {
    width: 0.1rem;
    height: 0.1rem;
    background: #ff3131;
    float: right;
    border-radius: 50%;
    top: 40%;
    position: absolute;
}

.touxiang {
    border-radius: 50%;
}

dl.talk-right {
    text-align: right;
    height: auto;
    clear: both;
}

dl.talk-right img.touxiang {
    margin-right: 0.3rem;
}

dl.talk-right .tishi {
    margin-bottom: 0.3rem;
    border-radius: 4px;
    background: #ff632d;
    float: right;
    margin-right: 1.2rem;
    position: relative;
    right: 0;
    margin-top: -0.43rem;
    min-width: 0.1rem;
    word-wrap: break-word;
    max-width: 3.25rem;
    min-height: 0.6rem;
    height: auto;
    font-size: 0;
}

dl.talk-right .tishi p {
    color: #fff;
    text-align: left;
}

dl.talk-right .tishi p img.voices2 {
    margin-left: 1rem;
    margin-right: 0;
    cursor: pointer;
}

.talk-right .voi-time {
    position: absolute;
    left: 0;
    margin-left: -0.65rem;
}

.talk-right .voi-time u {
    float: left;
    position: absolute;
    left: 0.35rem;
}

.shadow-pic {
    border-radius: 4px;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}

.shadow-pic img.load {
    cursor: pointer;
    position: absolute;
    top: 50%;
    margin-top: -0.16rem;
    left: 50%;
    margin-left: -0.16rem;
    width: 0.33rem;
    height: 0.33rem;
}

.shadow-pic span {
    position: absolute;
    bottom: 0.1rem;
    left: 0.1rem;
    color: #eae3d9;
}

.shadow-pic img.vedio {
    width: 0.21rem;
    height: 0.21rem;
    position: absolute;
    bottom: 0.1rem;
    right: 0.1rem;
}

.moretool {
    font-size: 0;
    margin-left: 0.13rem;
    margin-top: 0.1rem
}

.moretool li {
    display: inline-block;
    margin-right: 0.1rem;
}

.moretool li img {
    width: 1rem;
    height: 1rem;
}

#btnsend {
    cursor: pointer;
    font-size: 0.24rem;
    background: #12b7f5;
    width: 0.66rem;
    height: 0.44rem;
    text-align: center;
    line-height: 0.44rem;
    color: #fff;
    border-radius: 3px;
    margin-top: 0.04rem;
}

.tishi .locals img {
    width: 100%;
    border-radius: 0 0 4px 4px;
}

.tishi .locals .local-txt {
    text-align: left;
    border-radius: 4px 4px 0 0;
    background: #fff;
    height: 0.8rem;
    padding-top: 0.1rem;
    padding-left: 0.15rem;
}

.tishi .locals .local-txt span {
    display: block;
    line-height: 0.33rem;
}

.tishi .locals .local-txt span.loc-name {
    font-size: 0.26rem;
}

.tishi .locals .local-txt span.loc-add {
    font-size: 0.2rem;
    color: #999;
}


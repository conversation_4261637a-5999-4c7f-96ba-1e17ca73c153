body,ul,p,dd,dl{margin:0;padding:0;}
body {font-family:"冬青黑体简体中文 W3";font-size: 16px;background-size: 100% 100%;}
h1,h2,h3,h4,h5,h6{margin: 0; font-weight: normal;}
li{list-style:none;}
a {font-size:100%;vertical-align:baseline;background:transparent;text-decoration:none;outline:none;cursor:pointer;color:#7f7f7f;}
em{color:#f00;font-style:normal;}
i{font-style:normal;}
button,input{*overflow: visible;line-height: normal;font-family: '冬青黑体简体中文 W3';}
textarea{resize:none;word-break: break-all;}
select{font-family:"冬青黑体简体中文 W3";}
input,textarea{font-family:"冬青黑体简体中文 W3"; font-size: 14px;}
input:focus,textarea:focus,button{outline: none;}
input[type='checkbox'],input[type='radio']{vertical-align: middle;border:0;}
th{text-align: center;}
img{border:0;}
table{width:100%;border-collapse: collapse;}
.pa{position: absolute;}
.pr{position: relative;}
.font12{font-size: 12px;}
.font14{font-size: 14px;}
.font18{font-size: 18px;}
.font36{font-size: 36px;}
.font26{ font-size:0.26rem; padding:5px;}
.fl{float: left;}
.fr{float: right;}
.block{display: block;}
.pointer{cursor: pointer;}
.zfl{ text-align:left}
.zcenP{ text-align:center}
.zright{ text-align:right}
/*color*/
.white{color:#fff;}

/*clearfix*/
.clearfix:after{
  content:".";
  display:block;
  height:0;
  clear:both;
  visibility:hidden;
}
.clearfix{
  display:inline-block;
}
* html .clearfix{
  height:1%;
}
.clearfix{
  display:block;
}









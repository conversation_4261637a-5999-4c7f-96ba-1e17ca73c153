require.config({
    paths: {
        system: '../../sys/system_zepto',
        uploader: '../uploader'//通用方法
    },
    waitSeconds: 0
});
require(['system', 'uploader'], function (system, uploader) {
    var uploadCount = 0;
    var uploadList = [];
    var url = "http://tbfile.oss-cn-beijing.aliyuncs.com";
    var _selector = $(".btnup");
    var handletype = 'outer';//inner 内部处理显示 和 进度   outer： 外部处理
    if (handletype == 'inner') {
        _selector = $("#divupload");
    } else {
        _selector = $(".btnup");
    }
    new uploader({
        url: url,//'http://' + location.hostname + ':8002/upload',
        urltype: "oss",
        handletype: handletype,//inner 内部处理显示 和 进度   outer： 外部处理
        //divouter:$("#divupload"),
        divbtn: $(".btnup"),
        upfolder: "wxhome/test",//功能类型 enroll  drug 等
        needwh: true,
        auto: false,
        type: 'file',
        fileVal: 'fileVal',
        compress: false,
        compress: {
            width: 1600,
            height: 1600,
            //compress: 'high'//,
            quality: 1//high 重度压缩 medium 中度压缩 low 轻度压缩 或者传 0-1 的值
        },
        onBeforeQueued: function onBeforeQueued(files) {
            if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                alert('请上传图片');
                return false;
            }
            if (this.size > 10 * 1024 * 1024) {
                alert('请上传不超过10M的图片');
                return false;
            }
            if (files.length > 5) {
                // 防止一下子选中过多文件
                alert('最多只能上传5张图片，请重新选择');
                return false;
            }
            if (uploadCount + 1 > 5) {
                alert('最多只能上传5张图片');
                return false;
            }
            if (handletype == 'outer') {
                var _obj = $('<li class="weui-uploader__file weui-uploader__file_status" data-id="' + this.id + '"> <div class=weui-uploader__file-content> <i class=weui-loading style=width:30px;height:30px></i> </div> </li>');
                $("#uploaderFiles").append(_obj);
            }
        },
        onQueued: function onQueued(_div) {
            if (handletype == 'outer') {
                _div = $("#uploaderFiles").find('[data-id="' + this.id + '"]');
                _div.css({
                    backgroundImage: 'url("' + (this.base64 || this.url) + '")'
                });
                _div.removeClass('weui-uploader__file_status');
                _div.find('.weui-uploader__file-content').remove();
            }
            _div.data("file", this);
        },
        onBeforeSend: function onBeforeSend(data, headers) {
            console.log(this, data, headers);
            // $.extend(data, { test: 1 }); // 可以扩展此对象来控制上传参数
            // $.extend(headers, { Origin: 'http://127.0.0.1' }); // 可以扩展此对象来控制上传头部

            // return false; // 阻止文件上传
        },
        onProgress: function onProgress(procent) {
            console.log(procent);
            var $file = $("#uploaderFiles").find('[data-id="' + this.id + '"]');
            var $fileCtn = $file.find('.weui-uploader__file-content');
            if (!$fileCtn.length) {
                $fileCtn = $('<div class="weui-uploader__file-content"></div>');
                $file.append($fileCtn);
            }
            $file.addClass('weui-uploader__file_status');
            $fileCtn.html(procent + '%');
        },
        onSuccess: function onSuccess() {
            var $file = $("#uploaderFiles").find('[data-id="' + this.id + '"]').removeClass('weui-uploader__file_status');
            $file.find('.weui-uploader__file-content').remove();
            console.log(this);
            $('body').append('<img style="width: 45%" src="' + this.url + '">')
        },
        onError: function onError(err) {
            var $file = $("#uploaderFiles").find('[data-id="' + this.id + '"]');
            var $fileCtn = $file.find('.weui-uploader__file-content');

            if (!$fileCtn.length) {
                $fileCtn = $('<div class="weui-uploader__file-content"></div>');
                $file.append($fileCtn);
            }
            $file.addClass('weui-uploader__file_status');
            $fileCtn.html('<i class="weui-icon-warn"></i>');
            console.log(this, err);
        },
        onComplete: function (issucc) {

        }
    });
    $("#btnupload").click(function () {
        $("#uploaderFiles").children('li').each(function () {
            var file = $(this).data("file");
            if (file) {
                file.upload();
            }
        })
    })
});
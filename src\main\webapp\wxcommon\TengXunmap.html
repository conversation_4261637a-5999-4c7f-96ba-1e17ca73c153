<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <title>自定义的标记</title>
    <style type="text/css">
        * {
            margin: 0px;
            padding: 0px;
        }

        body, button, input, select, textarea {
            font: 12px/16px Verdana, Helvetica, Arial, sans-serif;
        }

        #container {
            min-width: 600px;
            min-height: 767px;
        }
    </style>
    <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77"></script>
    <script>
        var init = function () {
            var center = new qq.maps.LatLng(39.916527, 116.397128);
            var map = new qq.maps.Map(document.getElementById('container'), {
                center: center,
                zoom: 13
            });

            var anchor = new qq.maps.Point(6, 6),
                size = new qq.maps.Size(24, 24),
                origin = new qq.maps.Point(0, 0),
                icon = new qq.maps.MarkerImage('img/center.gif', size, origin, anchor);
            var marker = new qq.maps.Marker({
                icon: icon,
                map: map,
                position: map.getCenter()
            });
        }
    </script>
</head>
<body onload="init()">
<div id="container"></div>
<p id="info" style="margin-top:10px;"></p>
</body>
</html>

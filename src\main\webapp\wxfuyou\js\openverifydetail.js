require.config({
    baseUrl: '../',//指定js文件的基路径
    paths: {
        system: 'sys/system_zepto',
        moment:'plugin/js/moment',
        common: 'wxcommon/common',
        fun: 'sys/function',
        chdccommon: 'wxchdc/js/chdccommon',
        open: 'wxchdc/js/openverifyjson'
    },
    waitSeconds: 0
});
var objdata = {

};
require(['system', 'common', 'chdccommon','moment','open','fun'], function (s, c, p, moment,commonwx) {
    objdata.curmoment = moment();
    //获取用户信息
    selectEmployeeInfo(function (obj) {
        if (!obj) {
            console.log("没有obj");
            $("#divunsub").show().siblings().hide();
            return;
        }
        window.objinfo = obj;
        console.log(JSON.stringify(obj));
        objdata.openid = obj.openid;
        if (!obj.mobile) {
            parent.layer.open({
                content: "没有绑定手机号，请绑定后使用",
                btn: '确定',
                yes: function () {
                    var args = Arg.all();
                    parent.location.href = "bind.html?v=" + Arg('v') + "&frompage=openverifydetail" + "&flag=" + args['flag'] + (Arg("id") ? '&id=' + Arg("id") : "");
                }
            });
            return;
        }
        console.log(obj.mobile);
        console.log("objinfo.areacode:" + objinfo.areacode);
        if (objinfo.areacode) {
        } else {
            parent.layer.msg("请先添加地区用户！");
            return;
        }
        objdata.areacode = objinfo.areacode;//地区编码
        objdata.curyeyname = Arg("areaname") || "";//地区名称
        objdata.truename = objinfo.truename;//真实姓名
        $('body').on('click', '#back', function () {
            history.back();
        });
        //处理当前页面逻辑
        objopenverify.initEvent();
        objopenverify.initData();
    });
});

var objopenverify = {
    /**
     * 注册事件
     */
    initEvent: function () {
        //初始化页面
        if(!$.isEmptyObject(objverify)){
            var xmnum = 1, arrhtml = [];//xmnum项目序号，zxmnum子项目序号
            for (var n in objverify) {
                var objitem = objverify[n];
                zxmnum = 1;
                arrhtml.push('<div class="set-radio-style3" style="margin-bottom: 10px;background: #ffffff;"><h5 class="obj-title">项目' + xmnum + '：' + objitem.name + '</h5>');
                for (var o in objitem) {
                    if(o != 'name'){
                        arrhtml.push('<div class="verify-cell codeverify" code="' + n + "_" + o + '"><h6>' + o + "." + objitem[o].name + '</h6>\
                            <div class="verify-txt"><p>核验要点：'  + objitem[o].content + '</p><p>核验方法：' + objitem[o].way + '</p></div>\
                            <div class="verify-form">\
                                <div class="weui-flex" style="margin-top: 10px;">核验结果：\
                                    <label class="weui-flex_center"><input type="radio" name="result' + n + "_" + o + '" value="1">是</label>\
                                    <label class="weui-flex_center red-radio" style="margin-left: 20px;"><input type="radio" name="result' + n + "_" + o + '" value="2">否</label>\
                                </div>\
                                <div class="weui-flex" id="divremark' + n + "_" + o + '" style="margin-top: 3px;">备注：\
                                <span id="remark' + n + "_" + o + '" class="weui-flex__item" style="color: #666666;font-size: 14px;"></span>\
                        </div></div></div>');
                    }
                }
                arrhtml.push('</div>');
                xmnum++;
            }
            $("#divconment").html(arrhtml.join(""));
        }
    },
    //初始化开学条件核验数据
    initData: function () {
        $.sm(function (re, err) {
            if (err) {
                parent.layer.msg(err);
            } else if(re && re[0]){
                var nums = 0, yesnum = 0;//nums全部项，yesnum选择是的项
                var item = re[0];
                var p = $("#divconment");
                for (var cd in item) {
                    if(cd.indexOf('result') == 0){//结果
                        p.find('input[name="' + cd + '"][value="' + item[cd] + '"]').attr('checked', true);
                        p.find('input[name="' + cd + '"]').prop('disabled', 'disabled');
                        nums++;//全部数量
                        item[cd] == 1 ? yesnum++ : "";//核验通过的数量
                    }else if(cd.indexOf('remark') == 0){//备注
                        if(!item[cd]){
                            p.find('#div' + cd).hide();
                        }else{
                            p.find('#' + cd).html(item[cd]);
                        }
                    // }else if(cd == "id"){
                    //     objdata.verifyid = item[cd];//核验id
                    }
                }
                $("#lbverifyunit").text(item.verifiedunit);//最近核检单位
                $("#lbverifydate").text(item.verifieddate);//最近核检时间
                $("#spstandards").html(yesnum);//达标的人数
                $("#spall").html(nums);//全部人数
                $(".bar").css("width", (yesnum ? formatFloatValue(yesnum/nums * 100) : 0) + '%');
                if(nums == yesnum){
                    $("#btn_state").html("已达标");
                    if($("#div_state").hasClass('success-state')){
                    }else{
                        $("#div_state").addClass('success-state');
                    }
                    $("#div_tosatate").html('<img src="images/verify/icon_success.png" style="width: 23px;height: 23px;margin-right: 10px;">已达标');
                }else{
                    $("#btn_state").html("未达标");
                    $("#div_state").removeClass('success-state');
                    $("#div_tosatate").html('<img src="images/verify/icon_remind.png" style="width: 23px;height: 23px;margin-right: 10px;">未达标');
                }
            }
        }, ["openverify.seljson", Arg("yid")]);
    }
};
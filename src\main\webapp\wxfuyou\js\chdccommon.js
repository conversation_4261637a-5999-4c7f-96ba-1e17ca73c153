
/**
 * 查询信息，并赋session
 * @param cb
 */
function selectEmployeeInfo(cb) {
    selectUserInfo(function (objinfo) {
        if (!objinfo) return cb && cb(null);
        if (!objinfo.mobile) {
            cb(objinfo, null);
        } else {
            // if (objinfo.areacode) {//如果有地区
                cb(objinfo, null);
            // } else {
                // $.sm(function (re, err) {
                //     if (re && re.length) {
                //         var obj = re[0];
                //         objinfo.yeyid = obj.yeyid;
                //         objinfo.yeyname = obj.yeyname;
                //         objinfo.curyguid = obj.yguid;
                //         objinfo.fromtype = -1;
                //         objinfo.fromid = obj.employeeid;
                //         objinfo.fromname = obj.truename;
                //         $.sm(function (re, err) {
                //             cb(objinfo, null);
                //         }, ["wxgroup.setsession.yguid_yeyid", obj.yguid, obj.yeyid]);
                //     } else {
                //         cb(objinfo);
                //     }
                // }, ["wxgroup.groupuser", ""])
            // }
        }
    });
}
/**
 */
//查询个人信息
function selectUserInfo(cb) {
    $.sm(function (re, err) {
        if (re && re.length) {//nickname,userimg,wx.mobile,openid,u.truename,u.areacode,u.isadmin,u.roletype,u.roles
            var objinfo = re[0];
            cb(objinfo);
        } else {
            cb(null, err);
        }
    }, ['wxchdc.myJsonInfo']);
}

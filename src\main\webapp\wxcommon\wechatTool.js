﻿define(['../wxcommon/wechatFace.js'], function () {// swiper: '../../plugin/swiper/swiper.jquery.min',
    require.config({
        paths: {
            'swiper': '../../plugin/swiper/swiper.jquery.min'
        }
    });
    var wxtool = null;
    var wx = null;
    var str = '<section class="footertool">' +
        '<span class="ltimg1"><img src="images/yuyinico.png" id="btnchange"></span>' +
        '<div class="centerdiv">' +
        '<textarea name="txtinput" cols="" class="txtinput" id="txtinput" placeholder="说点什么吧..."  rows="1" style="overflow:hidden"></textarea>' +
        '<!-- <div class="txtinput" id="txtinput" contenteditable="true" placeholder="说点什么吧..."></div> -->' +
        '<!-- <input  class="txtinput" id="txtinput" contenteditable="true" placeholder="说点什么吧..."/> -->' +
        '<div class="btnvoice" style="height: auto" id="btnvoice">按住说话</div>' +
        '</div>' +
        '<span class="ltimg1" id="spface"><img src="images/xiaolianico.png"></span>' +
        '<span class="ltimg1" id="btnmoretool"><img src="images/moreico.png"></span>' +
        '<span class="ltimg1" id="btnsend" style="display:none;">发送</span>' +
        '</section>' +
        '<section style="height:100px;display:none;" id="divfacetool">' +
        '<section style="height:100%;" id="divfacelist">' +
        '<div id="faceswiper" class="swiper-container" style="width:100%;height:100%;">' +
        '<div class="swiper-wrapper">' +
        '<div class="swiper-slide">表情加载中</div>' +
        '<div class="swiper-slide"></div>' +
        '<div class="swiper-slide"></div>' +
        '</div>' +
        '<!-- 如果需要分页器 -->' +
        '<div class="swiper-pagination" style="bottom:4px;"></div>' +
        '</div>' +
        '</section>' +
        '<section style="height:100%;" id="divmoretool">' +
        '<ul class="moretool">' +
        '<li><img src="images/pic.png" id="btnPickImg"></li>' +
        '<li><img src="images/vid.png" id="btnPickVideo"></li>' +
        '<li><img src="images/loc.png" id="btnPickLocation"></li>' +
        '</ul>' +
        '</section>' +
        '</section>';
    //$('#talkfooter').append(str);
    var obj = {
        init: function (config) {
            config.elem.html(str);
            wechatEvent();
            wxtool = config.wxtool;
        },
        changeContentHeight: function () {

        },
        send: function (type, obj) {
            this.onsend && this.onsend(type, obj);
        }
    };

    function wechatEvent() {
        objdata._content = $("#talkcontent");
        var _btnvoice = $("#btnvoice");//声音按钮
        var _txtinput = $("#txtinput");//输入框
        var _divfacetool = $("#divfacetool");//表情按钮
        var _btnmoretool = $("#btnmoretool");//更多按钮
        var _btnsend = $("#btnsend");//发送
        var _divfacelist = $("#divfacelist");//表情div
        var _divmoretool = $("#divmoretool");//更多

        objdata._btnvoice = _btnvoice;
        objdata._txtinput = _txtinput;
        objdata._divfacetool = _divfacetool;
        objdata._btnmoretool = _btnmoretool;
        objdata._btnsend = _btnsend;
        objdata._divfacelist = _divfacelist;

        var starttime = null;
        var startY = 0;
        var moveY = 0;
        var cancelindex = 0;
        _btnvoice.on("touchstart", function (event) {
            wxtool.ready(function (issucc, mywx) {
                wx = mywx;
                if (!issucc)return layer.msg('请在微信中使用');
                starttime = new Date();
                _btnvoice.html("正在录音");
                var touch = event.originalEvent.targetTouches[0];
                startY = touch.pageY;
                wx.startRecord();
                layer.msg("开始录音")
                wx.onVoiceRecordEnd({
                    // 录音时间超过一分钟没有停止的时候会执行 complete 回调
                    complete: function (res) {
                        if (res.errMsg.indexOf("ok") > 0) {
                            var localId = res.localId;
                            layer.msg("超时" + localId);
                            voiceAfterRecord(localId);
                        }
                    }
                });
            });
            return false;
        }).on("touchmove", function (event) {
            var touch = event.originalEvent.targetTouches[0];
            moveY = startY - touch.pageY;
            console.log(moveY);
            if (moveY > 60) {
                if (!cancelindex) {
                    cancelindex = layer.open({
                        content: "松手取消"
                    });
                }
            } else {
                if (cancelindex) {
                    layer.close(cancelindex);
                    cancelindex = null;
                }
            }
        }).on("touchend", function (event) {
            wxtool.ready(function (issucc, wx) {
                if (!issucc)return;
                _btnvoice.html("按住说话");
                if (cancelindex) {
                    layer.close(cancelindex);
                    cancelindex = null;
                }
                var endtime = new Date();
                var dis = endtime - starttime;
                if (dis < 1000) {
                    console.log(dis);
                    return layer.msg("时间太短了");
                }
                if (moveY > 60) {
                    return layer.msg("取消发送");
                }
                wx.stopRecord({
                    success: function (res) {
                        if (res.errMsg.indexOf("ok") > 0) {
                            var localId = res.localId;
                            voiceAfterRecord(localId);
                        }
                    }
                });
            });
        });
        //点击发送
        _btnsend.click(function () {
            var txt = _txtinput.val();
            if (txt) {
                _txtinput.val('').height(objdata.minHeight);
                _btnsend.hide();
                _btnmoretool.show();
                //发送
                obj.send(1, {
                    text: getStrHtml(txt)
                });
            } else {
                layer.msg("说点什么吧...");
            }
        });
        //输入框事件
        _txtinput.focus(function () {
            _divfacetool.hide();
            obj.changeContentHeight();
            var txt = $(this).val();
            if (txt) {
                _btnsend.show();
                _btnmoretool.hide();
            } else {
                _btnsend.hide();
                _btnmoretool.show();
            }
        }).on('input', function () {
            var txt = $(this).val();
            if (txt) {
                _btnsend.show();
                _btnmoretool.hide();
            } else {
                _btnsend.hide();
                _btnmoretool.show();
            }
        });
        //点击切换
        $("#btnchange").click(function () {
            if (_btnvoice.css('display') == 'none') {//显示声音和更多
                _btnvoice.show();
                _txtinput.hide();

                _btnsend.hide();
                _btnmoretool.show();
            } else {
                _btnvoice.hide();
                _txtinput.show().focus();
            }
            _divfacetool.hide();
        });
        //点击表情
        $("#spface").click(function () {
            if (_divfacetool.css('display') == 'none') {
                //显示表情
                if (!objdata.hasshowface) {
                    objdata.hasshowface = true;
                    //init face
                    var fileref = document.createElement('link');
                    fileref.setAttribute("rel", "stylesheet");
                    fileref.setAttribute("type", "text/css");
                    fileref.setAttribute("href", "../plugin/swiper/swiper.min.css");
                    document.getElementsByTagName("head")[0].appendChild(fileref);
                    require(['swiper'], function () {
                        setTimeout(function () {
                            initFace();
                        }, 100);
                    });
                }
                _divfacetool.show();
                _divfacelist.show();
                _divmoretool.hide();
            } else {//底部打开 表情打开
                if (_divfacelist.css('display') != 'none') {
                    _divfacetool.hide();
                    _divfacelist.hide();
                    _divmoretool.hide();
                } else {
                    _divfacelist.show();
                    _divmoretool.hide();
                }
            }
            obj.changeContentHeight();
        });
        //点击更多
        $("#btnmoretool").click(function () {
            if (_divfacetool.css('display') == 'none') {
                _divfacetool.show();
                _divfacelist.hide();
                _divmoretool.show();
            } else {
                //底部打开 更多打开
                if (_divmoretool.css('display') != 'none') {
                    _divfacetool.hide();
                    _divfacelist.hide();
                    _divmoretool.hide();
                } else {
                    _divfacelist.hide();
                    _divmoretool.show();
                }
            }
            obj.changeContentHeight();
        });
        //隐藏表情和底部
        $('#talkcontent').on('touchstart', function (e) {
            if ($(e.target).parents(".ltfooter").length < 1) {
                _divfacetool.hide();
                _divfacelist.hide();
                _divmoretool.hide();
                obj.changeContentHeight();
            }
        }).on('click', '.hometouxiang', function () {
            _txtinput.val(_txtinput.val() + '@' + $(this).attr('strname'));
        }).on('click', '.oneimg', function () {//查看图片
            var src = $(this).attr('big');
            wxtool.ready(function (issucc, wx) {
                if (!issucc)return;
                var arr = [];
                $(".oneimg").each(function () {
                    arr.push($(this).attr('big'));
                });
                wx.previewImage({
                    current: src, // 当前显示图片的http链接
                    urls: arr // 需要预览的图片http链接列表
                });
            });
        }).on('click', '.onevideo', function () {//查看图片
            var videourl = $(this).siblings('img').attr('videourl');
            if (videourl)
                parent.openwin('videoplay', 'videoplay.html?v=' + Arg('v') + "&url=" + videourl, "视频播放");
        }).on('click', '.onevoice', function () {//播放声音
            function playvoice(_elem) {
                if (objdata.playingvoice) {
                    var localid = _elem.children('.voiceurl').attr('localid');
                    stopDongHua(objdata.playingvoice, null, localid);
                    //停止动画
                    objdata.playingvoice = null;
                }
                var localid = _elem.children('.voiceurl').attr('localid');
                if (localid) {
                    objdata.playingvoice = _elem;
                    //开始动画
                    startDongHua(_elem);
                    wxtool.ready(function (issucc, wx) {
                        if (!issucc)return;
                        wx.playVoice({
                            localId: localid
                        });
                        wx.onVoicePlayEnd({
                            success: function (res) {
                                var localId = res.localId; // 返回音频的本地ID
                                stopDongHua(objdata.playingvoice, null, localId);
                            }
                        });
                    });
                } else {
                    layer.msg('播放失败')
                }
            }

            //停止动画
            function stopDongHua(_elem, _audio, localid) {
                var _playimg = _elem.find(".voices");
                _playimg.prop('src', _playimg.attr('oldsrc'));
                if (_audio) {
                    _audio[0].pause();
                } else if (localid) {
                    wxtool.ready(function (issucc, wx) {
                        if (!issucc)return;
                        wx.stopVoice({
                            localId: localid
                        });
                    });
                }
            }

            //开始动画
            function startDongHua(_elem) {
                var _playimg = _elem.find(".voices");
                var oldsrc = _playimg.prop('src');
                var tosrc = null;
                if (oldsrc.indexOf('voices2') > 0) {//代表右边
                    tosrc = 'images/voices2.gif';
                } else {
                    tosrc = 'images/voices.gif';
                }
                _playimg.attr('oldsrc', oldsrc).prop('src', tosrc);
            }

            function playmp3(_elem, src) {
                var _playimg = _elem.find(".voices");

                var _audio = $("#audioPlay");
                if (_audio.length <= 0) {
                    _audio = $("<audio id='audioPlay' src='" + src + "' hidden='true'>");
                    $("body").append(_audio);
                    _audio.bind('ended', function () {
                        stopDongHua(objdata.playingvoice, _audio);
                        //停止动画
                        objdata.playingvoice = null;
                    });
                    _audio.bind('error', function () {
                        stopDongHua(objdata.playingvoice, _audio);
                        //停止动画
                        objdata.playingvoice = null;
                        layer.msg('播放出错');
                    });
                } else {
                    _audio.attr('src', src);
                }
                if (objdata.playingvoice) {
                    //停止动画
                    stopDongHua(objdata.playingvoice, _audio);
                }
                objdata.playingvoice = _elem;
                //开始动画
                startDongHua(_elem);
                //开始播放
                _audio[0].play();
            }

            wxtool.ready(function (issucc, wx) {
                if (!issucc)return;
                var url = $(this).children('.voiceurl').attr('mp3url');
                if (url) {
                    playmp3($(this), url);
                } else {
                    playvoice($(this));
                }
            });
        })
        $("#talkfooter").on('click', '#btnPickImg', function () {//发送图片
            wxtool.ready(function (issucc, wx) {
                if (!issucc)return;
                wx.chooseImage({
                    count: 9, // 默认9
                    sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                    success: function (res) {
                        if (res.errMsg == "chooseImage:ok") {
                            var localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                            for (var i = 0; i < localIds.length; i++) {
                                (function (i) {
                                    console.log("开始" + localIds[i]);
                                    wx.uploadImage({
                                        localId: localIds[i], // 需要上传的图片的本地ID，由chooseImage接口获得
                                        isShowProgressTips: 1, // 默认为1，显示进度提示
                                        success: function (res) {
                                            if (res.errMsg.indexOf("ok") > 0) {
                                                var serverId = res.serverId; // 返回图片的服务器端ID
                                                obj.send(2, {
                                                    localId: localIds[i],
                                                    MediaId: serverId
                                                });
                                            } else {
                                                layer.msg("上传失败");
                                            }
                                        }
                                    });

                                })(i);
                            }
                        } else {
                            layer.msg(JSON.stringify(res));
                        }
                    }
                });
            });
        }).on('click', '#btnPickVideo', function () {//发送视频
            layer.msg("功能建设中");
        }).on('click', '#btnPickLocation', function () {//发送位置
            layer.msg("功能建设中");
        });
    }

    function voiceAfterRecord(localid) {
        wx.uploadVoice({
            localId: localid, // 需要上传的音频的本地ID，由stopRecord接口获得
            isShowProgressTips: 1, // 默认为1，显示进度提示
            success: function (res) {
                var serverId = res.serverId; // 返回音频的服务器端ID
                obj.send(5, {
                    localId: localid,
                    MediaId: serverId
                });
            }
        });
    }

    return obj;
});
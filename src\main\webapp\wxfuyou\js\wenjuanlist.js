var objnotices = {};
var objdata = {};
require.config({
    paths:{
        system:'../../sys/system_zepto',
        moment:'../../plugin/js/moment.min',
        common:'../../wxcommon/common',
        commonwx:'../../wxcommon/commonwx',
        commonhome:'../../wxcommon/commonhome',
        mescroll:'../../plugin/mescroll/mescroll'
    },
    waitSeconds:0
});
require(['system','moment','commonwx','mescroll','common','commonhome'],function (s,moment,wxtool,MeScroll) {
    getMyInfo(function (objinfo) {
        if (!objinfo || !objinfo.objyey) {
            return
        }
        var stuno = objinfo.objbaby.stuno;
        $("#thelist").on('click','.questionnaier-con',function () {
            var id = $(this).data('id');
            parent.openwin('wenjuandetail','wenjuandetail.html?v=' + Arg('v') + '&id=' + id,'问卷详情');
        })
        window.msmubanku = new MeScroll("mescrollmubanku",{
            down: {//解析: down.callback默认调用mescroll.resetUpScroll(),而resetUpScroll会将page.num=1,再触发up.callback
                callback: function () {
                    msmubanku.resetUpScroll();
                }
            },
            up:{
                callback:function (page) {
                    $.sm(function (re,err) {
                        if (!re || !re.length) {
                            msmubanku.endSuccess(0)
                            return;
                        }
                        var strhtml = '';
                        for (var i = 0; i < re.length; i++) {
                            var objone = re[i];
                            var stronehtml = makeonehtml(objone)
                            strhtml += stronehtml;
                        }
                        $("#thelist").html(strhtml);
                        msmubanku.endSuccess(1)
                    }, ["weixin.wj.getlist", page.size, page.size * (page.num-1)])
                },
                isBounce:false, //此处禁止ios回弹,解析(务必认真阅读,特别是最后一点): http://www.mescroll.com/qa.html#q10
                noMoreSize:4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
                empty:{
                    warpId:'thelist',
                    icon:"../plugin/mescroll/mescroll-empty.png", //图标,默认null
                    tip:"暂无相关数据~", //提示
                    btntext:"刷新", //按钮,默认""
                    btnClick:function () {//点击按钮的回调,默认null
                        msmubanku.resetUpScroll(true);
                    }
                },
                toTop:{ //配置回到顶部按钮
                    src:"../plugin/mescroll/mescroll-totop.png"
                }
            }
        });
    });
});
function makeonehtml(objone) {
    var arrhtml = [];
    arrhtml.push('<div className="questionnaier-con" data-id="' + objone.id + '">');
    if(objone.personid){
        arrhtml.push('    <span className="stat-sign graybg">已提交</span>');
    } else {
        arrhtml.push('    <span className="stat-sign greenbg">未提交</span>');
    }
    arrhtml.push('    <div className="questionnaier-txt1">');
    arrhtml.push('        <h5 className="ellipsis-clamp">' + objone.wtitle + '</h5>');
    arrhtml.push('        <span className="weui-badge weui-badge_dot" style="display:none;"></span>');
    arrhtml.push('    </div>');
    arrhtml.push('    <div className="questionnaier-txt2 weui-flex">');
    arrhtml.push('        <p>');
    arrhtml.push('            <span className="weui-flex_center"><img src="images/icon_votenoname.png" style="width: 15px;margin-right: 10px;"></span>');
    arrhtml.push('            ' + (objone.isniming ? '匿名投票' : '非匿名投票'));
    arrhtml.push('        </p>');
    // arrhtml.push('        <p>');
    // arrhtml.push('            <span className="weui-flex_center">');
    // arrhtml.push('                <img src="images/icon_time.png" style="width: 14px;margin-right: 10px;"></span> 2024-03-27 16:39到2024-03-27 16:39');
    // arrhtml.push('            <span style="color: red">(已结束)</span>');
    // arrhtml.push('        </p>');
    arrhtml.push('    </div>');
    arrhtml.push('    <div className="questionnaier-txt3 weui-flex">');
    arrhtml.push('        <div className="weui-flex_center">');
    arrhtml.push('            <img src="images/img_teachernv1.png" style="width: 28px;height: 28px;border-radius: 50%;margin-right: 5px;">' + objone.truename);
    arrhtml.push('        </div>');
    // arrhtml.push('        <div className="weui-flex_center">');
    // arrhtml.push('            <i className="iconfont icon_user2"></i>调查对象：班级老师<i className="iconfont icon_others2" style="display: none"></i>');
    // arrhtml.push('        </div>');
    arrhtml.push('    </div>');
    arrhtml.push('</div>');
    return arrhtml.join('');
}
var openPhotoSwipe = function () {
    var pswpElement = document.querySelectorAll('.pswp')[0];

    // build items array
    /*var items = [
     {
     src: 'https://farm2.staticflickr.com/1043/5186867718_06b2e9e551_b.jpg',
     w: 964,
     h: 1024
     },
     {
     src: 'https://farm7.staticflickr.com/6175/6176698785_7dee72237e_b.jpg',
     w: 1024,
     h: 683
     }
     ];
     */

    var items = [{
        src: 'http://tbfile.oss-cn-beijing.aliyuncs.com/wxyey/hc4OFNFu15EKfmzmTC5pYmJC6jfznhgdRsSKHpoKIgDcWL3-zyNGHMmIReAMCohR.jpg',
        w: 424,
        h: 683
    }, {
        src: 'http://tbfile.oss-cn-beijing.aliyuncs.com/wxyey/UyWExRrcAckZ_VX73BskwcdfKFXpiSj3m7H7mTaqDuVaX_kutob5ZNrdMlIHhAHY.jpg',
        w: 1024,
        h: 683
    }, {
        src: 'http://tbfile.oss-cn-beijing.aliyuncs.com/wxyey/2l5linzFCNHrpKQDvFaRaba23jaWKnECClX6QVCz-oMrxkw4xwMBLCglH5gk0i4q.jpg',
        w: 1024,
        h: 683
    }];
    items = [{
        "id": 147,
        "mtype": "image",
        "src": "http://tbfile.oss-cn-beijing.aliyuncs.com/wxyey/s_9qW1gMk073gFKGt3vrO-lzAH80UXAeSE_Uz8d2UuJdix2tmI2587NzHbcfI-nR.jpg",
        "w": 4032,
        "h": 3024,
        "objmedia": {
            "s": 3776310,
            "w": 4032,
            "h": 3024,
            "url": "wxyey/s_9qW1gMk073gFKGt3vrO-lzAH80UXAeSE_Uz8d2UuJdix2tmI2587NzHbcfI-nR.jpg"
        },
        "title": " "
    }, {
        "id": 148,
        "mtype": "image",
        "src": "http://tbfile.oss-cn-beijing.aliyuncs.com/wxyey/q2z_lQBw5s3gV5SpX2Kib4KLWBgujX9EyRgNhy7fac-orvZEHKEn20Bp4319WGHa.jpg",
        "w": 4032,
        "h": 3024,
        "objmedia": {
            "s": 3185163,
            "w": 4032,
            "h": 3024,
            "url": "wxyey/q2z_lQBw5s3gV5SpX2Kib4KLWBgujX9EyRgNhy7fac-orvZEHKEn20Bp4319WGHa.jpg"
        },
        "title": " "
    }, {
        "id": 149,
        "mtype": "image",
        "src": "http://tbfile.oss-cn-beijing.aliyuncs.com/wxyey/0h3adgj_ZYSrjHihFiJYyeAVw7b2U5c70chAeRam0Vw7JjMbFVWvfECLzwjbMtL2.jpg",
        "w": 4032,
        "h": 3024,
        "objmedia": {
            "s": 3917089,
            "w": 4032,
            "h": 3024,
            "url": "wxyey/0h3adgj_ZYSrjHihFiJYyeAVw7b2U5c70chAeRam0Vw7JjMbFVWvfECLzwjbMtL2.jpg"
        },
        "title": " "
    }];
    var winWidth = 0;
    if (window.innerWidth)
        winWidth = window.innerWidth;
    else if ((document.body) && (document.body.clientWidth))
        winWidth = document.body.clientWidth;
    alert(7)
    for (var i = 0; i < items.length; i++) {
        items[i].w = winWidth * 2;
        items[i].h = winWidth * 2 / items[i].objmedia.w * items[i].objmedia.h;

        items[i].src = items[i].src + getImgOSSParam(items[i].objmedia, winWidth);
    }
    // define options (if needed)
    var options = {
        // history & focus options are disabled on CodePen
        history: false,
        focus: false,

        showAnimationDuration: 0,
        hideAnimationDuration: 0

    };

    var gallery = new PhotoSwipe(pswpElement, PhotoSwipeUI_Default, items, options);
    gallery.init();
    gallery.listen('gettingData', function (index, item) {
        item.html = '<div>Dynamically generated HTML ' + Math.random() + '</div>';
    });
};

openPhotoSwipe();

document.getElementById('btn').onclick = openPhotoSwipe;
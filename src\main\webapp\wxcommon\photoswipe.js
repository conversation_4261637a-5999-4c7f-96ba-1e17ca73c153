define(function () {
    require.config({
        paths: {
            'photoswipe': '../../wxcommon/photoswipe/photoswipe.min',
            'photoswipe-ui-default': '../../wxcommon/photoswipe/photoswipe-ui-default'
        },
        waitSeconds: 0
    });
    var isfirst = true;
    var obj = {
        init: function (arr, curindex, objconf, cb) {
            if (isfirst) {
                //加载css
                $("<link>").attr({
                    rel: "stylesheet",
                    type: "text/css",
                    href: "../wxcommon/photoswipe/photoswipe.css"
                }).appendTo("head");
                $("<link>").attr({
                    rel: "stylesheet",
                    type: "text/css",
                    href: "../wxcommon/photoswipe/default-skin/default-skin.css"
                }).appendTo("head");
            }
            var options = {
                // history & focus options are disabled on CodePen
                index: curindex,
                fullscreenEl: false,//不要全屏按钮
                shareButtons: objconf.shareButtons,
                isClickableElement : function(el) {
                    return el.tagName === 'A'||el.tagName === 'IMG';
                },
                allowPanToNext: true,
                loop:false,
                spacing: 0,
                //preload: [2, 2],
                getDoubleTapZoom: function (isMouseClick, item) {
                    return item.initialZoomLevel * 2;
                }
                //shareEl:false,
                // parseShareButtonOut:function (shareButtonData,shareButtonOut) {
                //     return "aaaa";
                // },
            };
            if (!objconf.shareButtons) {//家长不要分享按钮
                options.shareEl = false;
            }
            require(['photoswipe', 'photoswipe-ui-default'], function (PhotoSwipe, PhotoSwipeUI_Default) {
                //----------------------------处理hash----------------------
                if(parent.pushHash){
                    parent.pushHash({
                        key: "photoSwipe",
                        title: ""
                    });
                    objdata.canback = true;
                }
                var pswpElement = document.querySelectorAll('.pswp')[0];
                var gallery = new PhotoSwipe(pswpElement, PhotoSwipeUI_Default, arr, options);
                gallery.listen('afterChange', function (index, item) {
                    objconf.afterChange && objconf.afterChange(gallery);
                });
                gallery.init();
                gallery.listen('close', function (item, index) {
                    gallery.framework.bind(gallery.scrollWrap, 'pswpTap', clickFun, 'remove');
                    //----------------------------处理hash----------------------
                    if (objdata.canback)
                        history.back();
                });
                gallery.listen('removePhoto', function (item, index) {
                    objconf.removePhoto && objconf.removePhoto(this, item, index);
                });
                gallery.listen('editPhoto', function (item, index) {
                    objconf.editPhoto && objconf.editPhoto(this, item, index);
                });
                gallery.listen('viewPhoto', function (item, index) {
                    objconf.viewPhoto && objconf.viewPhoto(this, item, index);
                });
                gallery.listen('laoPhoto', function (item, index) {
                    objconf.laoPhoto && objconf.laoPhoto(this, item, index);
                });
                gallery.listen('download', function (item, index) {
                    objconf.download && objconf.download(this, item, index);
                });
                gallery.listen('shareLinkClick', function (e, target, item) {
                    var key = $(target).attr('key');
                    for (var i = 0; i < objconf.shareButtons.length; i++) {
                        if (key == objconf.shareButtons[i].id) {
                            objconf.shareButtons[i].click(item,this);
                            break;
                        }
                    }
                });
                var clickFun = function (e) {
                    if ($(e.target).parent().hasClass("playVideo")) {
                        objconf.playVideo && objconf.playVideo(gallery.currItem);
                    }
                    // e.detail.origEvent  // original event that finished tap (e.g. mouseup or touchend)
                    // e.detail.target // e.target of original event
                    // e.detail.releasePoint // object with x/y coordinates of tap
                    // e.detail.pointerType // mouse, touch, or pen
                }
                /* bind on any element of gallery */
                gallery.framework.bind(gallery.scrollWrap, 'pswpTap', clickFun);
                cb && cb(gallery);
            });
        }
    };
    return obj;
});
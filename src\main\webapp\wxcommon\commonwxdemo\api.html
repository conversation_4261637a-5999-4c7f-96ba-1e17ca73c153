<html>
	<head>
	  <meta charset="utf-8">
	  <meta http-equiv="X-UA-Compatible" content="IE=edge">
	  <title>jQuery WEUI</title>
	  <meta name="description" content="微信开发文档">
	  <meta name="baseurl" value="">
	  <link rel="icon" href="images/logo.png">
	  <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
	  <link rel="stylesheet" type="text/css" href="css/main.css">
	  <script>
	    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
	      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
	        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
	    ga('create', 'UA-72409043-1', 'auto');
	    ga('send', 'pageview');
	  </script>
	</head>
	<body class="en">
    <nav class="navbar header navbar-default navbar-inverse navbar-static-top">
	  <div class="container-fluid">
	    <div class="navbar-header">
	      <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
	        <span class="sr-only">Toggle navigation</span>
	        <span class="icon-bar"></span>
	        <span class="icon-bar"></span>
	        <span class="icon-bar"></span>
	      </button>
	      <a class="navbar-brand" href="/">
	        <img src="images/logo.png" alt="">
	      </a>
	    </div>	
	    <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
	      <ul class="nav navbar-nav">
	        <li class="link-index active"><a href="/">首页 </a></li>
	        <li class="link-components"><a href="/components">基础组件</a></li>
	        <li class="link-extends"><a href="/extends">拓展组件</a></li>
	        <li class="link-cases"><a href="/cases">案例</a></li>
	        <li class="link-download"><a href="/download">下载</a></li>
	        <li class="link-resource"><a href="/resource">学习资料</a></li>
	        <li class="link-donate"><a href="/donate">捐助</a></li>
	        <li class="link-about"><a href="/about">关于</a></li>
	      </ul>
	      <ul class="nav navbar-nav pull-right">
	        <li>
	          <a href="https://jqweui.cn/" target="_blank" class="en-only">国内站</a>
	          <a href="https://jqweui.com/" target="_blank" class="cn-only">国外站</a>
	        </li>
	      </ul>
	    </div>
	  </div>
	</nav>
	<div class="alert alert-success" role="alert" style="margin: 0;">
	速度更快更稳定的国内镜像网站<a href="https://jqweui.cn">https://jqweui.cn</a>已经备案完成并上线。请注意目前只有 <a href="http://jqweui.com">http://jqweui.com</a> (国外) 和 <a href="https://jqweui.cn">https://jqweui.cn</a>(国内) 是官网网站，请不要使用其他盗版网站。
	</div>
    <div class="page-content">
      <div class="home">
		  <div class="brand-header">
		    <div class="container-fluid">
		      <h1>基础组件</h1>
		      <p>WeUI 包含了大量常用的组件：按钮，表单，列表，对话框等。这里的组件全部都是官方的样式。</p>		      
		    </div>
		  </div>
  		  <div class="container-fluid docs-container">
		    <div class="menu-col">
		        <ul class="nav side-nav" id="side-nav" style="">
				  <li class="active"><a href="#grid">九宫格</a></li>
				  <li class=""><a href="#flex">Flex</a></li>
				  <li class=""><a href="#buttons">按钮</a></li>
				  <li class=""><a href="#cell">列表</a></li>
				  <li><a href="#swipeout">滑动删除</a></li>		  
				</ul>
		    </div>  
		    <div class="docs-col">
				<article id="grid" class="component active" data-url="../dist/demos/grid.html">
			        <h2>九宫格</h2>
					<figure class="highlight"><pre><p>九宫格</p><p>九宫格</p><p>九宫格</p><p>九宫格</p><p>九宫格</p><p>九宫格</p><p>九宫格</p></pre>
					</figure>
			    </article>
		        <article id="flex" class="component" data-url="../dist/demos/flex.html">
		 		  <h2>Flex</h2>
		  		  <p>使用 Flex 实现的栅格</p>
				  <figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-flex"</span><span class="nt">&gt;</span>
				 	 <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-flex__item"</span><span class="nt">&gt;</span>weui<span class="nt">&lt;/div&gt;</span>
				 	 <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-flex__item"</span><span class="nt">&gt;</span>weui<span class="nt">&lt;/div&gt;</span>
				     <span class="nt">&lt;/div&gt;</span></code></pre>
				  </figure>
				</article>
		        <article id="buttons" class="component" data-url="../dist/demos/buttons.html">
			  		<h2>按钮</h2>
			  		<p>按钮可以使用 <code>a</code> 或者 <code>button</code> 标签。wap上要触发按钮的 <code>active</code>态，必须触发 <code>ontouchstart</code>事件，可以在 <code>body</code> 上加上 <code>ontouchstart=""</code> 全局触发。</p>
			  		<p>按钮常见的操作场景：确定、取消、警示，分别对应class：<code>weui-btn_primary</code>、<code>weui-btn_default</code>、<code>weui-btn_warn</code>，每种场景都有自己的置灰态 <code>weui-btn_disabled</code>，除此外还有一种镂空按钮 <code>weui-btn_plain-xxx</code>，客户端webview里的按钮尺寸有两类，默认宽度100%，小型按钮宽度自适应，两边边框与文本间距0.75em：</p>
					<figure class="highlight">
						<pre>
							<code class="language-html" data-lang="html"><span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_primary"</span><span class="nt">&gt;</span>按钮<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_disabled weui-btn_primary"</span><span class="nt">&gt;</span>按钮<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_warn"</span><span class="nt">&gt;</span>确认<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_disabled weui-btn_warn"</span><span class="nt">&gt;</span>确认<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_default"</span><span class="nt">&gt;</span>按钮<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_disabled weui-btn_default"</span><span class="nt">&gt;</span>按钮<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_plain-default"</span><span class="nt">&gt;</span>按钮<span class="nt">&lt;/a&gt;</span>
								<span class="nt">&lt;a</span> <span class="na">href=</span><span class="s">"javascript:;"</span> <span class="na">class=</span><span class="s">"weui-btn weui-btn_plain-primary"</span><span class="nt">&gt;</span>按钮<span class="nt">&lt;/a&gt;</span>
							</code>
						</pre>
					</figure>
		 			<h3>Loading状态</h3>
		 		    <p><code>.weui-btn_loading</code> 可以使按钮变为loading状态</p>
		        </article>
		        <article id="cell" class="component" data-url="../dist/demos/cell.html">
			  		<h2>列表</h2>
			  		<p>Cell，列表视图，用于将信息以列表的结构显示在页面上，是wap上最常用的内容结构。Cell由多个section组成，每个section包括section header <code>weui-cells_title</code> 以及 <code>weui-cells</code>。</p>
			  		<p>cell由 <code>thumbnailweui-cell__hd</code>、<code>bodyweui-cell__bd</code>、<code>accessoryweui-cell__ft</code>三部分组成，cell采用自适应布局：</p>
			  		<h3>简单列表</h3>
					<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cells"</span><span class="nt">&gt;</span>
					  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell"</span><span class="nt">&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__bd"</span><span class="nt">&gt;</span>
					      <span class="nt">&lt;p&gt;</span>标题文字<span class="nt">&lt;/p&gt;</span>
					    <span class="nt">&lt;/div&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__ft"</span><span class="nt">&gt;</span>说明文字<span class="nt">&lt;/div&gt;</span>
					  <span class="nt">&lt;/div&gt;</span>
					  <span class="nt">&lt;/div&gt;</span></code></pre>
					</figure>
			  		<h3>带图标的列表</h3>
					<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cells"</span><span class="nt">&gt;</span>
					  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell"</span><span class="nt">&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__hd"</span><span class="nt">&gt;&lt;img</span> <span class="na">src=</span><span class="s">""</span><span class="nt">&gt;&lt;/div&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__bd"</span><span class="nt">&gt;</span>
					      <span class="nt">&lt;p&gt;</span>标题文字<span class="nt">&lt;/p&gt;</span>
					    <span class="nt">&lt;/div&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__ft"</span><span class="nt">&gt;</span>说明文字<span class="nt">&lt;/div&gt;</span>
					  <span class="nt">&lt;/div&gt;</span>
					  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell"</span><span class="nt">&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__hd"</span><span class="nt">&gt;&lt;img</span> <span class="na">src=</span><span class="s">""</span><span class="nt">&gt;&lt;/div&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__bd"</span><span class="nt">&gt;</span>
					      <span class="nt">&lt;p&gt;</span>标题文字<span class="nt">&lt;/p&gt;</span>
					    <span class="nt">&lt;/div&gt;</span>
					    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"weui-cell__ft"</span><span class="nt">&gt;</span>说明文字<span class="nt">&lt;/div&gt;</span>
					  <span class="nt">&lt;/div&gt;</span>
					<span class="nt">&lt;/div&gt;</span></code></pre>
					</figure>
				</article>
			    <article class="component" id="swipeout" data-url="../dist/demos/swipeout.html">
			  		<h2 class="component-title">滑动删除(Swipeout)</h2>
					<p>Swipeout 可以在列表的某一项中向左滑动出现操作按钮，类似微信聊天列表中的滑动功能。</p>
					<p><strong>从 <code>V1.1.0</code> 版本开始可用</strong></p>
					<h3>初始化</h3>
					<p>默认情况下，当页面加载完成后，会自动初始化带有 <code>.weui-cell_swiped</code> 类的列表条目，此时不需要做任何手动初始化。</p>
					<p>如果你是动态生成的DOM，或者在JS加载之后的DOM，那么这样初始化:</p>
					<figure class="highlight"><pre><code class="language-js" data-lang="js"><span class="nx">$</span><span class="p">(</span><span class="s1">'.weui-cell_swiped'</span><span class="p">).</span><span class="nx">swipeout</span><span class="p">()</span></code></pre></figure>
				  	<h3>方法</h3>
				  	<p>你可以手动调用方法来打开或者关闭</p>
					<figure class="highlight"><pre><code class="language-js" data-lang="js"><span class="nx">$</span><span class="p">(</span><span class="s1">'.weui-cell_swiped'</span><span class="p">).</span><span class="nx">swipeout</span><span class="p">(</span><span class="s1">'open'</span><span class="p">)</span> <span class="c1">//打开</span>
					<span class="nx">$</span><span class="p">(</span><span class="s1">'.weui-cell_swiped'</span><span class="p">).</span><span class="nx">swipeout</span><span class="p">(</span><span class="s1">'close'</span><span class="p">)</span> <span class="c1">//关闭</span></code></pre></figure>
			  		<p><strong>但是请注意，只能同时打开一个swipeout，如果你同时打开多个，之后最后一个会被打开</strong></p>
			  		<h3>事件</h3>
			  		<p>无论是用户操作，还是调用JS打开或者关闭，都会触发事件，分别是 <code>swipeout-open</code> 和 <code>swipeout-close</code></p>
				</article>
		    </div>
  		  </div>
      </div>
      <script src="js/qrcode.js"></script>
    </div>  
	<footer>
	  <div class="footer-inner">
	    <ul class="links">
	      <li><a href="http://old.jqweui.com" target="_blank">v0.8.3 文档</a></li>
	      <li><a href="https://weui.io/#/" target="_blank">WeUI</a></li>
	      <li><a href="http://light7.org" target="_blank">Light7</a></li>
	      <li><a href="http://idangero.us/framework7/" target="_blank">Framework7</a></li>
	    </ul>
	    <p>
	    jQuery WeUI <AUTHOR> @time 2016/01/11
	    </p>
	    <div class="cn-only">
	      <p>
	        <a href="http://www.miitbeian.gov.cn/">浙ICP备16003227号-2</a>
	      </p>
	      <p>Hosted by <a href="https://pages.coding.me" style="font-weight: bold">Coding Pages</a></p>
	    </div>
	  </div>
	</footer>
	
	<script src="js/jquery-3.3.1.min.js"></script>
	<script src="js/bootstrap.min.js"></script>	
	<script src="js/docs.js"></script>
		
	<script>
	  $('.pop').click(function () {
	    ga('send', 'event', 'Pop', 'click')
	  })
	</script>
	
	<script>
	  if (/jqweui\.cn/.test(location.host)) {
	    $(document.body).addClass('cn')
	  } else {
	    $(document.body).addClass('en')
	  }
	</script>
	</body>
</html>
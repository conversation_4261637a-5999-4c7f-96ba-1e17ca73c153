﻿require.config({
    baseUrl: '../',//指定js文件的基路径
    paths: {
        layer: 'plugin/layer_mobile/layer_mobile.min',
        aboutright: 'wxchdc/js/aboutright'
    },
    waitSeconds: 0
});
var objdata = {};
require(["layer", 'aboutright'], function (layer, aboutright) {
    aboutright.then(function (data) {
        if (!data) {
            window.location.href = "./bind.html?v=" + Arg("v");
            return
        }
        objdata.strtoday = moment().format('YYYY-MM-DD');
        $("#ultoptab").children().click(function () {
            var index = $(this).index();
            $(this).addClass('current').siblings().removeClass('current');
            $(this).parent().children().each(function (i) {
                if (i == 0) {
                    $(this).children('img').prop('src', 'images/schoolbus/icon_stattotal' + (index == i ? '_HL' : '') + '.png')
                } else if (i == 1) {
                    $(this).children('img').prop('src', 'images/schoolbus/icon_alarm' + (index == i ? '_HL' : '') + '.png')
                } else if (i == 2) {
                    $(this).children('img').prop('src', 'images/schoolbus/icon_busdistribute' + (index == i ? '_HL' : '') + '.png')
                }
            })
            $(".page").eq(index).show().siblings('.page').hide();
            var isfirst = $(this).data('hasload') ? false : true;
            if (index == 0) {
                loaddata(isfirst);
            } else if (index == 1) {
                loadwarning(isfirst);
            } else {
                loadmap(isfirst);
            }
        }).eq(0).trigger('click');
        $.sm(function (re, err, obj) {
            var count = 0;
            if (obj && obj.count) {
                count = obj.count;
            }
            if (count > 0) {
                $("#tabwarningnum").text(count).show()
            }
        }, ['bus.todaywarningnum', objdata.strtoday])
    });
});
function loaddata() {
    $.sm(function (res) {
        $("#busupnum").html(res[0].sum);//总上车
        $("#busdownnum").html(res[1].sum);//送下车
        $("#inbusnum").html(res[0].sum - res[1].sum);

        $("#todayjienum").text(res[2].sum || 0)//安全到园
        $("#todaysongnum").text(res[3].sum || 0)//安全到家
        $("#jiesongcha").text(Math.abs((res[2].sum || 0) - (res[3].sum || 0)));//差值

    }, [['bus.gettodayallupnum', objdata.strtoday], ['bus.gettodayalldownnum', objdata.strtoday], ['bus.gettodayjienum', objdata.strtoday], ['bus.gettodaysongnum', objdata.strtoday]])
    $.sm(function (res, err) {
        $("#totalroutenum").html(res[0].count);
        $("#dirveroutenum").html(res[1].count);
        $("#drivebusnum").html(res[2].count);
        $("#undriveroutenum").html(res[0].count - res[1].count);

        $("#drivebusyeynum").html(res[3].count);
    }, [['bus.getallroutenum'], ['bus.getstartroutenum', objdata.strtoday], ['bus.getstartbusnum', objdata.strtoday], ['bus.getstartbusyeynum', objdata.strtoday]])
    $.sm(function (re, err) {
        if (re && re.length) {
            var objyey = {};
            for (var i = 0; i < re.length; i++) {
                var yeyid = re[i].yeyid;
                var stronehtml =
                    '<div class="breakdown-list">\
                        <div class="weui-cell">\
                            <div class="weui-cell__hd">\
                                <img src="images/schoolbus/bus_img1.png">\
                            </div>\
                            <div class="weui-cell__bd">\
                                <p class="schoolbus-num">' + re[i].busname + '(' + re[i].busnumber + ')</p>\
                                <p>跟车老师：' + re[i].empname + '（' + re[i].empmobile + '）</p>\
                                <p>司机：' + re[i].drivername + '（' + re[i].drivermobile + '）</p>\
                            </div>\
                        </div>\
                        <div class="breakdown-label">故障时间：<span>' + (re[i].badtime ? re[i].badtime.substring(0, 16) : '暂无') + '</span></div>\
                        <div class="breakdown-label">故障原因：<span>' + re[i].cancelreason + '</span></div>\
                    </div>';
                if (!objyey[yeyid]) {
                    objyey[yeyid] = {
                        yeyname: re[i].yeyname,
                        arrbus: []
                    };
                }
                objyey[yeyid].arrbus.push(stronehtml);
            }

            var strhtml = '';
            for (var key in objyey) {
                strhtml +=
                    '<div class="breakdown-cell">\
                        <h5 class="weui-flex">蓝天幼儿园<span style="display: none">(李华：13152125123)</span></h5>\
                        ' + objyey[yeyid].arrbus.join('') + '\
                    </div>';
            }
            $("#divbadbus").html(strhtml).show();
            $("#divbadbus_nodata").hide();
        } else {
            $("#divbadbus_nodata").show();
            $("#divbadbus").hide();
        }
    }, ['bus.gettodaybadbus', objdata.strtoday]);
}
function loadwarning() {
    $("#strtoday").text(objdata.strtoday);
    $.sm(function (res, err) {
        var totalwarningnum = res[0].count;
        var todaywarningnum = res[1].count;
        $("#totalwarningnum").text(totalwarningnum)
        $("#todaywarningnum").text(todaywarningnum)
        var arr = res[2];
        if (!arr.length) {
            $("#divnowarning").show();
            return;
        }
        var strhtml = '';
        for (var i = 0; i < arr.length; i++) {
            var title = '';
            var strnum = '';
            if (arr[i].wtype == 'jieovertimeunstart') {
                title = '接宝宝车辆超时未发车';
                strnum = '<p>涉及校车数：' + arr[i].unstartroutenumber + '辆</p>';
            } else if (arr[i].wtype == 'songovertimeunstart') {
                title = '送宝宝车辆超时未发车';
                strnum = '<p>涉及校车数：' + arr[i].unstartroutenumber + '辆</p>';
            } else if (arr[i].wtype == 'jiewarning') {
                title = '接宝宝车辆未下车报警';
                strnum = '<p>涉及宝宝数：' + arr[i].inbusstunum + '人</p>';
            } else if (arr[i].wtype == 'songwarning') {
                title = '送宝宝车辆未下车报警';
                strnum = '<p>涉及宝宝数：' + arr[i].inbusstunum + '人</p>';
            }
            strhtml +=
                '<div class="weui-panel weui-panel_access">\
                    <div class="weui-panel__bd">\
                        <a href="javascript:void(0);" class="weui-media-box weui-media-box_appmsg">\
                            <div class="weui-media-box__bd">\
                                <h4 class="weui-media-box__title">' + title + '</h4>\
                                <div class="weui-media-box__desc">今天 ' + arr[i].wtime.substring(11, 19) + '</div>\
                                <div style="margin: 5px 0;font-size: 14px;display: none" >当前有宝宝未下车，请重点关注</div>\
                                ' + strnum + '\
                            </div>\
                        </a>\
                    </div>\
                    <div class="weui-panel__ft" style="display: none">\
                        <a href="javascript:void(0);" class="weui-cell weui-cell_access weui-cell_link">\
                            <div class="weui-cell__bd">详情</div>\
                            <span class="weui-cell__ft"></span>\
                        </a>\
                    </div>\
                </div>';
        }
        $("#divwarning").show().html(strhtml);
    }, [['bus.totalwarningnum'], ['bus.todaywarningnum', objdata.strtoday], ['bus.todaywarning', objdata.strtoday]])
}
function loadmap() {

}
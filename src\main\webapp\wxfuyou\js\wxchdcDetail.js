handleLinkV();
var objdata = {
    title: '<h3 style="text-align: center;margin-top: 15px">{{title}}</h3>'
};
layui.use(["layer"], function () {
    var isPC = /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent) ? false : true;
    if (!isPC) {
        objdata.title = '<h3 style="text-align: left;margin-top: 15px;margin-left: 10px">{{title}}</h3>';
        $("#container").parent().css({
            position: "absolute",
            width: "100%",
            overflowY: "scroll",
            top: 0,
            bottom: 0
        });
    }
    var outCon = $("#container"),
        ossBaseUrl = 'http://tbfile.oss-cn-beijing.aliyuncs.com/',
        noticeId = Arg("noticeid"),
        mobile = Arg("mobile"),
        attacheIcon = {
            baseUrl: "../images/",
            png: "txt_img1.png",
            jpg: "txt_img1.png",
            doc: "txt_img2.png",
            docx: "txt_img2.png",
            zip: "txt_img3.png",
            txt: "txt_img5.png",
            xlsx: "txt_img6.png",
            xls: "txt_img6.png",
            pdf: "txt_img7.png",
            ppt: "txt_img10.png"
        };
//定义模板
    var templete = {
//    详情头部
            headTem: objdata.title +
            '<hr style="height:1px;border:none;border-top:1px dashed lightgray;margin: 0 10px;opacity: 0.5;">' +
            '<div class="noti-tit-list">' +
            '<div>' +
            '<p>发布人：<span>{{uname}}</span></p>' +
            '<p class="qr-type-name" style="margin-left: 1.5rem">通知类型：<span>{{typename}}</span></p>' +
            '</div>' +
            '<div>' +
            '<p>发布日期：<span>{{publishtime}}</span></p>' +
            '</div>' +
            '</div>' +
            '<hr style="height:1px;border:none;border-top:1px dashed lightgray;margin: 0 10px;opacity: 0.5;">',
            //附件
            attacheTem: '<dl>' +
            '<dt><img src="{{icon}}"></dt>' +
            '<dd>' +
            '<p class="layui-txt-tit">{{name}}<label>（{{size}}）</label></p>' +
            '<a class="js-down-file-pc" data-path="{{path}}" style="display: {{downPcShow}};color: #1ca89f !important;">下载</a>' +
            '<a class="js-down-file-wx" target="_blank" href="{{hrefPath}}" style="display: {{downWxShow}};color: #1ca89f !important;">下载</a>' +
            '</dd>' +
            '</dl>'
        },
        /**
         * 替换模板数据 模板与数据中的变量名完全一致
         * @param template {String} 模板字符串
         * @param model {Object} 模板数据
         * @returns {*} {strHtml} 数据替换后的模板字符串
         */
        renderTemp = function (template, model) {
            var reg = /\{\{\w+\}\}/g;
            template = template.replace(reg, function (regStr) {
                var reg2 = /\{\{(\w+)\}\}/g,
                    key = reg2.exec(regStr)[1];
                return model[key];
            });
            return template;
        },
        params = {
            yeyId: Arg("yeyid"),
            noticeId: noticeId,
            areaCode: Arg("areacode")
        };

    function init() {
        initEvent();
        new Promise(function (resolve, reject) {
            $.sm(function (re, err) {//获取setsession
                if (!err && re) {
                    resolve(re);
                } else {
                    layer.msg("发送消息失败");
                }
            }, ["notice.setsession", params.areaCode]);
        }).then(function () {
            aboutEnroll();
            return new Promise(function (resolve, reject) {
                $.sm(function (re, err) {//n.fromid, n.fromdepart, nt.typename, to_char(n.publishtime, 'YYYY-MM-DD HH24:MI') as publishtime, nd.title, nd.content
                    if (!err && re) {
                        resolve(re[0]);
                    } else {
                        layer.msg("发送消息失败")
                    }
                }, ["notice.getOneNotice", params.noticeId]);
            });
        }).then(function (data) {
            if (!data) {
                return new Promise.reject("参数不合法");
            }
            var getUname = function (fromId) {
                    return new Promise(function (resolve, reject) {
                        $.sm(function (re, err) {
                            if (!err && re) {
                                data.uname = re[0] && re[0].uname || "暂无";
                                resolve(data);
                            } else {
                                layer.msg("发送消息失败")
                            }
                        }, ["notice.get1Uname", fromId]);
                    });
                },
                getAttaches = function (noticeId) {
                    return new Promise(function (resolve, reject) {
                        $.sm(function (re, err) {
                            if (!err && re) {
                                resolve(re);
                            } else {
                                layer.msg("发送消息失败")
                            }
                        }, ["notice.selAttache", noticeId]);
                    });
                };
            return Promise.all([getUname(data.fromid), getAttaches(params.noticeId)]);
        }).then(function (data) {
            showContent(data);
        }).catch(function (msg) {
            $("body").html("<h2>" + msg + "</h2>");
        });
        //更新阅读状态
        if (!Arg("readstatus") || Arg("readstatus") == "noread") {
            new Promise(function (resolve, reject) {
                    var noticeid = noticeId;
                $.sm(function (re, err) {
                    if (re) {
                        resolve(re);
                    } else {
                        console.log("请求失败！");
                    }
                }, ["updata_noticeread",noticeid, mobile]);

                $.sm(function (re,err) {
                    if (re){
                    }else {
                        console.log("跟新notice失败");
                    }
                },["update_notice",noticeid]);
            }).then(function (data) {
            });
        }
    }

    function initEvent() {
//    下载附件
        outCon.off("click", ".js-down-file-pc").on("click", ".js-down-file-pc", function (ev) {
            var fileObj = {};
            fileObj.fileName = $(this).prev().get(0).childNodes[0].nodeValue;
            fileObj.ossKey = $(this).attr("data-path");
            downAttache(fileObj);
        });
        outCon.off("click", ".js-down-file-wx").on("click", ".js-down-file-wx", function (ev) {
            // layer.msg("如果微信端不能打开或下载，请前往pc端下载相关附件");
        });

        // 验证电话
        $('#txtmobile').on('blur', function () {
            var $this = $(this);
            var $val = $.trim($this.val());
            if ($val) {
                if (!mobileCheck($.trim($val))) {
                    layer.msg("请输入有效的手机号码！", {shift: 6});
                    return false;
                }
            }
        });
    }

//展现通告内容
    function showContent(data) {
        var content = data[0],
            attaches = data[1];
        outCon.children().eq(0).prepend(renderTemp(templete.headTem, content));
        if (!isPC) {
            outCon.find(".qr-type-name").css({
                display: "block",
                marginLeft: "10px"
            });
        }
        outCon.children().eq(0).find(".noti-txt-con").html(content.content);
        outCon.find(".end-txt").html("发文单位：" + content.fromdepart);
        if (attaches.length === 0) {
            return;
        }
        var arrHtml = [];
        for (var i = 0; i < attaches.length; i++) {
            var curAttache = attaches[i],
                ext = /\.\w+/.exec(curAttache.name)[1];
            curAttache.icon = attacheIcon.baseUrl + (attacheIcon[ext] || attacheIcon.txt);
            curAttache.hrefPath = ossBaseUrl + curAttache.path;
            curAttache.path = curAttache.path.replace("sxchdc/notice/", "").replace("chdc/notice/", "");
            curAttache.downPcShow = isPC ? "inline" : "none";
            curAttache.downWxShow = isPC ? "none" : "inline";
            arrHtml.push(renderTemp(templete.attacheTem, attaches[i]));
        }
        outCon.find(".down-txt").show().html(arrHtml.join("")).append('<p style="font-size: x-small;text-align: left;color: red;">注意：&lt;如果微信端不能打开或下载，请前往pc端下载相关附件&gt;</p>');
    }

//以附件形式下载文件
    function downAttache(fileOption) {
        var form = document.createElement("form");
        form.style.display = "none";
        form.method = "post";
        form.action = "http://" + location.host + $.projectpath + "/download?filename=" + fileOption.ossKey + "&toname=" + fileOption.fileName + "&downtype=notice";
        document.body.appendChild(form);
        form.submit();
    }

//通知是否报名，及我的报名状态
    function aboutEnroll() {
        var btnBox = $("#qr-enroll-btn");
        new Promise(function (resolve, reject) {
            $.sm(function (re, err) {
                if (re && !err) {
                    resolve(re[0]);
                }
            }, ["notice.h5.notiEnroll", noticeId, mobile])
        }).then(function (data) {
            if (!data.isenroll) return false;
            var arrBtnClass = "qr-type" + data.enrollstatus;
            if (+moment(data.enrolltime)._d < +new Date()) {
                arrBtnClass = "qr-type3"
            }
            btnBox.show().find("." + arrBtnClass).show();
            btnBox.off("click", "a").on("click", "a", function (ev) {
                var conBox = $("#qr-enroll-info"),
                    btnClass = $(this).attr("class");
                switch (btnClass) {
                    case "qr-enroll" : {
                        //我要报名
                        layer.open({
                            type: 1,
                            title: "填写报名信息",
                            area: [isPC ? "50%" : "100%", "380px"],
                            content: $("#qr-enroll-info"),
                            btn: ["取消", "提交"],
                            btnAlign: "c",
                            shift: 'top', //从左动画弹出
                            success: function (layer) {
                                //这个函数就是在弹窗弹出成功后执行的函数
                                //这个layer包含了弹窗里面的所有的html内容
                                //有了这个就非常好办了，我用js调试了一下马上就定位到了弹窗框的位置然后改了他的class属性，成功。
                                //这是我定位的第一个按钮的代码
                                layer[0].childNodes[3].childNodes[0].attributes[0].value = 'layui-layer-btn1';
                                layer[0].childNodes[3].childNodes[1].attributes[0].value = 'layui-layer-btns';

                            },
                            yes: function (index, layero) {
                                layer.close(index);
                            },
                            btn2: function (index, layero) {
                                var inputs = conBox.find("input"),
                                    enrollman = inputs.eq(0).encodeval(),
                                    enrollmobile = inputs.eq(1).encodeval(),
                                    enrollnum = inputs.eq(2).encodeval(),
                                    enrollmark = conBox.find("textarea").encodeval();
                                if (!enrollman || !enrollmobile || !enrollnum) {
                                    layer.msg("必填项不可为空");
                                    return false;
                                }
                                if (!mobileCheck($.trim(enrollmobile))) {
                                    layer.msg("请输入有效的手机号码！", {shift: 6});
                                    return false;
                                }
                                $.sm(function (re, err) {
                                    if (!err && re) {
                                        layer.msg("报名成功");
                                        btnBox.children().hide().eq(1).show();
                                        setTimeout(function () {
                                            layer.close(index);
                                        }, 800);
                                    } else {
                                        layer.msg("报名失败,请稍后再试");
                                        setTimeout(function () {
                                            layer.close(index);
                                        }, 800);
                                    }
                                }, ["notice.enroll", enrollman, enrollmobile, enrollnum, enrollmark, noticeId, mobile]);
                                return false;
                            }
                        });
                        break;
                    }
                    case "qr-no-enroll" : {
                        //不报名
                        layer.open({
                            type: 1,
                            title: "提示",
                            icon: 3,
                            btnAlign: "c",
                            area: [isPC ? "30%" : "80%", "30%"],
                            content: "<div style='text-align: center; margin-top: 22px;'>确认不报名？<sapn style='display: block;font-size: small;padding-top: 20px;text-align: center'>(不报名后不能修改!!!)</sapn></div>",
                            btn: ["确定", "取消"],
                            yes: function (index) {
                                $.sm(function (re, err) {
                                    if (!err && re) {
                                        layer.msg("您已确认不报名");
                                        btnBox.children().hide().eq(2).show();
                                        setTimeout(function () {
                                            layer.close(index);
                                        }, 800);
                                    } else {
                                        layer.msg("不报名失败,请稍后再试");
                                        layer.close(index);
                                    }
                                }, ["notice.notenroll", noticeId, mobile]);
                            }
                        });
                        break;
                    }
                    case "qr-my-enroll" : {
                        //我的报名信息
                        var cancelBtn = ["关闭"];
                        if ($(this).parent().prop("class") === "qr-type1") {
                            cancelBtn = ["取消", "修改"];
                        }
                        layer.open({
                            type: 1,
                            title: "我的报名信息",
                            area: [isPC ? "50%" : "100%", "380px"],
                            content: $("#qr-enroll-info"),
                            btn: cancelBtn,
                            btnAlign: "c",
                            success: function (index, layero) {
                                var inputs = conBox.find("input");
                                inputs.val("");
                                $.sm(function (re, err) {
                                    if (!err && re) {
                                        var objInfo = re[0];
                                        inputs.eq(0).val(objInfo.enrollman).end().eq(1).val(objInfo.enrollmobile).end().eq(2).val(objInfo.enrollnum);
                                        conBox.find("textarea").val(objInfo.enrollmark);
                                    } else {
                                        layer.msg("查看报名信息失败,请稍后再试");
                                        layer.close(index);
                                    }
                                }, ["notice.getEnrollInfo", noticeId, mobile]);
                            },
                            yes: function (index, layero) {
                                layer.close(index);
                            }, btn2: function (index, layero) {
                                var inputs = conBox.find("input"),
                                    enrollman = inputs.eq(0).encodeval(),
                                    enrollmobile = inputs.eq(1).encodeval(),
                                    enrollnum = inputs.eq(2).encodeval(),
                                    enrollmark = conBox.find("textarea").encodeval();
                                if (!enrollman || !enrollmobile || !enrollnum) {
                                    layer.msg("必填项不可为空");
                                    return false;
                                }
                                if (!mobileCheck($.trim(enrollmobile))) {
                                    layer.msg("请输入有效的手机号码！", {shift: 6});
                                    return false;
                                }
                                $.sm(function (re, err) {
                                    if (!err && re) {
                                        layer.msg("修改报名成功");
                                        btnBox.children().hide().eq(1).show();
                                        setTimeout(function () {
                                            layer.close(index);
                                        }, 800);
                                    } else {
                                        layer.msg("修改报名失败,请稍后再试");
                                        setTimeout(function () {
                                            layer.close(index);
                                        }, 800);
                                    }
                                }, ["notice.enroll", enrollman, enrollmobile, enrollnum, enrollmark, noticeId, mobile]);
                                return false;
                            }
                        });
                        break;
                    }
                    case "qr-cancel-enroll" : {
                        //取消报名
                        layer.open({
                            type: 1,
                            title: "提示",
                            icon: 3,
                            btnAlign: "c",
                            area: [isPC ? "30%" : "80%", "30%"],
                            content: "<div style='text-align: center;margin-top: 22px;'>确认取消报名？</div>",
                            btn: ["确定", "取消"],
                            yes: function (index) {
                                $.sm(function (re, err) {
                                    if (!err && re) {
                                        layer.msg("取消报名成功");
                                        $("#txtname").val("");
                                        $("#txtmobile").val("");
                                        $("#txtnum").val("1");
                                        $("#txtbeizhu").val("");
                                        btnBox.children().hide().eq(0).show();
                                        setTimeout(function () {
                                            layer.close(index);
                                        }, 800);
                                    } else {
                                        layer.msg("取消报名失败,请稍后再试");
                                        layer.close(index);
                                    }
                                }, ["notice.cancelEnroll", noticeId, mobile]);
                            }
                        });
                        break;
                    }
                }
            });
        });
    }

    init();
});
//处理link中的version
function handleLinkV() {
    var version = $.cookie('homeVersion'),
        version1 = Arg("homeversion");
    if (!version || version != version1) {
        $.cookie("homeVersion", version1);
        var links = $("link");
        links.eq(0).prop("href", links.eq(0).prop("href") + "?v=" + version1);
        links.eq(1).prop("href", links.eq(1).prop("href") + "?v=" + version1);
    }
}
function mobileCheck(mobile) {
    var first = mobile.charAt(0);
    if (first==1 && mobile.length==11){
        return true;
    }else {
        return false;
    }
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <title>测试</title>
    <link rel="stylesheet" type="text/css" href="weui.min.css">
    <link rel="stylesheet" type="text/css" href="jquery-weui.min.css">
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <style>
        body {
            background: #F3F4F6;
        }

        #selSex {
            color: #dddddd;
        }

        #selSex .current {
            color: #000000;
        }
    </style>
</head>
<body>
<section class="addbodydiv">
    <div style="height:100px;" id="div1" class="mypickerouter">
        <input type="button" value="datetimePicker" id="btn1">
    </div>
    <div style="height:100px;" id="div2" class="btncalender">
        <input type="button" value="calender" id="btn2">
    </div>
</section>
<script src="jquery-weui.js?v=22"></script>
<script>
    $(function () {
        var _div1 = $("#div1");
        var _div2 = $("#div2");
        var _txt1 = $("#txt1");
        var _txt2 = $("#txt2");
        _div2.calendar({
            onChange: function (p, values, displayValues) {
                debugger
            }
        });

        _div1.datetimePicker({
            inputChange: false,//滑动时,input值不改变
            min: '2018-03-29',
            max: '2018-04-02',
            times: false,
            onPick: function (picker) {
                var values = picker.value;
                console.log(values)
            }
        });
    })
</script>
</body>
</html>

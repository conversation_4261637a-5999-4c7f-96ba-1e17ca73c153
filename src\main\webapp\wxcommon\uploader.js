﻿define(function () {
    // type： one:单个图片 multi:多个图片  weui//weui仿朋友圈
    function uploader(options) {
        var _id = 0;
        var _btn = null;
        var $uploader = options.divouter;
        if (options.handletype == 'outer') {//外部处理进度 显示灯
            _btn = options.divbtn.find('input[type="file"]');
        } else {
            _btn = $uploader.find('input[type="file"]');
        }
        var URL = window.URL || window.webkitURL || window.mozURL;

        // 找到DOM里file-content，若无，则插入一个。
        function findFileCtn($uploader, id) {
            var $file = $uploader.find('[data-id="' + id + '"]');
            var $fileCtn = $file.find('.weui-uploader__file-content');

            if (!$fileCtn.length) {
                $fileCtn = $('<div class="weui-uploader__file-content"></div>');
                $file.append($fileCtn);
            }
            $file.addClass('weui-uploader__file_status');
            return $fileCtn;
        }

        // 清除DOM里的上传状态
        function clearFileStatus($uploader, id) {
            var $file = $uploader.find('[data-id="' + id + '"]').removeClass('weui-uploader__file_status');
            $file.find('.weui-uploader__file-content').remove();
        }

        //生成文件名字的key
        function makeFilename() {
            var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
            var maxPos = chars.length;
            var random_string = '';
            for (i = 0; i < 10; i++) {
                random_string += chars.charAt(Math.floor(Math.random() * maxPos));
            }
            return random_string + new Date().getTime();
        }

        function getOsssignature(upfolder) {
            if (!window.osssignature) {
                $.sm(function (re, err) {
                    if (re) {
                        window.upexpire = re.expire;
                        window.osssignature = {
                            'key': re.dir,
                            'policy': re.policy,
                            'OSSAccessKeyId': re.accessid,
                            'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                            'signature': re.signature
                        };
                    }
                }, ["oss.getsignature", upfolder || 'test'], "", "", {
                    async: false
                });
            }
            return window.osssignature;
        }

        function getImageWidth(url, callback) {
            if (!url) {
                return callback(null, null, "图片地址为空");
            }
            var img = new Image();
            img.src = url;
            // 如果图片被缓存，则直接返回缓存数据
            if (img.complete) {
                callback(img.width, img.height);
            } else {
                // 完全加载完毕的事件
                img.onload = function () {
                    callback(img.width, img.height);
                }
                img.onerror = function () {
                    callback(null, null, "获取图片失败");
                }
            }
        }

        function upload(options) {
            var url = options.url,
                file = options.file,
                fileVal = options.fileVal,
                onBeforeSend = options.onBeforeSend,
                onProgress = options.onProgress,
                onError = options.onError,
                onSuccess = options.onSuccess,
                onComplete = options.onComplete;
            var name = file.name,
                type = file.type,
                lastModifiedDate = file.lastModifiedDate;

            var data = {
                name: name,
                type: type,
                size: options.type == 'file' ? file.size : file.base64.length,
                lastModifiedDate: lastModifiedDate
            };
            var headers = {};
            var beforesendresult = onBeforeSend(file, data, headers);
            if (beforesendresult === false) {
                return;
            }
            file.status = 'progress';
            onProgress(file, 0);
            if (options.urltype == 'oss') {//上传到阿里云oss
                var osssigntature = getOsssignature(options.upfolder || 'test');
                if (osssigntature) {
                    var filename = makeFilename() + "." + file.name.split(".")[1];
                    var request = new FormData();
                    request.append("OSSAccessKeyId", osssignature.OSSAccessKeyId);//Bucket 拥有者的Access Key Id。
                    request.append("policy", osssignature.policy);//policy规定了请求的表单域的合法性
                    request.append("Signature", osssignature.signature);//根据Access Key Secret和policy计算的签名信息，OSS验证该签名信息从而验证该Post请求的合法性
                    //---以上都是阿里的认证策略
                    request.append("key", osssignature.key + "/" + filename);//文件名字，可设置路径
                    request.append("success_action_status", '200');// 让服务端返回200,不然，默认会返回204
                    request.append('file', file);//需要上传的文件 file
                    //request.append("callback", "");//回调，非必选，可以在policy中自定义
                    $.ajax({
                        url: url,  //上传阿里地址
                        data: request,
                        processData: false,//默认true，设置为 false，不需要进行序列化处理
                        cache: false,//设置为false将不会从浏览器缓存中加载请求信息
                        //async: false,//发送同步请求
                        contentType: false,//避免服务器不能正常解析文件---------具体的可以查下这些参数的含义
                        //dataType: 'JSONP',//不涉及跨域  写json即可
                        type: 'post',
                        xhr: function () { //用以显示上传进度
                            var xhr = $.ajaxSettings.xhr();
                            if (xhr.upload) {
                                xhr.upload.addEventListener('progress', function (event) {
                                    if (event.total) {
                                        var percent = Math.floor(event.loaded / event.total * 100);
                                        onProgress(file, percent);
                                    }
                                }, false);
                            }
                            return xhr
                        },
                        success: function (callbackHost, request) { //callbackHost：success,request中就是 回调的一些信息，包括状态码什么的
                            file.key = osssignature.key + "/" + filename;
                            file.url = url + "/" + osssignature.key + "/" + filename;
                            if (options.needwh) {
                                getImageWidth(url + "/" + osssignature.key + "/" + filename, function (w, h) {
                                    file.width = w;
                                    file.height = h;
                                    onSuccess && onSuccess(file);
                                    onComplete && onComplete(file, true);
                                })
                            } else {
                                onSuccess && onSuccess(file);
                                onComplete && onComplete(file, true);
                            }
                        },
                        error: function (returndata) {
                            onError(file, new Error('XMLHttpRequest response status is ' + returndata.readyState));
                            onComplete && onComplete(file, false);
                        }
                    });
                } else {

                }

            } else {


                var formData = new FormData();
                var xhr = new XMLHttpRequest();

                file.xhr = xhr;

                // 设置参数
                Object.keys(data).forEach(function (key) {
                    formData.append(key, data[key]);
                });
                if (options.type == 'file') {
                    formData.append(fileVal, file, name);
                } else {
                    formData.append(fileVal, file.base64);
                }

                xhr.onreadystatechange = function () {
                    if (xhr.readyState == 4) {
                        if (xhr.status == 200) {
                            try {
                                // 只支持json
                                var ret = JSON.parse(xhr.responseText);
                                onSuccess(file, ret);
                            } catch (err) {
                                onError(file, err);
                            }
                        } else {
                            onError(file, new Error('XMLHttpRequest response status is ' + xhr.status));
                        }
                    }
                };
                xhr.upload.addEventListener('progress', function (evt) {
                    if (evt.total == 0) return;

                    var percent = Math.ceil(evt.loaded / evt.total) * 100;

                    onProgress(file, percent);
                }, false);

                xhr.open('POST', url);

                // 设置头部信息
                Object.keys(headers).forEach(function (key) {
                    xhr.setRequestHeader(key, headers[key]);
                });

                xhr.send(formData);
            }
        }

        // 设置上传
        function setUploadFile(file) {
            file.url = URL.createObjectURL(file);
            file.status = 'ready';
            file.upload = function (cb) {
                upload($.extend({
                    $uploader: $uploader,
                    file: file
                }, options), cb);
            };
            file.stop = function () {
                this.xhr.abort();
            };

            options.onQueued(file);
            if (options.auto) file.upload();
        }

        options = $.extend({
            url: '',
            auto: true,
            type: 'file',
            fileVal: 'file',
            onBeforeQueued: noop,
            onQueued: noop,
            onBeforeSend: noop,
            onSuccess: noop,
            onProgress: noop,
            onError: noop
        }, options);

        if (options.compress !== false) {
            options.compress = $.extend({
                width: 1600,
                height: 1600,
                quality: .8
            }, options.compress);
        }

        if (options.onBeforeQueued) {
            (function () {
                var onBeforeQueued = options.onBeforeQueued;
                options.onBeforeQueued = function (file, files) {
                    if (ret === false) {
                        return false;
                    }
                    if (ret === true) {
                        return;
                    }
                    if (options.handletype == 'inner') {
                        var _obj = $('<li class="weui-uploader__file weui-uploader__file_status" data-id="' + file.id + '"> <div class=weui-uploader__file-content> <i class=weui-loading style=width:30px;height:30px></i> </div> </li>');
                        $uploader.find('.weui-uploader__files').append(_obj);
                    }
                    var ret = onBeforeQueued.call(file, files, _obj);
                };
            })();
        }
        if (options.onQueued) {
            (function () {
                var onQueued = options.onQueued;
                options.onQueued = function (file) {
                    if (!onQueued.call(file) && options.handletype == 'inner') {
                        var $file = $uploader.find('[data-id="' + file.id + '"]');
                        $file.css({
                            backgroundImage: 'url("' + (file.base64 || file.url) + '")'
                        });
                        if (!options.auto) {
                            clearFileStatus($uploader, file.id);
                        }
                    }
                };
            })();
        }
        if (options.onBeforeSend) {
            (function () {
                var onBeforeSend = options.onBeforeSend;
                options.onBeforeSend = function (file, data, headers) {
                    var ret = onBeforeSend.call(file, data, headers);
                    if (ret === false) {
                        return false;
                    }
                };
            })();
        }
        if (options.onSuccess) {
            (function () {
                var onSuccess = options.onSuccess;
                options.onSuccess = function (file, ret) {
                    file.status = 'success';
                    if (!onSuccess.call(file, ret) && options.handletype == 'inner') {
                        clearFileStatus($uploader, file.id);
                    }
                };
            })();
        }
        if (options.onProgress) {
            (function () {
                var onProgress = options.onProgress;
                options.onProgress = function (file, percent) {
                    if (!onProgress.call(file, percent) && options.handletype == 'inner') {
                        findFileCtn($uploader, file.id).html(percent + '%');
                    }
                };
            })();
        }
        if (options.onError) {
            (function () {
                var onError = options.onError;
                options.onError = function (file, err) {
                    file.status = 'fail';
                    if (!onError.call(file, err) && options.handletype == 'inner') {
                        findFileCtn($uploader, file.id).html('<i class="weui-icon-warn"></i>');
                    }
                };
            })();
        }
        if (options.onComplete) {
            (function () {
                var onComplete = options.onComplete;
                options.onComplete = function (file, issucc) {
                    onComplete.call(file, issucc);
                };
            })();
        }
        //选择完图片
        _btn.change(function (evt) {
            var _this = $(this);
            var files = evt.target.files;
            if (files.length === 0) {
                return;
            }
            if (options.compress === false && options.type == 'file') {
                // 以原文件方式上传
                Array.prototype.forEach.call(files, function (file) {
                    file.id = ++_id;
                    file._btn = _this;
                    if (options.onBeforeQueued(file, files) === false) return;

                    setUploadFile(file);
                });
            } else {
                // base64上传 和 压缩上传
                Array.prototype.forEach.call(files, function (file) {
                    file.id = ++_id;
                    file._btn = _this;
                    if (options.onBeforeQueued(file, files) === false) return;
                    compress(file, options, function (blob) {
                        blob._btn = _this;
                        if (blob) setUploadFile(blob);
                    });
                });
            }

            this.value = '';
        });
    }

    //没有操作的方法
    function noop() {

    }

    /**
     * 检查图片是否有被压扁，如果有，返回比率
     * ref to http://stackoverflow.com/questions/11929099/html5-canvas-drawimage-ratio-bug-ios
     */
    function detectVerticalSquash(img) {
        // 拍照在IOS7或以下的机型会出现照片被压扁的bug
        var data;
        var ih = img.naturalHeight;
        var canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = ih;
        var ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        try {
            data = ctx.getImageData(0, 0, 1, ih).data;
        } catch (err) {
            console.log('Cannot check verticalSquash: CORS?');
            return 1;
        }
        var sy = 0;
        var ey = ih;
        var py = ih;
        while (py > sy) {
            var alpha = data[(py - 1) * 4 + 3];
            if (alpha === 0) {
                ey = py;
            } else {
                sy = py;
            }
            py = ey + sy >> 1; // py = parseInt((ey + sy) / 2)
        }
        var ratio = py / ih;
        return ratio === 0 ? 1 : ratio;
    }

    /**
     * dataURI to blob, ref to https://gist.github.com/fupslot/5015897
     * @param dataURI
     */
    function dataURItoBuffer(dataURI) {
        var byteString = atob(dataURI.split(',')[1]);
        var buffer = new ArrayBuffer(byteString.length);
        var view = new Uint8Array(buffer);
        for (var i = 0; i < byteString.length; i++) {
            view[i] = byteString.charCodeAt(i);
        }
        return buffer;
    }

    function dataURItoBlob(dataURI) {
        var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
        var buffer = dataURItoBuffer(dataURI);
        return new Blob([buffer], {type: mimeString});
    }

    /**
     * 获取图片的orientation
     * ref to http://stackoverflow.com/questions/7584794/accessing-jpeg-exif-rotation-data-in-javascript-on-the-client-side
     */
    function getOrientation(buffer) {
        var view = new DataView(buffer);
        if (view.getUint16(0, false) != 0xFFD8) return -2;
        var length = view.byteLength,
            offset = 2;
        while (offset < length) {
            var marker = view.getUint16(offset, false);
            offset += 2;
            if (marker == 0xFFE1) {
                if (view.getUint32(offset += 2, false) != 0x45786966) return -1;
                var little = view.getUint16(offset += 6, false) == 0x4949;
                offset += view.getUint32(offset + 4, little);
                var tags = view.getUint16(offset, little);
                offset += 2;
                for (var i = 0; i < tags; i++) {
                    if (view.getUint16(offset + i * 12, little) == 0x0112) return view.getUint16(offset + i * 12 + 8, little);
                }
            } else if ((marker & 0xFF00) != 0xFF00) break; else offset += view.getUint16(offset, false);
        }
        return -1;
    }

    /**
     * 修正拍照时图片的方向
     * ref to http://stackoverflow.com/questions/19463126/how-to-draw-photo-with-correct-orientation-in-canvas-after-capture-photo-by-usin
     */
    function orientationHelper(canvas, ctx, orientation) {
        var w = canvas.width,
            h = canvas.height;
        if (orientation > 4) {
            canvas.width = h;
            canvas.height = w;
        }
        switch (orientation) {
            case 2:
                ctx.translate(w, 0);
                ctx.scale(-1, 1);
                break;
            case 3:
                ctx.translate(w, h);
                ctx.rotate(Math.PI);
                break;
            case 4:
                ctx.translate(0, h);
                ctx.scale(1, -1);
                break;
            case 5:
                ctx.rotate(0.5 * Math.PI);
                ctx.scale(1, -1);
                break;
            case 6:
                ctx.rotate(0.5 * Math.PI);
                ctx.translate(0, -h);
                break;
            case 7:
                ctx.rotate(0.5 * Math.PI);
                ctx.translate(w, -h);
                ctx.scale(-1, 1);
                break;
            case 8:
                ctx.rotate(-0.5 * Math.PI);
                ctx.translate(-w, 0);
                break;
        }
    }

    /**
     * 压缩图片
     */
    function compress(file, options, callback) {
        var reader = new FileReader();
        reader.onload = function (evt) {
            if (options.compress === false) {
                // 不启用压缩 & base64上传 的分支，不做任何处理，直接返回文件的base64编码
                file.base64 = evt.target.result;
                callback(file);
                return;
            }

            // 启用压缩的分支
            var img = new Image();
            img.onload = function () {
                var ratio = detectVerticalSquash(img);
                var orientation = getOrientation(dataURItoBuffer(img.src));
                var canvas = document.createElement('canvas');
                var ctx = canvas.getContext('2d');

                var maxW = options.compress.width;
                var maxH = options.compress.height;
                var w = img.width;
                var h = img.height;
                var dataURL = void 0;

                if (w < h && h > maxH) {
                    w = parseInt(maxH * img.width / img.height);
                    h = maxH;
                } else if (w >= h && w > maxW) {
                    h = parseInt(maxW * img.height / img.width);
                    w = maxW;
                }

                canvas.width = w;
                canvas.height = h;

                if (orientation > 0) {
                    orientationHelper(canvas, ctx, orientation);
                }
                ctx.drawImage(img, 0, 0, w, h / ratio);

                if (/image\/jpeg/.test(file.type) || /image\/jpg/.test(file.type)) {
                    var quality = 1;
                    if (options.compress.compress == 'high') {//控制在200K
                        var size_m = file.size / 1024;
                        if (size_m > 200) {
                            if (size_m < 2 * 200) {
                                quality = 0.95;
                            } else if (size_m < 4 * 1024) {
                                quality = 0.88;
                            } else if (size_m >= 4 * 1024) {
                                quality = 0.5;
                            }
                        } else {
                            quality = 1;
                        }
                    } else if (options.compress.compress == 'medium') {//控制在500K
                        var size_m = file.size / 1024;
                        if (size_m > 500) {
                            if (size_m < 1024) {
                                quality = 0.99;
                            } else if (size_m < 4 * 1024) {
                                quality = 0.96;
                            } else if (size_m >= 4 * 1024) {
                                quality = 0.8;
                            }
                        } else {
                            quality = 1;
                        }
                    } else if (options.compress.compress == 'low') {//尽量 控制在1m 左右
                        var size_m = file.size / 1024;
                        if (size_m > 1024) {
                            quality = 0.99;
                        } else {
                            quality = 1;
                        }
                    } else {
                        quality = options.compress.quality;
                    }
                    dataURL = canvas.toDataURL('image/jpeg', quality);
                } else {
                    dataURL = canvas.toDataURL(file.type);
                }

                if (options.type == 'file') {
                    if (/;base64,null/.test(dataURL) || /;base64,$/.test(dataURL)) {
                        // 压缩出错，以文件方式上传的，采用原文件上传
                        console.warn('Compress fail, dataURL is ' + dataURL + '. Next will use origin file to upload.');
                        callback(file);
                    } else {
                        var blob = dataURItoBlob(dataURL);
                        blob.id = file.id;
                        blob.name = file.name;
                        blob.lastModified = file.lastModified;
                        blob.lastModifiedDate = file.lastModifiedDate;
                        callback(blob);
                    }
                } else {
                    if (/;base64,null/.test(dataURL) || /;base64,$/.test(dataURL)) {
                        // 压缩失败，以base64上传的，直接报错不上传
                        options.onError(file, new Error('Compress fail, dataURL is ' + dataURL + '.'));
                        callback();
                    } else {
                        file.base64 = dataURL;
                        callback(file);
                    }
                }
            };
            img.src = evt.target.result;
        };
        reader.readAsDataURL(file);
    }

    return function (options) {
        //每次都返回一个新对象
        return new uploader(options);
    };
});
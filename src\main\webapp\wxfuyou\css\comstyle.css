body,h1,h2,h3,h4,h5,h6,hr,p,blockquote,dl,dt,dd,ul,ol,li,pre,form,fieldset,legend,button,input,textarea,th,td{outline:none;margin:0;padding:0;list-style:none;background:none;border:0;}
i{font-style: normal;}
.white{ color:#fff;}
.red,.order-main .weui-cell__ft.red{ color:#ff4747;}
.zred{ color:red;}
.blue{ color:#1fb8b3}
.green{ color:#20e0bb}
.yellow{ color:#fcff00}
.pink{ color:#fa4cbb}
.cred{color: #ff5a5a}
.sky-blue{color: #399bf7}
.grey{color:#999999}
.greylist{color:#cccccc; cursor: pointer}
.whitebg{background:#fff;}
.redbg{ background:#ff4747;}
.greenbg{ background:#00de6e;}
.bluebg{ background:#53b8ff;}
.martop{ background: #F3F4F6 ; height: 10px; width: 100%;}
.bg2{background:#a6a5aa;}
.bg3{background:#008CDC;}
.bg4{background:#FF4C3C;}
.bg5{background:#667279;}
.bg6{background:#00C85E;}
.bg7{background:#FF971D;background:#13b7f6}
.bg8{background:#98BA00;}
.bg9{background:#FFC700;}
.bg10{background:#FC015A;}
.c1{ color:#12b7f5;}
.zblue{ background:#12b7f5}
.orange-yellow{ background:#ff9900}
.orange{ background:#FF632E;}
.pinkbg{ background:#ffe6e6;  }
.top5{ margin-top:5px}
.top10{ margin-top:10px}
.top20{ margin-top:20px}
.padd20{ padding-top:20px}
.padd10{ padding-right: 10px;}
.clr{ clear: both}
.padd15{ padding-left: 15px;}
.paddbottom20{ padding-bottom:20px;}
select{border: 1px solid #E8E8E8;height: 25px;width: 64px;outline: none;}
textarea{font-family: -apple-system-font, "Helvetica Neue", sans-serif;outline: none;}
/*通用按钮*/
.common-btn{background: #5497FC;color: #ffffff;font-size: 13px;padding: 10px 15px;min-width:60px;border-radius: 5px;display: -webkit-box;display: -webkit-flex;display: flex;align-items:center;justify-content:center;}
/*确定取消按钮*/
.weui-btn_opre{background: -webkit-linear-gradient(left, #FF7C68 , #FF4360);background: linear-gradient(left, #FF7C68 , #FF4360);margin: 20px 15px 15px 15px!important;}
.weui-btn_blue{background: -webkit-linear-gradient(left, #53b8ff , #53b8ff);background: linear-gradient(left, #FF7C68 , #FF4360);margin: 20px 15px 15px 15px!important;}
.weui-btn_empty{border: 1px solid #DCDCDD;color: #333!important;margin: 15px 15px 25px 15px !important;}
.weui-btn_empty:after{border: 0!important;}
/*切换按钮*/
.mark-btn-oran{background: #FFBC5A;font-size: 12px;padding: 2px 10px;border-radius: 0 10px 10px 0;color: #fff;}
.mark-btn-right{font-size: 12px;padding: 2px 7px;border-radius: 10px 0 0 10px;color: #fff;height: 20px;position: absolute;right: 0;}
.right-btn-oran{background: #FF9900;}
.right-btn-blue{background: #399BF7;}
.right-btn-gray{background: #EEEEEE;color: #A5A5A5;}
/*按钮*/
.txt-btn-blue{font-size: 12px;margin-right:15px;border: 1px solid #57A9F1;padding: 2px 10px;color: #57A9F1;border-radius: 20px;}
.txt-btn{font-size: 12px;margin-right:15px;border: 1px solid #FF6565;padding: 2px 10px;color: #FF6565;border-radius: 20px;}
.txt-btn-oran{border: 1px solid #FFAD5E;color: #FFAD5E;}
/*weui单选框*/
.remarks-main .weui-cells_checkbox .weui-check:checked + .weui-icon-checked::before{color: #ff5a5a;}
/* 通用样式 */
.out-container {
	position: absolute;
	top: 0;
	bottom: 0;
	width: 100%;
	overflow: scroll;
	-webkit-overflow-scrolling: touch;
}
/*底部菜单*/
.weui-tabbar .weui-tabbar__item.weui-bar__item_on .weui-tabbar__label{color:#00c87f;font-size: 12px;}
.weui-tabbar:before{border: 0;}
.weui-tabbar .weui-badge{padding: 0.02em .25em;}
.weui-tabbar__label{color:#cecece;}
.weui-tabbar__item { margin-top: 6px}
/*自定义复选框*/
.set-checkbox-style input[type="checkbox"] {
	-webkit-appearance: none;
	width: 17px;
	height: 17px;
	border: 1px solid #ccc;
	border-radius: 2px;
	outline: none;
}
.set-checkbox-style input[type="checkbox"]:checked {
	background: url(../images/sel-blue.png) 50%;
	background-size: 17px 17px;
	outline: none;
	border: 0;
}
.set-checkbox-style input[type="checkbox"] {
	vertical-align: middle;
	margin-right: 7px;
}
/*自定义单选框*/
.set-radio-style input[type="radio"] {
	-webkit-appearance: none;
	width: 20px;
	height: 20px;
	border: 1px solid #ccc;
	border-radius: 50%;
	outline: none;
	vertical-align:middle;
}
.set-radio-style input[type="radio"]:checked {
	background: url(../images/sel_radio.png) 50%;
	background-size: 20px 20px;
	outline: none;
	border: 0;
}
.set-radio-style input[type="radio"] {
	margin-right: 7px;
}
.set-radio-style .sel-radio {
	margin-left: 15px;
}
/*左边带线条的标题*/
.checkdj {
	color: #333333;
	width: 100%;
	overflow: hidden;
	background: #fff;
	padding: 10px 0;
	border-bottom: 1px solid #e8e8e8
}
.checkdj:before {
	background: #ff5a5a;
	content: "";
	display: inline-block;
	width: 4px;
	height: 25px;
	float: left;
	margin-right: 20px;
}
.checkdj span {
	float: left;
	display: inline-block;
	font-size: 14px;
	height: 25px;
	line-height: 25px;
}
/*右箭头*/
.icon_arrow_right:after {
	content: "\e66f";
	font-size: 22px;
	color: #333;
}
/*更改weui列表左右内容宽度*/
.weui-cell-align .weui-cells .weui-cell .weui-cell__bd{
  -webkit-box-flex:inherit;
  -webkit-flex:inherit;
  flex:inherit;
}
.weui-cell-align .weui-cells .weui-cell .weui-cell__ft{
  -webkit-box-flex:1;
  -webkit-flex:1;
  flex:1;
}
/*列表无右箭头*/
.weui-cell_access .weui-cell__pft{
	-webkit-box-flex:1;
  	-webkit-flex:1;
  	flex:1;
  	text-align: right;
  	color: #999999;
}
/*通用切换*/
.common-tab{display: -webkit-box;display: -webkit-flex;display: flex;align-items:center;justify-content:center;border: 1px solid #00C87F;height: 34px;border-radius: 5px;}		
.common-tab li{border-right: 1px solid #00C87F;display: -webkit-box;display: -webkit-flex;display: flex;align-items:center;justify-content:center;-webkit-box-flex: 1;-webkit-flex: 1;flex: 1;color: #00C87F;height: 34px;font-size: 15px;}
.common-tab li:first-child{border-radius: 4px 0 0 4px;}
.common-tab li:last-child{border-right: none;border-radius: 0 4px 4px 0;}
.common-tab li.current{background: #00C87F;color: #ffffff;}	
/*表格*/
.define-table{border-collapse: collapse;border-spacing: 0;width: 100%;text-align: left;}
.define-table td,.define-table th{padding: 5px 10px;font-weight:normal;border-width: 1px;border-style: solid;border-color: #e6e6e6;min-height: 20px;line-height: 20px;font-size: 15px;}
/*输入框*/
.def-input{border-bottom: 1px solid #D3D3D3;font-size: 15px;color: #999999;padding: 3px 0;}
.def-input input[type="text"]{height: 24px;line-height: 22px;}			
body,ul,p,dd,dl{margin:0;padding:0;}
body {font-family:"Microsoft yahei";font-size: 16px;background-size: 100% 100%;}
h1,h2,h3,h4,h5,h6{margin: 0; font-weight: normal;}
li{list-style:none;}
a {font-size:100%;vertical-align:baseline;background:transparent;text-decoration:none;outline:none;cursor:pointer;}
em{color:#f00;font-style:normal;}
i{font-style:normal;}
button,input{*overflow: visible;line-height: normal;font-family: 'microsoft yahei';}
textarea{resize:none;word-break: break-all;}
select{font-family:"Microsoft yahei";}
input,textarea{font-family:"Microsoft yahei"; font-size: 14px;}
input:focus,textarea:focus,button{outline: none;}
input[type='checkbox'],input[type='radio']{vertical-align: middle;border:0;}
th{text-align: center;}
img{border:0;}
table{width:100%;border-collapse: collapse;}
.pa{position: absolute;}
.pr{position: relative;}
.font11{font-size: 12px;}
.font12{font-size: 12px;}
.font13{font-size: 13px;}
.font14{font-size: 14px;}
.font15{font-size: 15px;}
.font16{font-size: 16px;}
.font17{font-size: 17px;}
.font18{font-size: 18px;}
.font20{font-size: 20px;}
.font22{font-size: 22px;}
.font24{font-size: 24px;}
.font30{font-size: 30px;}
.font36{font-size: 36px;}
.fl{float: left;}
.fr{float: right;}
.block{display: block;}
.pointer{cursor: pointer;}
.bold{font-weight: bold;}
/* 通用样式下划线 */
.bottom-bor{border-bottom: 1px solid #eeeeee;}
/*color*/
.white{color:#fff;}
/*clearfix*/
.clearfix:after{
  content:".";
  display:block;
  height:0;
  clear:both;
  visibility:hidden;
}
.clearfix{
  display:inline-block;
}
* html .clearfix{
  height:1%;
}
.clearfix{
  display:block;
}
.weui-flex-cen{ display:flex; align-items: center;}
.weui-flex-jucen{justify-content: center;display:flex; align-items: center;}
.surplus-words{display: inline-block;white-space: nowrap; width: 100%; overflow: hidden;text-overflow:ellipsis;}
.hide {display:none}
require.config({
    baseUrl: '../',//指定js文件的基路径
    paths: {
        jquery: 'sys/jquery',
        system: 'sys/system_zepto',
        fun: 'sys/function',
        layer: 'plugin/layer_mobile/layer',
        'jquery-weui': 'wxcommon/weui/jquery-weui',
        moment:'plugin/js/moment',
        chdccommon: 'wxchdc/js/chdccommon',
        iframecommon: 'wxchdc/js/iframecommon',
        open: 'wxchdc/js/openverifyjson'
    },
    waitSeconds: 0
});
var objdata = {
    arryeyid: []
    , objyeydata: {}
    , areacode: ""//地区编号
    , allnum: 0//总项目数
};
require(['jquery'], function ($) {
    require(['system','fun', 'layer','moment', 'chdccommon','iframecommon','open','jquery-weui'], function (a,c, p) {
    // objdata.moment = moment;
    //objdata.curmoment = moment();
    //获取用户信息
        $.sm(function (data, err) {
            if (data && data[0] && !data[0][0]) {
                window.location.href = "./bind.html?v=" + Arg("v");
                return;
            }
            selectEmployeeInfo(function (obj) {
                if (!obj) {
                    console.log("没有用户信息");
                    $("#divunsub").show().siblings().hide();
                    return;
                }
                window.objinfo = obj;
                // console.log(JSON.stringify(obj));
                objdata.openid = obj.openid;
                if (!obj.mobile) {
                    layer.open({
                        content: "没有绑定手机号，请绑定后使用",
                        btn: '确定',
                        yes: function () {
                            var args = Arg.all();
                            parent.location.href = "bind.html?v=" + Arg('v') + "&frompage=openverifylist" + "&flag=" + args['flag'] + (Arg("id") ? '&id=' + Arg("id") : "");
                        }
                    });
                    return;
                }
                // console.log(obj.mobile);
                // console.log("objinfo.areacode:" + objinfo.areacode);
                if (objinfo.areacode) {
                    objdata.areacode = objinfo.areacode;
                } else {
                    layer.msg("请先添加地区用户！");
                    return;
                }
                $('body').on('click', '#back', function () {
                    history.back();
                });

                window.addEventListener("popstate", function (e) {
                    if (objiframecom.arrwin && objiframecom.arrwin.length > 1) {
                        var obj = objiframecom.arrwin[objiframecom.arrwin.length - 1];
                        objiframecom.curhash = objiframecom.arrwin[objiframecom.arrwin.length - 1].key;
                        if (obj.onPageBack) {
                            window.history.pushState(null, obj.title, "#" + obj.key);
                            obj.onPageBack(function () {
                                obj.onPageBack = null;
                                history.back();
                            });
                        } else {
                            objiframecom.arrwin.pop();
                            backEvent(obj);
                        }
                    }
                }, false);
                //处理当前页面逻辑
                objopenverify.initEvent();
                objopenverify.getYey();
                //参数跳转页面
                initBottom();
            });
        },["wx.aboutright.userIsBind"])
    });
});
var objopenverify = {
    /**
     * 注册事件
     */
    initEvent: function () {
        $(".out-container").off("click","#stutab li").on("click","#stutab li",function () {
            var _this = $(this);
            _this.addClass("current").siblings().removeClass("current");
            $(".divdabiaostate").hide().eq(_this.index()).show();
        });
        $("#divverifyyeylist").on('click', '.state-list', function () {
            var yid = $(this).attr('yid'), areaname = $(this).attr('areaname');
            openwin("openverify", 'openverify.html?v=' + Arg("v") + '&frompage=' + window.name + "&yid=" + yid + "&areaname=" + areaname + (Arg("id") ? '&id=' + Arg("id") : ""), "开学核验");
        });
        if(!$.isEmptyObject(objverify)){
            var xmnum = 1, znum = 0;//xmnum项目序号
            for (var n in objverify) {
                var objitem = objverify[n];
                for (var o in objitem) {
                    if(o != 'name'){
                        objdata.allnum++;
                    }
                }
                xmnum++;
            }
        }
    },
    //获取集团园所有幼儿园
    getYey: function (cb) {
        objdata.arryeyid = [];
        objdata.objyeydata = {};
        $.sm(function (re, err) {
            if (err) {
                layer.msg(err);
            } else {
                for (var i = 0; i < re.length; i++) {
                    objdata.arryeyid.push(re[i].id);
                    objdata.objyeydata[re[i].id] = re[i];
                }
                objopenverify.initData();
            }
        }, ["openverifylist.list", objdata.areacode]);
    },
    //初始化所有园所开学条件核验数据
    initData: function () {
        $.sm(function (re, err) {
            if (err) {
                layer.msg(err);
            } else{
                var allnum = 0, arrdabiao = [], arrweidabiao = [], arrhj = [], arrweihejian = [], objd = {}, objdwhynum = {'hydb': 0, 'hywdb': 0, 'zcdb': 0,'zcwdb': 0};//objdwhynum单位核验数 chdc与集团园核验的为核验数，幼儿园自查的为自查数,分达标、未达标两项统计
                $("#divdabiao").html('');
                $("#divweidabiao").html('');
                $("#divweiheyan").html('');
                for (var i = 0; i < re.length; i++) {
                    var item = re[i];
                    if(item){
                        var nums = 0, yesnum = 0, dabiaostate = '不达标', successstate = '';
                        //yeyid,gid,areacode,verifiedUnit,verified,verifieddate,registtype
                        for (var j = 8; j < item.length; j++) {//
                            nums++;
                            if(item[j] == 1){
                                yesnum++;
                            }
                        }
                        var statebi = nums ? formatFloatValue(yesnum/nums * 100) : 0;
                        if(nums == yesnum){//达标
                            dabiaostate = '达标';
                            successstate = 'success-state'
                        }
                        var who = ((item[6] == 'c' || item[6] == 'g') ? '<span class="verify-label" style="background: #f78f62;">核查</span>' : item[6] == 'y' ? '<span class="verify-label" style="background: #6799ff;">自查</span>' : "");
                        objd[item[0]] = {
                            yid: item[0]//幼儿园id
                            , yeyname: who + objdata.objyeydata[item[0]].yeyname
                            , areaname: objdata.objyeydata[item[0]].areaname1//地区名称
                            , dabiao: yesnum//核验达标数
                            , all: nums//所有核验项
                            , dabiaozhanbi: statebi//达标比
                            , successstate: successstate//状态class
                            , dabiaostate: dabiaostate//状态（达标or未达标）
                        };
                        if(item[7] != 1){//幼儿园没有提交的不查看
                            continue;
                        }
                        if(nums == yesnum){//核验项与合格项相等
                            if(item[6] == 'c' || item[6] == 'g'){//核验
                                objdwhynum['hydb']++;
                            }else if(item[6] == 'y'){//自查
                                objdwhynum['zcdb']++;
                            }
                            $("#divdabiao").append(objopenverify.renderTemp(objopenverify.htmlmodel, objd[item[0]]));
                            arrdabiao.push(item[0]);
                        }else{
                            if(item[6] == 'c' || item[6] == 'g'){//核验
                                objdwhynum['hywdb']++;
                            }else if(item[6] == 'y'){//自查
                                objdwhynum['zcwdb']++;
                            }
                            $("#divweidabiao").append(objopenverify.renderTemp(objopenverify.htmlmodel, objd[item[0]]));
                            arrweidabiao.push(item[0]);
                        }
                        arrhj.push(item[0]);
                        objdata.allnum = nums;
                    }
                }
                arryid = objdata.arryeyid;
                for (var i = 0; i < arryid.length; i++) {
                    if($.inArray(arryid[i] + "", arrhj) < 0){
                        var objhy = {
                            yid: arryid[i]//幼儿园id
                            , yeyname: objdata.objyeydata[arryid[i]].yeyname
                            , areaname: objdata.objyeydata[arryid[i]].areaname1//地区名称
                            , dabiao: objd[arryid[i]] && objd[arryid[i]].dabiao || 0
                            , all: objdata.allnum
                            , dabiaozhanbi: objd[arryid[i]] && objd[arryid[i]].dabiaozhanbi || 0
                            , successstate: ''
                            , dabiaostate: '未达标'
                        };
                        $("#divweiheyan").append(objopenverify.renderTemp(objopenverify.htmlmodel, objhy));
                    }
                }
                $("#hadcheck").html(arrhj.length);//
                $("#allyey").html(arryid.length);//
                $("#spweiheyan").html(arryid.length - arrhj.length);//

                $("#bhywdb").html((objdwhynum['hywdb'] || 0));//核验未达标
                $("#bhydb").html((objdwhynum['hydb'] || 0));//核验已达标
                $("#bzcwdb").html((objdwhynum['zcwdb'] || 0));//核验未达标
                $("#bzcdb").html((objdwhynum['zcdb'] || 0));//核验已达标
                // $("#spweidabiao").html(arrweidabiao.length);//
                // $("#spdabiao").html(arrdabiao.length);//

                $("#bdabiao").html(arrdabiao.length);
                $("#bweidabiao").html(arrweidabiao.length);
                $("#bweiheyan").html(arryid.length - arrhj.length);
                objopenverify.initStatisticsChart(arrhj.length, $(".wrapouter"));
            }
        }, ["openverify.data", objdata.arryeyid.length > 0 ? objdata.arryeyid.join(",") : 0]);
    },
    //HTML模版
    htmlmodel: '<div class="state-list" yid="{{yid}}" areaname="{{areaname}}">\
                <h5>{{yeyname}}</h5>\
                <div class="{{successstate}} weui-flex" style="align-items: center;justify-content: space-between;padding: 0px 0 4px 0;">\
                    <div class="weui-flex__item" style="margin-right: 20px;">\
                        <p style="font-size: 12px;color: #999999;text-align: right;">达标项：<span class="state-txt">{{dabiao}}</span>/{{all}}</p>\
                        <div class="progressbar_1" style="width: 100%;">\
                            <div class="bar" style="width: {{dabiaozhanbi}}%;"></div>\
                        </div></div>\
                    <a class="btn-state weui-flex_center">{{dabiaostate}}<i class="iconfont icon_arrowcir-right weui-flex" style="height: 12px;margin-left: 5px;line-height: initial;"></i></a>\
                </div></div>',

    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp: function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg,function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    },

    /**
     * 初始化统计图表
     */
    initStatisticsChart: function (num, $par) {
        var stop = objdata.arryeyid.length == 0 ? 0 : (num / objdata.arryeyid.length * 100).toFixed(1);
        stop = parseFloat(getFloatValue(stop));
        if(stop > 50) {
            $par.find('.circle').addClass('clip-auto');
            $par.find('.right').removeClass('wth0');
        }else{
            $par.find('.right').addClass('wth0');
            $par.find('.circle').removeClass('clip-auto');
        }
        $par.find('.left').css("-webkit-transform","rotate("+((18/5) * stop) + "deg)");
        $par.find('.num>b').text(stop);
        if(stop == 0){
            $par.find('.left').css("border-color","#eee");
            $par.find('.right').css("border-color","#eee");
        }else{
            $par.find('.left').css("border-color","");
            $par.find('.right').css("border-color","");
        }
    }
};

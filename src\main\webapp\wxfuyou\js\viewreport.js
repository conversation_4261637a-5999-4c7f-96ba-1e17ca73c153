require.config({
    paths: {
        jquery: '../../sys/jquery',
        system: '../../sys/system',
        moment: '../../wxcommon/moment.min',
        common: '../../wxcommon/common',
        commonwx: '../../wxcommon/commonwx',
        commonhome: '../../wxcommon/commonhome',
        layer: '../../plugin/layer_mobile/layer',
    },
    waitSeconds: 0
});
var objdata = {};
require(['jquery', 'system', 'moment', 'common', 'commonwx', 'commonhome', 'layer'], function (s, moment, c, wxtool) {
    if (Arg("from")) {
        return;
    }
    //初始化微信 注册微信
    window.wxtool = wxtool;
    var checkid = Arg("id");
    objdata.cguid = Arg("cguid");
    $.sm(function (res, err) {
        var objperson = res[0];//个人信息
        $("#checkorganization").html(objperson.checkorganization);
        var remarksInfo = "备注：" + objperson.remarks;
        $("#div_remarks").html(remarksInfo);
        var objimg = {};
        // 汇总结果图片
        //objperson.summary = "{\"10111211\":{\"arrpic\":[\"bodycheck/check/DN8CnQdF5H1548676286380.png\"],\"isyc\":0}}";
        if (objperson.summary) {
            var summaraykey = "10111211";
            var summaraydata = JSON.parse(objperson.summary)[summaraykey];
            var summmaryarr = summaraydata.arrpic;
            var summaryhtml = '<div><table style="width:100%">'
                + '<tr' + (summaraydata.isyc && summaraydata.isyc == '1' ? ' style="color: #ff5151;"' : '') + '>\
			            <td>体检项目汇总结果图片</td>\
			            <td colspan="3" class="hasimage" data-key="' + summaraykey + '" style="text-align:right"><i class="iconfont icon_pic"></i>' + summmaryarr.length + '张</td>\
			        </tr></table></div>';
            $("#reportsummary").show().html(summaryhtml);
            var imgshtml = [];
            var arrimgs = summmaryarr
            for (var i = 0; i < arrimgs.length; i++) {
                imgshtml.push('<img src="' + (ossPrefix + arrimgs[i]) + '" alt="" style="width: 100%;">');
            }
            $("#checkimgs").html(imgshtml.join(''));
            objimg[summaraykey] = summmaryarr;
        }
        var arrre = res[1];//体检结果
        var strids = "";//解析得到所有指标 查询 名称和范围
        var firstflag = true;
        for (var i = 0; i < arrre.length; i++) {
            strids += (firstflag ? "" : ",") + arrre[i].tb_checkitems_id;
            firstflag = false;
            if(arrre[i].iscardpublic==1){
                $("#myhealthcard").show();
            }
        }
        /*设置的对象*/
        var objitem = {};
//        var objimg = {};
        $.sm(function (arritem, err) {
            //循环设置
            for (var i = 0; i < arritem.length; i++) {
                objitem[arritem[i].id] = arritem[i];
            }
            var strreporthtml = '';
            var abnormalnum = 0;
            var strhtml = '';
            //循环体检项
            for (var i = 0; i < arrre.length; i++) {
                if (arrre[i].isabnormal) {
                    abnormalnum += 1;
                    strreporthtml +=
                        '<div class="check-con">\
                            <p><i class="iconfont icon_star"></i>' + arrre[i].itemname + '：</p>\
                            <p>' + arrre[i].remark + '</p>\
                        </div>';
                }
                var obj = {};
                if (arrre[i].checkresult) {
                    obj = JSON.parse(arrre[i].checkresult);
                }
                var onehtml = '';
                var hasint = false;//是否有整数
                var isyc = false;
                //循环体检指标结果
                for (var key in obj) {
                    var arrflag = key.split("_");
                    var oneid = arrflag[0];
                    var intype = arrflag[1];
                    var objoneitem = objitem[oneid];
                    if (!objoneitem) continue;
                    var objvalue = obj[key];
//                    console.log(objoneitem);
                    isyc = isyc || (objvalue.isyc && objvalue.isyc == '1');
                    if (intype == 1) {//图片
                        var arrpic = objvalue.arrpic;
                        onehtml +=
                            '<tr' + (objvalue.isyc && objvalue.isyc == '1' ? ' style="color: #ff5151;"' : '') + '>\
                                <td>' + objoneitem.itemname + '</td>\
                                <td colspan="3" class="hasimage" data-key="' + key + '"><i class="iconfont icon_pic"></i>' + arrpic.length + '张</td>\
                            </tr>';
                        objimg[key] = arrpic;
                    } else if (intype == 2) {//数值
                        hasint = true;
                        var objset = JSON.parse(objoneitem.resultset);
                        //objset "{"unit":"cm","isrange":"1","rangestart":"80","rangeend":"190","isresultpj":"1","isequal":"1","ltrange":"太矮了","equalrange":"标准身高","gtrange":"太高了了"}"
                        onehtml +=
                            '<tr' + (objvalue.isyc && objvalue.isyc == '1' ? ' style="color: #ff5151;"' : '') + '>\
                                <td>' + objoneitem.itemname + '</td>\
                                <td>' + objvalue.value + '</td>\
                                <td>' + objset.unit + '</td>\
                                <td>' + (objset.isrange ? (objset.rangestart + '-' + objset.rangeend) : "") + '</td>\
                            </tr>';
                    } else if (intype == 3) {//选项
                        var strre = "";
                        var firstflag = true;
                        var objvalue = objvalue.objvalue;
                        for (var vk in objvalue) {
                            strre += (firstflag ? "" : ",") + vk;
                            firstflag = false;
                        }
                        onehtml +=
                            '<tr' + (objvalue.isyc && objvalue.isyc == '1' ? ' style="color: #ff5151;"' : '') + '>\
                                <td>' + objoneitem.itemname + '</td>\
                                <td colspan="3">' + strre + '</td>\
                            </tr>';
                    } else if (intype == 4 || intype == 5) {//文字输入
                        onehtml +=
                            '<tr' + (objvalue.isyc && objvalue.isyc == '1' ? ' style="color: #ff5151;"' : '') + '>\
                                <td>' + objoneitem.itemname + '</td>\
                                <td colspan="3">' + objvalue.value + '</td>\
                            </tr>';
                    } else {
                        onehtml +=
                            '<tr' + (objvalue.isyc && objvalue.isyc == '1' ? ' style="color: #ff5151;"' : '') + '>\
                                <td>' + objoneitem.itemname + '</td>\
                                <td colspan="3">' + objvalue.value + '</td>\
                            </tr>';
                    }
                }
                strhtml +=
                    '<div class="report-obj">\
                        <div class="report-obj-top"><span>' + arrre[i].itemname + '</span>' + (isyc ? '<span class="weui-flex" style="align-items: center;color: #ff0000;">有异常情况</span>' : '') + '</div>\
						<table class="define-table phy-table">\
							<thead>\
							<tr>\
								' + (hasint ? '<td>项目</td><td>结果</td><td>单位</td><td>参考范围</td>' : '<td>项目</td><td colspan="3">结果</td>') + '\
							</tr>\
							</thead>\
							<tbody>\
							' + onehtml + '\
							<tr style="color: #333333;">\
								<td>体检结果</td>\
								<td colspan="3" style="font-weight: bold;color: #00c87f;">' + arrre[i].remark + '</td>\
							</tr>\
							</tbody>\
						</table>\
						<div style="text-align: right;padding: 0 10px 10px 0;font-size: 15px;">检查医师：' + arrre[i].checkuser + '</div>\
					</div>';
            }
            //体检报告
            $("#abnormalnum").text(abnormalnum);//异常项数目
            if (abnormalnum) {
                $("#reportdata").html(strreporthtml).show();
            } else {
                $("#reportnodata").show();
            }
            //体检结果详情
            $("#divdata").html(strhtml);
            var imgkey = $(".hasimage").eq(0).data("key");
            if (imgkey) {
                var arrimg = objimg[imgkey];
                if (arrimg.length > 0) {
                    $(".phyresult-usual p").html("您可查看上传的图片中的体检结果哦~");
                }
            }
            $(".divdata").on('click', '.hasimage', function (re, err) {
                var key = $(this).data("key");
                var arrimg = objimg[key];
                if (arrimg.length == 0) {
                    return;
                }
                wxtool.ready(function (issucc, wx) {
                    if (!issucc) return;
                    var arr = [];
                    for (var i = 0; i < arrimg.length; i++) {
                        console.log(wxtool.osshost + "/" + arrimg[i]);
                        arr.push(wxtool.osshost + "/" + arrimg[i]);
                    }
                    wx.previewImage({
                        current: arr[0], // 当前显示图片的http链接
                        urls: arr // 需要预览的图片http链接列表
                    });
                });
            });
        }, ["xcx.cpmch.viewpackagesonitem", strids], "f", objdata.cguid, {rpc: 'fuyou'});
        //xcx.cpmch.viewcheckorgrecord 替换查询详情
    }, [["xcx.cpmch.viewcheckorg", checkid], ["xcx.cpmch.checkitemresult.all", checkid]], "f", objdata.cguid, {rpc: 'fuyou'});

    $("#myhealthcard").click(function () {
        var checkid = Arg("id");
        // 小程序跳转方法
        wx.miniProgram.navigateTo({
            url: '/pages/mine/healthcarddetail?perid=' + checkid,       // 指定跳转至小程序页面的路径
            success: (res) => {
                console.log(res);   // 页面跳转成功的回调函数
            },
            fail: (err) => {
                console.log(err);   // 页面跳转失败的回调函数
            }
        });
    });
});
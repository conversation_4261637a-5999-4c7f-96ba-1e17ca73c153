<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title></title>
    <link type="text/css" rel="stylesheet" href="../styles/reset.css" />
    <link type="text/css" rel="stylesheet" href="css/style.css" />
    <link type="text/css" rel="stylesheet" href="css/wechat.css" />
    <link type="text/css" rel="stylesheet" href="../plugin/iscroll/css/iscroll.css" />
    <link type="text/css" rel="stylesheet" href="../plugin/layer_mobile/layer_mobile.min.css" />
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <script type="text/javascript" src="js/scrollclass.js"></script>
    <script type="text/javascript">
	    new function() {
	        var _self = this;
	        _self.width = 640; //设置默认最大宽度
	        _self.fontSize = 100; //默认字体大小
	        _self.widthProportion = function() {
	            var p = (document.body && document.body.clientWidth || document.getElementsByTagName("html")[0].offsetWidth) / _self.width;
	            return p > 1 ? 1 : p < 0.5 ? 0.5 : p;
	        };
	        _self.changePage = function() {
	            document.getElementsByTagName("html")[0].setAttribute("style", "font-size:" + _self.widthProportion() * _self.fontSize + "px !important");
	        }
	        _self.changePage();
	        window.addEventListener('resize', function() {
	            _self.changePage();
	        }, false);
	    };
    </script>
</head>
<body>
   	<div id="wrapper" class="wrapper">
        <div id="scroller" class="scroller">
            <div id="pullDown" class="pullDown">
                <span class="pullDownIcon"></span><span class="pullDownLabel">下拉刷新</span>
            </div>
            <div id="thelist" class="pd15 talk">
            </div>
            <div id="pullUp" class="pullUp">
                <span class="pullUpIcon"></span><span class="pullUpLabel">上拉加载</span>
            </div>
        </div>
    </div>
    <script data-main="--iscrolltest" src="http://ptlib.tb-n.com/lib/sys/require.min.js"></script>
</body>

</html>

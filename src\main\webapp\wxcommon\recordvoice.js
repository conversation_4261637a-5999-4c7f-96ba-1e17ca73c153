function initRecordVoice(_obj, callback) {
    var strhtml =
        '<section class="recordvoice-con" id="divrecordvoicepage">\
            <a id="btncloserecordvoicepage">关闭</a>\
            <section class="send-state weui-flex" id="voicestate_send" style="display: none;">\
                <img src="../wxcommon/images/voicerecord.gif" style="width: 180px;">\
            </section>\
            <section class="send-state weui-flex" id="voicestate_cancel" style="display: none;">\
                <img src="images/round_img.png" style="width: 53px;">\
                <p style="background: #ffffff;color: #ff6b6b;font-size: 12px;margin-top: 10px;padding: 0 3px;">松开手指，取消发送</p>\
            </section>\
            <div class="panel-opr weui-flex" style="position: absolute;bottom:0;width: 100%" id="bottom_voice">\
                <div class="weui-flex" style="flex-direction: column;justify-content: center;align-items: center;margin: 0 10px;" id="btnrecordvoice">\
                    <i class="iconfont icon_sound bigsound weui-flex" style="width: 87px;height: 87px;border-radius: 50%;border: 2px solid #eaeaea;"></i>\
                    <p style="color: #797979;font-size: 12px;margin-top: 5px;">长按录音</p>\
                </div>\
            </div>\
        </section>';
    _obj.click(function () {
        //验证录音权限授权
        new Promise(function (resolve) {
            if (!wx) {
                return parent.layer.msg("没有微信api");
            }
            wxtool.ready(function (succ, err) {
                if (!succ) {
                    parent.layer.msg(err);
                }
            })
            if (!objdata.recordauth || objdata.recordauth == 0 || objdata.recordauth == -1) {
                wx.startRecord({
                    success: function () {
                        wx.stopRecord();
                        resolve();
                        objdata.recordauth = 1;
                    },
                    fail: function () {
                        parent.layer.msg("没有录音权限");
                    },
                    cancel: function () {
                        parent.layer.msg("没有录音权限");
                    }
                });
            } else {
                resolve();
            }
        }).then(function () {
            if ($("#divrecordvoicepage").length) {
                $("#divrecordvoicepage").show();
            } else {
                var _dom = $(strhtml);
                _dom.find('#btncloserecordvoicepage').click(function () {
                    _dom.hide();
                })
                $("body").append(_dom);
                initRecordVoiceEvent(callback);
            }
        })
    });
}

function initRecordVoiceEvent(callback) {
    //点击语音
    var starttime = null;
    var startY = 0;
    var moveY = 0;
    var recordstate = '';
    $('#btnrecordvoice').on('touchstart', function (event) {
        //status = '打开语音';
        event.preventDefault();
        starttime = null;
        if (recordstate == 'ing') return;
        new Promise(function (dostart) {
            if (wx) {
                wx.startRecord();
                dostart();
                return false;
            } else {
                dostart();
            }
        }).then(function () {
            starttime = new Date();
            var touch = event.originalEvent.targetTouches[0];
            startY = touch.pageY;
            $('#voicestate_send').show();
            recordstate = 'ing';
        })
        return false;
    }).on('touchmove', function (event) {
        var touch = event.originalEvent.targetTouches[0];
        moveY = startY - touch.pageY;
        if (moveY > 60) {
            if (recordstate == 'ing') {
                recordstate = 'tocancel';
                $('#voicestate_send').hide();
                $('#voicestate_cancel').show();
            }
        } else {
            if (recordstate == 'tocancel') {
                recordstate = 'ing';
                $('#voicestate_cancel').hide();
                $('#voicestate_send').show();
            }
        }
    }).on('touchend', function (event) {
        $('#voicestate_cancel').hide();
        $('#voicestate_send').hide();
        if (recordstate != 'ing') return;
        var endtime = new Date();
        var dis = endtime - starttime;
        var isok = true;
        if (dis < 1000) {
            parent.layer.msg('时间太短了');
            isok = false;
        } else {
            if (recordstate != 'tocancel') {

            } else {
                layer.msg('取消发送');
                isok = false;
            }
        }
        if (!isok) {
            recordstate = '';
            return;
        }
        new Promise(function (voiceAfterRecord) {
            if (wx) {
                wx.stopRecord({
                    success: function (res) {
                        if (res.errMsg.indexOf('ok') > 0) {
                            if (isok) {
                                voiceAfterRecord({
                                    localId: res.localId,
                                    dis: dis
                                });
                            }
                        }
                    }
                });
            } else {
                voiceAfterRecord({
                    dis: dis
                });
            }
        }).then(function (obj) {
            recordstate = '';
            objdata.seconds = obj.dis ? Math.ceil(obj.dis / 1000) : 1;
            objdata.localId = obj.localId;
            if (obj.localId) {
                $("#divrecordvoicepage").hide();
                if (objdata.seconds > 0) {
                    callback && callback(objdata.localId, objdata.seconds);
                }
            }
        })
    });
}
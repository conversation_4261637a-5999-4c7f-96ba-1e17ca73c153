﻿require.config({
    baseUrl:'../',//指定js文件的基路径
    paths : {
        layer : "./plugin/layer/layer",
        aboutright : 'wxchdc/js/aboutright'
    },
    waitSeconds: 0
});
var objdata ={
    sid : Arg("sid"),
    seid : Arg("seid"),
    qrcode : Arg("qrcode")
};
require(["layer","aboutright"],function (layer,aboutright) {
    var askT = Arg("time");
    aboutright.then(function (data) {
        if (!data) {
            window.location.href = "./bind.html?v=" + Arg("v");
            return;
        }
        if(data){
            if(objdata.qrcode){
                //    扫描二维码登陆
                $("#qr-auto-text").show().next().hide();
                $("#qr-auto-btn").show().next().hide();
            }else{
                //发送授权登陆
                checkPcLogin();
            }
            initEvent();
        }
    });
//    检测pc端是否已经登陆
    function checkPcLogin() {
        if(!(objdata.sid && objdata.seid)){
        //    菜单页直接打开授权登陆页，查看本用户是否pc端已经登陆到login页面
            $.sm(function (re, err) {
                objdata.sid = re;
                if(re == 1 || re == 2){
                //    未检测到PC端登陆到login页面
                    $("#qr-no-pc-login").show().siblings().hide();
                } else {
                    $("#qr-auto-text").show().next().hide();
                    $("#qr-auto-btn").show().next().hide();
                }
            },["wx.auth.checkUserLogin"]);
            return;
        }
        if((+new Date() - askT) > (30*60*1000)){
            $("#qr-overtime").show().nextAll().hide();
            return;
        }
        $.sm(function (re, err) {
            if(re && !err){
                if(re == 2){
                    //已经授权过登陆
                    $("#qr-sure").show().siblings().hide();
                    return;
                }else if(re == 3){
                //    已操作pc下线
                    $("#qr-sure").hide().siblings().hide();
                    layer.msg("已经操作电脑端下线，<br>请电脑端重新发起授权");
                    return;
                }
            }else{
                layer.msg("授权操作失败");
            }
        },["wx.checkPcLogin",objdata.sid, objdata.seid]);
    }
    function initEvent() {
        //授权登陆
        $("#qr-agree-btn").click(function (ev) {
            $("#qr-loading").show().siblings().hide();
            $.sm(function(re,err,obj){
                if(re){
                    //登陆成功
                    $("#qr-sure").show().siblings().hide();
                } else {

                }
            },["wx.agreePcLogin",objdata.sid, objdata.seid]);
        });
        //操作pc下线
        $("#qr-logout").click(function (ev) {
            layer.open({
                title : "提示",
                content : "确认操作电脑端下线吗？",
                btn : ["确定","取消"],
                style : "width:100%; height:50px;",
                // btnAlign : "c",
                success : function (ele) {
                    $(ele).find(".layermbtn span").css({
                        width : "60px"
                    }).eq(0).css({
                        marginRight : "30px"
                    });
                },
                yes : function (index) {
                    $.sm(function (re, err) {
                        if(re && !err){
                            layer.msg("电脑端已下线");
                            objdata.qrcode = "";
                            $("#out-container").children().hide();
                            $("#qr-auto-text").show().next().hide().parent().show();
                            $("#qr-auto-btn").show();
                        }else{
                            layer.msg("操作失败，稍后再试");
                        }
                    },["wx.orderPcLogout",objdata.sid]);
                    return false;
                },
                no : function (index) {
                    layer.close(index);
                }
            });
        });
    //    下线操作后一键授权登陆
        $("#qr-auto-btn").click(function (ev) {
            $("#qr-loading").show().siblings().hide();
            $.sm(function (re, err) {
                if(!err){
                    //登陆成功
                    $("#qr-sure").show().siblings().hide();
                }
            },["wx.autoPcLogin",objdata.qrcode,objdata.sid]);
        });
    }
});

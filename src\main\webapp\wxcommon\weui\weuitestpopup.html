<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <title>测试</title>
    <link rel="stylesheet" type="text/css" href="weui.min.css">
    <link rel="stylesheet" type="text/css" href="jquery-weui.css">
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <style>
        body {
            background: #f3f4f6;
        }
        #selSex {
            color: #dddddd;
        }
        #selSex .current {
            color: #000000;
        }
        .weui-picker-calendar {
            height: auto;
        }
    </style>
</head>
<body>
    <section class="addbodydiv">
        <div id="divpop1" class="weui-popup__container" style="height: 500px">
            <div class="weui-popup__overlay"></div>
            <div class="weui-popup__modal">
                <div>
                    a<br>a<br>a<br>a<br>a<br>a<br>a<br>a<br>a<br>
                    <input type="button" value="关闭" id="btnclose">
                </div>
            </div>
        </div>
        <script>
            $(function () {
                var _divpop1 = $("#divpop1")
                _divpop1.find('.weui-popup__modal').on('close',function () {
                    debugger
                })
                _divpop1.popup();
                $("#btnclose").click(function () {
                    $.closePopup()
                })
            })
        </script>
    </section>
    <script src="jquery-weui.js?v=44"></script>
</body>
</html>

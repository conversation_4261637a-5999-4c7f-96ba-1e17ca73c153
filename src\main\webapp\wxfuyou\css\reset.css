﻿body, dl, dd, h1, h2, h3, h4, h5, h6, p, form{margin:0;font-weight: normal;}   
body{font-family:"microsoft yahei";font-size:16px;}
ol,ul{margin:0; padding:0;list-style: none;}
*{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	-webkit-box-sizing:border-box;
	}
body{font-family:arial, sans-serif;}
textarea{resize:none;}
input,textarea{line-height: normal;font-family:"microsoft yahei";}
/* font */
.font12{ font-size:12px;}
.font14{ font-size:14px;}
.font16{ font-size:1em;}
.font18{ font-size:1.125em;}
.font20{ font-size:1.25em;}
.font22{font-size:1.375em;}
.font24{font-size:1.5em;}
.font30{font-size:1.875em;}
h1{font-size:2.25em;}
h2{font-size:1.5em;}
h3{font-size:1.125em;}
h4{font-size:1em;}
h5{font-size:14px;}
h6{font-size:12px;}
ol, ul{list-style: none;}
a{text-decoration: none;}
a:visited{text-decoration: none;}
a:active, a:active, a:focus{text-decoration: none;}
.clearfix:after {
    clear: both;
    content: ".";
    display: block;
    height: 0;
    visibility: hidden;
}
.clearfix {
    display: inline-block;
}
* html .clearfix {
    height: 1%;
}
.clearfix {
    display: block;
}
.fl {float: left;}
.fr {float: right;}
.pr{ position:relative;}
.pa{ position:absolute;}
.pf{ position:fixed;}
.block{ display:block;}
.bold{ font-weight:bold;}
.text2em{ text-indent:2em;}
i{font-style:normal;}
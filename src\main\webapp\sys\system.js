﻿window.isdebug = 0;
window.lan = 'zh';
window.bucketName = 'btkjtest';
window.ossPrefix = 'http://tbfile.oss-cn-beijing.aliyuncs.com/';
window.tbptossPrefix = 'https://osstbfile.tb-n.com/';
window.islocal = false;//是否上传到服务器
if(!window.islocal){
    window.ossPrefix = '//' + bucketName + '.oss-cn-beijing.aliyuncs.com/';
} else {
    window.ossPrefix = location.origin + "/fuyoumini/";
    if(location.origin.indexOf("localhost") < 0){
        window.ossPrefix = location.origin + "/";
    }
}
window.uploadPrefix = 'userfiles/chengdu/fuyou';
window.$spChar = "φψ";//φψ
window.notloginalert = false;
if (window.layui) {
    layui.define(function (exports) { // 提示：模块也可以依赖其它模块，如：layui.define('layer', callback);
        var $ = layui.jquery;
        var obj = {};
        initSystemMsg($);
        // 输出test接口
        exports('system', obj);
    });
} else {
    (function (factory) {
        if (typeof define === "function" && define.amd) {
            // AMD. Register as an anonymous module.
            define(["jquery"], factory);
        } else {
            // Browser globals
            factory(jQuery);
        }
    }(function ($) {
        initSystemMsg($);
    }));
}
function initSystemMsg($) {
    $.projectpath = "/fuyoumini";
    if (!window.location.origin) {
        window.location.origin = window.location.protocol + "//" + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
    }
    $.baseurl = $.fn.baseurl = location.origin;
    $.smurl = $.baseurl + $.projectpath + "/Enter";
    $.encodeArr = function (arr) {//数组转码
        for (var j = 0; j < arr.length; j++) {
            if (typeof arr[j] == "string") {
                arr[j] = $.EncodeSpChar(arr[j]);
            }
        }
        return arr;
    };
    //cb 回调 arr 参数 t 连接类型 did 数据库标识 pobj 扩展参数 timeout 超时时间（待废除）
    $.getSmParam = function (arr, t, did, pobj, timeout, trans) {
        var strp = "";
        var ismul = 0;
        if (typeof arr[0] == "object") {
            ismul = 1;
            var arr2 = [];
            for (var i = 0; i < arr.length; i++) {
                arr2.push($.encodeArr(arr[i]).join('%15'));
            }
            strp = arr2.join('%18');
        } else {
            strp = $.encodeArr(arr).join('%15');
        }
        var res = {
            "trans": (pobj && pobj.trans) || trans || "",
            "isspChar": 1,
            "ismul": ismul,
            "arr": strp,
            "t": t || "",
            "did": did || "",
            "lan": window.lan
        };
        if (pobj && pobj.rpc) {
            res.rpc = pobj.rpc;
        }
        if (pobj && pobj.rpcurl) {
            res.rpcurl = pobj.rpcurl;
        }
        if (pobj && pobj.isPage) {
            res.isPage = pobj.isPage;
        }
        return res;
    };
    $.getSmStr = function (arr, t, did, pobj, timeout, trans) {
        var obj = $.getSmParam(arr, t, did, pobj, timeout, trans);
        var str = "";
        for (var o in obj) {
            str += o + "=" + obj[o] + "&";
        }
        str = str.substring(0, str.length - 1);
        return str;
    };
    $.getAuthorization = function () {
        return "Bearer " + localStorage.getItem("token");
    };
    $.getAuthParam = function () {
        return "&Authorization=" + $.getAuthorization();
    };
    //请求地址
    $.geturl = function(route, action){
        route = route || $.projectpath;
        action = action || 'Enter';
        return $.baseurl + (!route.startsWith("/") ? "/" : "") + route + "/" + action;
    };
    //layUI请求地址
    $.getLayUrl = function(route){
        return $.geturl(route, 'LayEnter');
    };
    /***
     * 发送whoami消息
     * @type {$.sm}
     */
    $.sm = $.fn.sm = function (cb, arr, t, did, pobj, timeout, trans) {
        if (typeof arr == 'function') {
            var arrtemp = cb;
            cb = arr;
            arr = arrtemp;
        }
        if(!pobj){
            pobj = {};
        }
        var objp = $.getSmParam(arr, t, did, pobj, timeout, trans);
        var ajaxobj = {
            type: "post",
            url: $.geturl(pobj.route, pobj.action), //$.smurl,
            //timeout : timeout || 20000,
            data: objp,
            beforeSend: function (request) {
                request.setRequestHeader("Authorization", $.getAuthorization());
            },
            success: function (obj) {
                if (obj) {
                    try {
                        if (!obj.code) {//兼容老版本
                            obj = JSON.parse(obj);
                        } else {
                            obj.data = JSON.parse(obj.data);
                        }
                        $.decodeRN(obj, pobj && pobj.escape);
                        // $.decodeObject(obj);
                    } catch (e) {
                        console.log(e);
                        console.log(obj);
                        // return cb(null, "系统错误，请联系管理员");
                    }
                    //新版返回
                    if (obj.code) {
                        if (obj.code === 200) {
                            return cb(obj.data, null, obj.data);
                        } else {
                            console.log(objp.arr + "   " + obj.msg);
                            return cb(null, obj.msg);
                        }
                    }
                    //老版返回
                    if ($.isArray(obj)) {
                        cb(obj, null);
                    } else if (typeof obj == "object") {
                        if (obj.error && obj.error === "nologin") {
                            if (!window.notloginalert) {
                                window.notloginalert = true;
                                alert("登录已过期，请重新登录！");
                            }
                            top.location.href = location.protocol + "//" + location.host;
                        } else {
                            if (obj.error) {
                                try {
                                    obj.error = JSON.parse(obj.error);
                                } catch (e) {
                                    // TODO: handle exception
                                }
                                console.log(objp.arr + "   " + obj.error);
                            }
                            cb(obj.re || obj, obj.error, obj);
                        }
                    } else {
                        cb(obj);
                    }
                } else {
                    cb(null, "结果为空");
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                console.log("status:" + XMLHttpRequest.status + "readyState:" + XMLHttpRequest.readyState + "textStatus:" + textStatus);
                if (XMLHttpRequest.status != 0) //未发送的不提示
                    cb(null, "ajax error");
            },
            complete: function (XMLHttpRequest, status) { //请求完成后最终执行参数
                if (status == 'timeout') {
                    //alert('请求超时，请稍后再试！');
                }
            },
            async: true
        };
        if (pobj)
            ajaxobj = $.extend(ajaxobj, pobj);
        if (timeout)
            ajaxobj = $.extend(ajaxobj, {timeout: timeout});
        $.ajax(ajaxobj);
    };
    /**
     * 请求controller的方法
     * @param {Object} cb 调用结束后的回调
     * @param {Object} param 请求的参数 json对象格式
     * @param {Object} pobj 必填，包含route(路由，既微服务名称)、action(controller路径)，其他key自定。 例如：{route:'bzn-xcx-tbpt', action: 'yey/list'}
     */
    $.smaction = $.fn.smaction = function(cb, param, pobj, timeout){
        if (typeof cb == 'object') {
            var arrtemp = cb;
            cb = param;
            param = arrtemp;
        }
        if(!pobj || !pobj.action){
            return cb(null, '路由不能为空');
        }
        var url = $.geturl(pobj.route, pobj.action);
        var ajaxobj = {
            type: pobj.method || "post",
            url: url,
            // dataType: "json",
            contentType: "application/json",
            //请求数据
            data: pobj.datastring ? JSON.stringify(param) : param,
            beforeSend: function (request) {
                request.setRequestHeader("Authorization", $.getAuthorization());
            },
            success: function (obj) {
                if (obj) {
                    try {
                        if (!obj.code) {//兼容老版本
                            obj = JSON.parse(obj);
                        } else {
                            obj.data = JSON.parse(obj.data);
                        }
                        // $.decodeRN(obj, pobj && pobj.escape);
                        // $.decodeObject(obj);
                    } catch (e) {
                        console.log(e);
                        console.log(obj);
                        // return cb(null, "系统错误，请联系管理员");
                    }
                    //新版返回
                    if (obj.code) {
                        if (obj.code === 200) {
                            return cb(obj.data, null, obj.data);
                        } else {
                            console.log(obj.msg);
                            return cb(null, obj.msg);
                        }
                    }
                    //老版返回
                    if ($.isArray(obj)) {
                        cb(obj, null);
                    } else if (typeof obj == "object") {
                        if (obj.error && obj.error === "nologin") {
                            if (!window.notloginalert) {
                                window.notloginalert = true;
                                alert("登录已过期，请重新登录！");
                            }
                            top.location.href = location.protocol + "//" + location.host;
                        } else {
                            if (obj.error) {
                                try {
                                    obj.error = JSON.parse(obj.error);
                                } catch (e) {
                                    // TODO: handle exception
                                }
                                // console.log(objp.arr + "   " + obj.error);
                            }
                            cb(obj.re || obj, obj.error, obj);
                        }
                    } else {
                        cb(obj);
                    }
                } else {
                    cb(null, "结果为空");
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                console.log("status:" + XMLHttpRequest.status + "readyState:" + XMLHttpRequest.readyState + "textStatus:" + textStatus);
                if (XMLHttpRequest.status != 0) //未发送的不提示
                    cb(null, "ajax error");
            },
            complete: function (XMLHttpRequest, status) { //请求完成后最终执行参数
                if (status == 'timeout') {
                    //alert('请求超时，请稍后再试！');
                }
            },
            async: true
        };
        if (pobj)
            ajaxobj = $.extend(ajaxobj, pobj);
        if (timeout)
            ajaxobj = $.extend(ajaxobj, {timeout: timeout});
        $.ajax(ajaxobj);
    },
        /**
         * 封装where参数
         */
        $.msgwhere = function (obj1, obj2) {
            var obj = {};
            if (obj1) {
                obj = $.extend(obj, obj1);
            }
            if (obj2) {
                obj = $.extend(obj, obj2);
            }
            return JSON.stringify({
                msg_where: obj
            })
        };
    /**
     * 封装arrwhere参数
     */
    $.msgArrwhere = function (arr) {
        return JSON.stringify({
            msg_where: arr
        })
    };
    /**
     * 封装p标签arr参数格式
     */
    $.msgpJoin = function (arr) {
        var arrnew = [];
        for (var i = 0; i < arr.length; i++) {
            arrnew.push([arr[i]]);
        }
        return arrnew;
    };

    //页面权限按钮显隐
    $.buttonPermShow = function () {
        let hasPerms = localStorage.getItem('permissions') || "";
        if (hasPerms) {
            let permsArray = hasPerms.split(',');
            $("[lay-perm]").each(function () {
                let _this = $(this);
                let _perm = _this.attr("lay-perm");
                if (_perm) {
                    if (permsArray.includes(_perm)) {
                        _this.show();
                    } else {
                        _this.remove();
                    }
                } else {
                    _this.show();
                    _this.removeAttr("lay-perm");
                }
            });
        }
    };
    (function ($) {
        $.buttonPermShow();
    })($);
    /**
     * 替换换行符
     * 避免换行符JSON.parse: bad control character in string literal
     */
    $.replFeed = function (text) {
        return text.replace(/[\n\r\t]/g, '').replace(/\\\+/g, '+');
    };
    //监控F12事件
    (function ($) {
        document.onkeydown = function (event) {
            var e = event || window.event || arguments.callee.caller.arguments[0];
            if (e && e.keyCode == 123) { // 按 F12
                $.sm(function (re, err) {
                    if (err) {
                    } else {
                        console.log("tongbang is the first;");
                    }
                }, ["w_keyMonitor.add", location.href, $.cookie && $.cookie("s") || ""]);
            }
        };
    })($);
    /*
     * 作者 郭玉峰 摘要 基于jquery的输入 时间 2015-9-10
     */
    (function ($) {
        $.fn.input = function (set) {
            var defset = {
                startlet: 0,
                ms: 300,
                cb: null
            }, objdata = {}, _this = $(this), timer;
            set = $.extend(defset, set);
            _this.bind('input propertychange', function () {
                var v = _this.val();
                if (v.length) {
                    if (v.length > set.startlet) {
                        clearTimeout(timer);
                        timer = setTimeout(function () {
                            if (set.cb) {
                                set.cb(v, timer);
                            }
                        }, set.ms);
                    }
                } else {
                    set.cb(v, timer);
                }
            });
        }
    })($);
    //特殊字符验证
    $.regCharacter = function (val, isn) {
        if (isn == ",") {//去掉逗号验证
            return /[`~!@#$%^&*_+<>?:".\/\\;']/im.test(val);
        } else {
            return /[`~!@#$%^&*_+<>?:",.\/\\;']/im.test(val);
        }
    };
    //转义相关
    $.fn.encodeval = function () {
        return (this.length ? (($.nodeName(this[0], "input") || $.nodeName(this[0], "textarea")) ? $.EncodeSpChar($(this[0]).val()) : $(this[0]).val()) : null);
    };
    $.fn.encodehtml = function () {
        return (this.length ? $.EncodeSpChar(this[0].innerHTML) : null);
    };
    $.fn.encodetext = function () {
        return (this.length ? $.EncodeSpChar($(this).text()) : null);
    };
    $.fn.encodehtmlval = function () {
        return (this.length ? $.EncodeSpChar(this[0].value, true) : null);
    };
    /*
     功能：拼接字符串时 将'"进行html转义
     grid列表 拼接字符串，或其他拼接时使用
     */
    $.htmlencode = function (text, isrn) {
        var o = {"'": "&acute;", '"': "&quot;", '\\r\\n': "<br>", '\\n': "<br>", '\\t': "&nbsp;&nbsp;&nbsp;&nbsp;"};
        if (isrn) {
            return text.replace(/\'|\"|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
                return c == "\\n" && text.substring(d, d - 1) == "\\" ? "n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "r" : o[c]);
            });
        }
        return text.replace(/\'|\"|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
            return c == "\\'" || c == '\\"' ? o[c] : (c == "\\n" && text.substring(d, d - 1) == "\\" ? "\\n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "\\r" : ""));
        });
    };

    /**
     * 转义html，预防xss攻击
     * @param html
     * @returns {string|*}
     */
    $.fn.escapehtml = function (html) {
        return (this.length ? $(this[0]).html($.escape(html)) : null);
    };
    $.escape = function (html) {
        var exp = /[<"'>]|&(?=#[a-zA-Z0-9]+)/g;
        if (html === undefined || html === null) return '';

        html += '';
        if (!exp.test(html)) return html;

        return html.replace(/&(?!#?[a-zA-Z0-9]+;)/g, '&amp;')
            .replace(/</g, '&lt;').replace(/>/g, '&gt;')
            .replace(/'/g, '&#39;').replace(/"/g, '&quot;');
    };

    /**
     * 还原转义的 html
     * @param html
     * @returns {string}
     */
    $.fn.unescapehtml = function (html) {
        return (this.length ? $(this[0]).html($.unescape(html)) : null);
    };
    $.unescape = function (html) {
        if (html === undefined || html === null) html = '';
        html += '';

        return html.replace(/\&amp;/g, '&')
            .replace(/\&lt;/g, '<').replace(/\&gt;/g, '>')
            .replace(/\&#39;/g, '\'').replace(/\&quot;/g, '"');
    };

    /*
     功能：
     &acute;替换'
     &quot;替换"
     */
    $.dehtmlencode = function (text) {
        if (!text) return '';
        return text.replace(/&acute;/g, "'").replace(/&quot;/g, '"');
    };
    /*
     功能：替换掉文章中的特殊符号 前台html静态显示时使用
     明确是\r \n的,数据库存的是\\r\\n 则明文显示\r \n
     前台html静态显示 使用jQuery.htmldecode(v,1)则会处理\r\n情况换成<br>
     "&gt;":">",'&lt;':"<",
     &gt;|&lt;|
     c=="&gt;"||c=='&lt;'||
     */
    $.htmldecode = function (text, isrn) {
        if (!text) return '';
        var o = {'&amp;': "&", '\\r\\n': "<br>", '\\n': "<br>", '\\t': "&nbsp;&nbsp;&nbsp;&nbsp;"};
        if (isrn) {
            return text.replace(/&amp;|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
                return c == "\\n" && text.substring(d, d - 1) == "\\" ? "n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "r" : o[c]);
            });
        }
        return text.replace(/&amp;|\\r\\n|\\n|\\r|\\t/g, function (c, d) {
            return c == '&amp;' ? o[c] : (c == "\\n" && text.substring(d, d - 1) == "\\" ? "\\n" : (c == "\\r" && text.substring(d, d - 1) == "\\" ? "\\r" : ""));
            //	        return o[c];
        });
    };
    /*
     功能：根据ty对obj进行相应的html处理
     */
    $.htmlcodeObject = function (obj, ty) {
        if (typeof (obj) != "object") return obj;
        if (!ty) return;
        for (var key in obj) {
            var kv = obj[key];
            if (typeof (kv) == "string") {
                if (ty == 1) {
                    kv = $.htmlencode(kv);
                } else if (ty == 2) {//rn处理
                    kv = $.htmlencode(kv, 1);
                } else if (ty == 3) {
                    kv = $.htmldecode(kv);
                } else if (ty == 4) {//rn处理
                    kv = $.htmldecode(kv, 1);
                }
                kv = $.htmlimg(kv);
                obj[key] = kv;
            } else if (typeof (kv) == "object") $.htmlcodeObject(kv, ty);
        }
    };
    /*
     功能：将字符串\r\n处理成换行符
     文本区域textarea和编辑器 换行符\r\n的显示为编码后的特殊字符，即显示换行
     编辑器赋值时 使用jQuery.toCharCode(v)的返回值
     文本区域textarea 使用jQuery.toCharCode(v)的返回值或者直接使用$('#id').areaval(v);
     */
    $.toCharCode = function (str) {//debugger;
        var spc = {
            "\\\\\\\\r": "\\r",
            "\\\\\\\\n": "\\n",
            "\\\\\\\\t": "\\t",
            "\\\\\\\\": "\\",
            "\\\\r": String.fromCharCode(13),
            "\\\\n": String.fromCharCode(10),
            "\\\\t": String.fromCharCode(9)
        };
        var t = "";
        for (var i in spc) {
            if (typeof (spc[i]) == "string") t += i + "|";
        }
        t = t.substr(0, t.length - 1);
        var p = new RegExp(t, "gim");
        //	    var r=str.match(p);
        return str.replace(p, function (c) {
            return spc["\\\\" + c] || spc["\\" + c] || spc[c];
        });
    };
    $.SpecialChars = {
        "%": "%25",
        ",": "%2C",
        "'": "%27",
        '"': "%22",
        "\\+": "%2B",
        "\\r": "%0D",
        "\\n": "%0A",
        "\\t": "%09",
        "\\\\": "\\\\",
        "<": "%3C",
        ">": "%3E",
        "&": "%26"
    };
    $.DecodeSpChar = function (str) {
        var a = $.SpecialChars;
        if (!$.DeSpecialChars) {
            $.DeSpecialChars = {};
            for (var i in a) {
                if (typeof (a[i]) == "string") {
                    var v1 = a[i];
                    $.DeSpecialChars[v1] = i;
                }
            }
        }
        var b = $.DeSpecialChars;
        var t = "";
        for (var i in b) {
            if (typeof (b[i]) == "string") t += i + "|";
        }
        t = t.substr(0, t.length - 1);
        var p = new RegExp(t, "gim");
        return str.replace(p, function (c) {
            if (c == "\\r") return "\\r";
            if (c == "\\n") return "\\n";
            if (c == "%2B") return "+";
            return $.DeSpecialChars[c] || "";
        });
    };
    /**
     * 处理换行符
     * @param obj
     * @param isescape 是否处理<>,防xss
     * @returns {*}
     */
    $.decodeRN = function (obj, isescape) {
        if (typeof (obj) != "object") return obj;
        for (var key in obj) {
            var kv = obj[key];
            if (typeof (obj[key]) == "string") {
                obj[key] = $.toCharCode(kv);
                if (isescape) obj[key] = $.escape(obj[key]);
            }
            if (typeof (obj[key]) == "object") {
                $.decodeRN(obj[key], isescape);
            }
        }
    };
    $.decodeObject = function (obj) {
        if (typeof (obj) != "object") return obj;
        for (var key in obj) {
            var kv = obj[key];
            try {
                if (typeof (kv) == "string") {
                    obj[key] = decodeURIComponent(kv);
                    continue;
                }
            } catch (e) {
            }
            if (typeof (obj[key]) == "string") obj[key] = $.DecodeSpChar(kv);
            if (typeof (obj[key]) == "object") $.decodeObject(obj[key]);
        }
    };
    $.EncodeSpChar = function (str, h) {//debugger;'&acute;"&quot;明确是\r \n的数据库存的是\\r\\n
        var a = $.SpecialChars;
        var t = "";
        for (var i in a) {
            if (typeof (a[i]) == "string") t += i + "|";
        }
        t = t.substr(0, t.length - 1);
        if (h) {
            var o = {"'": "&acute;", '"': "&quot;"};
            str = str.replace(/\'|\"/g, function (c) {
                return o[c];
            });
        }
        var p = new RegExp(t, "gim");
        return str.replace(p, function (c) {
            //debugger;
            var r1 = $.SpecialChars["\\" + c] || $.SpecialChars[c];
            if (!r1) {
                //	            if(c=="\n"||c=="\r"||c=="\t")return "";
                if (c == "\n") return "%0A";
                if (c == "\\\n") return "\\\\%0A";
                if (c == "\r") return "%0D";
                if (c == "\t") return "%09";
            } else
                return r1;
        });
    };
    //多级iframe存在时，得到最外层的window
    $.getparent = function () {
        return top;
    };
    // 导出页面html(table)到Excel
    // strHtml:[可选] 要导出的html
    // 使用示例：$("#divtab").exportExcel("","");
    $.fn.exportExcel = function (eid, strHtml, type) {
        type = type ? type : 1;
        var obj = this.get(0),
            obj_id = $(this).attr("id");
        try {
            if (ExcellentExport) {
                var a = document.createElement("a");
                a.download = eid + ".xls";
                a.onclick = function () {
                    return ExcellentExport.excel(this, obj_id, eid);
                }
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                return;
            }
        } catch (err) {
            console.log("excel error！");
        }
        strHtml = (strHtml) ? strHtml : (obj.tagName == "TABLE" ? obj.outerHTML : obj.innerHTML);
        var htmlvalue = "";
        if (type == "1") {
            htmlvalue = "index.stexcel" + "%15" + $.EncodeSpChar(eid) + "%15" + $.EncodeSpChar(strHtml.replace(new RegExp('"', "g"), "'"));//.replace(new RegExp('"', "g"), "'")
        } else {
            htmlvalue = "common.tabletoexcel" + "%15" + type + "%15" + $.EncodeSpChar(obj.outerHTML.replace(new RegExp('&nbsp;', "g"), "").replace(new RegExp('"', "g"), $spChar)) + "%15" + "%15";//.replace(new RegExp('"', "g"), "'")
        }
        var cid = obj.id;
        if ($("#edivOutexportExcel" + cid)[0])
            $("div").remove("#edivOutexportExcel" + cid);
        var arrstr = [];
        arrstr.push('<div id="edivOutexportExcel' + cid + '" style="width: 0px;height: 0px;display:none;">');
        arrstr.push('<form id="form' + cid + '" action="' + $.smurl + '" accept-charset="UTF-8" method="post" enctype="application/x-www-form-urlencoded" target="formFrame' + cid + '">');
        arrstr.push('<input id="Password' + cid + '" name="arr" type="hidden" value="' + htmlvalue + '" style="display:none;" />');
        arrstr.push('</form>');
        arrstr.push('<iframe name="formFrame' + cid + '" id="formFrame' + cid + '" style="width: 0px;height: 0px;display:none;"></iframe>');
        arrstr.push('</div>');
        $(document.body).append(arrstr.join(''));
        $('#form' + cid)[0].submit();
    };
    /**
     * 生成excel对象
     * @type {{version, excel, csv}}
     */
    var ExcellentExport = function () {
        var e = function (e, t, n) {
            t = t || "", n = n || 512;
            var o = window.atob(e), r = [], c = void 0;
            for (c = 0; c < o.length; c += n) {
                var i = o.slice(c, c + n), l = new Array(i.length), a = void 0;
                for (a = 0; a < i.length; a += 1) l[a] = i.charCodeAt(a);
                var f = new window.Uint8Array(l);
                r.push(f)
            }
            return new window.Blob(r, {type: t})
        }, t = {excel: '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e</head><body>{table}</body></html>'}, n = ",", o = "\r\n", r = function (e) {
            return window.btoa(window.unescape(encodeURIComponent(e)))
        }, c = function (e, t) {
            return e.replace(new RegExp("{(\\w+)}", "g"), function (e, n) {
                return t[n]
            })
        }, i = function (e) {
            return e.nodeType ? e : document.getElementById(e)
        }, l = function (e) {
            var t = e, o = -1 !== e.indexOf(n) || -1 !== e.indexOf("\r") || -1 !== e.indexOf("\n"), r = -1 !== e.indexOf('"');
            return r && (t = t.replace(/"/g, '""')), (o || r) && (t = '"' + t + '"'), t
        }, a = function (e) {
            var t = "", r = void 0, c = void 0, i = void 0, a = void 0;
            for (r = 0; r < e.rows.length; r += 1) {
                for (i = e.rows[r], c = 0; c < i.cells.length; c += 1) a = i.cells[c], t = t + (c ? n : "") + l(a.textContent.trim());
                t += o
            }
            return t
        }, f = function (t, n, o, r) {
            var c = void 0;
            return window.navigator.msSaveBlob ? (c = e(n, o), window.navigator.msSaveBlob(c, r), !1) : (window.URL.createObjectURL ? (c = e(n, o), t.href = window.URL.createObjectURL(c)) : (t.download = r, t.href = "data:" + o + ";base64," + n), !0)
        };
        return {
            version: function () {
                return "2.0.3"
            }, excel: function (e, n, o) {
                n = i(n);
                if ($(n).find(":hidden").length > 0) {
                    $("body").append('<div id="_Exporthtml"></div>');
                    $("#_Exporthtml").html(n.innerHTML);
                    $("#_Exporthtml").find(":hidden").remove();
                    var l = {worksheet: o || "Worksheet", table: document.getElementById("_Exporthtml").innerHTML}, a = r(c(t.excel, l));
                    $("#_Exporthtml").remove();
                } else {
                    var l = {worksheet: o || "Worksheet", table: n.innerHTML}, a = r(c(t.excel, l));
                }
                return f(e, a, "application/vnd.ms-excel", "export.xls")
            }, csv: function (e, t, c, l) {
                void 0 !== c && c && (n = c), void 0 !== l && l && (o = l), t = i(t);
                var u = "\ufeff" + a(t), s = r(u);
                return f(e, s, "application/csv", "export.csv")
            }
        }
    }();
    if (!$.browser) $.browser = {};
    $.browser.mozilla = /firefox/.test(navigator.userAgent.toLowerCase());
    $.browser.webkit = /webkit/.test(navigator.userAgent.toLowerCase());
    $.browser.opera = /opera/.test(navigator.userAgent.toLowerCase());
    $.browser.msie = /msie/.test(navigator.userAgent.toLowerCase());
    $.browser.safari = /safari/.test(navigator.userAgent.toLowerCase());
    $.browser.msie6 = false; // do not check for 6.0 alone, userAgent in Windows Vista has "Windows NT 6.0"
    $.fn.checkboxval = function () {
        var outArr = [];
        this.filter(':checked').each(function () {
            outArr.push(this.getAttribute("value"));
        });
        return outArr.join(',');
    };
    //处理checkbox，radio
    $.fn.val = function (value) {
        var rradiocheck = /^(?:radio|checkbox)$/i;
        if (!arguments.length) {
            var elem = this[0];
            if (elem) {
                if ($.nodeName(elem, "option")) {
                    var val = elem.attributes.value;
                    return !val || val.specified ? elem.value : elem.text
                }
                if ($.nodeName(elem, "select")) {
                    var index = elem.selectedIndex,
                        values = [],
                        options = elem.options,
                        one = elem.type === "select-one";
                    if (index < 0) {
                        return null
                    }
                    for (var i = one ? index : 0, max = one ? index + 1 : options.length; i < max; i++) {
                        var option = options[i];
                        if (option.selected && ($.support.optDisabled ? !option.disabled : option.getAttribute("disabled") === null) && (!option.parentNode.disabled || !$.nodeName(option.parentNode, "optgroup"))) {
                            value = $(option).val();
                            if (one) {
                                return value
                            }
                            values.push(value)
                        }
                    }
                    if (one && !values.length && options.length) {
                        return $(options[index]).val()
                    }
                    return values
                }
                if (rradiocheck.test(elem.type) && !$.support.checkOn) {
                    return elem.getAttribute("value") === null ? "on" : elem.value
                }
                return (elem.value == "0" ? "0" : String(elem.value || "").replace(/\u202c/g, '').replace(/\u202d/g, ''))
            } else {
                var o = $('input[name="' + this.selector + '"]');
                if (o.length > 0 && /radio|checkbox/.test(o[0].type)) {
                    var arr = [];
                    for (var i = 0; i < o.length; i++) {
                        if (o[i].checked) arr.push(o[i].value)
                    }
                    if (o.length <= 0) return '';
                    return arr.join(',')
                }
            }
            return undefined
        }
        var isFunction = $.isFunction(value);
        return this.each(function (i) {
            var self = $(this),
                val1 = value;
            if (this.nodeType !== 1) {
                return
            }
            if (isFunction) {
                val1 = value.call(this, i, self.val())
            }
            if (val1 == null) {
                val1 = ""
            } else if (typeof val1 === "number") {
                val1 += ""
            } else if ($.isArray(val1)) {
                val1 = $.map(val1, function (value) {
                    return value == null ? "" : value + ""
                })
            }
            if ($.isArray(val1) && rradiocheck.test(this.type)) {
                this.checked = $.inArray(self.val(), val1) >= 0
            } else if ($.nodeName(this, "select")) {
                var values = $.makeArray(val1);
                $("option", this).each(function () {
                    this.selected = $.inArray($(this).val(), values) >= 0
                });
                if (!values.length) {
                    this.selectedIndex = -1
                }
            }
                //else if ($.nodeName(this, "textarea")) {
                //    var spc = {
                //        "\\\\\\\\r": "\\r",
                //        "\\\\\\\\n": "\\n",
                //        "\\\\r": String.fromCharCode(13),
                //        "\\\\n": String.fromCharCode(10),
                //        "\\\\t": String.fromCharCode(9)
                //    };
                //    var t = "";
                //    for (var i in spc) {
                //        if (typeof (spc[i]) == "string") t += i + "|"
                //    }
                //    t = t.substr(0, t.length - 1);
                //    var p = new RegExp(t, "gim");
                //    this.value = value.replace(p, function (c) {
                //        return spc["\\\\" + c] || spc["\\" + c] || spc[c]
                //    })
            //}
            else {
                this.value = val1
            }
        })
    };
    //导出word处理
    var saveAs = saveAs || (function (view) {
        "use strict";
        if (typeof view === "undefined" || typeof navigator !== "undefined" && /MSIE [1-9]\./.test(navigator.userAgent)) {
            return;
        }
        var doc = view.document
            , get_URL = function () {
            return view.URL || view.webkitURL || view;
        }
            , save_link = doc.createElementNS("http://www.w3.org/1999/xhtml", "a")
            , can_use_save_link = "download" in save_link
            , click = function (node) {
            var event = new MouseEvent("click");
            node.dispatchEvent(event);
        }
            , is_safari = /constructor/i.test(view.HTMLElement)
            , is_chrome_ios = /CriOS\/[\d]+/.test(navigator.userAgent)
            , throw_outside = function (ex) {
            (view.setImmediate || view.setTimeout)(function () {
                throw ex;
            }, 0);
        }
            , force_saveable_type = "application/octet-stream"
            , arbitrary_revoke_timeout = 1000 * 40 // in ms
            , revoke = function (file) {
            var revoker = function () {
                if (typeof file === "string") { // file is an object URL
                    get_URL().revokeObjectURL(file);
                } else { // file is a File
                    file.remove();
                }
            };
            setTimeout(revoker, arbitrary_revoke_timeout);
        }
            , dispatch = function (filesaver, event_types, event) {
            event_types = [].concat(event_types);
            var i = event_types.length;
            while (i--) {
                var listener = filesaver["on" + event_types[i]];
                if (typeof listener === "function") {
                    try {
                        listener.call(filesaver, event || filesaver);
                    } catch (ex) {
                        throw_outside(ex);
                    }
                }
            }
        }
            , auto_bom = function (blob) {
            if (/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(blob.type)) {
                return new Blob([String.fromCharCode(0xFEFF), blob], {type: blob.type});
            }
            return blob;
        }
            , FileSaver = function (blob, name, no_auto_bom) {
            if (!no_auto_bom) {
                blob = auto_bom(blob);
            }
            var filesaver = this
                , type = blob.type
                , force = type === force_saveable_type
                , object_url
                , dispatch_all = function () {
                dispatch(filesaver, "writestart progress write writeend".split(" "));
            }
                , fs_error = function () {
                if ((is_chrome_ios || (force && is_safari)) && view.FileReader) {
                    var reader = new FileReader();
                    reader.onloadend = function () {
                        var url = is_chrome_ios ? reader.result : reader.result.replace(/^data:[^;]*;/, 'data:attachment/file;');
                        var popup = view.open(url, '_blank');
                        if (!popup) view.location.href = url;
                        filesaver.readyState = filesaver.DONE;
                        dispatch_all();
                    };
                    reader.readAsDataURL(blob);
                    filesaver.readyState = filesaver.INIT;
                    return;
                }
                if (!object_url) {
                    object_url = get_URL().createObjectURL(blob);
                }
                if (force) {
                    view.location.href = object_url;
                } else {
                    var opened = view.open(object_url, "_blank");
                    if (!opened) {
                        view.location.href = object_url;
                    }
                }
                filesaver.readyState = filesaver.DONE;
                dispatch_all();
                revoke(object_url);
            };
            filesaver.readyState = filesaver.INIT;

            if (can_use_save_link) {
                object_url = get_URL().createObjectURL(blob);
                setTimeout(function () {
                    save_link.href = object_url;
                    save_link.download = name;
                    click(save_link);
                    dispatch_all();
                    revoke(object_url);
                    filesaver.readyState = filesaver.DONE;
                });
                return;
            }
            fs_error();
        }
            , FS_proto = FileSaver.prototype
            , saveAs = function (blob, name, no_auto_bom) {
            return new FileSaver(blob, name || blob.name || "download", no_auto_bom);
        };
        if (typeof navigator !== "undefined" && navigator.msSaveOrOpenBlob) {
            return function (blob, name, no_auto_bom) {
                name = name || blob.name || "download";

                if (!no_auto_bom) {
                    blob = auto_bom(blob);
                }
                return navigator.msSaveOrOpenBlob(blob, name);
            };
        }
        FS_proto.abort = function () {
        };
        FS_proto.readyState = FS_proto.INIT = 0;
        FS_proto.WRITING = 1;
        FS_proto.DONE = 2;
        FS_proto.error =
            FS_proto.onwritestart =
                FS_proto.onprogress =
                    FS_proto.onwrite =
                        FS_proto.onabort =
                            FS_proto.onerror =
                                FS_proto.onwriteend =
                                    null;
        return saveAs;
    }(
        typeof self !== "undefined" && self
        || typeof window !== "undefined" && window
        || this.content
    ));
    //wordExport导出方法
    if (typeof jQuery !== "undefined" && typeof saveAs !== "undefined") {
        (function ($) {
            $.fn.wordExport = function (fileName, pagetype) {
                fileName = typeof fileName !== 'undefined' ? fileName : "jQuery-Word-Export";
                var mstatic = {
                    mhtml: {
                        top: "Mime-Version: 1.0\nContent-Base: " + location.href + "\nContent-Type: Multipart/related; boundary=\"NEXT.ITEM-BOUNDARY\";type=\"text/html\"\n\n--NEXT.ITEM-BOUNDARY\nContent-Type: text/html; charset=\"utf-8\"\nContent-Location: " + location.href + "\n\n<!DOCTYPE html>\n<html>\n_html_</html>",
                        head: "<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n<style>\n_styles_\n</style>\n</head>\n",
                        body: "<body>_body_</body>"
                    }
                };
                if(pagetype && pagetype == 'page'){//页面形式
                    mstatic = {
                        //设置默认打开视图为页面视图
                        mhtml: {
                            top: "Mime-Version: 1.0\nContent-Base: " + location.href + "\nContent-Type: Multipart/related; boundary=\"NEXT.ITEM-BOUNDARY\";type=\"text/html\"\n\n--NEXT.ITEM-BOUNDARY\nContent-Type: text/html; charset=\"utf-8\"\nContent-Location: " + location.href + "\n\n<!DOCTYPE html>\n" + "<html xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\" xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\">\n_html_</html>",
                            head: "<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n<style>\n_styles_\n</style>\n<!--[if gte mso 9]><xml><w:WordDocument><w:View>Print</w:View><w:TrackMoves>false</w:TrackMoves><w:TrackFormatting/><w:ValidateAgainstSchemas/><w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid><w:IgnoreMixedContent>false</w:IgnoreMixedContent><w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText><w:DoNotPromoteQF/><w:LidThemeOther>EN-US</w:LidThemeOther><w:LidThemeAsian>ZH-CN</w:LidThemeAsian><w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript><w:Compatibility><w:BreakWrappedTables/><w:SnapToGridInCell/><w:WrapTextWithPunct/><w:UseAsianBreakRules/><w:DontGrowAutofit/><w:SplitPgBreakAndParaMark/><w:DontVertAlignCellWithSp/><w:DontBreakConstrainedForcedTables/><w:DontVertAlignInTxbx/><w:Word11KerningPairs/><w:CachedColBalance/><w:UseFELayout/></w:Compatibility><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><m:mathPr><m:mathFont m:val=\"Cambria Math\"/><m:brkBin m:val=\"before\"/><m:brkBinSub m:val=\"--\"/><m:smallFrac m:val=\"off\"/><m:dispDef/><m:lMargin m:val=\"0\"/> <m:rMargin m:val=\"0\"/><m:defJc m:val=\"centerGroup\"/><m:wrapIndent m:val=\"1440\"/><m:intLim m:val=\"subSup\"/><m:naryLim m:val=\"undOvr\"/></m:mathPr></w:WordDocument></xml><![endif]--></head>\n",
                            body: "<body>_body_</body>"
                        }
                    };
                }
                var options = {
                    maxWidth: 624
                };
                var markup = $(this).clone();
                markup.each(function () {
                    var self = $(this);
                    if (self.is(':hidden'))
                        self.remove();
                });
                var images = Array();
                var img = markup.find('img');
                for (var i = 0; i < img.length; i++) {
                    var w = Math.min(img[i].width, options.maxWidth);
                    var h = img[i].height * (w / img[i].width);
                    var canvas = document.createElement("CANVAS");
                    canvas.width = w;
                    canvas.height = h;
                    var context = canvas.getContext('2d');
                    context.drawImage(img[i], 0, 0, w, h);
                    var uri = canvas.toDataURL("image/png/jpg");
                    $(img[i]).attr("src", img[i].src);
                    img[i].width = w;
                    img[i].height = h;
                    images[i] = {
                        type: uri.substring(uri.indexOf(":") + 1, uri.indexOf(";")),
                        encoding: uri.substring(uri.indexOf(";") + 1, uri.indexOf(",")),
                        location: $(img[i]).attr("src"),
                        data: uri.substring(uri.indexOf(",") + 1)
                    };
                }
                var mhtmlBottom = "\n";
                for (var i = 0; i < images.length; i++) {
                    mhtmlBottom += "--NEXT.ITEM-BOUNDARY\n";
                    mhtmlBottom += "Content-Location: " + images[i].location + "\n";
                    mhtmlBottom += "Content-Type: " + images[i].type + "\n";
                    mhtmlBottom += "Content-Transfer-Encoding: " + images[i].encoding + "\n\n";
                    mhtmlBottom += images[i].data + "\n\n";
                }
                mhtmlBottom += "--NEXT.ITEM-BOUNDARY--";

                var styles = "";
                var fileContent = mstatic.mhtml.top.replace("_html_", mstatic.mhtml.head.replace("_styles_", styles) + mstatic.mhtml.body.replace("_body_", markup.html())) + mhtmlBottom;
                var blob = new Blob([fileContent], {
                    type: "application/msword;charset=utf-8"
                });
                saveAs(blob, fileName + ".doc");
            };
        })($);
    } else {
        if (typeof $ === "undefined") {
            console.error("jQuery Word Export: missing dependency (jQuery)");
        }
        if (typeof saveAs === "undefined") {
            console.error("jQuery Word Export: missing dependency (FileSaver.js)");
        }
    }

    /***************************************************************************
     *                     Number.prototype.toFixed() -> rewrite                *
     ***************************************************************************/
    var toSuperNumber = function (x) {//转换科学计数法
        if (Math.abs(x) < 1.0) {
            var e = parseInt(x.toString().split('e-')[1]);
            if (e) {
                x *= Math.pow(10, e - 1);
                x = '0.' + (new Array(e)).join('0') + x.toString().substring(2);
            }
        } else {
            var e = parseInt(x.toString().split('+')[1]);
            if (e > 20) {
                e -= 20;
                x /= Math.pow(10, e);
                x += (new Array(e + 1)).join('0');
            }
        }
        return x;
    }
    Number.prototype.toFixed = function (d) {
        var s = this + "";
        // 处理科学计算情况
        if (s.indexOf("e") > -1) {
            // var m = s.match(/\d(?:\.(\d*))?e([+-]\d+)/);
            // s = this.toFixed(Math.max(0, (m[1] || '').length - m[2]))
            s = toSuperNumber(s);
        }
        if (!d) d = 0;
        else d = parseInt(d);
        if (s.indexOf(".") === -1) s += ".";
        s += new Array(d + 1).join("0");
        if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
            var s = "0" + RegExp.$2, pm = RegExp.$1, a = RegExp.$3.length, b = true;
            if (a == d + 2) {
                a = s.match(/\d/g);
                if (parseInt(a[a.length - 1]) > 4) {
                    for (var i = a.length - 2; i >= 0; i--) {
                        a[i] = parseInt(a[i]) + 1;
                        if (a[i] == 10) {
                            a[i] = 0;
                            b = i != 1;
                        } else break;
                    }
                }
                s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2");
            }
            if (b) s = s.substr(1);
            return (pm + s).replace(/\.$/, "");
        }
        return this + "";
    };
    /***
     * 处理数字转string，自动处理科学计数法显示，from百度计算器
     * @returns {string}
     */
    Number.prototype.toNumString = function () {
        var t = this + "";
        var e = String(t)
            , u = function (t) {
            return String(parseFloat(t))
        }
            , n = "";
        "-" === e[0] && (e = e.slice(1),
            n = "-");
        var a, i, s, o = e.indexOf("."), r = e.indexOf("e"), c = "";
        if (-1 < r ? 10 <= r && (c = t.toExponential(7)) : -1 < o ? (a = /0\.00000/.exec(e),
            12 <= o ? c = t.toExponential(7) : a ? (i = Math.min(7, e.length - a.index),
                c = t.toExponential(i)) : 12 < e.length && (i = 12 - o,
                s = Number(e).toFixed(i),
                e = u(s))) : 12 <= e.length && (c = t.toExponential(7)),
            c) {
            var l = c.indexOf("e");
            return u(c.slice(0, l)) + c.slice(l)
        }
        return n + e;
    }
    /***
     * 将科学计数法转换成小数
     * @param inputNumber
     * @returns {string|*}
     */
    Number.prototype.scientificToNumber = function () {
        var inputNumber = this;
        if (isNaN(inputNumber)) {
            return inputNumber;
        }
        inputNumber = '' + inputNumber;
        inputNumber = parseFloat(inputNumber);
        var eformat = inputNumber.toExponential() // 转换为标准的科学计数法形式（字符串）
        var tmpArray = eformat.match(/\d(?:\.(\d*))?e([+-]\d+)/) // 分离出小数值和指数值
        var number = inputNumber.toFixed(Math.max(0, (tmpArray[1] || '').length - tmpArray[2]));
        return number;
    }
    /***************************************************************************
     *                     解决JS浮点数(小数)计算加减乘除的BUG Start                *
     ***************************************************************************/
    //给Number类型增加一个add加法方法，调用起来更加方便。
    Number.prototype.add = function (arg) {
        return FloatAdd(this, arg);
    };
    // 给Number类型增加一个sub减法方法，调用起来更加方便。
    Number.prototype.sub = function (arg) {
        return FloatSub(this, arg);
    };
    // 给Number类型增加一个mul乘法方法，调用起来更加方便。
    Number.prototype.mul = function (arg) {
        return FloatMul(this, arg);
    };
    //给Number类型增加一个div除法方法，调用起来更加方便。
    Number.prototype.div = function (arg) {
        return FloatDiv(this, arg);
    };
    //给Number类型增加一个parseFloatMoney方法，统一格式化金额。
    Number.prototype.parseFloatMoney = function () {
        return parseFloatMoney(this);
    };
    //给Number类型增加一个parseFloatPrice方法，统一格式化金额。
    Number.prototype.parseFloatPrice = function () {
        return parseFloatPrice(this);
    };
}

/**
 ** 加法函数，用来得到精确的加法结果
 ** 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
 ** 调用：FloatAdd(arg1,arg2)
 ** 返回值：arg1加上arg2的精确结果
 **/
function FloatAdd(arg1, arg2) {
    var r1 = 0;
    var r2 = 0;
    try {
        r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
    }
    try {
        r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
    }

    var m = Math.pow(10, Math.max(r1, r2));
    return (Math.round(arg1 * m) + Math.round(arg2 * m)) / m;
}

/**
 ** 减法函数，用来得到精确的减法结果
 ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
 ** 调用：FloatSub(arg1,arg2)
 ** 返回值：arg1加上arg2的精确结果
 **/
function FloatSub(arg1, arg2) {
    return FloatAdd(arg1, -arg2);
}

/**
 ** 乘法函数，用来得到精确的乘法结果
 ** 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
 ** 调用：FloatMul(arg1,arg2)
 ** 返回值：arg1乘以 arg2的精确结果
 **/
function FloatMul(arg1, arg2) {
    var s1 = arg1.toString();
    var s2 = arg2.toString();
    var m = 0;
    try {
        m += s1.split('.')[1].length;
    } catch (e) {
    }
    try {
        m += s2.split('.')[1].length;
    } catch (e) {
    }

    return (s1.replace('.', '') - 0) * (s2.replace('.', '') - 0) / Math.pow(10, m);
}

/**
 ** 除法函数，用来得到精确的除法结果
 ** 说明：javascript的除法结果会有误差，在两个浮点数相除的时候会比较明显。这个函数返回较为精确的除法结果。
 ** 调用：FloatDiv(arg1,arg2)
 ** 返回值：arg1除以arg2的精确结果
 **/
function FloatDiv(arg1, arg2) {
    var s1 = arg1.toString();
    var s2 = arg2.toString();
    var m = 0;
    try {
        m = s2.split('.')[1].length;
    } catch (e) {
    }
    try {
        m -= s1.split('.')[1].length;
    } catch (e) {
    }

    return FloatMul((s1.replace('.', '') - 0) / (s2.replace('.', '') - 0), Math.pow(10, m));
}

/**
 * 统一处理金钱位数 **
 * 调用：parseFloatMoney(arg1)
 ** 返回值：arg1除以arg2的精确结果
 */
function parseFloatMoney(arg1) {
    var dd = 2;//处理位数
    return parseFloat(arg1).toFixed(dd);
}

/**
 * 统一处理价格位数 **
 * 调用：parseFloatMoney(arg1)
 ** 返回值：arg1除以arg2的精确结果
 */
function parseFloatPrice(arg1) {
    return parseFloat(arg1);
}
//获取logo图片地址
function getlogo(type) {
    var host = location.host;
    if (host == "ibao365.tb-n.com") {
        if (type == 1) {
            return "images/logo_lh01.png";
        } else if (type == 2) {
            return "images/logo_lh02.png";
        } else if (type == 3) {
            return "images/logo_lh03.png";
        } else if (type == 4) {
            return "images/loginbgbg_01.png";
        }
    } else {
        if (type == 1) {
            return "images/logo_01.png";
        } else if (type == 2) {
            return "images/logo_02.png";
        } else if (type == 3) {
            if (location.hostname.indexOf('app-n') > -1 || location.hostname.indexOf('ssjkjh.cn') > -1) {
                return "images/logo_03_2.png";
                // return "images/01.png";
            } else {
                return "images/logo_03.png";
                // return "images/logo_03_2.png";
            }
        } else if (type == 4) {
            return "images/loginbgbg.png";
        }
    }
}
function jparentwindow() {
    return $.getparent().parentWindow();
}
/*
 功能：幼儿园平台模式下 得到第二层全局方法和变量的页面window
 */
window.SystemPath = "../sys/";
//初始化语言
function initlanguage(lan, data) {
    if (lan == 'en') {
        changtoen(data, 1);
    } else {
        $(".lanflag").css('visibility', 'visible');
    }
}
//中文
function changtozh() {
    $(".lanflag").each(function () {
        var _this = $(this);
        if (_this.attr('zh_html')) {
            _this.html(_this.attr('zh_html'));
        }
        if (_this.attr('zh_title')) {
            _this.prop("title", _this.attr('zh_title'));
        }
        if (_this.attr('zh_placeholder')) {
            _this.prop("placeholder", _this.attr('zh_placeholder'));
        }
    });
}
//英文
function changtoen(entext, isshow) {
    var key = location.href.replace(/(.*\/)*([^.]+).*/ig, "$2");
    $(".lanflag").each(function () {
        var _this = $(this);
        var id = _this.prop('id');
        for (var t in entext[key][id]) {
            if (t == "html") {
                _this.attr("zh_" + t, _this.html());
                _this.html(entext[key][id][t]);
            } else if (t == "title") {
                _this.attr("zh_" + t, _this.prop('title'));
                _this.prop('title', entext[key][id][t]);
            } else if (t == "placeholder") {
                _this.attr("zh_" + t, _this.prop('placeholder'));
                _this.prop('placeholder', entext[key][id][t]);
            }
        }
        //		if (type == 'INPUT') {
        //			_this.attr('zh', _this.val());
        //			_this.val(entext[key][id]);
        //		} else if (type == 'IMG') {
        //			_this.attr('zh', _this.prop('src'));
        //			_this.prop('src', entext[key][id]);
        //		} else {
        //			_this.attr('zh', _this.html());
        //			_this.html(entext[key][id]);
        //		}
        if (isshow)
            _this.css('visibility', 'visible');
    });
}
//增加css文件
function addcssfile(name) {
    var link = document.createElement("link");
    link.rel = "stylesheet";
    link.type = "text/css";
    link.href = name;
    document.getElementsByTagName("head")[0].appendChild(link);
}
if (typeof JSON !== 'object') {
    JSON = {};
}

(function () {
    'use strict';

    var rx_one = /^[\],:{}\s]*$/, rx_two = /\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,
        rx_three = /"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, rx_four = /(?:^|:|,)(?:\s*\[)+/g,
        rx_escapable = /[\\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
        rx_dangerous = /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;

    function f(n) {
        // Format integers to have at least two digits.
        return n < 10 ? '0' + n : n;
    }

    function this_value() {
        return this.valueOf();
    }

    if (typeof Date.prototype.toJSON !== 'function') {

        Date.prototype.toJSON = function () {

            return isFinite(this.valueOf()) ? this.getUTCFullYear() + '-' + f(this.getUTCMonth() + 1) + '-' + f(this.getUTCDate()) + 'T' + f(this.getUTCHours()) + ':' + f(this.getUTCMinutes()) + ':' + f(this.getUTCSeconds()) + 'Z' : null;
        };

        Boolean.prototype.toJSON = this_value;
        Number.prototype.toJSON = this_value;
        String.prototype.toJSON = this_value;
    }

    var gap, indent, meta, rep;

    function quote(string) {

        // If the string contains no control characters, no quote characters,
        // and no
        // backslash characters, then we can safely slap some quotes around it.
        // Otherwise we must also replace the offending characters with safe
        // escape
        // sequences.

        rx_escapable.lastIndex = 0;
        return rx_escapable.test(string) ? '"' + string.replace(rx_escapable, function (a) {
            var c = meta[a];
            return typeof c === 'string' ? c : '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
        }) + '"' : '"' + string + '"';
    }

    function str(key, holder) {

        // Produce a string from holder[key].

        var i, // The loop counter.
            k, // The member key.
            v, // The member value.
            length, mind = gap, partial, value = holder[key];

        // If the value has a toJSON method, call it to obtain a replacement
        // value.

        if (value && typeof value === 'object' && typeof value.toJSON === 'function') {
            value = value.toJSON(key);
        }

        // If we were called with a replacer function, then call the replacer to
        // obtain a replacement value.

        if (typeof rep === 'function') {
            value = rep.call(holder, key, value);
        }

        // What happens next depends on the value's type.

        switch (typeof value) {
            case 'string':
                return quote(value);

            case 'number':

                // JSON numbers must be finite. Encode non-finite numbers as null.

                return isFinite(value) ? String(value) : 'null';

            case 'boolean':
            case 'null':

                // If the value is a boolean or null, convert it to a string. Note:
                // typeof null does not produce 'null'. The case is included here in
                // the remote chance that this gets fixed someday.

                return String(value);

            // If the type is 'object', we might be dealing with an object or an
            // array or
            // null.

            case 'object':

                // Due to a specification blunder in ECMAScript, typeof null is
                // 'object',
                // so watch out for that case.

                if (!value) {
                    return 'null';
                }

                // Make an array to hold the partial results of stringifying this
                // object value.

                gap += indent;
                partial = [];

                // Is the value an array?

                if (Object.prototype.toString.apply(value) === '[object Array]') {

                    // The value is an array. Stringify every element. Use null as a
                    // placeholder
                    // for non-JSON values.

                    length = value.length;
                    for (i = 0; i < length; i += 1) {
                        partial[i] = str(i, value) || 'null';
                    }

                    // Join all of the elements together, separated with commas, and
                    // wrap them in
                    // brackets.

                    v = partial.length === 0 ? '[]' : gap ? '[\n' + gap + partial.join(',\n' + gap) + '\n' + mind + ']' : '[' + partial.join(',') + ']';
                    gap = mind;
                    return v;
                }

                // If the replacer is an array, use it to select the members to be
                // stringified.

                if (rep && typeof rep === 'object') {
                    length = rep.length;
                    for (i = 0; i < length; i += 1) {
                        if (typeof rep[i] === 'string') {
                            k = rep[i];
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                } else {

                    // Otherwise, iterate through all of the keys in the object.

                    for (k in value) {
                        if (Object.prototype.hasOwnProperty.call(value, k)) {
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                }

                // Join all of the member texts together, separated with commas,
                // and wrap them in braces.

                v = partial.length === 0 ? '{}' : gap ? '{\n' + gap + partial.join(',\n' + gap) + '\n' + mind + '}' : '{' + partial.join(',') + '}';
                gap = mind;
                return v;
        }
    }

    // If the JSON object does not yet have a stringify method, give it one.

//	if (typeof JSON.stringify !== 'function') {
    meta = { // table of character substitutions
        '\b': '\\b',
        '\t': '\\t',
        '\n': '\\n',
        '\f': '\\f',
        '\r': '\\r',
        '"': '\\"',
        '\\': '\\\\'
    };
    JSON.stringify = function (value, replacer, space) {

        // The stringify method takes a value and an optional replacer, and
        // an optional
        // space parameter, and returns a JSON text. The replacer can be a
        // function
        // that can replace values, or an array of strings that will select
        // the keys.
        // A default replacer method can be provided. Use of the space
        // parameter can
        // produce text that is more easily readable.

        var i;
        gap = '';
        indent = '';

        // If the space parameter is a number, make an indent string
        // containing that
        // many spaces.

        if (typeof space === 'number') {
            for (i = 0; i < space; i += 1) {
                indent += ' ';
            }

            // If the space parameter is a string, it will be used as the
            // indent string.

        } else if (typeof space === 'string') {
            indent = space;
        }

        // If there is a replacer, it must be a function or an array.
        // Otherwise, throw an error.

        rep = replacer;
        if (replacer && typeof replacer !== 'function' && (typeof replacer !== 'object' || typeof replacer.length !== 'number')) {
            throw new Error('JSON.stringify');
        }

        // Make a fake root object containing our value under the key of ''.
        // Return the result of stringifying the value.

        return str('', {
            '': value
        });
    };
//	}

    // If the JSON object does not yet have a parse method, give it one.

//	if (typeof JSON.parse !== 'function') {
    JSON.parse = function (text, reviver) {

        // The parse method takes a text and an optional reviver function,
        // and returns
        // a JavaScript value if the text is a valid JSON text.

        var j;

        function walk(holder, key) {

            // The walk method is used to recursively walk the resulting
            // structure so
            // that modifications can be made.

            var k, v, value = holder[key];
            if (value && typeof value === 'object') {
                for (k in value) {
                    if (Object.prototype.hasOwnProperty.call(value, k)) {
                        v = walk(value, k);
                        if (v !== undefined) {
                            value[k] = v;
                        } else {
                            delete value[k];
                        }
                    }
                }
            }
            return reviver.call(holder, key, value);
        }

        // Parsing happens in four stages. In the first stage, we replace
        // certain
        // Unicode characters with escape sequences. JavaScript handles many
        // characters
        // incorrectly, either silently deleting them, or treating them as
        // line endings.

        text = String(text);
        rx_dangerous.lastIndex = 0;
        if (rx_dangerous.test(text)) {
            text = text.replace(rx_dangerous, function (a) {
                return '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
            });
        }

        // In the second stage, we run the text against regular expressions
        // that look
        // for non-JSON patterns. We are especially concerned with '()' and
        // 'new'
        // because they can cause invocation, and '=' because it can cause
        // mutation.
        // But just to be safe, we want to reject all unexpected forms.

        // We split the second stage into 4 regexp operations in order to
        // work around
        // crippling inefficiencies in IE's and Safari's regexp engines.
        // First we
        // replace the JSON backslash pairs with '@' (a non-JSON character).
        // Second, we
        // replace all simple value tokens with ']' characters. Third, we
        // delete all
        // open brackets that follow a colon or comma or that begin the
        // text. Finally,
        // we look to see that the remaining characters are only whitespace
        // or ']' or
        // ',' or ':' or '{' or '}'. If that is so, then the text is safe
        // for eval.

        if (rx_one.test(text.replace(rx_two, '@').replace(rx_three, ']').replace(rx_four, ''))) {

            // In the third stage we use the eval function to compile the
            // text into a
            // JavaScript structure. The '{' operator is subject to a
            // syntactic ambiguity
            // in JavaScript: it can begin a block or an object literal. We
            // wrap the text
            // in parens to eliminate the ambiguity.

            j = eval('(' + text + ')');

            // In the optional fourth stage, we recursively walk the new
            // structure, passing
            // each name/value pair to a reviver function for possible
            // transformation.

            return typeof reviver === 'function' ? walk({
                '': j
            }, '') : j;
        }

        // If the text is not JSON parseable, then a SyntaxError is thrown.

        throw new SyntaxError('JSON.parse');
    };
//	}
}());
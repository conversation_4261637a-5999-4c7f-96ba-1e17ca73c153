require.config({
    baseUrl: '../',//指定js文件的基路径
    paths: {
        system: 'sys/system_zepto',
        moment: 'plugin/js/moment',
        aboutright : 'wxchdc/js/aboutright'
    },
    waitSeconds: 0
});
require(['system',"aboutright"], function (s,aboutright) {
    aboutright.then(function (data) {
        if(!data){
            window.location.href = "./bind.html?v="+Arg("v");
            return;
        }
        if(data){
            $(function () {
                clickevent();
                $.sm(function (re, err) {
                    if (re && re[0]) {
                        var data=re[0];
                        $("#headimg").prop('src', data.userimg);
                        $("#nickname").html(data.nickname);
                        $("#mobile").html(data.mobile);
                        $("#truename").val(data.truename);
                        $("#email").val(data.email);
                        $("#departname").val(data.departname);
                    } else {

                    }
                }, ["wxchdc.mine.info"]);

                $("#save").on("click",function () {
                	var mobile = $("#mobile").html();
                    var truename = $("#truename").val();
                    var email = $("#email").val();
                    var departname = $("#departname").val();
                    $.sm(function (re,err) {
                        if(re){
                            parent.layer.msg("保存成功")
                        }else {
                            parent.layer.msg("保存失败")
                        }
                    },["update.info",truename,email,departname,mobile]);
                });

                $("#jiechu").on('click',function () {
                    $.sm(function (re,err) {
                        if(re){
                            parent.layer.msg("解绑成功");
                            var url='bind.html';
                            openwin('bind',url,"绑定页");

                        }else {
                            parent.layer.msg("解绑失败")
                        }

                    },["update.jiebang"]);
                });
            })
        }
    });
});
function clickevent() {
    $("#truename").focus(function(){
        $("#truename").removeClass("inp-print").addClass("input-center");
    });
    $("#truename").blur(function(){
        $("#truename").removeClass("input-center").addClass("inp-print");
    });
    $("#email").focus(function(){
        $("#email").removeClass("inp-print").addClass("input-center");
    });
    $("#email").blur(function(){
        $("#email").removeClass("input-center").addClass("inp-print");
    });
    $("#departname").focus(function(){
        $("#departname").removeClass("inp-print").addClass("input-center");
    });
    $("#departname").blur(function(){
        $("#departname").removeClass("input-center").addClass("inp-print");
    });
}
function openwin(key,src,title){
    var curzindex=10;
    document.title = title||"";
    pushHash({
        key:key,
        title:title
    });
    if(!src){
        src = key + '.html?v='+Arg('v');
    }
    var str = '<div class="oneopenframe" style="border:none;z-index:'+ curzindex +'">'
        +'<iframe id="frame'+key+'" src="'+src+'" style="width:100%;height:100%;border:none;position: absolute;top: 0;left: 0;"/></div>';
    curzindex +=1;
    $('body').append(str);
}
function pushHash(obj){
    // objdata.arrwin.push(obj);
    window.history.pushState(null, obj.title, "#"+obj.key);
}

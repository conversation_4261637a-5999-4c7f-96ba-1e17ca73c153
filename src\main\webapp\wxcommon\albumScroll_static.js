﻿define(['../../plugin/iscroll/js/iscroll','../wxcommon/scrollclass.js'],function () {
    var templete = {
        oneDate:'<div class="photo-pic">'+
				'<p class="time-star">5-21</p>'+
				'<i class="iconfont"></i>'+
				'<div class="photo-stat">'+
					'<div class="photo-list">'+
						'<div class="pho-medi">'+
							'<div>'+
								'<img src="images/picture_01.jpg" class="medi-pho">'+
							'</div>'+
						'</div>'+
					'</div>'+
				'</div>'+
			'</div>',
        onePic:'<div class="pho-medi">'+
                    '<div>'+
                        '<img src="images/picture_01.jpg" class="medi-pho">'+
                    '</div>'+
                '</div>'
    },
    /**
     * 替换模板数据 模板与数据中的变量名完全一致
     * @param template {String} 模板字符串
     * @param model {Object} 模板数据
     * @returns {*} {strHtml} 数据替换后的模板字符串
     */
    renderTemp = function (template, model) {
        var reg = /\{\{\w+\}\}/g;
        template = template.replace(reg,function (regStr) {
            var reg2 = /\{\{(\w+)\}\}/g,
                key = reg2.exec(regStr)[1];
            return model[key];
        });
        return template;
    };
    /**
     * 查询数据
     * @param ismore
     * @param callback
     * @param isfirst
     */
    function querydata(ismore, callback) {
        var swhere = '';
        if (!objCur.isFirst) {
            if (ismore) {
                swhere += " and altime < '" + objCur.starttime +"'";
            } else {
                swhere += " and altime > '" + objCur.endtime +"'";
            }
        }
        objCur.dbParam.swhere = swhere;
        $.sm(function (re, err) {
            if (re) {
                callback(re);
            } else {
                callback([]);
            }
        }, ['weixin.material.getData', JSON.stringify(objCur.dbParam)]);
    }

    /**
     * @param arr[Arrary] 消息列表数据
     * @param noticeLen[Number|null] 页面已有消息条数
     * @param direction["up"|"down"|default down] 消息插入方向
     * */
    function makeHtml(arr, direction) {
        var html = [];
        if (arr.length) {
            objCur.isFirst = false;
            for (var i = 0; i < arr.length; i++) {  //
                var obj = arr[i];
                html.push(makeonehtml(obj));
            }
        }
        return html;
    }
    function makeonehtml(obj) {
        objCur.data[obj.id] = obj;
        return '<img style="max-width: 100%" data-id="'+ obj.id +'" src="'+ ossPrefix + obj.murl + '?x-oss-process=image/resize,m_fill,w_300,limit_0,h_300' +'"/>';
    }
    return {
        init: function () {
            objCur.data = {};
            //objCur.dbParam  = JSON.parse(JSON.stringify(objParam));
            var elem = $("#" + objCur.wrapperId);
            //查询班级数据，并显示
            querydata(true, function (re) {
                if (re.length > 0) {
                    elem.find(".thelist").html(makeHtml(re));
                    objCur.endtime = re[0].altime;
                    objCur.starttime = re[re.length - 1].altime;
                    setTimeout(function () {
                        objCur.myscroll.scroll.refresh();
                    },1000);
                    setTimeout(function () {
                        objCur.myscroll.scroll.refresh();
                    },3000);
                } else {
                    elem.find(".thelist").siblings('.pullUp').children('.pullUpLabel').html('暂无内容');
                }
                setTimeout(function () {
                    objCur.myscroll = new scrollClass({
                        wrapper: objCur.wrapperId,
                        // onScrollMove:function (_this) {
                        //     var top = -(_this.y+objCur.myscroll.pullDownOffset);
                        //     if(top>174){
                        //         $("#head").show();
                        //     } else {
                        //         $("#head").hide();
                        //     }
                        // },
                        // onScrollEnd:function (_this) {
                        //     var top = -(_this.y+objCur.myscroll.pullDownOffset);
                        //     if(top>174){
                        //         $("#head").show();
                        //     } else {
                        //         $("#head").hide();
                        //     }
                        // },
                        downAction: function () {
                            querydata(false, function (re) {
                                if (re.length > 0) {
                                    elem.find(".thelist").prepend(makeHtml(re, "down"));
                                    objCur.endtime = re[0].altime;
                                }
                                objCur.myscroll.scroll.refresh();
                            });
                        },
                        upAction: function () {
                            querydata(true, function (re) {
                                if (re.length > 0) {
                                    //上拉之前消息条数
                                    elem.find(".thelist").append(makeHtml(re, "up"));
                                    objCur.myscroll._wrapper.find('.pullUpLabel').html('下拉加载更多');
                                    objCur.starttime = re[re.length - 1].altime;
                                    setTimeout(function () {
                                        objCur.myscroll.scroll.refresh();
                                    },1000);
                                    setTimeout(function () {
                                        objCur.myscroll.scroll.refresh();
                                    },3000);
                                } else {
                                    objCur.myscroll._wrapper.find('.pullUpLabel').html('没有啦');
                                    objCur.myscroll._wrapper.find('.pullUpIcon').hide();
                                }
                            });
                        }
                    });
                    objCur.myscroll.init();
                    setTimeout(function () {
                        elem.css('left',0);
                        //document.getElementById('wrapper').style.left = '0';
                    }, 10);
                }, 10);
            });
            this.initEvent(elem);
        },initEvent:function (elem) {
            elem.find(".thelist").on('click','img',function () {
                var curid = $(this).data("id");
                var arr = [];
                var curindex = 0;
                elem.find(".thelist").find("img").each(function () {
                    var id = $(this).data("id");
                    var obj = objCur.data[id];
                    if(id==curid){
                        curindex = $(this).index();
                    }
                    var objmedia = JSON.parse(obj.mdetail);
                    arr.push({
                        src:  ossPrefix + obj.murl,
                        w: objmedia.w,
                        h: objmedia.h,
                        title:'<div>图片介绍'+id+'</div>'+
                        '<div><span>编辑</span><span style="float: right">删除</span></div>'
                    });
                })
                require(['../wxcommon/photoswipe.js'],function (objswipe) {
                    objswipe.init(arr,curindex);
                })
            });
        }
    };
});
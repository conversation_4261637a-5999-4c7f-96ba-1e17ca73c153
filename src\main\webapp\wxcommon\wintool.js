//微信sdkcommon
define(function () {
    function pushHash(obj) {
        objdata.arrwin.push(obj);
        window.history.pushState(null, obj.title, "#" + obj.key);
    }

    function closewin(obj) {
        if (obj && obj.wintype && obj.wintype == 'layer') {
            closelayer(obj);
        } else {
            var _obj = $("#frame" + obj.key).parent();
            _obj.remove();
        }
    }

    function backEvent(obj) {
        closewin(obj);
        var objto = objdata.arrwin[objdata.arrwin.length - 1];
        document.title = objto.title || '';
    }


    function openwin(key, src, title) {
        if (title) document.title = title || "";
        pushHash({
            key: key,
            title: title
        });
        //修改objwin
        objdata.objwin[key] = {
            wintype: 'win',
            key: key
        };
        if (!src) {
            src = key + '.html?v=' + Arg('v');
        }
        var str =
            '<div class="oneopenpage" style="border:none;z-index:' + objdata.curzindex + '">\
            <div class="oneopenpage_overlay"></div>\
            <iframe class="oneopenpage_frame" id="frame' + key + '" src="" name="' + key + '"></iframe>\
        </div>';
        var _obj = $(str);
        objdata.curzindex += 1;
        if (!title) {
            setTimeout(function () {
                var win = $('#frame' + key).length ? $('#frame' + key)[0].contentWindow : null;
                if (win && win.document && win.document.title) {
                    objdata.arrwin[objdata.arrwin.length - 1].title = win.document.title;
                    document.title = win.document.title;
                }
            }, 2000);
        }
        $('body').append(_obj);
        _obj.show();
        _obj.width();
        _obj.addClass('oneopenpage--visible');
        var _frame = _obj.find('.oneopenpage_frame');
        _frame.width();
        _frame.transitionEnd(function () {
            _frame.prop('src', src);
        });
        /*configwx(function () {
            wx.hideAllNonBaseMenuItem();
        })*/
    }

    function getprevwin() {
        var obj = objdata.arrwin[objdata.arrwin.length - 2];
        if (obj && obj.key) {
            var win = $("#frame" + obj.key)[0].contentWindow;
            return win;
        }
        return parent;
    }

    function setOnPageBack(backevent) {
        objdata.arrwin[objdata.arrwin.length - 1].onPageBack = backevent;
    }
    function removeOnPageBack() {
        objdata.arrwin[objdata.arrwin.length - 1].onPageBack = null;
    }
    var objwin = {
        init: function (key, title) {
            objdata.curzindex = 3000;
            if(!objdata.objwin){
                objdata.objwin = {};
            }
            var _this = this;
            if (parent.openwin || parent.wintool) {
                return;
            }
            objdata.arrwin = [{
                key: key,
                title: title || document.title
            }];
            console.log(22222);
            // 倒退按钮 w.history
            window.addEventListener("popstate", function (e) { // 后退操作
                if (objdata.arrwin && objdata.arrwin.length > 1) {
                    var obj = objdata.arrwin[objdata.arrwin.length - 1];
                    objdata.curhash = objdata.arrwin[objdata.arrwin.length - 1].key; // 当前打开的额外iframe的key
                    if (obj.onPageBack) {
                        window.history.pushState(null, obj.title, "#" + obj.key);
                        obj.onPageBack(function () {
                            obj.onPageBack = null;
                            history.back();
                        });
                    } else {
                        objdata.arrwin.pop();// 移除arrwin的存储
                        backEvent(obj);
                    }
                    // objdata.arrwin.pop(); // 移除arrwin的存储
                    // backEvent();
                }
            }, false);
            window.openwin = openwin;
            window.getprevwin = getprevwin;
            window.setOnPageBack = setOnPageBack;
            window.removeOnPageBack = removeOnPageBack;
        }
    };
    return objwin;
});
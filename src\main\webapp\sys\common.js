﻿/*
內容摘要:　公共方法
*/
//功能：弹出txt 文本time 时间fun 回调
function myAlert(txt, time, fun) {
    var obj = $("#h5myAlert");
    if (obj.length <= 0) {
        obj = $('<h5 id="h5myAlert" class="success">' + txt + '</h5>');
        $('body').append(obj);
    } else {
        obj.html(txt).show();
    }
    var timer = setTimeout(function () {
        obj.hide();
        clearTimeout(timer);
        if (fun) {
            fun();
        }
    }, time || 3000);
}
//功能：指向指定位置的弹出obj 元素txt 文本time 时间fun 回调
function myAlert2(obj, txt, time, fun) {
    var top = obj.offset().top - 55;
    var obj = $("#h4myAlert");
    if (obj.length <= 0) {
        obj = $('<h4 id="h4myAlert" style="top:' + top + 'px" class="help pa">' + txt + '</h4>');
        $('body').append(obj);
    } else {
        obj.html(txt).css('top', top).show();
    }
    var timer = setTimeout(function () {
        obj.hide();
        clearTimeout(timer);
        if (fun) {
            fun();
        }
    }, time || 3000);
}
//得到时间字符串 到秒
function getStrByDateTime(d) {
    return d.getFullYear() + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate()) + ((d.getHours() < 10) ? "0" + d.getHours() : d.getHours()) + ((d.getMinutes() < 10) ? "0" + d.getMinutes() : d.getMinutes()) + ((d.getSeconds() < 10) ? "0" + d.getSeconds() : d.getSeconds());
}

function imgerrorfun(defaultimg) {
    var img = event.srcElement;
    img.src = defaultimg;
    img.onerror = null;
}
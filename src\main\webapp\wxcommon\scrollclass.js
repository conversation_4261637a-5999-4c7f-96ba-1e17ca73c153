﻿/*
新增日期: 2014.07.29
作 者: 郭玉峰
內容摘要:　iscroll公共方法
*/

function scrollClass(option) {
    this.option = option;
}
scrollClass.prototype = {
    init: function () {
        this._wrapper = $("#" + this.option.wrapper);
        this.pullDownEl = this._wrapper.find('.pullDown');
        if (this.pullDownEl[0]) {
            this.pullDownOffset = this.pullDownEl[0].offsetHeight;
        }
        this.pullUpEl = this._wrapper.find('.pullUp');
        this.pullUpOffset = this.pullUpEl[0]?this.pullUpEl[0].offsetHeight:0;
        var _this = this;
        this.scroll = new iScroll(this.option.wrapper, {
            useTransition: true,
            hideScrollbar: true,
            topOffset: this.pullDownOffset,
            onRefresh: function () {
                if (_this.pullDownEl.hasClass('loading')) {
                    _this.pullDownEl.removeClass('loading');
                    _this.pullDownEl.find('.pullDownLabel').html('下拉刷新');
                } else if (_this.pullUpEl.hasClass('loading')) {
                    _this.pullUpEl.removeClass('loading');
                    _this.pullUpEl.find('.pullUpLabel').html('上拉加载');
                }
            },
            onScrollMove: function () {
                if (this.y > 5 && !_this.pullDownEl.hasClass('flip')) {
                    _this.pullDownEl.addClass('flip').find('.pullDownLabel').html('松手开始刷新');
                    this.minScrollY = 0;
                } else if (this.y < 5 && _this.pullDownEl.hasClass('flip')) {
                    _this.pullDownEl.removeClass('flip').find('.pullDownLabel').html('下拉刷新');
                    this.minScrollY = -_this.pullDownOffset;
                } else if (this.y < (this.maxScrollY - 5) && !_this.pullUpEl.hasClass('flip')) {
                    _this.pullUpEl.addClass('flip').find('.pullUpLabel').html('松手开始刷新');
                    this.maxScrollY = this.maxScrollY;
                } else if (this.y > (this.maxScrollY + 5) && _this.pullUpEl.hasClass('flip')) {
                    _this.pullUpEl.removeClass('flip').find('.pullUpLabel').html('上拉加载');
                    this.maxScrollY = _this.pullUpOffset;
                }
            },
            onScrollEnd: function () {
                if (_this.pullDownEl.hasClass('flip')) {
                    _this.pullDownEl.removeClass('flip').addClass('loading').find('.pullDownLabel').html('加载中');
                    _this.option.downAction();
                } else if (_this.pullUpEl.hasClass('flip')) {
                    _this.pullUpEl.removeClass('flip').addClass('loading').find('.pullUpLabel').html('加载中');
                    _this.option.upAction();
                }
            }
        });
    }
};
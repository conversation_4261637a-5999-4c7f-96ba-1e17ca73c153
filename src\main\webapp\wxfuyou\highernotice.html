<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title></title>
    <link href="../plugin/layer_mobile/layer_mobile.min.css" rel="stylesheet" type="text/css"/>
    <link href="../css/reseta.css" rel="stylesheet" type="text/css"/>
    <link href="../css/stylea.css" rel="stylesheet" type="text/css"/>
    <script src="../sys/jquery.js" type="text/javascript"></script>
    <script type="text/javascript">
        new function () {
            var _self = this;
            _self.width = 640; //设置默认最大宽度
            _self.fontSize = 100; //默认字体大小
            _self.widthProportion = function () {
                var p = (document.body && document.body.clientWidth || document.getElementsByTagName("html")[0].offsetWidth) / _self.width;
                return p > 1 ? 1 : p < 0.5 ? 0.5 : p;
            };
            _self.changePage = function () {
                document.getElementsByTagName("html")[0].setAttribute("style", "font-size:" + _self.widthProportion() * _self.fontSize + "px !important");
            }
            _self.changePage();
            window.addEventListener('resize', function () {
                _self.changePage();
            }, false);
        };
        $(function () {
            $('.search_bef').click(function () {
                $(this).css('display', 'none');
                $('.search_aft').css('display', 'block');
                $('.inp-txt').focus();
            })
            $('.cancel-btn').click(function () {
                $('.search_bef').css('display', 'block');
                $('.search_aft').css('display', 'none').find('input').val("");
            })
        })
    </script>
    <style>
        .mark-read {
            position: absolute;
            top: 0;
            left: 0;
            width: 0.38rem;
        }
        
        .time {
            float: right;
            color: #999;
            font-size: 0.24rem;
        }
        
        .highlilst-p2 {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 220px;
        }
        
        .title {
            width: 180px;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .oneopenframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            -webkit-overflow-scrolling: touch;
            overflow-y: scroll;
            background: white;
        }
    </style>
    <link rel="stylesheet" href="../plugin/mescroll/mescroll.min.css">
</head>
<body>
<div class="out-container">
    <div class="search-school" id="search-box" style="margin:0;padding:0.2rem 0 0.2rem 0 ;border: 0;border-bottom: 1px solid #E8E8E8;">
        <div class="search-txt" style="width: 80%;display: inline-block;vertical-align: middle;">
            <div class="search_bef" style="text-align: center; display: block;">
                <img src="../images/search.png"><span>请输入通知内容关键字</span>
            </div>
            <div class="search_aft" style="display: none;">
                <p style="width: 88%;">
                    <i><img src="../images/search.png"></i>
                    <input type="text" placeholder="请输入通知内容关键字" class="inp-txt">
                </p>
                <a class="cancel-btn">取消</a>
            </div>
        </div>
        <span class="mark-stat"></span>
    </div>
    <div id="wrapper" class="mescroll" style="position:absolute;top: 59px;bottom: 0;height: auto;">
        <div id="thelist"></div>
    </div>
</div>
<script type="text/javascript" src="../plugin/js/jquery.cookie.js"></script>
<script data-main="js/highernotice.js" src='../sys/require.min.js'></script>
</body>
</html>
<!--iscrollTest-->
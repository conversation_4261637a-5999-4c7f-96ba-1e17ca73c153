﻿/*****************************************************
*                                                    *
*                     日期处理函数                   *
*                                                    *
*****************************************************/
Date.prototype.toString = function () {
    return getDateString(this);
};

//获取农历信息的方法   根据公历获取农历信息  
// 返回：{week:星期几，lunarmonth：农历中文月，lunarday：农历中文日，lunar：农历年月日，ganzhi：干支，animal：生肖}  
function getLunarObj(date){  
    //农历年信息  
    var lunarInfo = new Array(  
        0x04bd8,0x04ae0,0x0a570,0x054d5,0x0d260,0x0d950,0x16554,0x056a0,0x09ad0,0x055d2,  
        0x04ae0,0x0a5b6,0x0a4d0,0x0d250,0x1d255,0x0b540,0x0d6a0,0x0ada2,0x095b0,0x14977,  
        0x04970,0x0a4b0,0x0b4b5,0x06a50,0x06d40,0x1ab54,0x02b60,0x09570,0x052f2,0x04970,  
        0x06566,0x0d4a0,0x0ea50,0x06e95,0x05ad0,0x02b60,0x186e3,0x092e0,0x1c8d7,0x0c950,  
        0x0d4a0,0x1d8a6,0x0b550,0x056a0,0x1a5b4,0x025d0,0x092d0,0x0d2b2,0x0a950,0x0b557,  
        0x06ca0,0x0b550,0x15355,0x04da0,0x0a5d0,0x14573,0x052d0,0x0a9a8,0x0e950,0x06aa0,  
        0x0aea6,0x0ab50,0x04b60,0x0aae4,0x0a570,0x05260,0x0f263,0x0d950,0x05b57,0x056a0,  
        0x096d0,0x04dd5,0x04ad0,0x0a4d0,0x0d4d4,0x0d250,0x0d558,0x0b540,0x0b5a0,0x195a6,  
        0x095b0,0x049b0,0x0a974,0x0a4b0,0x0b27a,0x06a50,0x06d40,0x0af46,0x0ab60,0x09570,  
        0x04af5,0x04970,0x064b0,0x074a3,0x0ea50,0x06b58,0x055c0,0x0ab60,0x096d5,0x092e0,  
        0x0c960,0x0d954,0x0d4a0,0x0da50,0x07552,0x056a0,0x0abb7,0x025d0,0x092d0,0x0cab5,  
        0x0a950,0x0b4a0,0x0baa4,0x0ad50,0x055d9,0x04ba0,0x0a5b0,0x15176,0x052b0,0x0a930,  
        0x07954,0x06aa0,0x0ad50,0x05b52,0x04b60,0x0a6e6,0x0a4e0,0x0d260,0x0ea65,0x0d530,  
        0x05aa0,0x076a3,0x096d0,0x04bd7,0x04ad0,0x0a4d0,0x1d0b6,0x0d250,0x0d520,0x0dd45,  
        0x0b5a0,0x056d0,0x055b2,0x049b0,0x0a577,0x0a4b0,0x0aa50,0x1b255,0x06d20,0x0ada0);  
    var Animals=new Array("鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪");  
    var Gan=new Array("甲","乙","丙","丁","戊","己","庚","辛","壬","癸");  
    var Zhi=new Array("子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥");  
      
    //==== 传回农历 y年的总天数  
    function lYearDays(y) {  
        var i, sum = 348  
        for(i=0x8000; i>0x8; i>>=1) sum += (lunarInfo[y-1900] & i)? 1: 0  
        return(sum+leapDays(y))  
    }  
    //==== 传回农历 y年闰月的天数  
    function leapDays(y) {  
        if(leapMonth(y))     
            return((lunarInfo[y-1900] & 0x10000)? 30: 29)  
        else   
            return(0)  
    }  
    //==== 传回农历 y年闰哪个月 1-12 , 没闰传回 0  
    function leapMonth(y) {   
        return(lunarInfo[y-1900] & 0xf);  
    }  
    //==== 传回农历 y年m月的总天数  
    function monthDays(y,m) {   
        return((lunarInfo[y-1900] & (0x10000>>m))? 30: 29 );  
    }  
    //==== 算出农历, 传入日期物件, 传回农历日期物件  
    //      该物件属性有 .year .month .day .isLeap .yearCyl .dayCyl .monCyl  
        function lunar(objDate) {  
            var i, leap=0, temp=0  
            var baseDate = new Date(1900,0,31)  
            var offset    = (objDate - baseDate)/86400000  
  
            this.dayCyl = offset + 40  
            this.monCyl = 14  
  
            for(i=1900; i<2050 && offset>0; i++) {  
                temp = lYearDays(i)  
                offset -= temp  
                this.monCyl += 12  
            }  
            if(offset<0) {  
                offset += temp;  
                i--;  
                this.monCyl -= 12  
            }  
  
            this.year = i  
            this.yearCyl = i-1864  
  
            leap = leapMonth(i) //闰哪个月  
            this.isLeap = false  
  
            for(i=1; i<13 && offset>0; i++) {  
                //闰月  
                if(leap>0 && i==(leap+1) && this.isLeap==false)  
                { --i; this.isLeap = true; temp = leapDays(this.year); }  
                else  
                { temp = monthDays(this.year, i); }  
  
                //解除闰月  
                if(this.isLeap==true && i==(leap+1)) this.isLeap = false  
  
                offset -= temp  
                if(this.isLeap == false) this.monCyl ++  
            }  
  
            if(offset==0 && leap>0 && i==leap+1)  
                if(this.isLeap)  
            { this.isLeap = false; }  
            else  
            { this.isLeap = true; --i; --this.monCyl;}  
  
            if(offset<0){ offset += temp; --i; --this.monCyl; }  
  
            this.month = i  
            this.day = offset + 1  
        }  
        //获取农历（月）中文格式  
    function get_lunarmonth(month){  
        var fm = ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","腊月"];  
        return fm[month-1];  
    }  
    //获取农历（日）中文格式  
    function get_lunarday(day){  
        var fd = ["十","一","二","三","四","五","六","七","八","九","十"];  
        if(day <= 10){  
            return "初"+fd[day];  
        }  
        else if(day < 20){  
            return "十"+fd[day-10];  
        }  
        else if(day==20){  
            return "二十";  
        }  
        else if(day < 30){  
            return "廿"+fd[day-20];  
        }  
        else{  
            return "三"+fd[day-30];  
        }  
    }  
      
    //获取干支  
    function get_ganzhi(year) {   
        var num = year-1900+36;  
        return(Gan[num%10]+Zhi[num%12]);   
    }  
    //获取生肖  
    function get_animal(year){  
        return Animals[(year-4)%12];  
    }  
    //获取周  
    function get_week(date){  
        var values = ["日", "一", "二", "三", "四", "五", "六"];  
        date = getDateByStr(date);  
        return values[date.getDay()];  
    }  
      
    var viewdate = {};  
    date = getDateByStr(date);  
    //星期  
    viewdate.week = get_week(date);  
    //农历信息  
    var lunar_obj = new lunar(date);  
    //农历中文月  
    viewdate.lunarmonth =  get_lunarmonth(lunar_obj.month);  
    //农历中文日  
    var lunar_day =  Math.floor(lunar_obj.day);  
    viewdate.lunarday = get_lunarday(lunar_day);  
    //农历年月日  
    viewdate.lunar = lunar_obj.year + "-" + lunar_obj.month + "-" + lunar_day;  
    //干支  
    viewdate.ganzhi = get_ganzhi(lunar_obj.year);  
    //生肖  
    viewdate.animal = get_animal(lunar_obj.year);  
          
    return viewdate;  
}

function getMonthOnebynum(dstr, num) {//根据传的参数num 来加减月 获得月一号
    var arr = getDateArray(dstr);
    var d = new Date(arr[0], Math.floor(arr[1]) + num, "01");
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}
function getDateArray(datestr) {
    var arr = datestr.split(' ')[0].split("-");
    arr[1] = arr[1].replace(/^0(\d)/, "$1");
    arr[2] = arr[2].replace(/^0(\d)/, "$1");
    return [parseInt(arr[0]), parseInt(arr[1]) - 1, parseInt(arr[2])];
};
//根据日期判断周几 周一到周日   1-7表示
function getweek(date) {
    var currentDate = getDateByStr(date);
    var idendity = currentDate.getDay();  //返回值0-6 ,分别表示这个礼拜的星期日到星期六
    if (idendity == 0) {
        idendity = 7;
    }
    return idendity;
}
function getLastDay1(d) {
    return new Date(d.getFullYear(), d.getMonth() + 1, '00');
}
function getFirstDay(d) {//得到第一天
    return new Date(d.getFullYear(), d.getMonth(), '01');
}
//根据日期判断周几 周一到周日   1-7表示
function isholiday(date) {
	var currentDate = getDateByStr(date);
	var idendity = currentDate.getDay();  //返回值0-6 ,分别表示这个礼拜的星期日到星期六
	if (idendity == 0 || idendity == 6) {
	    return true;
	}
	return false;
}
/*
	返回配餐周
*/
function gettrueweek(str){
	var f = getDateByStr(str);
	f = getFirstDay(f);
	var l = getLastDay1(f);
    var d = f.getDay();
    var d1 = (d == 0) ? 2 : ((d != 1) ? 9 - d : d);
    var d2 = l.getDate();
    var rowlen = Math.ceil(((d2 - d1) + 1) / 7);
    var year = f.getFullYear();
    var month = f.getMonth() + 1;
    var m1 = month;
    m1 = m1 < 10 ? '0' + m1 : m1;
    var temp1 = year + "-" + (m1) + "-" + (d1 < 10 ? '0' + d1 : d1);
    var _week = 1;
    for (var i = 1; i <= rowlen; i++) {
        if (i != 1) {
            temp1 = year + "-" + (m1) + "-" + (d1 < 10 ? '0' + d1 : d1);
        }
        if(temp1 == str){
        	_week = i;
        	break;
        }
        for (var j = 1; j < 8; j++) {
            m1 = month;
            m1 = m1 < 10 ? '0' + m1 : m1;
            d1 = d1 + 1;
            d1 = (d1 <= d2) ? d1 : 1;
            month = (d1 == 1) ? month + 1 : month;
        }
    }
    return _week;
}
//获得本周周一日期
function getmonday(date) {
    var currentDate = getDateByStr(date);
    var idendity = currentDate.getDay();  //返回值0-6 ,分别表示这个礼拜的星期日到星期六
    var arr = [6, 0, 1, 2, 3, 4, 5];
    var d = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - arr[idendity]);
    return getStrByDate(d);
}
//根据日期判断是本月第几周
function getMonthWeek(datestr) {
    var arr = datestr.split("-");
    var date = new Date(arr[0], parseInt(arr[1]) - 1, arr[2]), w = date.getDay(), d = date.getDate();
    return Math.ceil((d + 6 - w) / 7);
};
//获得num天后日期
function getnextday(date, num) {
    var currentDate = getDateByStr(date);
    var d = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + num);
    return getStrByDate(d);
}
//获得num天后日期
function getpreday(date, num) {
    var currentDate = getDateByStr(date);
    var d = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - num);
    return getStrByDate(d);
}
/*
功能：日期串转日期类型的变量
*/
function getDateByStr(dstr) {
	if(typeof(dstr) == "object") return dstr;
    var arr = getDateArray(dstr);
    return new Date(arr[0], arr[1], arr[2]);
}
/*
功能：得到格式为2008-8-8或2008年8月8日 日期串
*/
function getDateString(d, isChina) {
    var dstr = d.getFullYear() + ((isChina) ? "年" : "-");
    if ((d.getMonth() + 1) < 10)
        dstr += "0";
    dstr += d.getMonth() + 1;
    dstr += (isChina) ? "月" : "-";
    if (d.getDate() < 10)
        dstr += "0";
    dstr += d.getDate() + ((isChina) ? "日" : "");
    return dstr;
}

/*
功能：参数月最后一天
dt当前月 则0或省略 1表示下一个月 -1表示上一个月
*/
function getLastDay(str, dt) {
    if (!str) return '';
    var arrd = str.split('-');
    var d = new Date(arrd[0], Math.floor(arrd[1]) + (dt || 0), "00"); //当月最后一天
    return getStrByDate(d);
}

/*
功能：得到日期字符串
*/
function getStrByDate(d) {
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}

/*
功能 得到两个日期的差值 
interval：y表示得到年差；m表示得到月差；d表示得到天差  ,noabs表示不取绝对值
*/
function DateDiff(interval, date1, date2, noabs) {
    var y1 = date1.getFullYear();
    var y2 = date2.getFullYear();
    var m1 = date1.getMonth();
    var m2 = date2.getMonth();
    var d1 = date1.getDate();
    var d2 = date2.getDate();
    var parse = function (s) {
        return Date.parse(s.toString().replace(/-/g, '/'));
    };
    switch (interval) {
        case "y":
            return noabs ? y2 - y1 : Math.abs(y2 - y1);
        case "m":
        	if(noabs){
        		return (y2 == y1) ? m2 - m1 : (y2 - y1) * 12 + (m2 - m1);
        	}else{
        		return (y2 == y1) ? Math.abs(m2 - m1) : Math.abs((y2 - y1) * 12 + (m2 - m1));
        	}
        case "d":
            var step = 1000 * 60 * 60 * 24;
        	if(noabs){
                return Math.floor((parse(date2) - parse(date1)) / step);
        	}else{
                return Math.floor(Math.abs(parse(date2) - parse(date1)) / step);
        	}
    }
}
/*
 * 比较连个字符串时间前后 a,b两个时间字符串  a<b返回false，a>=b返回true
 */
function DateCompare(a, b) {
    var arr = getDateArray(a);
    var starttime = new Date(arr[0], arr[1], arr[2]);
    var starttimes = starttime.getTime();
    var arrs = getDateArray(b);
    var lktime = new Date(arrs[0], arrs[1], arrs[2]);
    var lktimes = lktime.getTime();
    if (starttimes >= lktimes) 
        return true;
    else
        return false;
}
/*
 * 获得n天、或n个月、或n年前/后的日期时间，间隔n（n取负数表示n天/月/年前）， 参数：strdate日期，timeUnit取值"d"、"m"、"y"分别表示天、月、年，ishms是否带时分秒
 */
function initDefaultDate(strdate,n,timeUnit, ishms) {  
	strdate = getDateByStr(strdate);
    var curr_date = strdate || new Date();  
    if (timeUnit === 'd') {  
    	curr_date.setDate(curr_date.getDate() + n);  
    } else if (timeUnit === 'm') {  
        curr_date.setMonth(curr_date.getMonth() + n);  
    } else if (timeUnit === 'y') {  
        curr_date.setFullYear(curr_date.getFullYear() + n);  
    }  
    var strYear = curr_date.getFullYear();  
    var strMonth = curr_date.getMonth()+1;  
    var strDay = curr_date.getDate();  
    var strHours = curr_date.getHours();  
    var strMinutes = curr_date.getMinutes();  
    var datastr = strYear + '-' + formatNumber(strMonth) + '-'  
        + formatNumber(strDay) + (ishms ? ' '+ formatNumber(strHours) + ':' + formatNumber(strMinutes) : "");  
    return datastr;  
}
/*
 * 处理日期自动补0，如1=01。
 */
function formatNumber(value){    
    return (value < 10 ? '0' : '') + value;    
} 

function getPreviousDate(dstr) {
    var arr = getDateArray(dstr);
    var d = new Date(arr[0], arr[1], arr[2] - 1);
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}
function getNextDate(dstr) {
    var arr = getDateArray(dstr);
    var d = new Date(arr[0], arr[1], arr[2] + 1);
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}
function getNextMonth(dstr) {//得到下一个月的今天
    var arr = getDateArray(dstr);
    var d = new Date(arr[0], Math.floor(arr[1]) + 1, arr[2]);
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}
function getNextMonthOne(dstr) {//得到下一个月一号
    var arr = getDateArray(dstr);
    var d = new Date(arr[0], Math.floor(arr[1]) + 1, "01");
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}
function getSevenLaterDate(objdate) {
    return new Date(objdate.getFullYear(), objdate.getMonth(), objdate.getDate() + 6);
}
function getDateByLen(objdate, len) {
    return new Date(objdate.getFullYear(), objdate.getMonth(), objdate.getDate() + parseInt(len));
}
function getDateByMLen(objdate, len) {

    return new Date(objdate.getFullYear(), objdate.getMonth() + parseInt(len), objdate.getDate());
}
function getCurrentDate() {
    return getDateString(new Date());
}
function getCurrentTime() {
    var d = new Date();
    var s = "";
    s += d.getHours();
    s += ":";
    if (d.getMinutes() < 10)
        s += "0" + d.getMinutes();
    else
        s += d.getMinutes();
    s += ":";
    if (d.getSeconds() < 10)
        s += "0" + d.getSeconds();
    else
        s += d.getSeconds();

    return s;
};
/*
功能：根据2010-1-1 得到2010-01-01格式
*/
function getdoubledate(d) {
    var arrdate = getDateArray(d);
    if (arrdate[1] < 9)
        arrdate[1] = '0' + (arrdate[1] + 1);
    else
        arrdate[1] = arrdate[1] + 1;
    if (arrdate[2] < 10)
        arrdate[2] = '0' + arrdate[2];
    return arrdate.join('-');
};

/*
功能：根据出生日期strBirthday 得到格式（2.01）的年龄   ischina是否显示汉字（2岁1月）
参数都是字符串格式都是2008-08-08
d2不传时为当前日期2002-11-21
*/
function GetAge(strBirthday, d2, ischina) {
    if (!strBirthday) return "";
    var dtBeginTime = getDateByStr(strBirthday);
    // var time = new Date(parent.objdata.arrtime[0], parent.objdata.arrtime[1] * 1 - 1, parent.objdata.arrtime[2]);
    // var nowDate = d2 ? getDateByStr(d2) : time;
    var nowDate = getDateByStr(d2);
    var nowYear = nowDate.getFullYear();
    var birYear = dtBeginTime.getFullYear();
    var nowMonth = nowDate.getMonth() + 1;
    var birMonth = dtBeginTime.getMonth() + 1;
    var nowDate = nowDate.getDate();
    var birDate = dtBeginTime.getDate();
    var ageMonth;
    var ageYear;
    if (birMonth > nowMonth) {
        nowYear -= 1;
        ageMonth = 12 - birMonth + nowMonth;
    }
    else
        ageMonth = nowMonth - birMonth;
    ageYear = nowYear - birYear;
    if (nowDate < birDate)
        ageMonth--;
    if (ageMonth < 0) {
        ageYear--;
        ageMonth = 11;
    }
    if (parseInt(ageMonth) == 0){
        return ischina ? ageYear + "年" : ageYear + ".00";
    }else {
        if (ageMonth < 10)
            ageMonth = "0" + ageMonth;
        //        else if (ageMonth == 10)
        //            ageMonth = "1";
        return ischina ? ageYear + "岁" + parseInt(ageMonth) + "月": ageYear + "." + ageMonth;
    }
}
/*
功能：得到默认人群的平均年龄传入和当前时间相差天数
*/
function GetAgeDefault(dayno) {
    if (!parent.objdata.arrtime) return 0;
    var time = new Date(parent.objdata.arrtime[0], parent.objdata.arrtime[1] * 1 - 1, parent.objdata.arrtime[2], parent.objdata.arrtime[3], parent.objdata.arrtime[4], parent.objdata.arrtime[5])
    var curdate = getDateByLen(time, -dayno).toString();
    return GetAge(curdate);
};
function checkIsDate(obj) {
    var v = obj.trim();
    if (v == "") return false;
    if (v.length > 10 || !isDate(v)) {
        obj = "";
        return false;
    } else {
        return true;
    }
}
/*
 * 判断是否是日格式（例如2012-2-3或2012/2/3）
 */
function isDate(str)
{
    if(!/^(\d{1,4})(-|\/)(\d{1,2})(-|\/)(\d{1,2})$/.test(str))
        return false;
    var year = RegExp.$1-0;
    var month = RegExp.$3-1;
    var date = RegExp.$5-0;
    var obj = new Date(year,month,date);
    return !!(obj.getFullYear()==year && obj.getMonth()==month && obj.getDate()==date);
}
// function isDate(s) {
//     //    var re=new RegExp("(((19)|(20))[0-9][0-9])[-,/](1[0-2]|0?[1-9])[-,/](3[0,1]|[1,2][0-9]|0?[1-9])");
//     return s.length > 10 ? false : s.match(new RegExp("(((19)|(20))[0-9][0-9])[-](1[0-2]|0+[1-9])[-]((3[0,1])|([1,2][0-9])|(0?[1-9]))"));
// }
/*
功能：得到某年某月去掉周末的天数
*/
function getDayNumByMonth(y, m) {
    var int1 = 0;
    var d = new Date(y, m - 1, "01");
    while (d.getMonth() == m - 1) {
        var w = d.getDay();
        if (w != 0 && w != 6)
            int1++;
        d = new Date(d.getFullYear(), d.getMonth(), d.getDate() + 1);
    }
    return int1;
}
function checkIsBeforeNow(obj) {
    var v = obj.trim();
    if (v == "") return false;
    if (v.length <= 10 && isDate(v)) {
        var time = new Date(parent.objdata.arrtime[0], parent.objdata.arrtime[1] * 1 - 1, parent.objdata.arrtime[2]);
        if (getDateString(time) >= v) {
            return true;
        }
        else {
            return false;
            //            dia.showMsg("选择日期不能在当前日期之后!");
        }
    }
    else {
        //        dia.showMsg("请输入正确的日期!");
        return false;
    }
};
/*
功能：得到对应日期在本年中的第几周
*/
function getYearWeek(strdate) {
    var arr = strdate.split('-');
    var d1 = new Date(arr[0], Math.floor(arr[1]) - 1, arr[2]), d2 = new Date(arr[0], 0, 1),
    d = Math.round((d1 - d2) / 86400000);
    var dd = Math.ceil((d + ((d2.getDay() + 1))) / 7);
    //1月1日是周五或六为上一年的最后一周 其他为第一周
    if (d2.getDay() <= 4) {
        //12月28是周日 29 30 31
        if (arr[1] == "12" && ((arr[2] == "28" && d1.getDay() == 0) || (arr[2] == "29" && d1.getDay() <= 1) || (arr[2] == "30" && d1.getDay() <= 2) || (arr[2] == "31" && d1.getDay() <= 3))) {
            return 1;
        }
        return dd;
    } else {
        //第一周的取上一年的最后一周         
        return dd == 1 ? getYearWeek((Math.floor(arr[0]) - 1) + '-12-31') : dd - 1;
    }
};

/*****************************************************
*                                                    *
*                     小数处理函数                   *
*                                                    *
*****************************************************/
/*
功能：保留小数点后一位 非零进一
*/
function getonevalue(value) {
    value = (value == "" || isNaN(value)) ? "" : parseFloat(value).toFixed(2);
    if (value != "") {
        var vtemp = value.split('.')[1];
        if (vtemp.substring(1, 2) != "0") {
            var addedvalue = parseInt(vtemp.substring(0, 1)) + 1;
            if (addedvalue < 10)
                value = value.split('.')[0] + "." + (parseInt(vtemp.substring(0, 1)) + 1);
            else
                value = (parseInt(value.split('.')[0]) + 1) + ".0";
        }
        else
            value = value.split('.')[0] + "." + (parseInt(vtemp.substring(0, 1)));
    }
    return value;
};
/*
功能：小数点保留一位且小数部分只能是5或0
*/
function getfzvalue(value) {
    if (value == "" || isNaN(value))
        return "";
    value = parseFloat(value).toFixed(1);
    var vtemp = value.split('.')[1];
    if (parseInt(vtemp) > 2 && parseInt(vtemp) < 8)
        value = (value.split('.')[0]) + ".5";
    else if (parseInt(vtemp) < 3)
        value = (value.split('.')[0]) + ".0";
    else
        value = (parseInt(value.split('.')[0]) + 1) + ".0";
    return value;
};

/*
功能：小数点数字后末尾只能是5或0。value值, f小数点后保留个数
*/
function getfzval(value, f) {
    if (value == "" || isNaN(value) && f < 0)
        return "";
    f = f || 2;
    value = parseFloat(value).toFixed(f);
    var vtemp = value.split('.')[1], lastnum = 0;
    if(vtemp){
        lastnum = vtemp.substring(f - 1);
        value = value.substring(0, value.length - 1);
    }
    if (parseInt(lastnum) > 2 && parseInt(lastnum) < 8){
        value = value + "5";
    } else if (parseInt(lastnum) < 3){
        value = value + "0";
    }else{
        if(f > 1){
            var ft = "0.";
            for (var i = 0; i < f - 2; i++) {
                ft += "0";
            }
            ft += "1";
            value = (parseFloat(value) + parseFloat(ft || 0)).toFixed(f - 1) + "0";
        }else if(f == 1){
            value = (parseFloat(value) + ".0");
        }
    }
    return value;
};
//检查是否为正整数
function isUnsignedInteger(strInteger, arrrange) {
    var newPar = /^(-|\+)?\d+$/;
    if (arrrange) {
        if (newPar.test(strInteger)) {
            if (strInteger <= arrrange[1] && strInteger >= arrrange[0])
                return true;
            else
                return false;
        } else
            return false;
    } else
        return newPar.test(strInteger);
};
/*
功能：转化为整型
*/
function parseToint(v) {
    if (v == "")
        return 0;
    else
        return parseInt(parseFloat(v));
}
/*
功能：保存1位精度
*/
function getOneFloat(v) {
    var ret = parseFloat((v + "").trim()).toFixed(1);
    if (ret == null || isNaN(ret) == true)
        return 0;
    //    if(ret.indexOf(".0")>0)
    //        return Math.round(ret);
    return ret;
};
/*
功能：保存两位(或num位)精度 
*/
function getFloatV(v, num) {
	if(v === "") return 0;
	var ret = parseFloat((v + "").trim()).toFixed((num) ? num : 2);
    if (ret == null || isNaN(ret) == true)
        return 0;
    return ret;
}
/*
功能：保存两位(或num位)精度 
*/
function getFloatValue(v, num) {
	if(v === "") return "";
	num = num || 2;
    var ret = parseFloat((v + "").trim()).toFixed(num);
    if (ret == null || isNaN(ret) == true)
        return 0;
    var reg = new RegExp("\\.0{" + num + "}$");
    return ret.replace(reg, "");
}
/**
 * 处理小数,去掉末尾0
 * @param v
 * @param num
 * @return {*}
 */
function getFloatNum(v, num){
    if(v === "") return "";
    num = num || 2;
    var ret = parseFloat((v + "").trim()).toFixed(num);
    if (ret == null || isNaN(ret) == true)
        return 0;
    return ret.match(/.*[1-9]/)[0] ;
}
/*
功能：格式化金额，返回的是字符串。  1.00返回1,1,1.30返回1.30。
v，需要格式化的值；defaultValue，当v是非数字是返回defaultValue；num，需要格式化的小数位。
*/
function formatFloatValue(v, defaultValue, num) {
    num = num || 2;
    var ret = parseFloat($.trim(v + "")).toFixed(num);
    if (!ret || isNaN(ret))
        return defaultValue == null || defaultValue == undefined ? 0 : defaultValue;
    var reg = new RegExp("\\.0{" + num + "}$");
    return ret.replace(reg, "");
}
function getFloat(v) {
    var ret = parseFloat((v + "").trim());
    if (ret == null || isNaN(ret) == true)
        return 0;
    return ret;
}
/* 
功能：转换String到Float,保存两位精度 
*/
function getMoneyValue(str) {
    return getFloatValue(str.replace(/,/g, ''));
}
/*
功能：根据年龄字符串得到对应的月数
*/
function getAgeMonth(str) {
    if (!str)
        return 0;
    var arrAge = str.toString().split(".");
    if (arrAge.length == 1)
        return parseInt(arrAge[0]) * 12;
    else {
        if (arrAge[1] == "1")
            return parseInt(arrAge[0]) * 12 + 10;
        else
            return parseInt(arrAge[0]) * 12 + Math.floor(arrAge[1]);
    }
};
/*
功能：根据传入的数字返回3位  传1返回001
*/
function thrno(v) {
    if (v < 9) {
        return '00' + (v + 1);
    } else if (v < 99 && v >= 10)
        return '0' + (v + 1);
    else
        return (v + 1);
};
/*
功能：在数组中获取指定值的元素索引
*/
function getinbyvalue(arr, value) {
    var index = -1;
    for (var i = 0; i < arr.length; i++) {
        if (arr[i][0] == value) {
            index = i;
            break;
        }
    }
    return index;
};
/*
功能：得到比例格式
flag为true表示 c2:c1 否则c1:c2
*/
function getBili(a1, a2, flag) {
    var c1 = a1;
    var c2 = a2;
    if (c1 == 0 || c2 == 0)
        return flag == true ? c2 + ":" + c1 : c1 + ":" + c2;
    else if (c1 == 0 && c2 == 0)
        return "1:1";
    //    for (var i = 2; i <= a1; i++) {
    //        var b1 = a1 / i;
    //        var b2 = a2 / i;
    //        if (b1 == parseInt(b1)) {
    //            if (b2 == parseInt(b2)) {
    //                c1 = b1;
    //                c2 = b2;
    //            }
    //        }
    //    }
    return (flag) ? c2 + ":" + c1 : c1 + ":" + c2;
}

/*****************************************************
*                                                    *
*                     扩展Array方法                      *
*                                                    *
*****************************************************/
Array.prototype.indexOf = function (o) {
    for (var i = 0; i < this.length; i++) {
        if (this[i] == o)
            return i;
    }
    return -1;
};
Array.prototype.lastIndexOf = function (o) {
    for (var i = this.length - 1; i >= 0; i--) {
        if (this[i] == o)
            return i;
    }
    return -1;
};
Array.prototype.contains = function (o) {
    return this.indexOf(o) != -1;
};
Array.prototype.copy = function (o) {
    return this.concat();
};
Array.prototype.insertAt = function (o, i) {
    this.splice(i, 0, o);
};
Array.prototype.insertBefore = function (o, o2) {
    var i = this.indexOf(o2);
    if (i == -1)
        this.push(o);
    else
        this.splice(i, 0, o);
};
Array.prototype.removeAt = function (i) {
    this.splice(i, 1);
};
Array.prototype.remove = function (o) {
    var i = this.indexOf(o); if (i != -1)
        this.splice(i, 1);
};
/*
*　方法:Array.remove(dx)
*　功能:删除数组元素.
*　参数:dx删除元素的下标.
*　返回:在原数组上修改数组
*/
//经常用的是通过遍历,重构数组.
Array.prototype.botremove = function (dx) {
    if (isNaN(dx) || dx > this.length) { return false; }
    for (var i = 0, n = 0; i < this.length; i++) {
        if (this[i] != this[dx]) {
            this[n++] = this[i]
        }
    }
    this.length -= 1
};
//在数组中获取指定值的元素索引
Array.prototype.getIndexByValue = function (value) {
    var index = -1;
    for (var i = 0; i < this.length; i++) {
        if (this[i][0] == value) {
            index = i;
            break;
        }
    }
    return index;
};
Array.prototype.clear = function () {
    this.splice(0, this.length);
};
Array.prototype.previousItem = function (o) {
    var i = this.indexOf(o);
    if (i > 0)
        return this[i - 1];
    return null;
};
Array.prototype.nextItem = function (o) {
    var i = this.indexOf(o);
    if ((i + 1) < this.length)
        return this[i + 1];
    return null;
};
/*
移除数组对应值通用方法
arr:对应数组
value：要移除的值
index:2维数组时对应值序号
修改：操作树数组出错，改为返回数组方法
*/
function removeArr(arr, value, index) {
    var len = arr.length;
    var arrtemp = [];
    for (var j = 0; j < len; j++) {
        if (index || index == 0) {
            if (arr[j][index] == value) {
                continue;
            }
        } else {
            if (arr[j] == value) {
                continue;
            }
        }
        arrtemp.push(arr[j]);
    }
    arr = arrtemp;
    return arrtemp;
}
/*****************************************************
*                                                    *
*                     扩展String方法                 *
*                                                    *
*****************************************************/
String.prototype.trim = function () {
    return this.replace(/(^\s+)|\s+$/g, "");
};
/*
功能：将金钱转换成大写的金钱
*/
function convertToChinNum(number) {
    var number = new String(Math.round(number * 100)); // 数字金额
    var chineseValue = "";           // 转换后的汉字金额
    var String1 = "零壹贰叁肆伍陆柒捌玖";        // 汉字数字
    var String2 = "万仟佰拾亿仟佰拾万仟佰拾元角分";      // 对应单位
    var len = number.length;          // number 的字符串长度
    var Ch1;              // 数字的汉语读法
    var Ch2;              // 数字位的汉字读法
    var nZero = 0;             // 用来计算连续的零值的个数
    var String3;             // 指定位置的数值
    if (len > 15) {
        return "超出计算范围";
    }
    if (number == 0) {
        chineseValue = "零元整";
        return chineseValue;
    }
    String2 = String2.substr(String2.length - len, len); // 取出对应位数的STRING2的值
    for (var i = 0; i < len; i++) {
        String3 = parseInt(number.substr(i, 1), 10); // 取出需转换的某一位的值
        if (i != (len - 3) && i != (len - 7) && i != (len - 11) && i != (len - 15)) {
            if (String3 == 0) {
                Ch1 = "";
                Ch2 = "";
                nZero = nZero + 1;
            } else if (String3 != 0 && nZero != 0) {
                Ch1 = "零" + String1.substr(String3, 1);
                Ch2 = String2.substr(i, 1);
                nZero = 0;
            } else {
                Ch1 = String1.substr(String3, 1);
                Ch2 = String2.substr(i, 1);
                nZero = 0;
            }
        } else {// 该位是万亿，亿，万，元位等关键位
            if (String3 != 0 && nZero != 0) {
                Ch1 = "零" + String1.substr(String3, 1);
                Ch2 = String2.substr(i, 1);
                nZero = 0;
            } else if (String3 != 0 && nZero == 0) {
                Ch1 = String1.substr(String3, 1);
                Ch2 = String2.substr(i, 1);
                nZero = 0;
            } else if (String3 == 0 && nZero >= 3) {
                Ch1 = "";
                Ch2 = "";
                nZero = nZero + 1;
            } else {
                Ch1 = "";
                Ch2 = String2.substr(i, 1);
                nZero = nZero + 1;
            }
            if (i == (len - 11) || i == (len - 3)) {// 如果该位是亿位或元位，则必须写上
                Ch2 = String2.substr(i, 1);
            }
        }
        chineseValue = chineseValue + Ch1 + Ch2;
    }
    if (String3 == 0) {// 最后一位（分）为0时，加上“整”
        chineseValue = chineseValue + "整";
    }
    return chineseValue;
};
/*
 * 功能：从chosen数组得到chosen对象
 */
function getobjfromarr(arr) {
    var obj = {};
    for (var i = 0; i < arr.length; i++) {
        obj[arr[i][1]] = arr[i][0];
    }
    return obj;
}

/*
 * 获取随机颜色
 */
function getRandomColor() 
{ 
	var c = '#';
	var cArray = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F']; 
	for(var i = 0; i < 2;i++) 
	{ 
		var cIndex = Math.round(Math.random()*15); 
		c += cArray[cIndex]; 
	} 
	return c + "0000"; 
}

/*
 功能：判断对象是否为空
 */
function isEmpty(obj) {
    for (var name in obj) {
        return false;
    }
    return true;
};
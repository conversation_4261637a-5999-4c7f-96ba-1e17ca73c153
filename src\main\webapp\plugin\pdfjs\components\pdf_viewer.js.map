{"version": 3, "file": "pdf_viewer.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;;;;;;;ACVA;;AAAA;;AAAA;;AAmCA,6BAA6B;AAI3BA,EAAAA,WAAAA,CAAY;AAAA;AAAA;AAAA;AAAA;AAKVC,IAAAA,iBAAAA,GALU;AAMVC,IAAAA,kBAAAA,GANU;AAOVC,IAAAA,sBAAAA,GAPU;AAQVC,IAAAA,IAAAA,GARU;AASVC,IAAAA,eAAAA,GATU;AAUVC,IAAAA,mBAAAA,GAVU;AAWVC,IAAAA,UAAAA,GAXFP;AAAY,GAAZA,EAYG;AACD,mBADC,OACD;AACA,mBAFC,OAED;AACA,uBAHC,WAGD;AACA,2BAJC,eAID;AACA,8BALC,kBAKD;AACA,kCANC,sBAMD;AACA,gBAPC,IAOD;AACA,6BARC,iBAQD;AACA,2BATC,eASD;AACA,gCAVC,mBAUD;AACA,uBAXC,UAWD;AAEA,eAbC,IAaD;AACA,sBAdC,KAcD;AA9ByB;;AAuC3BQ,EAAAA,MAAAA,CAAAA,QAAAA,EAAiBC,MAAAA,GAAjBD,SAAAA,EAAqC;AACnC,WAAO,YAAY,CACjB,4BAA4B;AADX;AACW,KAA5B,CADiB,EAEjB,KAFiB,qBAAZ,OAGC,CAAC,cAAcE,YAAAA,GAAf,KAAC,CAAD,KAAyC;AAC/C,UAAI,KAAJ,YAAqB;AAAA;AAD0B;;AAI/C,UAAIC,WAAAA,CAAAA,MAAAA,KAAJ,GAA8B;AAAA;AAJiB;;AAQ/C,YAAMC,UAAAA,GAAa;AACjBC,QAAAA,QAAAA,EAAUA,QAAAA,CAAAA,KAAAA,CAAe;AAAEC,UAAAA,QAAAA,EADV;AACQ,SAAfD,CADO;AAEjBE,QAAAA,GAAAA,EAAK,KAFY;AAAA;AAIjBC,QAAAA,IAAAA,EAAM,KAJW;AAKjBd,QAAAA,kBAAAA,EAAoB,KALH;AAMjBC,QAAAA,sBAAAA,EAAwB,KANP;AAOjBc,QAAAA,WAAAA,EAAa,KAPI;AAQjBC,QAAAA,eAAAA,EAAiB,KARA;AASjBjB,QAAAA,iBAAAA,EAAmB,KATF;AAUjBI,QAAAA,eAAAA,EAAiB,KAVA;AAAA;AAYjBE,QAAAA,UAAAA,EAAY,KAZK;AAAA,OAAnB;;AAeA,UAAI,KAAJ,KAAc;AAGZY,kCAAAA,MAAAA,CAHY,UAGZA;AAHF,aAIO;AAGL,mBAAWC,QAAAA,CAAAA,aAAAA,CAHN,KAGMA,CAAX;AACA,6BAJK,iBAIL;AACA,iCAAyB,KALpB,GAKL;AACAR,QAAAA,UAAAA,CAAAA,GAAAA,GAAiB,KANZ,GAMLA;;AAEAO,kCAAAA,MAAAA,CARK,UAQLA;;AACA,4BAAoB,KATf,GASL;AApC6C;AAJd,KAC5B,CAAP;AAxCyB;;AAoF3BE,EAAAA,MAAAA,GAAS;AACP,sBADO,IACP;AArFyB;;AAwF3BC,EAAAA,IAAAA,GAAO;AACL,QAAI,CAAC,KAAL,KAAe;AAAA;AADV;;AAIL,sBAJK,IAIL;AA5FyB;;AAAA;;;;AAmG7B,oCAAoC;AAclCC,EAAAA,4BAAAA,CAAAA,OAAAA,EAAAA,OAAAA,EAGEtB,iBAAAA,GAHFsB,IAAAA,EAIErB,kBAAAA,GAJFqB,EAAAA,EAKEpB,sBAAAA,GALFoB,IAAAA,EAMEnB,IAAAA,GANFmB,oBAAAA,EAOElB,eAAAA,GAPFkB,KAAAA,EAQEjB,mBAAAA,GARFiB,IAAAA,EASEhB,UAAAA,GATFgB,IAAAA,EAUE;AACA,WAAO,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAKhCN,MAAAA,WAAAA,EAAa,IALmB,mCAKnB,EALmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAA3B,CAAP;AAzBgC;;AAAA;;;;;;;;ACtIpC;;AAkBA,IAlBA,QAkBA;;AACA,IAAI,iCAAiCO,MAAAA,CAArC,sBAAqCA,CAArC,EAAqE;AACnEC,EAAAA,QAAAA,GAAWD,MAAAA,CADwD,sBACxDA,CAAXC;AADF,OAEO;AACLA,EAAAA,QAAAA,GAAWC,OAAAA,CADN,iBACMA,CAAXD;AAtBF;;AAwBAE,MAAAA,CAAAA,OAAAA,GAAAA,QAAAA;;;;;;;;;;;;;;ACNA,MAAMC,oBAAAA,GAAuB;AAC3BC,EAAAA,QAAAA,EAD2B;AAE3BC,EAAAA,aAAAA,EAF2B;AAI3BC,EAAAA,sBAAAA,EAJ2B;AAK3BC,EAAAA,sBAAAA,EAL2B;AAM3BC,EAAAA,+BAAAA,EAN2B;AAO3BC,EAAAA,yCAAAA,EAP2B;AAQ3BC,EAAAA,8CAAAA,EAR2B;AAS3BC,EAAAA,kDAAAA,EAT2B;AAU3BC,EAAAA,mDAAAA,EAV2B;AAW3BC,EAAAA,qCAAAA,EAX2B;AAY3BC,EAAAA,qCAAAA,EAZ2B;AAa3BC,EAAAA,yCAAAA,EAb2B;AAc3BC,EAAAA,wCAAAA,EAd2B;AAe3BC,EAAAA,8CAAAA,EAf2B;AAiB3BC,EAAAA,mDAAAA,EAjB2B;AAmB3BC,EAAAA,kCAAAA,EAnB2B;AAoB3BC,EAAAA,iCAAAA,EApB2B;AAsB3BC,EAAAA,sBAAAA,EAtB2B;AAwB3B,0BAxB2B;AAyB3B,wCAzB2B;AA4B3BC,EAAAA,iBAAAA,EA5B2B;AA6B3BC,EAAAA,aAAAA,EA7B2B;AA8B3BC,EAAAA,gBAAAA,EA9B2B;AA+B3BC,EAAAA,iBAAAA,EA/B2B;AAiC3BC,EAAAA,gBAAAA,EAjC2B;AAkC3BC,EAAAA,mBAAAA,EAlC2B;AAmC3B,2BAnC2B;AAoC3B,6BApC2B;AAqC3B,iCArC2B;AAsC3B,mCAtC2B;AAuC3BC,EAAAA,cAAAA,EAvC2B;AAyC3BC,EAAAA,kBAAAA,EAzC2B;AA0C3BC,EAAAA,aAAAA,EA1C2B;AA2C3BC,EAAAA,WAAAA,EA3C2B;AA4C3BC,EAAAA,UAAAA,EA5C2B;AA6C3BC,EAAAA,UAAAA,EA7C2B;AA8C3BC,EAAAA,eAAAA,EA9C2B;AAgD3BC,EAAAA,gBAAAA,EAhD2B;AAiD3BC,EAAAA,cAAAA,EAjD2B;AAkD3BC,EAAAA,eAAAA,EAlD2B;AAmD3BC,EAAAA,iBAAAA,EAnD2B;AAoD3BC,EAAAA,kBAAAA,EApD2B;AAsD3BC,EAAAA,OAAAA,EAtD2B;AAuD3BC,EAAAA,aAAAA,EAvD2B;AAwD3BC,EAAAA,kBAAAA,EAxD2B;AAyD3BC,EAAAA,kBAAAA,EAzD2B;AA0D3BC,EAAAA,yBAAAA,EA1D2B;AA4D3BC,EAAAA,sBAAAA,EA5D2B;AA8D3BC,EAAAA,kBAAAA,EA9D2B;AA+D3BC,EAAAA,kBAAAA,EA/D2B;AAAA,CAA7B;;AAmEA,oCAAoC;AAClC;AACE;AACEC,MAAAA,GAAAA,GAAM,oBAAoBC,IAAAA,CAAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,GAApB,OADR,GACED;AAFJ;;AAIE;AACEA,MAAAA,GAAAA,GAAM,0BAA0BC,IAAAA,CAAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,GAA1B,OADR,GACED;AALJ;AAAA;;AAQA,SAAO7C,oBAAAA,CAAAA,GAAAA,CAAAA,IAT2B,EASlC;AA9FF;;AAiGA,MAAM+C,kBAAAA,GAAqB;AACzBC,EAAAA,EAAAA,EADyB;AAEzBC,EAAAA,EAAAA,EAFyB;AAGzBC,EAAAA,EAAAA,EAHyB;AAIzBC,EAAAA,EAAAA,EAJyB;AAKzBC,EAAAA,EAAAA,EALyB;AAMzBC,EAAAA,EAAAA,EANyB;AAOzBC,EAAAA,EAAAA,EAPyB;AAQzBC,EAAAA,EAAAA,EARyB;AASzBC,EAAAA,EAAAA,EATyB;AAUzBC,EAAAA,EAAAA,EAVyB;AAWzBC,EAAAA,EAAAA,EAXyB;AAYzBC,EAAAA,EAAAA,EAZyB;AAazBC,EAAAA,EAAAA,EAbyB;AAczBC,EAAAA,EAAAA,EAdyB;AAAA,CAA3B;;AAkBA,iCAAiC;AAC/B,SAAOd,kBAAAA,CAAmBe,QAAAA,EAAnBf,WAAmBe,EAAnBf,CAAAA,IADwB,QAC/B;AApHF;;AAwHA,qCAAqC;AACnC,MAAI,CAAJ,MAAW;AACT,WADS,IACT;AAFiC;;AAInC,SAAO,qCAAqC,eAAe;AACzD,WAAOgB,IAAAA,IAAAA,IAAAA,GAAejB,IAAAA,CAAfiB,IAAejB,CAAfiB,GAA4B,cADsB,IACzD;AALiC,GAI5B,CAAP;AA5HF;;AAqIA,MAAMC,QAAAA,GAAW;AACf,sBAAoB;AAClB,WADkB,OAClB;AAFa;;AAKf,uBAAqB;AACnB,WADmB,KACnB;AANa;;AASf,iBAAelB,IAAAA,GAAf,MAA4BmB,QAAAA,GAAWC,eAAAA,CAAAA,GAAAA,EAAvC,IAAuCA,CAAvC,EAAmE;AACjE,WAAOC,eAAAA,CAAAA,QAAAA,EAD0D,IAC1DA,CAAP;AAVa;;AAaf,2BAAyB,CAbV;;AAAA,CAAjB;;;;;;;;;;;;;;ACrIA;;AAmCA,qBAAqB;AAInB/F,EAAAA,WAAAA,CAAY;AAAA;AAEVgG,IAAAA,kBAAAA,GAFU;AAGVC,IAAAA,eAAAA,GAHU;AAIVC,IAAAA,qBAAAA,GAJU;AAAA,MAAZlG,EAAAA,EAKQ;AACN,oBADM,QACN;AACA,8BAFM,kBAEN;AACA,2BAHM,eAGN;AACA,+BAJM,IAIN;AACA,kCALM,qBAKN;AAEA,mBAPM,IAON;AACA,uBARM,IAQN;AACA,qBATM,IASN;AACA,sBAVM,IAUN;AAEA,0BAZM,IAYN;AArBiB;;AAwBnBmG,EAAAA,WAAAA,CAAAA,WAAAA,EAAyBC,OAAAA,GAAzBD,IAAAA,EAAyC;AACvC,mBADuC,OACvC;AACA,uBAFuC,WAEvC;AACA,0BAAsBE,MAAAA,CAAAA,MAAAA,CAHiB,IAGjBA,CAAtB;AA3BiB;;AA8BnBC,EAAAA,SAAAA,CAAAA,SAAAA,EAAqB;AACnB,qBADmB,SACnB;AA/BiB;;AAkCnBC,EAAAA,UAAAA,CAAAA,UAAAA,EAAuB;AACrB,sBADqB,UACrB;AAnCiB;;AAyCnB,MAAIC,UAAJ,GAAiB;AACf,WAAO,mBAAmB,iBAAnB,WADQ,CACf;AA1CiB;;AAgDnB,MAAIxF,IAAJ,GAAW;AACT,WAAO,eADE,iBACT;AAjDiB;;AAuDnB,MAAIA,IAAJ,QAAgB;AACd,uCADc,KACd;AAxDiB;;AA8DnB,MAAIyF,QAAJ,GAAe;AACb,WAAO,eADM,aACb;AA/DiB;;AAqEnB,MAAIA,QAAJ,QAAoB;AAClB,mCADkB,KAClB;AAtEiB;;AA4EnBC,EAAAA,sBAAAA,CAAAA,OAAAA,EAAgCC,SAAAA,GAAhCD,IAAAA,EAAAA,YAAAA,EAAgE;AAE9D,UAAME,OAAAA,GAAUC,YAAAA,CAF8C,CAE9CA,CAAhB;AACA,QAH8D,UAG9D;;AAEA,QAAI,+BAA+BD,OAAAA,KAAnC,MAAqD;AACnDE,MAAAA,UAAAA,GAAa,uBADsC,OACtC,CAAbA;;AAEA,UAAIA,UAAAA,KAAJ,MAAyB;AAGvB,oDAEQC,SAAAA,IAAa;AACjB,4BAAkBA,SAAAA,GAAlB,GADiB,OACjB;;AACA,0DAFiB,YAEjB;AAJJ,iBAMS,MAAM;AACXC,UAAAA,OAAAA,CAAAA,KAAAA,CACE,gEACE,4CAHO,IACXA;AAVmB,SAGvB;AAHuB;AAH0B;AAArD,WAoBO,IAAIC,MAAAA,CAAAA,SAAAA,CAAJ,OAAIA,CAAJ,EAA+B;AACpCH,MAAAA,UAAAA,GAAaF,OAAAA,GADuB,CACpCE;AADK,WAEA;AACLE,MAAAA,OAAAA,CAAAA,KAAAA,CACE,gEACE,mDAHC,IACLA;AADK;AA3BuD;;AAkC9D,QAAI,eAAeF,UAAAA,GAAf,KAAiCA,UAAAA,GAAa,KAAlD,YAAmE;AACjEE,MAAAA,OAAAA,CAAAA,KAAAA,CACE,mEACE,yCAH6D,IACjEA;AADiE;AAlCL;;AA0C9D,QAAI,KAAJ,YAAqB;AAGnB,sBAHmB,mBAGnB;AACA,2BAAqB;AAAA;AAAA;AAAA;AAAA,OAArB;AA9C4D;;AAiD9D,sCAAkC;AAAA;AAEhCE,MAAAA,SAAAA,EAFgC;AAGhChB,MAAAA,qBAAAA,EAAuB,KAHS;AAAA,KAAlC;AA7HiB;;AAyInB,QAAMiB,eAAN,OAA4B;AAC1B,QAAI,CAAC,KAAL,aAAuB;AAAA;AADG;;AAI1B,mBAJ0B,YAI1B;;AACA,QAAI,gBAAJ,UAA8B;AAC5BR,MAAAA,SAAAA,GAD4B,IAC5BA;AACAE,MAAAA,YAAAA,GAAe,MAAM,gCAFO,IAEP,CAArBA;AAFF,WAGO;AACLF,MAAAA,SAAAA,GADK,IACLA;AACAE,MAAAA,YAAAA,GAAe,MAFV,IAELA;AAVwB;;AAY1B,QAAI,CAACO,KAAAA,CAAAA,OAAAA,CAAL,YAAKA,CAAL,EAAkC;AAChCJ,MAAAA,OAAAA,CAAAA,KAAAA,CACE,8DACE,4CAH4B,IAChCA;AADgC;AAZR;;AAmB1B,iDAnB0B,YAmB1B;AA5JiB;;AAoKnBK,EAAAA,QAAAA,CAAAA,GAAAA,EAAc;AACZ,QAAI,CAAC,KAAL,aAAuB;AAAA;AADX;;AAIZ,UAAMP,UAAAA,GACH,2BAA2B,qCAA5B,GAA4B,CAA3B,IACDQ,GAAAA,GANU,CAIZ;;AAGA,QACE,EACE,gCACAR,UAAAA,GADA,KAEAA,UAAAA,IAAc,KAJlB,UACE,CADF,EAME;AACAE,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,gCADd,wBACAA;AADA;AAbU;;AAkBZ,QAAI,KAAJ,YAAqB;AAGnB,sBAHmB,mBAGnB;AACA,+BAJmB,UAInB;AAtBU;;AAyBZ,sCAAkC;AAzBtB;AAyBsB,KAAlC;AA7LiB;;AAoMnBO,EAAAA,kBAAAA,CAAAA,IAAAA,EAAyB;AACvB,QAAI,gBAAJ,UAA8B;AAC5B,UAAIC,IAAAA,CAAAA,MAAAA,GAAJ,GAAqB;AACnB,eAAO,kBAAkB,MAAMC,MAAAA,CADZ,IACYA,CAAxB,CAAP;AAF0B;AAA9B,WAIO,IAAIL,KAAAA,CAAAA,OAAAA,CAAJ,IAAIA,CAAJ,EAAyB;AAC9B,YAAMM,GAAAA,GAAMC,IAAAA,CAAAA,SAAAA,CADkB,IAClBA,CAAZ;;AACA,UAAID,GAAAA,CAAAA,MAAAA,GAAJ,GAAoB;AAClB,eAAO,kBAAkB,MAAMD,MAAAA,CADb,GACaA,CAAxB,CAAP;AAH4B;AALT;;AAWvB,WAAO,kBAXgB,EAWhB,CAAP;AA/MiB;;AAwNnBG,EAAAA,YAAAA,CAAAA,MAAAA,EAAqB;AACnB,WAAQ,iBAAD,EAAC,IADW,MACnB;AAzNiB;;AA+NnBC,EAAAA,OAAAA,CAAAA,IAAAA,EAAc;AACZ,QAAI,CAAC,KAAL,aAAuB;AAAA;AADX;;AAIZ,oBAJY,IAIZ;;AACA,QAAIC,IAAAA,CAAAA,QAAAA,CAAJ,GAAIA,CAAJ,EAAwB;AACtB,YAAMC,MAAAA,GAASC,gCADO,IACPA,CAAf;;AACA,UAAID,MAAAA,CAAAA,GAAAA,CAAJ,QAAIA,CAAJ,EAA0B;AACxB,kDAA0C;AACxCE,UAAAA,MAAAA,EADwC;AAExCC,UAAAA,KAAAA,EAAOH,MAAAA,CAAAA,GAAAA,CAAAA,QAAAA,EAAAA,OAAAA,CAAAA,IAAAA,EAFiC,EAEjCA,CAFiC;AAGxCI,UAAAA,YAAAA,EAAcJ,MAAAA,CAAAA,GAAAA,CAAAA,QAAAA,MAH0B;AAAA,SAA1C;AAHoB;;AAUtB,UAAIA,MAAAA,CAAAA,GAAAA,CAAJ,MAAIA,CAAJ,EAAwB;AACtBjB,QAAAA,UAAAA,GAAaiB,MAAAA,CAAAA,GAAAA,CAAAA,MAAAA,IAAAA,CAAAA,IADS,CACtBjB;AAXoB;;AAatB,UAAIiB,MAAAA,CAAAA,GAAAA,CAAJ,MAAIA,CAAJ,EAAwB;AAEtB,cAAMK,QAAAA,GAAWL,MAAAA,CAAAA,GAAAA,CAAAA,MAAAA,EAAAA,KAAAA,CAFK,GAELA,CAAjB;AACA,cAAMM,OAAAA,GAAUD,QAAAA,CAHM,CAGNA,CAAhB;AACA,cAAME,aAAAA,GAAgBC,UAAAA,CAJA,OAIAA,CAAtB;;AAEA,YAAI,CAACF,OAAAA,CAAAA,QAAAA,CAAL,KAAKA,CAAL,EAA8B;AAG5Bb,UAAAA,IAAAA,GAAO,OAEL;AAAE7B,YAAAA,IAAAA,EAFG;AAEL,WAFK,EAGLyC,QAAAA,CAAAA,MAAAA,GAAAA,CAAAA,GAAsBA,QAAAA,CAAAA,CAAAA,CAAAA,GAAtBA,CAAAA,GAHK,MAILA,QAAAA,CAAAA,MAAAA,GAAAA,CAAAA,GAAsBA,QAAAA,CAAAA,CAAAA,CAAAA,GAAtBA,CAAAA,GAJK,MAKLE,aAAAA,GAAgBA,aAAAA,GAAhBA,GAAAA,GALK,QAAPd;AAHF,eAUO;AACL,cAAIa,OAAAA,KAAAA,KAAAA,IAAqBA,OAAAA,KAAzB,QAA6C;AAC3Cb,YAAAA,IAAAA,GAAO,OAAO;AAAE7B,cAAAA,IAAAA,EAAT;AAAO,aAAP,CAAP6B;AADF,iBAEO,IACLa,OAAAA,KAAAA,MAAAA,IACAA,OAAAA,KADAA,OAAAA,IAEAA,OAAAA,KAFAA,MAAAA,IAGAA,OAAAA,KAJK,SAKL;AACAb,YAAAA,IAAAA,GAAO,OAEL;AAAE7B,cAAAA,IAAAA,EAFG;AAEL,aAFK,EAGLyC,QAAAA,CAAAA,MAAAA,GAAAA,CAAAA,GAAsBA,QAAAA,CAAAA,CAAAA,CAAAA,GAAtBA,CAAAA,GAHK,KAAPZ;AANK,iBAWA,IAAIa,OAAAA,KAAJ,QAAwB;AAC7B,gBAAID,QAAAA,CAAAA,MAAAA,KAAJ,GAA2B;AACzBpB,cAAAA,OAAAA,CAAAA,KAAAA,CADyB,2DACzBA;AADF,mBAIO;AACLQ,cAAAA,IAAAA,GAAO,OAEL;AAAE7B,gBAAAA,IAAAA,EAFG;AAEL,eAFK,EAGLyC,QAAAA,CAAAA,CAAAA,CAAAA,GAHK,GAILA,QAAAA,CAAAA,CAAAA,CAAAA,GAJK,GAKLA,QAAAA,CAAAA,CAAAA,CAAAA,GALK,GAMLA,QAAAA,CAAAA,CAAAA,CAAAA,GANK,EAAPZ;AAN2B;AAAxB,iBAeA;AACLR,YAAAA,OAAAA,CAAAA,KAAAA,CACE,iDAFG,qBACLA;AA9BG;AAhBe;AAbF;;AAkEtB,gBAAU;AACR,0CAAkC;AAChCF,UAAAA,UAAAA,EAAYA,UAAAA,IAAc,KADM;AAEhCI,UAAAA,SAAAA,EAFgC;AAGhCsB,UAAAA,mBAAAA,EAHgC;AAAA,SAAlC;AADF,aAMO,gBAAgB;AACrB,oBADqB,UACrB;AAzEoB;;AA2EtB,UAAIT,MAAAA,CAAAA,GAAAA,CAAJ,UAAIA,CAAJ,EAA4B;AAC1B,2CAAmC;AACjCE,UAAAA,MAAAA,EADiC;AAEjCQ,UAAAA,IAAAA,EAAMV,MAAAA,CAAAA,GAAAA,CAF2B,UAE3BA;AAF2B,SAAnC;AA5EoB;;AAmFtB,UAAIA,MAAAA,CAAAA,GAAAA,CAAJ,WAAIA,CAAJ,EAA6B;AAC3B,6BAAqBA,MAAAA,CAAAA,GAAAA,CADM,WACNA,CAArB;AApFoB;AAAxB,WAsFO;AAELP,MAAAA,IAAAA,GAAOkB,QAAAA,CAFF,IAEEA,CAAPlB;;AACA,UAAI;AACFA,QAAAA,IAAAA,GAAOG,IAAAA,CAAAA,KAAAA,CADL,IACKA,CAAPH;;AAEA,YAAI,CAACJ,KAAAA,CAAAA,OAAAA,CAAL,IAAKA,CAAL,EAA0B;AAGxBI,UAAAA,IAAAA,GAAOA,IAAAA,CAHiB,QAGjBA,EAAPA;AANA;AAAJ,QAQE,WAAW,CAXR;;AAaL,UAAI,4BAA4BmB,0BAAAA,CAAhC,IAAgCA,CAAhC,EAAkE;AAChE,6BADgE,IAChE;AADgE;AAb7D;;AAiBL3B,MAAAA,OAAAA,CAAAA,KAAAA,CACE,4BAA4B0B,QAAAA,CAA5B,IAA4BA,CAA5B,cAlBG,sBAiBL1B;AA5GU;AA/NK;;AAqVnB4B,EAAAA,kBAAAA,CAAAA,MAAAA,EAA2B;AAEzB;AACE;AACE,YAAI,KAAJ,YAAqB;AACnB,0BADmB,IACnB;AAFJ;;AADF;;AAOE;AACE,YAAI,KAAJ,YAAqB;AACnB,0BADmB,OACnB;AAFJ;;AAPF;;AAaE;AACE,uBADF,QACE;AAdJ;;AAiBE;AACE,uBADF,YACE;AAlBJ;;AAqBE;AACE,oBAAY,KADd,UACE;AAtBJ;;AAyBE;AACE,oBADF,CACE;AA1BJ;;AA6BE;AA7BF;AAAA;;AAiCA,0CAAsC;AACpCX,MAAAA,MAAAA,EADoC;AAAA;AAAA,KAAtC;AAxXiB;;AAkYnBY,EAAAA,YAAAA,CAAAA,OAAAA,EAAAA,OAAAA,EAA+B;AAC7B,QAAI,CAAJ,SAAc;AAAA;AADe;;AAI7B,UAAMC,MAAAA,GACJC,OAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAoB,GAAGA,OAAAA,CAAH,GAApBA,GAAAA,GAAwC,GAAGA,OAAAA,CAAH,OAAkBA,OAAAA,CAAlB,GALb,EAI7B;AAEA,kCAN6B,OAM7B;AAxYiB;;AA8YnBC,EAAAA,iBAAAA,CAAAA,OAAAA,EAA2B;AACzB,UAAMF,MAAAA,GACJC,OAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAoB,GAAGA,OAAAA,CAAH,GAApBA,GAAAA,GAAwC,GAAGA,OAAAA,CAAH,OAAkBA,OAAAA,CAAlB,GAFjB,EACzB;AAEA,WAAO,iCAHkB,IAGzB;AAjZiB;;AAuZnBE,EAAAA,aAAAA,CAAAA,UAAAA,EAA0B;AACxB,WAAO,6BADiB,UACjB,CAAP;AAxZiB;;AA8ZnBC,EAAAA,YAAAA,CAAAA,UAAAA,EAAyB;AACvB,WAAO,4BADgB,UAChB,CAAP;AA/ZiB;;AAAA;;;;AAmarB,0CAA0C;AACxC,MAAI,CAAC9B,KAAAA,CAAAA,OAAAA,CAAL,IAAKA,CAAL,EAA0B;AACxB,WADwB,KACxB;AAFsC;;AAIxC,QAAM+B,UAAAA,GAAa3B,IAAAA,CAJqB,MAIxC;;AACA,MAAI2B,UAAAA,GAAJ,GAAoB;AAClB,WADkB,KAClB;AANsC;;AAQxC,QAAMnI,IAAAA,GAAOwG,IAAAA,CAR2B,CAQ3BA,CAAb;;AACA,MACE,EACE,4BACAP,MAAAA,CAAAA,SAAAA,CAAiBjG,IAAAA,CADjB,GACAiG,CADA,IAEAA,MAAAA,CAAAA,SAAAA,CAAiBjG,IAAAA,CAHnB,GAGEiG,CAHF,KAKA,EAAE,0BAA0BjG,IAAAA,IAN9B,CAME,CANF,EAOE;AACA,WADA,KACA;AAjBsC;;AAmBxC,QAAMoI,IAAAA,GAAO5B,IAAAA,CAnB2B,CAmB3BA,CAAb;;AACA,MAAI,EAAE,4BAA4B,OAAO4B,IAAAA,CAAP,SAAlC,QAAI,CAAJ,EAAkE;AAChE,WADgE,KAChE;AArBsC;;AAuBxC,MAAIC,SAAAA,GAvBoC,IAuBxC;;AACA,UAAQD,IAAAA,CAAR;AACE;AACE,UAAID,UAAAA,KAAJ,GAAsB;AACpB,eADoB,KACpB;AAFJ;;AADF;;AAME,SANF,KAME;AACA;AACE,aAAOA,UAAAA,KARX,CAQI;;AACF,SATF,MASE;AACA,SAVF,OAUE;AACA,SAXF,MAWE;AACA;AACE,UAAIA,UAAAA,KAAJ,GAAsB;AACpB,eADoB,KACpB;AAFJ;;AAZF;;AAiBE;AACE,UAAIA,UAAAA,KAAJ,GAAsB;AACpB,eADoB,KACpB;AAFJ;;AAIEE,MAAAA,SAAAA,GAJF,KAIEA;AArBJ;;AAuBE;AACE,aAxBJ,KAwBI;AAxBJ;;AA0BA,OAAK,IAAIC,CAAAA,GAAT,GAAgBA,CAAAA,GAAhB,YAAgCA,CAAhC,IAAqC;AACnC,UAAMC,KAAAA,GAAQ/B,IAAAA,CADqB,CACrBA,CAAd;;AACA,QAAI,EAAE,6BAA8B6B,SAAAA,IAAaE,KAAAA,KAAjD,IAAI,CAAJ,EAAmE;AACjE,aADiE,KACjE;AAHiC;AAlDG;;AAwDxC,SAxDwC,IAwDxC;AA9fF;;AAogBA,wBAAwB;AACtBvJ,EAAAA,WAAAA,GAAc;AACZ,8BADY,IACZ;AACA,2BAFY,IAEZ;AACA,+BAHY,IAGZ;AACA,kCAJY,KAIZ;AALoB;;AAWtB,MAAIwG,UAAJ,GAAiB;AACf,WADe,CACf;AAZoB;;AAkBtB,MAAIxF,IAAJ,GAAW;AACT,WADS,CACT;AAnBoB;;AAyBtB,MAAIA,IAAJ,QAAgB,CAzBM;;AA8BtB,MAAIyF,QAAJ,GAAe;AACb,WADa,CACb;AA/BoB;;AAqCtB,MAAIA,QAAJ,QAAoB,CArCE;;AA0CtB,QAAMU,eAAN,OAA4B,CA1CN;;AA+CtBE,EAAAA,QAAAA,CAAAA,GAAAA,EAAc,CA/CQ;;AAqDtBE,EAAAA,kBAAAA,CAAAA,IAAAA,EAAyB;AACvB,WADuB,GACvB;AAtDoB;;AA6DtBK,EAAAA,YAAAA,CAAAA,IAAAA,EAAmB;AACjB,WADiB,GACjB;AA9DoB;;AAoEtBC,EAAAA,OAAAA,CAAAA,IAAAA,EAAc,CApEQ;;AAyEtBe,EAAAA,kBAAAA,CAAAA,MAAAA,EAA2B,CAzEL;;AA+EtBC,EAAAA,YAAAA,CAAAA,OAAAA,EAAAA,OAAAA,EAA+B,CA/ET;;AAoFtBI,EAAAA,aAAAA,CAAAA,UAAAA,EAA0B;AACxB,WADwB,IACxB;AArFoB;;AA2FtBC,EAAAA,YAAAA,CAAAA,UAAAA,EAAyB;AACvB,WADuB,IACvB;AA5FoB;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrfxB,MAAMM,SAAAA,GAAY,OAflB,IAeA;;AACA,MAAMC,mBAAAA,GAhBN,MAgBA;;AACA,MAAMC,aAAAA,GAjBN,GAiBA;;AACA,MAAMC,SAAAA,GAlBN,GAkBA;;AACA,MAAMC,SAAAA,GAnBN,IAmBA;;AACA,MAAMC,aAAAA,GApBN,CAoBA;;AACA,MAAMC,cAAAA,GArBN,IAqBA;;AACA,MAAMC,iBAAAA,GAtBN,EAsBA;;AACA,MAAMC,gBAAAA,GAvBN,CAuBA;;AAEA,MAAMC,yBAAAA,GAzBN,yBAyBA;AAEA,MAAMC,qBAAAA,GAAwB;AAC5BC,EAAAA,OAAAA,EAD4B;AAE5BC,EAAAA,MAAAA,EAF4B;AAG5BC,EAAAA,QAAAA,EAH4B;AAI5BC,EAAAA,UAAAA,EAJ4B;AAAA,CAA9B;;AAOA,MAAMC,WAAAA,GAAc;AAClBJ,EAAAA,OAAAA,EAAS,CADS;AAElBK,EAAAA,IAAAA,EAFkB;AAGlBC,EAAAA,MAAAA,EAHkB;AAIlBC,EAAAA,OAAAA,EAJkB;AAKlBC,EAAAA,WAAAA,EALkB;AAMlBC,EAAAA,MAAAA,EANkB;AAAA,CAApB;;AASA,MAAMC,YAAAA,GAAe;AACnBC,EAAAA,MAAAA,EADmB;AAEnBC,EAAAA,GAAAA,EAFmB;AAAA,CAArB;;AAKA,MAAMC,aAAAA,GAAgB;AACpBC,EAAAA,OAAAA,EADoB;AAEpBC,EAAAA,MAAAA,EAFoB;AAGpBC,EAAAA,cAAAA,EAHoB;AAAA,CAAtB;;AAMA,MAAMC,UAAAA,GAAa;AACjBjB,EAAAA,OAAAA,EAAS,CADQ;AAEjBkB,EAAAA,QAAAA,EAFiB;AAGjBC,EAAAA,UAAAA,EAHiB;AAIjBC,EAAAA,OAAAA,EAJiB;AAAA,CAAnB;;AAOA,MAAMC,UAAAA,GAAa;AACjBrB,EAAAA,OAAAA,EAAS,CADQ;AAEjBK,EAAAA,IAAAA,EAFiB;AAGjBiB,EAAAA,GAAAA,EAHiB;AAIjBC,EAAAA,IAAAA,EAJiB;AAAA,CAAnB;;AAQA,MAAMC,eAAAA,GArEN,cAqEA;;;AAQA,6BAA6B;AAC3B,QAAMC,gBAAAA,GAAmBpK,MAAAA,CAAAA,gBAAAA,IADE,CAC3B;AACA,QAAMqK,iBAAAA,GACJC,GAAAA,CAAAA,4BAAAA,IACAA,GAAAA,CADAA,yBAAAA,IAEAA,GAAAA,CAFAA,sBAAAA,IAHyB,CAE3B;AAKA,QAAMC,UAAAA,GAAaH,gBAAAA,GAPQ,iBAO3B;AACA,SAAO;AACLI,IAAAA,EAAAA,EADK;AAELC,IAAAA,EAAAA,EAFK;AAGLC,IAAAA,MAAAA,EAAQH,UAAAA,KAHH;AAAA,GAAP;AArFF;;AAqGA,uCAAuCI,aAAAA,GAAvC,OAA8D;AAI5D,MAAIC,MAAAA,GAASC,OAAAA,CAJ+C,YAI5D;;AACA,MAAI,CAAJ,QAAa;AACXrF,IAAAA,OAAAA,CAAAA,KAAAA,CADW,0CACXA;AADW;AAL+C;;AAS5D,MAAIsF,OAAAA,GAAUD,OAAAA,CAAAA,SAAAA,GAAoBA,OAAAA,CAT0B,SAS5D;AACA,MAAIE,OAAAA,GAAUF,OAAAA,CAAAA,UAAAA,GAAqBA,OAAAA,CAVyB,UAU5D;;AACA,SACGD,MAAAA,CAAAA,YAAAA,KAAwBA,MAAAA,CAAxBA,YAAAA,IACCA,MAAAA,CAAAA,WAAAA,KAAuBA,MAAAA,CADzB,WAACA,IAEAD,aAAAA,KACE,8CACCK,gBAAAA,CAAAA,MAAAA,CAAAA,CAAAA,QAAAA,KALN,QAGGL,CAHH,EAME;AACAG,IAAAA,OAAAA,IAAWF,MAAAA,CADX,SACAE;AACAC,IAAAA,OAAAA,IAAWH,MAAAA,CAFX,UAEAG;AAEAH,IAAAA,MAAAA,GAASA,MAAAA,CAJT,YAIAA;;AACA,QAAI,CAAJ,QAAa;AAAA;AALb;AAjB0D;;AA0B5D,YAAU;AACR,QAAIK,IAAAA,CAAAA,GAAAA,KAAJ,WAA4B;AAC1BH,MAAAA,OAAAA,IAAWG,IAAAA,CADe,GAC1BH;AAFM;;AAIR,QAAIG,IAAAA,CAAAA,IAAAA,KAAJ,WAA6B;AAC3BF,MAAAA,OAAAA,IAAWE,IAAAA,CADgB,IAC3BF;AACAH,MAAAA,MAAAA,CAAAA,UAAAA,GAF2B,OAE3BA;AANM;AA1BkD;;AAmC5DA,EAAAA,MAAAA,CAAAA,SAAAA,GAnC4D,OAmC5DA;AAxIF;;AA+IA,gDAAgD;AAC9C,QAAMM,cAAAA,GAAiB,eAAe;AACpC,aAAS;AAAA;AAD2B;;AAKpCC,IAAAA,GAAAA,GAAM,6BAA6B,mCAAmC;AACpEA,MAAAA,GAAAA,GADoE,IACpEA;AAEA,YAAMC,QAAAA,GAAWC,eAAAA,CAHmD,UAGpE;AACA,YAAMC,KAAAA,GAAQC,KAAAA,CAJsD,KAIpE;;AACA,UAAIH,QAAAA,KAAJ,OAAwB;AACtBG,QAAAA,KAAAA,CAAAA,KAAAA,GAAcH,QAAAA,GADQ,KACtBG;AANkE;;AAQpEA,MAAAA,KAAAA,CAAAA,KAAAA,GARoE,QAQpEA;AACA,YAAMC,QAAAA,GAAWH,eAAAA,CATmD,SASpE;AACA,YAAMI,KAAAA,GAAQF,KAAAA,CAVsD,KAUpE;;AACA,UAAIC,QAAAA,KAAJ,OAAwB;AACtBD,QAAAA,KAAAA,CAAAA,IAAAA,GAAaC,QAAAA,GADS,KACtBD;AAZkE;;AAcpEA,MAAAA,KAAAA,CAAAA,KAAAA,GAdoE,QAcpEA;AACAG,MAAAA,QAAAA,CAfoE,KAepEA,CAAAA;AApBkC,KAK9B,CAANP;AAN4C,GAC9C;;AAwBA,QAAMI,KAAAA,GAAQ;AACZI,IAAAA,KAAAA,EADY;AAEZC,IAAAA,IAAAA,EAFY;AAGZN,IAAAA,KAAAA,EAAOD,eAAAA,CAHK;AAIZI,IAAAA,KAAAA,EAAOJ,eAAAA,CAJK;AAKZQ,IAAAA,aAAAA,EALY;AAAA,GAAd;AAQA,MAAIV,GAAAA,GAjC0C,IAiC9C;AACAE,EAAAA,eAAAA,CAAAA,gBAAAA,CAAAA,QAAAA,EAAAA,cAAAA,EAlC8C,IAkC9CA;AACA,SAnC8C,KAmC9C;AAlLF;;AA0LA,iCAAiC;AAC/B,QAAM9E,MAAAA,GAAS,IADgB,GAChB,EAAf;;AACA,qBAAmBG,KAAAA,CAAAA,KAAAA,CAAnB,GAAmBA,CAAnB,EAAqC;AACnC,UAAMqB,KAAAA,GAAQ+D,IAAAA,CAAAA,KAAAA,CAAd,GAAcA,CAAd;AAAA,UACE7I,GAAAA,GAAM8E,KAAAA,CAAAA,CAAAA,CAAAA,CADR,WACQA,EADR;AAAA,UAEEgE,KAAAA,GAAQhE,KAAAA,CAAAA,MAAAA,GAAAA,CAAAA,GAAmBA,KAAAA,CAAnBA,CAAmBA,CAAnBA,GAHyB,EACnC;AAGAxB,IAAAA,MAAAA,CAAAA,GAAAA,CAAWyF,kBAAAA,CAAXzF,GAAWyF,CAAXzF,EAAoCyF,kBAAAA,CAJD,KAICA,CAApCzF;AAN6B;;AAQ/B,SAR+B,MAQ/B;AAlMF;;AA8MA,iDAAiD;AAC/C,MAAI0F,QAAAA,GAD2C,CAC/C;AACA,MAAIC,QAAAA,GAAWC,KAAAA,CAAAA,MAAAA,GAFgC,CAE/C;;AAEA,MAAID,QAAAA,GAAAA,CAAAA,IAAgB,CAACE,SAAAA,CAAUD,KAAAA,CAA/B,QAA+BA,CAAVC,CAArB,EAAiD;AAC/C,WAAOD,KAAAA,CADwC,MAC/C;AAL6C;;AAO/C,MAAIC,SAAAA,CAAUD,KAAAA,CAAd,QAAcA,CAAVC,CAAJ,EAAgC;AAC9B,WAD8B,QAC9B;AAR6C;;AAW/C,SAAOH,QAAAA,GAAP,UAA4B;AAC1B,UAAMI,YAAAA,GAAgBJ,QAAAA,GAAD,QAACA,IADI,CAC1B;AACA,UAAMK,WAAAA,GAAcH,KAAAA,CAFM,YAENA,CAApB;;AACA,QAAIC,SAAAA,CAAJ,WAAIA,CAAJ,EAA4B;AAC1BF,MAAAA,QAAAA,GAD0B,YAC1BA;AADF,WAEO;AACLD,MAAAA,QAAAA,GAAWI,YAAAA,GADN,CACLJ;AANwB;AAXmB;;AAoB/C,SApB+C,QAoB/C;AAlOF;;AA4OA,gCAAgC;AAE9B,MAAIM,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,MAAJ,GAAyB;AACvB,WAAO,MAAP;AAH4B;;AAK9B,QAAMC,IAAAA,GAAO,IALiB,CAK9B;AACA,QAAMC,KAAAA,GANwB,CAM9B;;AACA,MAAID,IAAAA,GAAJ,OAAkB;AAChB,WAAO,UAAP;AADF,SAEO,IAAID,IAAAA,CAAAA,KAAAA,CAAAA,IAAAA,MAAJ,MAA+B;AACpC,WAAO,SAAP;AAV4B;;AAa9B,QAAMG,EAAAA,GAAKC,CAAAA,GAAAA,CAAAA,GAAAA,IAAAA,GAbmB,CAa9B;AAEA,MAAIC,CAAAA,GAAJ;AAAA,MACEC,CAAAA,GADF;AAAA,MAEEC,CAAAA,GAFF;AAAA,MAGEC,CAAAA,GAlB4B,CAe9B;;AAKA,eAAa;AAEX,UAAMC,CAAAA,GAAIJ,CAAAA,GAAV;AAAA,UACEK,CAAAA,GAAIJ,CAAAA,GAHK,CAEX;;AAEA,QAAII,CAAAA,GAAJ,OAAe;AAAA;AAJJ;;AAOX,QAAIP,EAAAA,IAAMM,CAAAA,GAAV,GAAiB;AACfF,MAAAA,CAAAA,GADe,CACfA;AACAC,MAAAA,CAAAA,GAFe,CAEfA;AAFF,WAGO;AACLH,MAAAA,CAAAA,GADK,CACLA;AACAC,MAAAA,CAAAA,GAFK,CAELA;AAZS;AApBiB;;AAmC9B,MAnC8B,MAmC9B;;AAEA,MAAIH,EAAAA,GAAKE,CAAAA,GAALF,CAAAA,GAAaI,CAAAA,GAAAA,CAAAA,GAAjB,IAA6B;AAC3BI,IAAAA,MAAAA,GAAS,WAAW,MAAX,GAAoB,MAA7BA;AADF,SAEO;AACLA,IAAAA,MAAAA,GAAS,WAAW,MAAX,GAAoB,MAA7BA;AAxC4B;;AA0C9B,SA1C8B,MA0C9B;AAtRF;;AAyRA,+BAA+B;AAC7B,QAAMC,CAAAA,GAAIR,CAAAA,GADmB,GAC7B;AACA,SAAOQ,CAAAA,KAAAA,CAAAA,GAAAA,CAAAA,GAAcZ,IAAAA,CAAAA,KAAAA,CAAWI,CAAAA,GAAAA,CAAAA,GAFH,GAERJ,CAArB;AA3RF;;AAqSA,2BAA2B;AAAA;AAAA;AAA3B;AAA2B,CAA3B,EAAuD;AACrD,QAAM,mBAD+C,IACrD;AAEA,QAAMa,iBAAAA,GAAoBC,MAAAA,GAAAA,GAAAA,KAH2B,CAGrD;AAEA,QAAMC,KAAAA,GAAU,MAAD,EAAC,IAAF,EAAE,GALqC,QAKrD;AACA,QAAMC,MAAAA,GAAW,MAAD,EAAC,IAAF,EAAE,GANoC,QAMrD;AAEA,SAAO;AACLD,IAAAA,KAAAA,EAAOF,iBAAAA,GAAAA,MAAAA,GADF;AAELG,IAAAA,MAAAA,EAAQH,iBAAAA,GAAAA,KAAAA,GAFH;AAAA,GAAP;AA7SF;;AA8TA,8DAA8D;AAa5D,MAAII,KAAAA,GAAJ,GAAe;AACb,WADa,KACb;AAd0D;;AAwC5D,MAAIC,GAAAA,GAAMC,KAAAA,CAAAA,KAAAA,CAAAA,CAxCkD,GAwC5D;AACA,MAAIC,OAAAA,GAAUF,GAAAA,CAAAA,SAAAA,GAAgBA,GAAAA,CAzC8B,SAyC5D;;AAEA,MAAIE,OAAAA,IAAJ,KAAoB;AAMlBF,IAAAA,GAAAA,GAAMC,KAAAA,CAAMF,KAAAA,GAANE,CAAAA,CAAAA,CANY,GAMlBD;AACAE,IAAAA,OAAAA,GAAUF,GAAAA,CAAAA,SAAAA,GAAgBA,GAAAA,CAPR,SAOlBE;AAlD0D;;AA6D5D,OAAK,IAAI7F,CAAAA,GAAI0F,KAAAA,GAAb,GAAwB1F,CAAAA,IAAxB,GAAgC,EAAhC,GAAqC;AACnC2F,IAAAA,GAAAA,GAAMC,KAAAA,CAAAA,CAAAA,CAAAA,CAD6B,GACnCD;;AACA,QAAIA,GAAAA,CAAAA,SAAAA,GAAgBA,GAAAA,CAAhBA,SAAAA,GAAgCA,GAAAA,CAAhCA,YAAAA,IAAJ,SAAiE;AAAA;AAF9B;;AAQnCD,IAAAA,KAAAA,GARmC,CAQnCA;AArE0D;;AAuE5D,SAvE4D,KAuE5D;AArYF;;AA2aA,4BAA4B;AAAA;AAAA;AAG1BI,EAAAA,gBAAAA,GAH0B;AAI1BC,EAAAA,UAAAA,GAJ0B;AAK1BC,EAAAA,GAAAA,GALF;AAA4B,CAA5B,EAMG;AACD,QAAMC,GAAAA,GAAMC,QAAAA,CAAZ;AAAA,QACEC,MAAAA,GAASF,GAAAA,GAAMC,QAAAA,CAFhB,YACD;AAEA,QAAME,IAAAA,GAAOF,QAAAA,CAAb;AAAA,QACErC,KAAAA,GAAQuC,IAAAA,GAAOF,QAAAA,CAJhB,WAGD;;AAaA,6CAA2C;AACzC,UAAMnD,OAAAA,GAAUsD,IAAAA,CADyB,GACzC;AACA,UAAMC,aAAAA,GACJvD,OAAAA,CAAAA,SAAAA,GAAoBA,OAAAA,CAApBA,SAAAA,GAAwCA,OAAAA,CAHD,YAEzC;AAEA,WAAOuD,aAAAA,GAJkC,GAIzC;AApBD;;AAsBD,oDAAkD;AAChD,UAAMvD,OAAAA,GAAUsD,IAAAA,CADgC,GAChD;AACA,UAAME,WAAAA,GAAcxD,OAAAA,CAAAA,UAAAA,GAAqBA,OAAAA,CAFO,UAEhD;AACA,UAAMyD,YAAAA,GAAeD,WAAAA,GAAcxD,OAAAA,CAHa,WAGhD;AACA,WAAOiD,GAAAA,GAAMO,WAAAA,GAANP,KAAAA,GAA4BQ,YAAAA,GAJa,IAIhD;AA1BD;;AA6BD,QAAMC,OAAAA,GAAN;AAAA,QACEC,QAAAA,GAAWd,KAAAA,CA9BZ,MA6BD;AAEA,MAAIe,sBAAAA,GAAyBC,qBAAAA,CAAAA,KAAAA,EAE3Bb,UAAAA,GAAAA,kCAAAA,GAjCD,2BA+B4Ba,CAA7B;;AASA,MACED,sBAAAA,GAAAA,CAAAA,IACAA,sBAAAA,GADAA,QAAAA,IAEA,CAHF,YAIE;AAMAA,IAAAA,sBAAAA,GAAyBE,iCAAAA,CAAAA,sBAAAA,EAAAA,KAAAA,EANzB,GAMyBA,CAAzBF;AAlDD;;AAiED,MAAIG,QAAAA,GAAWf,UAAAA,GAAAA,KAAAA,GAAqB,CAjEnC,CAiED;;AAEA,OAAK,IAAI/F,CAAAA,GAAT,wBAAqCA,CAAAA,GAArC,UAAmDA,CAAnD,IAAwD;AACtD,UAAMqG,IAAAA,GAAOT,KAAAA,CAAb,CAAaA,CAAb;AAAA,UACE7C,OAAAA,GAAUsD,IAAAA,CAF0C,GACtD;AAEA,UAAMU,YAAAA,GAAehE,OAAAA,CAAAA,UAAAA,GAAqBA,OAAAA,CAHY,UAGtD;AACA,UAAMiE,aAAAA,GAAgBjE,OAAAA,CAAAA,SAAAA,GAAoBA,OAAAA,CAJY,SAItD;AACA,UAAMkE,SAAAA,GAAYlE,OAAAA,CAAlB;AAAA,UACEmE,UAAAA,GAAanE,OAAAA,CANuC,YAKtD;AAEA,UAAMoE,SAAAA,GAAYJ,YAAAA,GAPoC,SAOtD;AACA,UAAMK,UAAAA,GAAaJ,aAAAA,GARmC,UAQtD;;AAEA,QAAIF,QAAAA,KAAa,CAAjB,GAAqB;AAKnB,UAAIM,UAAAA,IAAJ,QAA0B;AACxBN,QAAAA,QAAAA,GADwB,UACxBA;AANiB;AAArB,WAQO,IAAK,6BAAD,aAAC,IAAL,UAA4D;AAAA;AAlBb;;AAsBtD,QACEM,UAAAA,IAAAA,GAAAA,IACAJ,aAAAA,IADAI,MAAAA,IAEAD,SAAAA,IAFAC,IAAAA,IAGAL,YAAAA,IAJF,OAKE;AAAA;AA3BoD;;AA+BtD,UAAMM,YAAAA,GACJ5C,IAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAYwB,GAAAA,GAAZxB,aAAAA,IAAmCA,IAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAY2C,UAAAA,GAhCK,MAgCjB3C,CADrC;AAEA,UAAM6C,WAAAA,GACJ7C,IAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAY2B,IAAAA,GAAZ3B,YAAAA,IAAmCA,IAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAY0C,SAAAA,GAlCK,KAkCjB1C,CADrC;AAGA,UAAM8C,cAAAA,GAAkB,cAAD,YAAC,IAAxB;AAAA,UACEC,aAAAA,GAAiB,aAAD,WAAC,IArCmC,SAoCtD;AAEA,UAAMC,OAAAA,GAAWF,cAAAA,GAAAA,aAAAA,GAAD,GAACA,GAtCqC,CAsCtD;AAEAd,IAAAA,OAAAA,CAAAA,IAAAA,CAAa;AACXiB,MAAAA,EAAAA,EAAIrB,IAAAA,CADO;AAEXxB,MAAAA,CAAAA,EAFW;AAGX8C,MAAAA,CAAAA,EAHW;AAAA;AAAA;AAMXC,MAAAA,YAAAA,EAAeJ,aAAAA,GAAD,GAACA,GANJ;AAAA,KAAbf;AA3GD;;AAqHD,QAAMoB,KAAAA,GAAQpB,OAAAA,CAAd,CAAcA,CAAd;AAAA,QACEqB,IAAAA,GAAOrB,OAAAA,CAAQA,OAAAA,CAAAA,MAAAA,GAtHhB,CAsHQA,CADT;;AAGA,wBAAsB;AACpBA,IAAAA,OAAAA,CAAAA,IAAAA,CAAa,gBAAgB;AAC3B,YAAMsB,EAAAA,GAAKjD,CAAAA,CAAAA,OAAAA,GAAYC,CAAAA,CADI,OAC3B;;AACA,UAAIN,IAAAA,CAAAA,GAAAA,CAAAA,EAAAA,IAAJ,OAA0B;AACxB,eAAO,CADiB,EACxB;AAHyB;;AAK3B,aAAOK,CAAAA,CAAAA,EAAAA,GAAOC,CAAAA,CALa,EAK3B;AANkB,KACpB0B;AAzHD;;AAiID,SAAO;AAAA;AAAA;AAAeb,IAAAA,KAAAA,EAAf;AAAA,GAAP;AAljBF;;AAwjBA,mCAAmC;AACjCoC,EAAAA,GAAAA,CADiC,cACjCA;AAzjBF;;AA4jBA,2CAA2C;AACzC,MAAIC,KAAAA,GAAQxD,IAAAA,CAAAA,KAAAA,CAAWuD,GAAAA,CAAXvD,MAAAA,EAAuBuD,GAAAA,CADM,MAC7BvD,CAAZ;AACA,QAAMyD,KAAAA,GAAQzD,IAAAA,CAAAA,KAAAA,CAAWuD,GAAAA,CAAXvD,MAAAA,EAAuBuD,GAAAA,CAFI,MAE3BvD,CAAd;;AACA,MAAI,QAAQA,IAAAA,CAAR,cAA2ByD,KAAAA,GAAQ,OAAOzD,IAAAA,CAA9C,IAAuD;AAErDwD,IAAAA,KAAAA,GAAQ,CAF6C,KAErDA;AALuC;;AAOzC,SAPyC,KAOzC;AAnkBF;;AAskBA,uCAAuC;AACrC,MAAIA,KAAAA,GAAQE,4BAAAA,CADyB,GACzBA,CAAZ;AAEA,QAAMC,0BAAAA,GAH+B,CAGrC;AACA,QAAMC,yBAAAA,GAJ+B,CAIrC;AACA,QAAMC,qBAAAA,GAL+B,EAKrC;AACA,QAAMC,oBAAAA,GAN+B,EAMrC;;AAGA,MAAIP,GAAAA,CAAAA,SAAAA,KAAJ,4BAAkD;AAChDC,IAAAA,KAAAA,IAASK,qBAAAA,GADuC,oBAChDL;AADF,SAEO,IAAID,GAAAA,CAAAA,SAAAA,KAAJ,2BAAiD;AACtDC,IAAAA,KAAAA,IADsD,oBACtDA;AAZmC;;AAcrC,SAdqC,KAcrC;AAplBF;;AAulBA,gCAAgC;AAC9B,SAAOtK,MAAAA,CAAAA,SAAAA,CAAAA,KAAAA,KAA2BuK,KAAAA,GAAAA,EAAAA,KADJ,CAC9B;AAxlBF;;AA2lBA,iCAAiC;AAC/B,SACEvK,MAAAA,CAAAA,SAAAA,CAAAA,IAAAA,KACAZ,MAAAA,CAAAA,MAAAA,CAAAA,UAAAA,EAAAA,QAAAA,CADAY,IACAZ,CADAY,IAEAwB,IAAAA,KAAS2C,UAAAA,CAJoB,OAC/B;AA5lBF;;AAmmBA,iCAAiC;AAC/B,SACEnE,MAAAA,CAAAA,SAAAA,CAAAA,IAAAA,KACAZ,MAAAA,CAAAA,MAAAA,CAAAA,UAAAA,EAAAA,QAAAA,CADAY,IACAZ,CADAY,IAEAwB,IAAAA,KAAS+C,UAAAA,CAJoB,OAC/B;AApmBF;;AA2mBA,qCAAqC;AACnC,SAAOsG,IAAAA,CAAAA,KAAAA,IAAcA,IAAAA,CADc,MACnC;AA5mBF;;AA+mBA,MAAMC,UAAAA,GAAa;AACjBC,EAAAA,KAAAA,EADiB;AAEjBC,EAAAA,OAAAA,EAFiB;AAAA,CAAnB;;;AAsBA,8BAA8B;AAAA;AAAA;AAAgBC,EAAAA,KAAAA,GAA9C;AAA8B,CAA9B,EAA2D;AACzD,SAAO,YAAY,2BAA2B;AAC5C,QACE,8BACA,EAAE,QAAQ,gBADV,QACA,CADA,IAEA,EAAE,2BAA2BA,KAAAA,IAH/B,CAGE,CAHF,EAIE;AACA,YAAM,UADN,4CACM,CAAN;AAN0C;;AAS5C,2BAAuB;AACrB,UAAIC,MAAAA,YAAJ,UAAgC;AAC9BA,QAAAA,MAAAA,CAAAA,IAAAA,CAAAA,IAAAA,EAD8B,YAC9BA;AADF,aAEO;AACLA,QAAAA,MAAAA,CAAAA,mBAAAA,CAAAA,IAAAA,EADK,YACLA;AAJmB;;AAOrB,mBAAa;AACXC,QAAAA,YAAAA,CADW,OACXA,CAAAA;AARmB;;AAUrBC,MAAAA,OAAAA,CAVqB,IAUrBA,CAAAA;AAnB0C;;AAsB5C,UAAMC,YAAAA,GAAeC,OAAAA,CAAAA,IAAAA,CAAAA,IAAAA,EAAmBR,UAAAA,CAtBI,KAsBvBQ,CAArB;;AACA,QAAIJ,MAAAA,YAAJ,UAAgC;AAC9BA,MAAAA,MAAAA,CAAAA,GAAAA,CAAAA,IAAAA,EAD8B,YAC9BA;AADF,WAEO;AACLA,MAAAA,MAAAA,CAAAA,gBAAAA,CAAAA,IAAAA,EADK,YACLA;AA1B0C;;AA6B5C,UAAMK,cAAAA,GAAiBD,OAAAA,CAAAA,IAAAA,CAAAA,IAAAA,EAAmBR,UAAAA,CA7BE,OA6BrBQ,CAAvB;AACA,UAAME,OAAAA,GAAUC,UAAAA,CAAAA,cAAAA,EA9B4B,KA8B5BA,CAAhB;AA/BuD,GAClD,CAAP;AAtoBF;;AA2qBA,MAAMC,gBAAAA,GAAmB,YAAY,mBAAmB;AAWtDnR,EAAAA,MAAAA,CAAAA,qBAAAA,CAXsD,OAWtDA;AAtrBF,CA2qByB,CAAzB;;;AAiBA,qCAAqCkD,IAAAA,GAArC,MAAkD;AAE9C,QAAM,UAFwC,mCAExC,CAAN;AA9rBJ;;AAutBA,eAAe;AACb1E,EAAAA,WAAAA,CAAAA,OAAAA,EAAqB;AACnB,sBAAkBqG,MAAAA,CAAAA,MAAAA,CADC,IACDA,CAAlB;AAFW;;AAcbuM,EAAAA,EAAAA,CAAAA,SAAAA,EAAAA,QAAAA,EAAwBC,OAAAA,GAAxBD,IAAAA,EAAwC;AACtC,kCAA8B;AAC5BE,MAAAA,QAAAA,EAD4B;AAE5BC,MAAAA,IAAAA,EAAMF,OAAAA,EAFsB;AAAA,KAA9B;AAfW;;AA0BbG,EAAAA,GAAAA,CAAAA,SAAAA,EAAAA,QAAAA,EAAyBH,OAAAA,GAAzBG,IAAAA,EAAyC;AACvC,mCAA+B;AAC7BF,MAAAA,QAAAA,EAD6B;AAE7BC,MAAAA,IAAAA,EAAMF,OAAAA,EAFuB;AAAA,KAA/B;AA3BW;;AAiCbI,EAAAA,QAAAA,CAAAA,SAAAA,EAAoB;AAClB,UAAMC,cAAAA,GAAiB,gBADL,SACK,CAAvB;;AACA,QAAI,mBAAmBA,cAAAA,CAAAA,MAAAA,KAAvB,GAAoD;AAAA;AAFlC;;AAalB,UAAMxO,IAAAA,GAAO0C,KAAAA,CAAAA,SAAAA,CAAAA,KAAAA,CAAAA,IAAAA,CAAAA,SAAAA,EAbK,CAaLA,CAAb;AACA,QAdkB,iBAclB;;AAGA,eAAW;AAAA;AAAA;AAAX;AAAW,KAAX,IAA2C8L,cAAAA,CAAAA,KAAAA,CAA3C,CAA2CA,CAA3C,EAAoE;AAClE,gBAAU;AACR,6BADQ,QACR;AAFgE;;AAIlE,oBAAc;AACX,+BAAD,EAAC,EAAD,IAAC,CADW,QACX;AADW;AAJoD;;AAQlEC,MAAAA,QAAAA,CAAAA,KAAAA,CAAAA,IAAAA,EARkE,IAQlEA;AAzBgB;;AA6BlB,2BAAuB;AACrB,gDAA0C;AACxCA,QAAAA,QAAAA,CAAAA,KAAAA,CAAAA,IAAAA,EADwC,IACxCA;AAFmB;;AAIrBC,MAAAA,iBAAAA,GAJqB,IAIrBA;AAjCgB;AAjCP;;AA+EbC,EAAAA,GAAAA,CAAAA,SAAAA,EAAAA,QAAAA,EAAyBR,OAAAA,GAAzBQ,IAAAA,EAAyC;AACvC,UAAMH,cAAAA,GAAkB,+BADe,EACvC;AACAA,IAAAA,cAAAA,CAAAA,IAAAA,CAAoB;AAAA;AAElBJ,MAAAA,QAAAA,EAAUD,OAAAA,EAAAA,QAAAA,KAFQ;AAGlBE,MAAAA,IAAAA,EAAMF,OAAAA,EAAAA,IAAAA,KAHY;AAAA,KAApBK;AAjFW;;AA2FbI,EAAAA,IAAAA,CAAAA,SAAAA,EAAAA,QAAAA,EAA0BT,OAAAA,GAA1BS,IAAAA,EAA0C;AACxC,UAAMJ,cAAAA,GAAiB,gBADiB,SACjB,CAAvB;;AACA,QAAI,CAAJ,gBAAqB;AAAA;AAFmB;;AAKxC,SAAK,IAAI5J,CAAAA,GAAJ,GAAWiK,EAAAA,GAAKL,cAAAA,CAArB,QAA4C5J,CAAAA,GAA5C,IAAoDA,CAApD,IAAyD;AACvD,UAAI4J,cAAAA,CAAAA,CAAAA,CAAAA,CAAAA,QAAAA,KAAJ,UAA6C;AAC3CA,QAAAA,cAAAA,CAAAA,MAAAA,CAAAA,CAAAA,EAD2C,CAC3CA;AAD2C;AADU;AALjB;AA3F7B;;AAAA;;;;AAyGf,4BAA4B;AAC1B,SAAOnF,IAAAA,CAAAA,GAAAA,CAASA,IAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAATA,GAASA,CAATA,EADmB,GACnBA,CAAP;AAj0BF;;AAo0BA,kBAAkB;AAChB/N,EAAAA,WAAAA,CAAAA,EAAAA,EAAgB;AAAA;AAAA;AAAA;AAAA,MAAhBA,EAAAA,EAA+C;AAC7C,mBAD6C,IAC7C;AAGA,eAAWoB,QAAAA,CAAAA,aAAAA,CAAuB4P,EAAAA,GAJW,YAIlC5P,CAAX;AAEA,eAAW,SANkC,UAM7C;AAGA,kBAAc2N,MAAAA,IAT+B,GAS7C;AACA,iBAAaD,KAAAA,IAVgC,GAU7C;AACA,iBAAa0E,KAAAA,IAXgC,GAW7C;AAGA,4BAAwB,cAAc,KAdO,KAc7C;AACA,mBAf6C,CAe7C;AAhBc;;AAmBhBC,EAAAA,UAAAA,GAAa;AACX,QAAI,KAAJ,gBAAyB;AACvB,6BADuB,eACvB;AACA,6BAAuB,aAAa,KAFb,KAEvB;AAFuB;AADd;;AAOX,8BAPW,eAOX;AACA,UAAMC,YAAAA,GAAgB,aAAa,KAAd,QAAC,GARX,GAQX;AACA,2BAAuBA,YAAAA,GAAe,KAT3B,KASX;AA5Bc;;AA+BhB,MAAI3C,OAAJ,GAAc;AACZ,WAAO,KADK,QACZ;AAhCc;;AAmChB,MAAIA,OAAJ,MAAiB;AACf,0BAAsB4C,KAAAA,CADP,GACOA,CAAtB;AACA,oBAAgBC,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,EAFD,GAECA,CAAhB;;AACA,SAHe,UAGf;AAtCc;;AAyChBC,EAAAA,QAAAA,CAAAA,MAAAA,EAAiB;AACf,QAAI,CAAJ,QAAa;AAAA;AADE;;AAIf,UAAMC,SAAAA,GAAYC,MAAAA,CAJH,UAIf;AACA,UAAMC,cAAAA,GAAiBF,SAAAA,CAAAA,WAAAA,GAAwBC,MAAAA,CALhC,WAKf;;AACA,QAAIC,cAAAA,GAAJ,GAAwB;AACtB,YAAMC,GAAAA,GAAM7S,QAAAA,CADU,eACtB;AACA6S,MAAAA,GAAAA,CAAAA,KAAAA,CAAAA,WAAAA,CAAAA,yBAAAA,EAAiD,iBAF3B,IAEtBA;AARa;AAzCD;;AAqDhB3S,EAAAA,IAAAA,GAAO;AACL,QAAI,CAAC,KAAL,SAAmB;AAAA;AADd;;AAIL,mBAJK,KAIL;AACA,2BALK,QAKL;AA1Dc;;AA6DhB4S,EAAAA,IAAAA,GAAO;AACL,QAAI,KAAJ,SAAkB;AAAA;AADb;;AAIL,mBAJK,IAIL;AACA,8BALK,QAKL;AAlEc;;AAAA;;;;AA0ElB,0CAA0C;AACxC,QAAMC,KAAAA,GAAN;AAAA,QACEC,GAAAA,GAAMC,GAAAA,CAFgC,MACxC;AAEA,MAAIC,KAAAA,GAHoC,CAGxC;;AACA,OAAK,IAAIC,IAAAA,GAAT,GAAmBA,IAAAA,GAAnB,KAA+B,EAA/B,MAAuC;AACrC,QAAI3G,SAAAA,CAAUyG,GAAAA,CAAd,IAAcA,CAAVzG,CAAJ,EAA0B;AACxBuG,MAAAA,KAAAA,CAAAA,IAAAA,CAAWE,GAAAA,CADa,IACbA,CAAXF;AADF,WAEO;AACLE,MAAAA,GAAAA,CAAAA,KAAAA,CAAAA,GAAaA,GAAAA,CADR,IACQA,CAAbA;AACA,QAFK,KAEL;AALmC;AAJC;;AAYxC,OAAK,IAAIE,IAAAA,GAAT,GAAmBD,KAAAA,GAAnB,KAAgC,QAAQ,EAAxC,OAAiD;AAC/CD,IAAAA,GAAAA,CAAAA,KAAAA,CAAAA,GAAaF,KAAAA,CADkC,IAClCA,CAAbE;AAbsC;AA94B1C;;AAu6BA,qCAAqC;AACnC,MAAIG,OAAAA,GAD+B,QACnC;AACA,MAAIC,kBAAAA,GACFD,OAAAA,CAAAA,aAAAA,IAAyBA,OAAAA,CAAAA,aAAAA,CAHQ,QAGRA,CAD3B;;AAGA,SAAOC,kBAAAA,EAAP,YAAuC;AACrCD,IAAAA,OAAAA,GAAUC,kBAAAA,CAD2B,UACrCD;AACAC,IAAAA,kBAAAA,GACED,OAAAA,CAAAA,aAAAA,IAAyBA,OAAAA,CAAAA,aAAAA,CAHU,QAGVA,CAD3BC;AAPiC;;AAWnC,SAXmC,kBAWnC;AAl7BF;;AA67BA,2CAA2C;AACzC;AACE,SADF,YACE;AACA;AACE,aAAOjJ,UAAAA,CAHX,IAGI;;AACF,SAJF,eAIE;AACA;AACE,aAAOA,UAAAA,CANX,GAMI;;AACF,SAPF,gBAOE;AACA;AACE,aAAOA,UAAAA,CATX,IASI;AATJ;;AAWA,SAAOA,UAAAA,CAZkC,IAYzC;AAz8BF;;AAo9BA,wCAAwC;AACtC;AACE;AACE,aAAOjB,WAAAA,CAFX,IAEI;;AACF;AACE,aAAOA,WAAAA,CAJX,MAII;;AACF;AACE,aAAOA,WAAAA,CANX,OAMI;;AACF;AACE,aAAOA,WAAAA,CARX,WAQI;;AACF;AACE,aAAOA,WAAAA,CAVX,MAUI;AAVJ;;AAYA,SAAOA,WAAAA,CAb+B,IAatC;AAj+BF;;;;;;;;;;;;;ACAA;;AAiBA,MAAMmK,mBAAAA,GAjBN,GAiBA;;AAmBA,uBAAuB;AACrB1U,EAAAA,WAAAA,CAAY;AAAA;AAAA;AAAA;AAAA;AAKV2U,IAAAA,cAAAA,GALU;AAMVC,IAAAA,oBAAAA,GANF5U;AAAY,GAAZA,EAOG;AACD,wBADC,YACD;AACA,oBAFC,QAED;AACA,uBAHC,IAGD;AACA,+BAJC,EAID;AACA,6BALC,IAKD;AACA,yBANC,KAMD;AACA,mBAPC,SAOD;AACA,sBAAkB,eARjB,CAQD;AACA,mBATC,EASD;AACA,oBAVC,QAUD;AACA,oBAXC,EAWD;AACA,0BAZC,cAYD;AACA,+BAbC,IAaD;AACA,gCAdC,oBAcD;AAEA,qCAhBC,IAgBD;;AACA,SAjBC,UAiBD;AAzBmB;;AA+BrB6U,EAAAA,gBAAAA,GAAmB;AACjB,yBADiB,IACjB;;AAEA,QAAI,CAAC,KAAL,sBAAgC;AAC9B,YAAMC,YAAAA,GAAe1T,QAAAA,CAAAA,aAAAA,CADS,KACTA,CAArB;AACA0T,MAAAA,YAAAA,CAAAA,SAAAA,GAF8B,cAE9BA;AACA,oCAH8B,YAG9B;AANe;;AASjB,gDAA4C;AAC1C7M,MAAAA,MAAAA,EAD0C;AAE1CnB,MAAAA,UAAAA,EAAY,KAF8B;AAG1CiO,MAAAA,WAAAA,EAAa,cAH6B;AAAA,KAA5C;AAxCmB;;AAqDrBvU,EAAAA,MAAAA,CAAOiS,OAAAA,GAAPjS,CAAAA,EAAoB;AAClB,QAAI,EAAE,oBAAoB,KAAtB,sBAAiD,KAArD,eAAyE;AAAA;AADvD;;AAIlB,SAJkB,MAIlB;AAEA,oBANkB,EAMlB;AACA,UAAMwU,aAAAA,GAAgB5T,QAAAA,CAPJ,sBAOIA,EAAtB;AACA,+BAA2B,+BAAgB;AACzC6T,MAAAA,WAAAA,EAAa,KAD4B;AAEzCC,MAAAA,iBAAAA,EAAmB,KAFsB;AAGzCpB,MAAAA,SAAAA,EAHyC;AAIzCjT,MAAAA,QAAAA,EAAU,KAJ+B;AAKzCsU,MAAAA,QAAAA,EAAU,KAL+B;AAMzCC,MAAAA,mBAAAA,EAAqB,KANoB;AAAA;AAQzCR,MAAAA,oBAAAA,EAAsB,KARmB;AAAA,KAAhB,CAA3B;AAUA,0CACE,MAAM;AACJ,oCADI,aACJ;;AACA,WAFI,gBAEJ;;AACA,WAHI,cAGJ;AAJJ,OAME,kBAAkB,CAxBF,CAkBlB;;AAWA,QAAI,CAAC,KAAL,2BAAqC;AACnC,uCAAiCtD,GAAAA,IAAO;AACtC,YAAIA,GAAAA,CAAAA,SAAAA,KAAkB,KAAlBA,OAAAA,IAAkCA,GAAAA,CAAAA,SAAAA,KAAkB,CAAxD,GAA4D;AAC1D,eAD0D,cAC1D;AAFoC;AADL,OACnC;;AAKA,kDAEE,KARiC,yBAMnC;AAnCgB;AArDC;;AAkGrBjQ,EAAAA,MAAAA,GAAS;AACP,QAAI,KAAJ,qBAA8B;AAC5B,+BAD4B,MAC5B;AACA,iCAF4B,IAE5B;AAHK;;AAKP,QAAI,KAAJ,2BAAoC;AAClC,mDAEE,KAHgC,yBAClC;;AAIA,uCALkC,IAKlC;AAVK;AAlGY;;AAgHrBgU,EAAAA,oBAAAA,CAAAA,cAAAA,EAAqC;AACnC,SADmC,MACnC;AACA,6BAFmC,cAEnC;AAlHmB;;AAqHrBC,EAAAA,cAAAA,CAAAA,WAAAA,EAA4B;AAC1B,SAD0B,MAC1B;AACA,uBAF0B,WAE1B;AAvHmB;;AA0HrBC,EAAAA,eAAAA,CAAAA,OAAAA,EAAAA,aAAAA,EAAwC;AAEtC,QAAI,CAAJ,SAAc;AACZ,aADY,EACZ;AAHoC;;AAKtC,UAAM;AAAA;AAAA,QALgC,IAKtC;AAEA,QAAIjM,CAAAA,GAAJ;AAAA,QACEkM,MAAAA,GARoC,CAOtC;AAEA,UAAMC,GAAAA,GAAML,mBAAAA,CAAAA,MAAAA,GAT0B,CAStC;AACA,UAAM1G,MAAAA,GAVgC,EAUtC;;AAEA,SAAK,IAAIgH,CAAAA,GAAJ,GAAWC,EAAAA,GAAKC,OAAAA,CAArB,QAAqCF,CAAAA,GAArC,IAA6CA,CAA7C,IAAkD;AAEhD,UAAIG,QAAAA,GAAWD,OAAAA,CAFiC,CAEjCA,CAAf;;AAGA,aAAOtM,CAAAA,KAAAA,GAAAA,IAAauM,QAAAA,IAAYL,MAAAA,GAASJ,mBAAAA,CAAAA,CAAAA,CAAAA,CAAzC,QAAwE;AACtEI,QAAAA,MAAAA,IAAUJ,mBAAAA,CAAAA,CAAAA,CAAAA,CAD4D,MACtEI;AACAlM,QAAAA,CAFsE;AALxB;;AAUhD,UAAIA,CAAAA,KAAM8L,mBAAAA,CAAV,QAAsC;AACpCpO,QAAAA,OAAAA,CAAAA,KAAAA,CADoC,mCACpCA;AAX8C;;AAchD,YAAM8O,KAAAA,GAAQ;AACZC,QAAAA,KAAAA,EAAO;AACLC,UAAAA,MAAAA,EADK;AAELC,UAAAA,MAAAA,EAAQJ,QAAAA,GAFH;AAAA;AADK,OAAd;AAQAA,MAAAA,QAAAA,IAAYK,aAAAA,CAtBoC,CAsBpCA,CAAZL;;AAIA,aAAOvM,CAAAA,KAAAA,GAAAA,IAAauM,QAAAA,GAAWL,MAAAA,GAASJ,mBAAAA,CAAAA,CAAAA,CAAAA,CAAxC,QAAuE;AACrEI,QAAAA,MAAAA,IAAUJ,mBAAAA,CAAAA,CAAAA,CAAAA,CAD2D,MACrEI;AACAlM,QAAAA,CAFqE;AA1BvB;;AA+BhDwM,MAAAA,KAAAA,CAAAA,GAAAA,GAAY;AACVE,QAAAA,MAAAA,EADU;AAEVC,QAAAA,MAAAA,EAAQJ,QAAAA,GAFE;AAAA,OAAZC;AAIApH,MAAAA,MAAAA,CAAAA,IAAAA,CAnCgD,KAmChDA;AA/CoC;;AAiDtC,WAjDsC,MAiDtC;AA3KmB;;AA8KrByH,EAAAA,cAAAA,CAAAA,OAAAA,EAAwB;AAEtB,QAAIP,OAAAA,CAAAA,MAAAA,KAAJ,GAA0B;AAAA;AAFJ;;AAKtB,UAAM;AAAA;AAAA;AAAA;AAAA;AAAA,QALgB,IAKtB;AAEA,UAAMQ,cAAAA,GAAiBC,OAAAA,KAAY1B,cAAAA,CAAAA,QAAAA,CAPb,OAOtB;AACA,UAAM2B,gBAAAA,GAAmB3B,cAAAA,CAAAA,QAAAA,CARH,QAQtB;AACA,UAAM4B,YAAAA,GAAe5B,cAAAA,CAAAA,KAAAA,CATC,YAStB;AACA,QAAI6B,OAAAA,GAVkB,IAUtB;AACA,UAAMC,QAAAA,GAAW;AACfT,MAAAA,MAAAA,EAAQ,CADO;AAEfC,MAAAA,MAAAA,EAFe;AAAA,KAAjB;;AAKA,yCAAqC;AACnC,YAAMD,MAAAA,GAASD,KAAAA,CADoB,MACnC;AACAZ,MAAAA,QAAAA,CAAAA,MAAAA,CAAAA,CAAAA,WAAAA,GAFmC,EAEnCA;AACA,aAAOuB,eAAAA,CAAAA,MAAAA,EAAAA,CAAAA,EAA2BX,KAAAA,CAA3BW,MAAAA,EAH4B,SAG5BA,CAAP;AAnBoB;;AAsBtB,sEAAkE;AAChE,YAAM3V,GAAAA,GAAMoU,QAAAA,CADoD,MACpDA,CAAZ;AACA,YAAMwB,OAAAA,GAAUvB,mBAAAA,CAAAA,MAAAA,CAAAA,CAAAA,SAAAA,CAAAA,UAAAA,EAFgD,QAEhDA,CAAhB;AAIA,YAAMwB,IAAAA,GAAOxV,QAAAA,CAAAA,cAAAA,CANmD,OAMnDA,CAAb;;AACA,qBAAe;AACb,cAAMyV,IAAAA,GAAOzV,QAAAA,CAAAA,aAAAA,CADA,MACAA,CAAb;AACAyV,QAAAA,IAAAA,CAAAA,SAAAA,GAAiB,YAFJ,WAEbA;AACAA,QAAAA,IAAAA,CAAAA,WAAAA,CAHa,IAGbA;AACA9V,QAAAA,GAAAA,CAAAA,WAAAA,CAJa,IAIbA;AACA,eAAO+V,SAAAA,CAAAA,QAAAA,CAAAA,UAAAA,IAAiCD,IAAAA,CAAjCC,UAAAA,GALM,CAKb;AAZ8D;;AAchE/V,MAAAA,GAAAA,CAAAA,WAAAA,CAdgE,IAchEA;AACA,aAfgE,CAehE;AArCoB;;AAwCtB,QAAIgW,EAAAA,GAAJ;AAAA,QACEC,EAAAA,GAAKD,EAAAA,GAzCe,CAwCtB;;AAEA,sBAAkB;AAChBA,MAAAA,EAAAA,GADgB,CAChBA;AACAC,MAAAA,EAAAA,GAAKpB,OAAAA,CAFW,MAEhBoB;AAFF,WAGO,IAAI,CAAJ,gBAAqB;AAAA;AA7CN;;AAkDtB,SAAK,IAAI1N,CAAAA,GAAT,IAAiBA,CAAAA,GAAjB,IAAyBA,CAAzB,IAA8B;AAC5B,YAAMwM,KAAAA,GAAQF,OAAAA,CADc,CACdA,CAAd;AACA,YAAMG,KAAAA,GAAQD,KAAAA,CAFc,KAE5B;AACA,YAAML,GAAAA,GAAMK,KAAAA,CAHgB,GAG5B;AACA,YAAMmB,UAAAA,GAAab,cAAAA,IAAkB9M,CAAAA,KAJT,gBAI5B;AACA,YAAM4N,eAAAA,GAAkBD,UAAAA,GAAAA,WAAAA,GALI,EAK5B;AACA,UAAIE,YAAAA,GANwB,CAM5B;;AAGA,UAAI,YAAYpB,KAAAA,CAAAA,MAAAA,KAAiBS,OAAAA,CAAjC,QAAiD;AAE/C,YAAIA,OAAAA,KAAJ,MAAsB;AACpBE,UAAAA,eAAAA,CAAgBF,OAAAA,CAAhBE,MAAAA,EAAgCF,OAAAA,CAAhCE,MAAAA,EAAgDD,QAAAA,CAD5B,MACpBC,CAAAA;AAH6C;;AAM/CU,QAAAA,SAAAA,CAN+C,KAM/CA,CAAAA;AANF,aAOO;AACLV,QAAAA,eAAAA,CAAgBF,OAAAA,CAAhBE,MAAAA,EAAgCF,OAAAA,CAAhCE,MAAAA,EAAgDX,KAAAA,CAD3C,MACLW,CAAAA;AAjB0B;;AAoB5B,UAAIX,KAAAA,CAAAA,MAAAA,KAAiBN,GAAAA,CAArB,QAAiC;AAC/B0B,QAAAA,YAAAA,GAAeT,eAAAA,CACbX,KAAAA,CADaW,MAAAA,EAEbX,KAAAA,CAFaW,MAAAA,EAGbjB,GAAAA,CAHaiB,MAAAA,EAIb,cAL6B,eAChBA,CAAfS;AADF,aAOO;AACLA,QAAAA,YAAAA,GAAeT,eAAAA,CACbX,KAAAA,CADaW,MAAAA,EAEbX,KAAAA,CAFaW,MAAAA,EAGbD,QAAAA,CAHaC,MAAAA,EAIb,oBALG,eACUA,CAAfS;;AAMA,aAAK,IAAIE,EAAAA,GAAKtB,KAAAA,CAAAA,MAAAA,GAAT,GAA2BuB,EAAAA,GAAK7B,GAAAA,CAArC,QAAiD4B,EAAAA,GAAjD,IAA0DA,EAA1D,IAAgE;AAC9DlC,UAAAA,QAAAA,CAAAA,EAAAA,CAAAA,CAAAA,SAAAA,GAAyB,qBADqC,eAC9DA;AARG;;AAULiC,QAAAA,SAAAA,CAAAA,GAAAA,EAAe,kBAVV,eAULA,CAAAA;AArC0B;;AAuC5BZ,MAAAA,OAAAA,GAvC4B,GAuC5BA;;AAEA,sBAAgB;AAEd7B,QAAAA,cAAAA,CAAAA,mBAAAA,CAAmC;AACjCtI,UAAAA,OAAAA,EAAS8I,QAAAA,CAASY,KAAAA,CADe,MACxBZ,CADwB;AAAA;AAGjCpO,UAAAA,SAAAA,EAHiC;AAIjCwQ,UAAAA,UAAAA,EAJiC;AAAA,SAAnC5C;AA3C0B;AAlDR;;AAsGtB,iBAAa;AACX+B,MAAAA,eAAAA,CAAgBF,OAAAA,CAAhBE,MAAAA,EAAgCF,OAAAA,CAAhCE,MAAAA,EAAgDD,QAAAA,CADrC,MACXC,CAAAA;AAvGoB;AA9KH;;AAyRrBc,EAAAA,cAAAA,GAAiB;AAEf,QAAI,CAAC,KAAL,eAAyB;AAAA;AAFV;;AAKf,UAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QALS,IAKf;AAEA,QAAIC,kBAAAA,GAAqB,CAPV,CAOf;;AAGA,SAAK,IAAInO,CAAAA,GAAJ,GAAWiK,EAAAA,GAAKqC,OAAAA,CAArB,QAAqCtM,CAAAA,GAArC,IAA6CA,CAA7C,IAAkD;AAChD,YAAMwM,KAAAA,GAAQF,OAAAA,CADkC,CAClCA,CAAd;AACA,YAAMG,KAAAA,GAAQhI,IAAAA,CAAAA,GAAAA,CAAAA,kBAAAA,EAA6B+H,KAAAA,CAAAA,KAAAA,CAFK,MAElC/H,CAAd;;AACA,WAAK,IAAI2J,CAAAA,GAAJ,OAAejC,GAAAA,GAAMK,KAAAA,CAAAA,GAAAA,CAA1B,QAA4C4B,CAAAA,IAA5C,KAAsDA,CAAtD,IAA2D;AACzD,cAAM3W,GAAAA,GAAMoU,QAAAA,CAD6C,CAC7CA,CAAZ;AACApU,QAAAA,GAAAA,CAAAA,WAAAA,GAAkBqU,mBAAAA,CAFuC,CAEvCA,CAAlBrU;AACAA,QAAAA,GAAAA,CAAAA,SAAAA,GAHyD,EAGzDA;AAN8C;;AAQhD0W,MAAAA,kBAAAA,GAAqB3B,KAAAA,CAAAA,GAAAA,CAAAA,MAAAA,GAR2B,CAQhD2B;AAlBa;;AAqBf,QAAI,CAAC9C,cAAAA,EAAL,kBAAuC;AAAA;AArBxB;;AA0Bf,UAAMgD,WAAAA,GAAchD,cAAAA,CAAAA,WAAAA,CAAAA,OAAAA,KA1BL,IA0Bf;AACA,UAAMiD,iBAAAA,GAAoBjD,cAAAA,CAAAA,iBAAAA,CAAAA,OAAAA,KA3BX,IA2Bf;AAEA,mBAAe,kCA7BA,iBA6BA,CAAf;;AACA,wBAAoB,KA9BL,OA8Bf;AAvTmB;;AAiUrBkD,EAAAA,UAAAA,GAAa;AACX,UAAM9W,GAAAA,GAAM,KADD,YACX;AACA,QAAI+W,eAAAA,GAFO,IAEX;AAEA/W,IAAAA,GAAAA,CAAAA,gBAAAA,CAAAA,WAAAA,EAAkCuQ,GAAAA,IAAO;AACvC,UAAI,6BAA6B,KAAjC,qBAA2D;AACzD,gDADyD,IACzD;;AACA,6BAGE;AACAc,UAAAA,YAAAA,CADA,eACAA,CAAAA;AACA0F,UAAAA,eAAAA,GAFA,IAEAA;AAPuD;;AAAA;AADpB;;AAavC,YAAMrC,GAAAA,GAAM1U,GAAAA,CAAAA,aAAAA,CAb2B,eAa3BA,CAAZ;;AACA,UAAI,CAAJ,KAAU;AAAA;AAd6B;;AAsBrC,UAAIgX,SAAAA,GAAYzG,GAAAA,CAAAA,MAAAA,KAtBqB,GAsBrC;AAEEyG,MAAAA,SAAAA,GACEA,SAAAA,IACAvW,MAAAA,CAAAA,gBAAAA,CAAAA,GAAAA,EAAAA,gBAAAA,CAAAA,kBAAAA,MA1BiC,MAwBnCuW;;AAMF,qBAAe;AACb,cAAMC,SAAAA,GAAYjX,GAAAA,CADL,qBACKA,EAAlB;AACA,cAAM4N,CAAAA,GAAIZ,IAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAa,aAAYiK,SAAAA,CAAb,GAAC,IAA6BA,SAAAA,CAFvC,MAEHjK,CAAV;AACA0H,QAAAA,GAAAA,CAAAA,KAAAA,CAAAA,GAAAA,GAAiB,KAAD,GAAC,EAAD,OAAC,CAAD,CAAC,IAHJ,GAGbA;AAjCmC;;AAoCvCA,MAAAA,GAAAA,CAAAA,SAAAA,CAAAA,GAAAA,CApCuC,QAoCvCA;AAxCS,KAIX1U;AAuCAA,IAAAA,GAAAA,CAAAA,gBAAAA,CAAAA,SAAAA,EAAgC,MAAM;AACpC,UAAI,6BAA6B,KAAjC,qBAA2D;AAEvD+W,QAAAA,eAAAA,GAAkB,WAAW,MAAM;AACjC,cAAI,KAAJ,qBAA8B;AAC5B,oDAD4B,KAC5B;AAF+B;;AAIjCA,UAAAA,eAAAA,GAJiC,IAIjCA;AAJgB,WAFqC,mBAErC,CAAlBA;AAFuD;AADvB;;AAepC,YAAMrC,GAAAA,GAAM1U,GAAAA,CAAAA,aAAAA,CAfwB,eAexBA,CAAZ;;AACA,UAAI,CAAJ,KAAU;AAAA;AAhB0B;;AAoBlC0U,MAAAA,GAAAA,CAAAA,KAAAA,CAAAA,GAAAA,GApBkC,EAoBlCA;AAEFA,MAAAA,GAAAA,CAAAA,SAAAA,CAAAA,MAAAA,CAtBoC,QAsBpCA;AAjES,KA2CX1U;AA5WmB;;AAAA;;;;AA0YvB,8BAA8B;AAS5BkX,EAAAA,sBAAAA,CAAAA,YAAAA,EAAAA,SAAAA,EAAAA,QAAAA,EAIErD,oBAAAA,GAJFqD,KAAAA,EAAAA,QAAAA,EAME;AACA,WAAO,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAArB,CAAP;AAhB0B;;AAAA;;;;;;;;;;;;;;;AC/Z9B;;AAfA;;AAAA;;AAyBA,qCAAqC;AACnC,QAAM7J,CAAAA,GAAIhN,QAAAA,CAAAA,aAAAA,CADyB,GACzBA,CAAV;;AACA,MAAI,CAACgN,CAAAA,CAAL,OAAc;AACZ,UAAM,UADM,gDACN,CAAN;AAHiC;;AAKnCA,EAAAA,CAAAA,CAAAA,IAAAA,GALmC,OAKnCA;AACAA,EAAAA,CAAAA,CAAAA,MAAAA,GANmC,SAMnCA;;AAGA,MAAI,cAAJ,GAAqB;AACnBA,IAAAA,CAAAA,CAAAA,QAAAA,GADmB,QACnBA;AAViC;;AAclC,oBAAiBhN,QAAAA,CAAlB,eAAC,EAAD,WAAC,CAdkC,CAclC;AACDgN,EAAAA,CAAAA,CAfmC,KAenCA;AACAA,EAAAA,CAAAA,CAhBmC,MAgBnCA;AAzCF;;AA4CA,sBAAsB;AACpBpO,EAAAA,WAAAA,GAAc;AACZ,yBAAqB,IADT,OACS,EAArB;AAFkB;;AAKpBkY,EAAAA,WAAAA,CAAAA,GAAAA,EAAAA,QAAAA,EAA2B;AACzB,QAAI,CAACC,sCAAAA,GAAAA,EAAL,oBAAKA,CAAL,EAAwD;AAAA;AAD/B;;AAIzBC,IAAAA,QAAAA,CAASC,GAAAA,GAATD,wBAAAA,EAJyB,QAIzBA,CAAAA;AATkB;;AAYpBE,EAAAA,YAAAA,CAAAA,IAAAA,EAAAA,QAAAA,EAAAA,WAAAA,EAA0C;AACxC,UAAMC,OAAAA,GAAUC,+BAAAA,IAAAA,EAAAA,WAAAA,EAGdC,iCAJsC,sBACxBD,CAAhB;AAKAJ,IAAAA,QAAAA,CAAAA,OAAAA,EANwC,QAMxCA,CAAAA;AAlBkB;;AAwBpBM,EAAAA,kBAAAA,CAAAA,OAAAA,EAAAA,IAAAA,EAAAA,QAAAA,EAA4C;AAC1C,UAAMC,SAAAA,GAAYC,yBADwB,QACxBA,CAAlB;AACA,UAAMC,WAAAA,GAAcF,SAAAA,GAAAA,iBAAAA,GAFsB,EAE1C;;AAEA,QAAIA,SAAAA,IAAa,CAACF,iCAAlB,wBAA8D;AAC5D,UAAIF,OAAAA,GAAU,uBAD8C,OAC9C,CAAd;;AACA,UAAI,CAAJ,SAAc;AACZA,QAAAA,OAAAA,GAAUO,GAAAA,CAAAA,eAAAA,CAAoB,SAAS,CAAT,IAAS,CAAT,EAAiB;AAAEC,UAAAA,IAAAA,EADrC;AACmC,SAAjB,CAApBD,CAAVP;;AACA,wCAFY,OAEZ;AAJ0D;;AAM5D,UAN4D,SAM5D;AAGES,MAAAA,SAAAA,GAAY,WAAWC,kBAAAA,CAAmBV,OAAAA,GAAAA,GAAAA,GATgB,QASnCU,CAAvBD;;AAWF,UAAI;AACFxX,QAAAA,MAAAA,CAAAA,IAAAA,CADE,SACFA;AACA,eAFE,IAEF;AAFF,QAGE,WAAW;AACXwF,QAAAA,OAAAA,CAAAA,KAAAA,CAAc,yBADH,EACXA;AAGA8R,QAAAA,GAAAA,CAAAA,eAAAA,CAJW,OAIXA;;AACA,kCALW,OAKX;AA5B0D;AAJpB;;AAoC1C,sCApC0C,WAoC1C;AACA,WArC0C,KAqC1C;AA7DkB;;AAsEpBV,EAAAA,QAAAA,CAAAA,IAAAA,EAAAA,GAAAA,EAAAA,QAAAA,EAA8Bc,eAAAA,GAA9Bd,UAAAA,EAA4D;AAC1D,QAAIK,iCAAJ,wBAAgD;AAE9C,4BAF8C,QAE9C;AAF8C;AADU;;AAM1D,UAAMF,OAAAA,GAAUO,GAAAA,CAAAA,eAAAA,CAN0C,IAM1CA,CAAhB;AACAV,IAAAA,QAAAA,CAAAA,OAAAA,EAP0D,QAO1DA,CAAAA;AA7EkB;;AAAA;;;;;;;;;;;;;;AC7BtB,MAAMK,mBAAAA,GAAsBpS,MAAAA,CAAAA,MAAAA,CAf5B,IAe4BA,CAA5B;;AACiE;AAC/D,QAAM8S,SAAAA,GACH,oCAAoCC,SAAAA,CAArC,SAAC,IAF4D,EAC/D;AAEA,QAAMC,QAAAA,GACH,oCAAoCD,SAAAA,CAArC,QAAC,IAJ4D,EAG/D;AAEA,QAAME,cAAAA,GACH,oCAAoCF,SAAAA,CAArC,cAAC,IAN4D,CAK/D;AAGA,QAAMG,SAAAA,GAAY,eAR6C,SAQ7C,CAAlB;AACA,QAAMC,KAAAA,GACJ,+CACCH,QAAAA,KAAAA,UAAAA,IAA2BC,cAAAA,GAXiC,CAS/D;AAGA,QAAMG,WAAAA,GAAc,aAZ2C,SAY3C,CAApB;;AAIC,iCAA8B;AAG7B,qBAAiB;AACfhB,MAAAA,mBAAAA,CAAAA,sBAAAA,GADe,IACfA;AAJ2B;AAhBgC,GAgB9D,GAAD;;AAUC,wCAAqC;AACpC,QAAIe,KAAAA,IAAJ,WAAwB;AACtBf,MAAAA,mBAAAA,CAAAA,eAAAA,GADsB,OACtBA;AAFkC;AA1ByB,GA0B9D,GAAD;AA1CF;AAiDA,MAAMiB,UAAAA,GAAa;AACjBC,EAAAA,MAAAA,EADiB;AAEjBC,EAAAA,GAAAA,EAFiB;AAGjBC,EAAAA,MAAAA,EAHiB;AAIjBC,EAAAA,UAAAA,EAJiB;AAAA,CAAnB;;AAWA,MAAMC,cAAAA,GAAiB;AACrBC,EAAAA,gBAAAA,EAAkB;AAEhBzM,IAAAA,KAAAA,EAFgB;AAGhB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHV;AAAA,GADG;AAMrBQ,EAAAA,UAAAA,EAAY;AAEV3M,IAAAA,KAAAA,EAFU;AAGV0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHI;AAAA,GANS;AAWrBS,EAAAA,gBAAAA,EAAkB;AAEhB5M,IAAAA,KAAAA,EAFgB;AAGhB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHV;AAAA,GAXG;AAgBrBU,EAAAA,cAAAA,EAAgB;AAEd7M,IAAAA,KAAAA,EAFc;AAGd0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHQ;AAAA,GAhBK;AAqBrBW,EAAAA,iBAAAA,EAAmB;AAEjB9M,IAAAA,KAAAA,EAFiB;AAGjB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHT;AAAA,GArBE;AA0BrBY,EAAAA,iBAAAA,EAAmB;AAEjB/M,IAAAA,KAAAA,EAFiB;AAGjB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHT;AAAA,GA1BE;AA+BrBa,EAAAA,qBAAAA,EAAuB;AAErBhN,IAAAA,KAAAA,EAFqB;AAGrB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHL;AAAA,GA/BF;AAoCrBrZ,EAAAA,eAAAA,EAAiB;AAEfkN,IAAAA,KAAAA,EAFe;AAGf0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHX;AAAA,GApCI;AAyCrBzT,EAAAA,eAAAA,EAAiB;AAEfsH,IAAAA,KAAAA,EAFe;AAGf0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHS;AAAA,GAzCI;AA8CrB1T,EAAAA,kBAAAA,EAAoB;AAElBuH,IAAAA,KAAAA,EAFkB;AAGlB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHR;AAAA,GA9CC;AAmDrBc,EAAAA,gBAAAA,EAAkB;AAEhBjN,IAAAA,KAAAA,EAFgB;AAGhB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHV;AAAA,GAnDG;AAwDrBxT,EAAAA,qBAAAA,EAAuB;AAErBqH,IAAAA,KAAAA,EAFqB;AAGrB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHL;AAAA,GAxDF;AA6DrBxZ,EAAAA,kBAAAA,EAAoB;AAElBqN,IAAAA,KAAAA,EAFkB;AAGlB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHY;AAAA,GA7DC;AAkErBe,EAAAA,eAAAA,EAAiB;AAEflN,IAAAA,KAAAA,EAFe;AAGfmN,IAAAA,aAAAA,EAAejC,mBAAAA,CAHA;AAIfwB,IAAAA,IAAAA,EAAMP,UAAAA,CAJS;AAAA,GAlEI;AAwErBiB,EAAAA,aAAAA,EAAe;AAEbpN,IAAAA,KAAAA,EAFa;AAGb0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHb;AAAA,GAxEM;AA6ErBkB,EAAAA,eAAAA,EAAiB;AAEfrN,IAAAA,KAAAA,EAFe;AAGf0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHS;AAAA,GA7EI;AAkFrBmB,EAAAA,QAAAA,EAAU;AAERtN,IAAAA,KAAAA,EAFQ;AAGR0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHE;AAAA,GAlFW;AAuFrBvZ,EAAAA,sBAAAA,EAAwB;AAEtBoN,IAAAA,KAAAA,EAFsB;AAGtB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHJ;AAAA,GAvFH;AA4FrBoB,EAAAA,iBAAAA,EAAmB;AAEjBvN,IAAAA,KAAAA,EAAO,CAFU;AAGjB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHT;AAAA,GA5FE;AAiGrBqB,EAAAA,gBAAAA,EAAkB;AAEhBxN,IAAAA,KAAAA,EAAO,CAFS;AAGhB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHV;AAAA,GAjGG;AAsGrBsB,EAAAA,gBAAAA,EAAkB;AAEhBzN,IAAAA,KAAAA,EAAO,CAFS;AAGhB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHV;AAAA,GAtGG;AA2GrBuB,EAAAA,aAAAA,EAAe;AAEb1N,IAAAA,KAAAA,EAFa;AAGb0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHb;AAAA,GA3GM;AAgHrBwB,EAAAA,cAAAA,EAAgB;AAEd3N,IAAAA,KAAAA,EAFc;AAGd0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHZ;AAAA,GAhHK;AAqHrByB,EAAAA,cAAAA,EAAgB;AAEd5N,IAAAA,KAAAA,EAFc;AAGd0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHZ;AAAA,GArHK;AA0HrB0B,EAAAA,UAAAA,EAAY;AAEV7N,IAAAA,KAAAA,EAFU;AAGV0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,MAAAA,GAAoBA,UAAAA,CAHhB;AAAA,GA1HS;AAgIrB2B,EAAAA,UAAAA,EAAY;AAEV9N,IAAAA,KAAAA,EAFU;AAGV0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHI;AAAA,GAhIS;AAqIrB4B,EAAAA,OAAAA,EAAS;AAEP/N,IAAAA,KAAAA,EAFO;AAMP0M,IAAAA,IAAAA,EAAMP,UAAAA,CANC;AAAA,GArIY;AA6IrB6B,EAAAA,gBAAAA,EAAkB;AAEhBhO,IAAAA,KAAAA,EAFgB;AAGhB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,GAAAA,GAAiBA,UAAAA,CAHP;AAAA,GA7IG;AAkJrB8B,EAAAA,eAAAA,EAAiB;AAEfjO,IAAAA,KAAAA,EAFe;AAGf0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,GAAAA,GAAiBA,UAAAA,CAHR;AAAA,GAlJI;AAuJrB+B,EAAAA,YAAAA,EAAc;AAEZlO,IAAAA,KAAAA,EAFY;AAGZ0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,GAAAA,GAAiBA,UAAAA,CAHX;AAAA,GAvJO;AA4JrBgC,EAAAA,aAAAA,EAAe;AAEbnO,IAAAA,KAAAA,EAFa;AAGb0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,GAAAA,GAAiBA,UAAAA,CAHV;AAAA,GA5JM;AAiKrBiC,EAAAA,UAAAA,EAAY;AAEVpO,IAAAA,KAAAA,EAFU;AAGV0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHI;AAAA,GAjKS;AAsKrBkC,EAAAA,SAAAA,EAAW;AAETrO,IAAAA,KAAAA,EAFS;AAKT0M,IAAAA,IAAAA,EAAMP,UAAAA,CAAAA,GAAAA,GAAiBA,UAAAA,CALd;AAAA,GAtKU;AA6KrBmC,EAAAA,mBAAAA,EAAqB;AAEnBtO,IAAAA,KAAAA,EAFmB;AAGnB0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHa;AAAA,GA7KA;AAkLrBoC,EAAAA,eAAAA,EAAiB;AAEfvO,IAAAA,KAAAA,EAFe;AAGf0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHS;AAAA,GAlLI;AAuLrBqC,EAAAA,YAAAA,EAAc;AAEZxO,IAAAA,KAAAA,EAAO,CAFK;AAGZ0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHM;AAAA,GAvLO;AA4LrBsC,EAAAA,MAAAA,EAAQ;AAENzO,IAAAA,KAAAA,EAFM;AAGN0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHA;AAAA,GA5La;AAiMrBuC,EAAAA,mBAAAA,EAAqB;AAEnB1O,IAAAA,KAAAA,EAFmB;AAMnB0M,IAAAA,IAAAA,EAAMP,UAAAA,CANa;AAAA,GAjMA;AAyMrBwC,EAAAA,SAAAA,EAAW;AAET3O,IAAAA,KAAAA,EAFS;AAGT0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHG;AAAA,GAzMU;AA+MrByC,EAAAA,UAAAA,EAAY;AAEV5O,IAAAA,KAAAA,EAFU;AAGV0M,IAAAA,IAAAA,EAAMP,UAAAA,CAHI;AAAA,GA/MS;AAoNrB0C,EAAAA,SAAAA,EAAW;AAET7O,IAAAA,KAAAA,EAFS;AAMT0M,IAAAA,IAAAA,EAAMP,UAAAA,CANG;AAAA;AApNU,CAAvB;AAgOE;AACAK,EAAAA,cAAAA,CAAAA,kBAAAA,GAAoC;AAElCxM,IAAAA,KAAAA,EAFkC;AAGlC0M,IAAAA,IAAAA,EAAMP,UAAAA,CAH4B;AAAA,GAApCK;AAKAA,EAAAA,cAAAA,CAAAA,MAAAA,GAAwB;AAEtBxM,IAAAA,KAAAA,EAAO,mCAAmC6L,SAAAA,CAAnC,WAFe;AAGtBa,IAAAA,IAAAA,EAAMP,UAAAA,CAHgB;AAAA,GAAxBK;AAKAA,EAAAA,cAAAA,CAAAA,gBAAAA,GAAkC;AAEhCxM,IAAAA,KAAAA,EAFgC;AAMhC0M,IAAAA,IAAAA,EAAMP,UAAAA,CAN0B;AAAA,GAAlCK;AASAA,EAAAA,cAAAA,CAAAA,QAAAA,CAAAA,IAAAA,IAAgCL,UAAAA,CApBhC,UAoBAK;AAhTF;AA8TA,MAAMsC,WAAAA,GAAchW,MAAAA,CAAAA,MAAAA,CA9TpB,IA8ToBA,CAApB;;AAEA,iBAAiB;AACfrG,EAAAA,WAAAA,GAAc;AACZ,UAAM,UADM,+BACN,CAAN;AAFa;;AAKf,SAAOsc,GAAP,OAAiB;AACf,UAAMC,UAAAA,GAAaF,WAAAA,CADJ,IACIA,CAAnB;;AACA,QAAIE,UAAAA,KAAJ,WAA8B;AAC5B,aAD4B,UAC5B;AAHa;;AAKf,UAAMC,aAAAA,GAAgBzC,cAAAA,CALP,IAKOA,CAAtB;;AACA,QAAIyC,aAAAA,KAAJ,WAAiC;AAC/B,aAAOA,aAAAA,CAAAA,aAAAA,IAA+BA,aAAAA,CADP,KAC/B;AAPa;;AASf,WATe,SASf;AAda;;AAiBf,SAAOC,MAAP,CAAcxC,IAAAA,GAAd,MAA2B;AACzB,UAAMpH,OAAAA,GAAUxM,MAAAA,CAAAA,MAAAA,CADS,IACTA,CAAhB;;AACA,uCAAmC;AACjC,YAAMmW,aAAAA,GAAgBzC,cAAAA,CADW,IACXA,CAAtB;;AACA,gBAAU;AACR,YAAK,QAAOyC,aAAAA,CAAR,IAAC,MAAL,GAAuC;AAAA;AAD/B;;AAIR,YAAIvC,IAAAA,KAASP,UAAAA,CAAb,YAAoC;AAClC,gBAAMnM,KAAAA,GAAQiP,aAAAA,CAAd;AAAA,gBACEE,SAAAA,GAAY,OAFoB,KAClC;;AAGA,cACEA,SAAAA,KAAAA,SAAAA,IACAA,SAAAA,KADAA,QAAAA,IAECA,SAAAA,KAAAA,QAAAA,IAA0BzV,MAAAA,CAAAA,SAAAA,CAH7B,KAG6BA,CAH7B,EAIE;AACA4L,YAAAA,OAAAA,CAAAA,IAAAA,CAAAA,GADA,KACAA;AADA;AARgC;;AAYlC,gBAAM,UAAU,oCAZkB,EAY5B,CAAN;AAhBM;AAFuB;;AAqBjC,YAAM0J,UAAAA,GAAaF,WAAAA,CArBc,IAqBdA,CAAnB;AACAxJ,MAAAA,OAAAA,CAAAA,IAAAA,CAAAA,GACE0J,UAAAA,KAAAA,SAAAA,GAAAA,UAAAA,GAEIC,aAAAA,CAAAA,aAAAA,IAA+BA,aAAAA,CAzBJ,KAsBjC3J;AAxBuB;;AA6BzB,WA7ByB,OA6BzB;AA9Ca;;AAiDf,SAAO8J,GAAP,cAAwB;AACtBN,IAAAA,WAAAA,CAAAA,IAAAA,CAAAA,GADsB,KACtBA;AAlDa;;AAqDf,SAAOO,MAAP,UAAuB;AACrB,gCAA4B;AAC1BP,MAAAA,WAAAA,CAAAA,IAAAA,CAAAA,GAAoBxJ,OAAAA,CADM,IACNA,CAApBwJ;AAFmB;AArDR;;AA2Df,SAAOQ,MAAP,OAAoB;AAClB,WAAOR,WAAAA,CADW,IACXA,CAAP;AA5Da;;AAAA;;;;;;;;;;;;;;;AChUjB;;AAgBA;;AAEA,MAAMS,OAAAA,GAAU1b,QAAAA,CAlBhB,OAkBA;;AAEA,kBAAkB;AAChBpB,EAAAA,WAAAA,CAAAA,IAAAA,EAAkB;AAChB,iBADgB,IAChB;AACA,kBAAc,YAAY,qBAAqB;AAC7C8c,MAAAA,OAAAA,CAAAA,WAAAA,CAAoBC,+BAApBD,IAAoBC,CAApBD,EAAyC,MAAM;AAC7CzK,QAAAA,OAAAA,CAD6C,OAC7CA,CAAAA;AAF2C,OAC7CyK;AAHc,KAEF,CAAd;AAHc;;AAUhB,QAAME,WAAN,GAAoB;AAClB,UAAM5c,IAAAA,GAAO,MAAM,KADD,MAClB;AACA,WAAOA,IAAAA,CAFW,WAEXA,EAAP;AAZc;;AAehB,QAAM6c,YAAN,GAAqB;AACnB,UAAM7c,IAAAA,GAAO,MAAM,KADA,MACnB;AACA,WAAOA,IAAAA,CAFY,YAEZA,EAAP;AAjBc;;AAoBhB,QAAMkc,GAAN,MAAe5X,IAAAA,GAAf,MAA4BmB,QAAAA,GAAWC,iCAAAA,GAAAA,EAAvC,IAAuCA,CAAvC,EAAmE;AACjE,UAAM1F,IAAAA,GAAO,MAAM,KAD8C,MACjE;AACA,WAAOA,IAAAA,CAAAA,GAAAA,CAAAA,GAAAA,EAAAA,IAAAA,EAF0D,QAE1DA,CAAP;AAtBc;;AAyBhB,QAAM8c,SAAN,UAAyB;AACvB,UAAM9c,IAAAA,GAAO,MAAM,KADI,MACvB;AACA,WAAOA,IAAAA,CAAAA,SAAAA,CAFgB,OAEhBA,CAAP;AA3Bc;;AAAA;;;;;;;;ACpBlB;;AAoCAgB,QAAAA,CAAAA,OAAAA,GAAoB,uCAAsC;AACxD,MAAI+b,SAAAA,GADoD,EACxD;AACA,MAAIC,SAAAA,GAFoD,EAExD;AACA,MAAIC,SAAAA,GAHoD,aAGxD;AACA,MAAIC,SAAAA,GAJoD,EAIxD;AACA,MAAIC,OAAAA,GALoD,EAKxD;AACA,MAAIC,WAAAA,GANoD,SAMxD;AAeA,MAAIC,qBAAAA,GArBoD,IAqBxD;;AAUA,kCAAgC;AAC9B,WAAOrc,QAAAA,CAAAA,gBAAAA,CADuB,+BACvBA,CAAP;AAhCsD;;AAmCxD,+BAA6B;AAC3B,QAAIsc,MAAAA,GAAStc,QAAAA,CAAAA,aAAAA,CADc,iCACdA,CAAb;AAEA,WAAOsc,MAAAA,GAAS/V,IAAAA,CAAAA,KAAAA,CAAW+V,MAAAA,CAApBA,SAAS/V,CAAT+V,GAHoB,IAG3B;AAtCsD;;AAyCxD,4CAA0C;AACxC,WAAOrR,OAAAA,GAAUA,OAAAA,CAAAA,gBAAAA,CAAVA,iBAAUA,CAAVA,GADiC,EACxC;AA1CsD;;AA6CxD,sCAAoC;AAClC,QAAI,CAAJ,SACE,OAFgC,EAEhC;AAEF,QAAIsR,MAAAA,GAAStR,OAAAA,CAAAA,YAAAA,CAJqB,cAIrBA,CAAb;AACA,QAAIuR,QAAAA,GAAWvR,OAAAA,CAAAA,YAAAA,CALmB,gBAKnBA,CAAf;AACA,QAAI3H,IAAAA,GAN8B,EAMlC;;AACA,kBAAc;AACZ,UAAI;AACFA,QAAAA,IAAAA,GAAOiD,IAAAA,CAAAA,KAAAA,CADL,QACKA,CAAPjD;AADF,QAEE,UAAU;AACVsC,QAAAA,OAAAA,CAAAA,IAAAA,CAAa,oCADH,MACVA;AAJU;AAPoB;;AAclC,WAAO;AAAEgK,MAAAA,EAAAA,EAAF;AAActM,MAAAA,IAAAA,EAAd;AAAA,KAAP;AA3DsD;;AA8DxD,kDAAgD;AAC9CmZ,IAAAA,SAAAA,GAAYA,SAAAA,IAAa,0BAA0B,CADL,CAC9CA;;AACAC,IAAAA,SAAAA,GAAYA,SAAAA,IAAa,sBAAsB,CAFD,CAE9CA;;AAEA,QAAIC,GAAAA,GAAM,IAJoC,cAIpC,EAAV;AACAA,IAAAA,GAAAA,CAAAA,IAAAA,CAAAA,KAAAA,EAAAA,GAAAA,EAL8C,qBAK9CA;;AACA,QAAIA,GAAAA,CAAJ,kBAA0B;AACxBA,MAAAA,GAAAA,CAAAA,gBAAAA,CADwB,2BACxBA;AAP4C;;AAS9CA,IAAAA,GAAAA,CAAAA,kBAAAA,GAAyB,YAAW;AAClC,UAAIA,GAAAA,CAAAA,UAAAA,IAAJ,GAAyB;AACvB,YAAIA,GAAAA,CAAAA,MAAAA,IAAAA,GAAAA,IAAqBA,GAAAA,CAAAA,MAAAA,KAAzB,GAA2C;AACzCF,UAAAA,SAAAA,CAAUE,GAAAA,CAD+B,YACzCF,CAAAA;AADF,eAEO;AACLC,UAAAA,SADK;AAHgB;AADS;AATU,KAS9CC;;AASAA,IAAAA,GAAAA,CAAAA,OAAAA,GAlB8C,SAkB9CA;AACAA,IAAAA,GAAAA,CAAAA,SAAAA,GAnB8C,SAmB9CA;;AAIA,QAAI;AACFA,MAAAA,GAAAA,CAAAA,IAAAA,CADE,IACFA;AADF,MAEE,UAAU;AACVD,MAAAA,SADU;AAzBkC;AA9DQ;;AAoHxD,uEAAqE;AACnE,QAAIE,OAAAA,GAAUC,IAAAA,CAAAA,OAAAA,CAAAA,SAAAA,EAAAA,EAAAA,KADqD,IACnE;;AAGA,8BAA0B;AACxB,UAAIC,IAAAA,CAAAA,WAAAA,CAAAA,IAAAA,IAAJ,GACE,OAFsB,IAEtB;AACF,aAAOA,IAAAA,CAAAA,OAAAA,CAAAA,OAAAA,EAAAA,IAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,IAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,IAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,IAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,IAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,IAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,GAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,GAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAAAA,GAAAA,EAAAA,OAAAA,CAAAA,MAAAA,EAHiB,GAGjBA,CAAP;AAPiE;;AAsBnE,6DAAyD;AACvD,UAAIC,UAAAA,GADmD,EACvD;AAGA,UAAIC,OAAAA,GAJmD,WAIvD;AACA,UAAIC,SAAAA,GALmD,aAKvD;AACA,UAAIC,SAAAA,GANmD,kBAMvD;AACA,UAAIC,QAAAA,GAPmD,gCAOvD;AACA,UAAIC,OAAAA,GARmD,wBAQvD;;AAGA,8EAAwE;AACtE,YAAIC,OAAAA,GAAUC,OAAAA,CAAAA,OAAAA,CAAAA,OAAAA,EAAAA,EAAAA,EAAAA,KAAAA,CADwD,SACxDA,CAAd;AACA,YAAIC,WAAAA,GAFkE,GAEtE;AACA,YAAIC,WAAAA,GAAcC,IAAAA,CAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,EAHoD,CAGpDA,CAAlB;AACA,YAAIC,QAAAA,GAJkE,KAItE;AACA,YAAIhJ,KAAAA,GALkE,EAKtE;;AAEA,6BAAqB;AAGnB,uBAAa;AACX,gBAAI,CAAC2I,OAAAA,CAAL,QAAqB;AACnBM,cAAAA,sBADmB;AAAA;AADV;;AAKX,gBAAIC,IAAAA,GAAOP,OAAAA,CALA,KAKAA,EAAX;AAGA,gBAAIJ,SAAAA,CAAAA,IAAAA,CAAJ,IAAIA,CAAJ,EARW;;AAYX,gCAAoB;AAClBvI,cAAAA,KAAAA,GAAQwI,SAAAA,CAAAA,IAAAA,CADU,IACVA,CAARxI;;AACA,yBAAW;AAIT6I,gBAAAA,WAAAA,GAAc7I,KAAAA,CAAAA,CAAAA,CAAAA,CAJL,WAIKA,EAAd6I;AACAG,gBAAAA,QAAAA,GAAYH,WAAAA,KAAD,GAACA,IACPA,WAAAA,KADM,IAACA,IACmBA,WAAAA,KANtB,WAKTG;AALS;AAAX,qBAQO,cAAc;AAAA;AAVH;;AAalBhJ,cAAAA,KAAAA,GAAQyI,QAAAA,CAAAA,IAAAA,CAbU,IAaVA,CAARzI;;AACA,yBAAW;AACTmJ,gBAAAA,UAAAA,CAAWjB,OAAAA,GAAUlI,KAAAA,CAArBmJ,CAAqBnJ,CAArBmJ,EADS,SACTA,CAAAA;AADS;AAdO;AAZT;;AAiCX,gBAAIC,GAAAA,GAAMF,IAAAA,CAAAA,KAAAA,CAjCC,OAiCDA,CAAV;;AACA,gBAAIE,GAAAA,IAAOA,GAAAA,CAAAA,MAAAA,IAAX,GAA4B;AAC1Bf,cAAAA,UAAAA,CAAWe,GAAAA,CAAXf,CAAWe,CAAXf,CAAAA,GAAqBgB,UAAAA,CAAWD,GAAAA,CADN,CACMA,CAAXC,CAArBhB;AAnCS;AAHM;AAPiD;;AAiDtEiB,QAAAA,SAjDsE;AAXjB;;AAgEvD,yCAAmC;AACjCC,QAAAA,WAAAA,CAAAA,GAAAA,EAAiB,mBAAkB;AACjCC,UAAAA,aAAAA,CAAAA,OAAAA,EAAAA,KAAAA,EADiC,QACjCA,CAAAA;AADFD,SAAAA,EAEG,YAAY;AACbrY,UAAAA,OAAAA,CAAAA,IAAAA,CAAaqR,GAAAA,GADA,aACbrR;AACAkG,UAAAA,QAFa;AAHkB,SACjCmS,CAAAA;AAjEqD;;AA0EvDC,MAAAA,aAAAA,CAAAA,IAAAA,EAAAA,IAAAA,EAA0B,YAAW;AACnCC,QAAAA,wBAAAA,CADmC,UACnCA,CAAAA;AA3EqD,OA0EvDD,CAAAA;AAhGiE;;AAsGnED,IAAAA,WAAAA,CAAAA,IAAAA,EAAkB,oBAAmB;AACnCjC,MAAAA,SAAAA,IADmC,QACnCA;AAGAoC,MAAAA,eAAAA,CAAAA,QAAAA,EAA0B,gBAAe;AAGvC,8BAAsB;AACpB;AAAA;AAAA,cAAcxQ,KAAAA,GAAQvK,GAAAA,CAAAA,WAAAA,CADF,GACEA,CAAtB;;AACA,cAAIuK,KAAAA,GAAJ,GAAe;AACbgC,YAAAA,EAAAA,GAAKvM,GAAAA,CAAAA,SAAAA,CAAAA,CAAAA,EADQ,KACRA,CAALuM;AACAyO,YAAAA,IAAAA,GAAOhb,GAAAA,CAAAA,SAAAA,CAAcuK,KAAAA,GAFR,CAENvK,CAAPgb;AAFF,iBAGO;AACLzO,YAAAA,EAAAA,GADK,GACLA;AACAyO,YAAAA,IAAAA,GAFK,SAELA;AAPkB;;AASpB,cAAI,CAACtC,SAAAA,CAAL,EAAKA,CAAL,EAAoB;AAClBA,YAAAA,SAAAA,CAAAA,EAAAA,CAAAA,GADkB,EAClBA;AAVkB;;AAYpBA,UAAAA,SAAAA,CAAAA,EAAAA,CAAAA,CAAAA,IAAAA,IAAsBuC,IAAAA,CAZF,GAYEA,CAAtBvC;AAfqC;;AAmBvC,6BAAqB;AACnBwC,UAAAA,eADmB;AAnBkB;AAJN,OAInCH,CAAAA;AAJFH,KAAAA,EAtGmE,eAsGnEA,CAAAA;AA1NsD;;AAyPxD,sCAAoC;AAGlC,cAAU;AACRR,MAAAA,IAAAA,GAAOA,IAAAA,CADC,WACDA,EAAPA;AAJgC;;AAOlC3R,IAAAA,QAAAA,GAAWA,QAAAA,IAAY,qBAAqB,CAPV,CAOlCA;;AAEA0S,IAAAA,KATkC;AAUlCtC,IAAAA,SAAAA,GAVkC,IAUlCA;AAIA,QAAIuC,SAAAA,GAAYC,oBAdkB,EAclC;AACA,QAAIC,SAAAA,GAAYF,SAAAA,CAfkB,MAelC;;AACA,QAAIE,SAAAA,KAAJ,GAAqB;AAEnB,UAAIC,IAAAA,GAAOC,iBAFQ,EAEnB;;AACA,UAAID,IAAAA,IAAQA,IAAAA,CAARA,OAAAA,IAAwBA,IAAAA,CAA5B,gBAAiD;AAC/ChZ,QAAAA,OAAAA,CAAAA,GAAAA,CAD+C,kDAC/CA;AACAmW,QAAAA,SAAAA,GAAY6C,IAAAA,CAAAA,OAAAA,CAFmC,IAEnCA,CAAZ7C;;AACA,YAAI,CAAJ,WAAgB;AACd,cAAI+C,aAAAA,GAAgBF,IAAAA,CAAAA,cAAAA,CADN,WACMA,EAApB;;AACA,kCAAwBA,IAAAA,CAAxB,SAAsC;AACpCG,YAAAA,WAAAA,GAAcA,WAAAA,CADsB,WACtBA,EAAdA;;AACA,gBAAIA,WAAAA,KAAJ,MAA0B;AACxBhD,cAAAA,SAAAA,GAAY6C,IAAAA,CAAAA,OAAAA,CADY,IACZA,CAAZ7C;AADwB;AAA1B,mBAGO,IAAIgD,WAAAA,KAAJ,eAAmC;AACxChD,cAAAA,SAAAA,GAAY6C,IAAAA,CAAAA,OAAAA,CAD4B,aAC5BA,CAAZ7C;AANkC;AAFxB;AAH+B;;AAe/CjQ,QAAAA,QAf+C;AAAjD,aAgBO;AACLlG,QAAAA,OAAAA,CAAAA,GAAAA,CADK,oCACLA;AApBiB;;AAuBnBwW,MAAAA,WAAAA,GAvBmB,UAuBnBA;AAvBmB;AAhBa;;AA4ClC,QAAI4C,gBAAAA,GA5C8B,IA4ClC;AACA,QAAIC,cAAAA,GA7C8B,CA6ClC;;AACAD,IAAAA,gBAAAA,GAAmB,YAAW;AAC5BC,MAAAA,cAD4B;;AAE5B,UAAIA,cAAAA,IAAJ,WAAiC;AAC/BnT,QAAAA,QAD+B;AAE/BsQ,QAAAA,WAAAA,GAF+B,UAE/BA;AAJ0B;AA9CI,KA8ClC4C;;AASA,oCAAgC;AAC9B,UAAInC,IAAAA,GAAOqC,IAAAA,CADmB,IAC9B;;AAGA,kBAAY,0BAAyB;AACnCC,QAAAA,aAAAA,CAAAA,IAAAA,EAAAA,IAAAA,EAAAA,QAAAA,EAAoC,YAAW;AAC7CvZ,UAAAA,OAAAA,CAAAA,IAAAA,CAAaiX,IAAAA,GADgC,aAC7CjX;AAEAA,UAAAA,OAAAA,CAAAA,IAAAA,CAAa,aAHgC,sBAG7CA;AACAsW,UAAAA,SAAAA,GAJ6C,EAI7CA;AAEApQ,UAAAA,QAN6C;AADZ,SACnCqT,CAAAA;AAL4B,OAI9B;AA3DgC;;AAuElC,SAAK,IAAIjX,CAAAA,GAAT,GAAgBA,CAAAA,GAAhB,WAA+BA,CAA/B,IAAoC;AAClC,UAAIkX,QAAAA,GAAW,qBAAqBX,SAAAA,CADF,CACEA,CAArB,CAAf;AACAW,MAAAA,QAAAA,CAAAA,IAAAA,CAAAA,IAAAA,EAFkC,gBAElCA;AAzEgC;AAzPoB;;AAuUxD,mBAAiB;AACfrD,IAAAA,SAAAA,GADe,EACfA;AACAC,IAAAA,SAAAA,GAFe,EAEfA;AACAE,IAAAA,SAAAA,GAHe,EAGfA;AA1UsD;;AAgWxD,gCAA8B;AAC5B,QAAImD,aAAAA,GAAgB;AAClB,YADkB;AAElB,YAFkB;AAGlB,YAHkB;AAIlB,YAJkB;AAKlB,aALkB;AAMlB,YANkB;AAOlB,YAPkB;AAQlB,aARkB;AASlB,aATkB;AAUlB,YAVkB;AAWlB,YAXkB;AAYlB,YAZkB;AAalB,YAbkB;AAclB,YAdkB;AAelB,YAfkB;AAgBlB,aAhBkB;AAiBlB,YAjBkB;AAkBlB,YAlBkB;AAmBlB,aAnBkB;AAoBlB,aApBkB;AAqBlB,YArBkB;AAsBlB,YAtBkB;AAuBlB,YAvBkB;AAwBlB,YAxBkB;AAyBlB,YAzBkB;AA0BlB,YA1BkB;AA2BlB,YA3BkB;AA4BlB,YA5BkB;AA6BlB,YA7BkB;AA8BlB,YA9BkB;AA+BlB,YA/BkB;AAgClB,YAhCkB;AAiClB,YAjCkB;AAkClB,YAlCkB;AAmClB,YAnCkB;AAoClB,YApCkB;AAqClB,aArCkB;AAsClB,YAtCkB;AAuClB,YAvCkB;AAwClB,aAxCkB;AAyClB,YAzCkB;AA0ClB,YA1CkB;AA2ClB,YA3CkB;AA4ClB,YA5CkB;AA6ClB,aA7CkB;AA8ClB,YA9CkB;AA+ClB,aA/CkB;AAgDlB,YAhDkB;AAiDlB,YAjDkB;AAkDlB,aAlDkB;AAmDlB,YAnDkB;AAoDlB,YApDkB;AAqDlB,YArDkB;AAsDlB,YAtDkB;AAuDlB,YAvDkB;AAwDlB,YAxDkB;AAyDlB,YAzDkB;AA0DlB,YA1DkB;AA2DlB,YA3DkB;AA4DlB,YA5DkB;AA6DlB,YA7DkB;AA8DlB,aA9DkB;AA+DlB,YA/DkB;AAgElB,YAhEkB;AAiElB,aAjEkB;AAkElB,aAlEkB;AAmElB,aAnEkB;AAoElB,aApEkB;AAqElB,aArEkB;AAsElB,YAtEkB;AAuElB,YAvEkB;AAwElB,YAxEkB;AAyElB,YAzEkB;AA0ElB,YA1EkB;AA2ElB,aA3EkB;AA4ElB,aA5EkB;AA6ElB,YA7EkB;AA8ElB,YA9EkB;AA+ElB,aA/EkB;AAgFlB,YAhFkB;AAiFlB,YAjFkB;AAkFlB,YAlFkB;AAmFlB,YAnFkB;AAoFlB,YApFkB;AAqFlB,YArFkB;AAsFlB,aAtFkB;AAuFlB,YAvFkB;AAwFlB,YAxFkB;AAyFlB,YAzFkB;AA0FlB,YA1FkB;AA2FlB,YA3FkB;AA4FlB,YA5FkB;AA6FlB,YA7FkB;AA8FlB,YA9FkB;AA+FlB,YA/FkB;AAgGlB,aAhGkB;AAiGlB,aAjGkB;AAkGlB,YAlGkB;AAmGlB,YAnGkB;AAoGlB,YApGkB;AAqGlB,YArGkB;AAsGlB,YAtGkB;AAuGlB,YAvGkB;AAwGlB,YAxGkB;AAyGlB,aAzGkB;AA0GlB,YA1GkB;AA2GlB,aA3GkB;AA4GlB,YA5GkB;AA6GlB,YA7GkB;AA8GlB,YA9GkB;AA+GlB,aA/GkB;AAgHlB,YAhHkB;AAiHlB,YAjHkB;AAkHlB,YAlHkB;AAmHlB,YAnHkB;AAoHlB,YApHkB;AAqHlB,aArHkB;AAsHlB,YAtHkB;AAuHlB,aAvHkB;AAwHlB,aAxHkB;AAyHlB,aAzHkB;AA0HlB,YA1HkB;AA2HlB,aA3HkB;AA4HlB,aA5HkB;AA6HlB,YA7HkB;AA8HlB,YA9HkB;AA+HlB,aA/HkB;AAgIlB,YAhIkB;AAiIlB,YAjIkB;AAkIlB,aAlIkB;AAmIlB,aAnIkB;AAoIlB,aApIkB;AAqIlB,aArIkB;AAsIlB,aAtIkB;AAuIlB,YAvIkB;AAwIlB,YAxIkB;AAyIlB,YAzIkB;AA0IlB,YA1IkB;AA2IlB,YA3IkB;AA4IlB,aA5IkB;AA6IlB,YA7IkB;AA8IlB,YA9IkB;AA+IlB,YA/IkB;AAgJlB,aAhJkB;AAiJlB,YAjJkB;AAkJlB,YAlJkB;AAmJlB,aAnJkB;AAoJlB,YApJkB;AAqJlB,YArJkB;AAsJlB,aAtJkB;AAuJlB,YAvJkB;AAwJlB,YAxJkB;AAyJlB,YAzJkB;AA0JlB,YA1JkB;AA2JlB,YA3JkB;AA4JlB,YA5JkB;AA6JlB,aA7JkB;AA8JlB,YA9JkB;AA+JlB,YA/JkB;AAgKlB,YAhKkB;AAiKlB,YAjKkB;AAkKlB,aAlKkB;AAmKlB,YAnKkB;AAoKlB,aApKkB;AAqKlB,YArKkB;AAsKlB,YAtKkB;AAuKlB,aAvKkB;AAwKlB,YAxKkB;AAyKlB,YAzKkB;AA0KlB,YA1KkB;AAAA,KAApB;;AA8KA,2BAAuB;AACrB,aAAOC,IAAAA,CAAAA,OAAAA,CAAAA,CAAAA,MAAoB,CADN,CACrB;AAhL0B;;AAkL5B,sCAAkC;AAChC,aAAOC,KAAAA,IAAAA,CAAAA,IAAcjJ,CAAAA,IADW,GAChC;AAnL0B;;AAwL5B,QAAIkJ,WAAAA,GAAc;AAChB,WAAK,aAAY;AACf,eADe,OACf;AAFc;AAIhB,WAAK,aAAY;AACf,YAAKC,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,CAAAA,EAAL,EAAKA,CAAL,EACE,OAFa,KAEb;AACF,YAAInJ,CAAAA,KAAJ,GACE,OAJa,MAIb;AACF,YAAKmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAAL,EAAKA,CAAL,EACE,OANa,MAMb;AACF,YAAInJ,CAAAA,IAAJ,GACE,OARa,KAQb;AACF,YAAIA,CAAAA,IAAJ,GACE,OAVa,KAUb;AACF,eAXe,OAWf;AAfc;AAiBhB,WAAK,aAAY;AACf,YAAIA,CAAAA,KAAAA,CAAAA,IAAYA,CAAAA,GAAD,EAACA,KAAhB,GACE,OAFa,MAEb;AACF,YAAIA,CAAAA,IAAJ,GACE,OAJa,KAIb;AACF,YAAIA,CAAAA,IAAJ,GACE,OANa,KAMb;AACF,eAPe,OAOf;AAxBc;AA0BhB,WAAK,aAAY;AACf,YAAIA,CAAAA,IAAJ,GACE,OAFa,KAEb;AACF,eAHe,OAGf;AA7Bc;AA+BhB,WAAK,aAAY;AACf,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAL,CAAKA,CAAL,EACE,OAFa,KAEb;AACF,eAHe,OAGf;AAlCc;AAoChB,WAAK,aAAY;AACf,YAAKA,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAAuBnJ,CAAAA,IAA5B,GACE,OAFa,KAEb;AACF,eAHe,OAGf;AAvCc;AAyChB,WAAK,aAAY;AACf,YAAIA,CAAAA,KAAJ,GACE,OAFa,MAEb;AACF,YAAKA,CAAAA,GAAD,EAACA,IAAD,CAACA,IAAiBA,CAAAA,GAAD,GAACA,IAAtB,IACE,OAJa,KAIb;AACF,eALe,OAKf;AA9Cc;AAgDhB,WAAK,aAAY;AACf,YAAIA,CAAAA,IAAJ,GACE,OAFa,KAEb;AACF,YAAIA,CAAAA,IAAJ,GACE,OAJa,KAIb;AACF,eALe,OAKf;AArDc;AAuDhB,WAAK,aAAY;AACf,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAL,CAAKA,CAAL,EACE,OAFa,KAEb;AACF,YAAKA,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAL,EAAKA,CAAL,EACE,OAJa,MAIb;AACF,YAAInJ,CAAAA,IAAJ,GACE,OANa,KAMb;AACF,YAAIA,CAAAA,IAAJ,GACE,OARa,KAQb;AACF,eATe,OASf;AAhEc;AAkEhB,WAAK,aAAY;AACf,YAAIA,CAAAA,KAAAA,CAAAA,IAAWA,CAAAA,IAAAA,CAAAA,IAAWmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,CAAAA,EAA1B,EAA0BA,CAA1B,EACE,OAFa,KAEb;AACF,YAAInJ,CAAAA,IAAJ,GACE,OAJa,KAIb;AACF,eALe,OAKf;AAvEc;AAyEhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAA8B,CAAEA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAArC,EAAqCA,CAArC,EACE,OAFc,KAEd;AACF,YAAKnJ,CAAAA,GAAD,EAACA,IAAD,CAACA,IAAgB,CAAEmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAAvB,EAAuBA,CAAvB,EACE,OAJc,KAId;AACF,eALgB,OAKhB;AA9Ec;AAgFhB,YAAM,aAAY;AAChB,YAAKA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAA8B,CAAEA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAArC,EAAqCA,CAArC,EACE,OAFc,KAEd;AACF,YAAKnJ,CAAAA,GAAD,EAACA,KAAD,CAACA,IACAmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EADD,CACCA,CADAnJ,IAEAmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAFL,EAEKA,CAFL,EAGE,OANc,MAMd;AACF,YAAKnJ,CAAAA,GAAD,EAACA,IAAD,CAACA,IAAiBA,CAAAA,GAAD,GAACA,IAAtB,IACE,OARc,KAQd;AACF,eATgB,OAShB;AAzFc;AA2FhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAL,CAAKA,CAAL,EACE,OAFc,KAEd;AACF,YAAInJ,CAAAA,IAAJ,GACE,OAJc,KAId;AACF,eALgB,OAKhB;AAhGc;AAkGhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAA8B,CAAEA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAArC,EAAqCA,CAArC,EACE,OAFc,KAEd;AACF,YAAInJ,CAAAA,IAAAA,CAAAA,IAAWmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EAAXnJ,CAAWmJ,CAAXnJ,IACCmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EADDnJ,CACCmJ,CADDnJ,IAECmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAFL,EAEKA,CAFL,EAGE,OANc,MAMd;AACF,YAAInJ,CAAAA,IAAJ,GACE,OARc,KAQd;AACF,eATgB,OAShB;AA3Gc;AA6GhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,CAAAA,EAAL,CAAKA,CAAL,EACE,OAFc,KAEd;AACF,YAAKnJ,CAAAA,GAAD,GAACA,IAAL,GACE,OAJc,KAId;AACF,YAAKA,CAAAA,GAAD,GAACA,IAAL,GACE,OANc,KAMd;AACF,eAPgB,OAOhB;AApHc;AAsHhB,YAAM,aAAY;AAChB,YAAIA,CAAAA,KAAAA,CAAAA,IAAYmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,CAAAA,EAAhB,EAAgBA,CAAhB,EACE,OAFc,KAEd;AACF,YAAKA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAAL,EAAKA,CAAL,EACE,OAJc,MAId;AACF,YAAInJ,CAAAA,IAAJ,GACE,OANc,KAMd;AACF,eAPgB,OAOhB;AA7Hc;AA+HhB,YAAM,aAAY;AAChB,YAAKA,CAAAA,GAAD,EAACA,IAAD,CAACA,IAAgBA,CAAAA,IAArB,IACE,OAFc,KAEd;AACF,eAHgB,OAGhB;AAlIc;AAoIhB,YAAM,aAAY;AAChB,YAAIA,CAAAA,IAAJ,GACE,OAFc,KAEd;AACF,YAAIA,CAAAA,KAAJ,GACE,OAJc,MAId;AACF,YAAIA,CAAAA,IAAJ,GACE,OANc,MAMd;AACF,YAAIA,CAAAA,IAAJ,GACE,OARc,KAQd;AACF,YAAIA,CAAAA,IAAJ,GACE,OAVc,KAUd;AACF,eAXgB,OAWhB;AA/Ic;AAiJhB,YAAM,aAAY;AAChB,YAAIA,CAAAA,KAAJ,GACE,OAFc,MAEd;AACF,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAAuBnJ,CAAAA,KAAxB,CAACmJ,IAAkCnJ,CAAAA,IAAvC,GACE,OAJc,KAId;AACF,eALgB,OAKhB;AAtJc;AAwJhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAL,EAAKA,CAAL,EACE,OAFc,KAEd;AACF,YAAKA,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAL,CAAKA,CAAL,EACE,OAJc,KAId;AACF,eALgB,OAKhB;AA7Jc;AA+JhB,YAAM,aAAY;AAChB,YAAK,WAAWnJ,CAAAA,GAAX,aAA+BA,CAAAA,GAAD,EAACA,IAAhC,CAAC,KAAiD,EAClD,UAAWA,CAAAA,GAAX,gBACAmJ,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EADA,EACAA,CADA,IAEAA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,GAAAA,EAAAA,EAAAA,EAHJ,EAGIA,CAHkD,CAAtD,EAKE,OANc,KAMd;AACF,YAAKnJ,CAAAA,GAAD,OAACA,KAAD,CAACA,IAAsBA,CAAAA,KAA3B,GACE,OARc,MAQd;AACF,YAAKA,CAAAA,GAAD,EAACA,IAAD,CAACA,IAAgB,CAAC,KAAMA,CAAAA,GAAN,KAAgB,YAAhB,CAAtB,EACE,OAVc,KAUd;AACF,YAAKA,CAAAA,GAAD,EAACA,IAAD,CAACA,IAAgB,CAAC,KAAMA,CAAAA,GAAN,KAAgB,YAAhB,CAAtB,EACE,OAZc,KAYd;AACF,eAbgB,OAahB;AA5Kc;AA8KhB,YAAM,aAAY;AAChB,YAAIA,CAAAA,KAAJ,GACE,OAFc,MAEd;AACF,YAAIA,CAAAA,IAAJ,GACE,OAJc,KAId;AACF,eALgB,OAKhB;AAnLc;AAqLhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAAwBA,SAAAA,CAAAA,CAAAA,EAAAA,EAAAA,EAA7B,EAA6BA,CAA7B,EACE,OAFc,KAEd;AACF,eAHgB,OAGhB;AAxLc;AA0LhB,YAAM,aAAY;AAChB,YAAKA,SAAAA,CAAWnJ,CAAAA,GAAXmJ,EAAAA,EAAAA,CAAAA,EAAD,CAACA,CAAAA,IAA+BnJ,CAAAA,GAAD,EAACA,KAApC,GACE,OAFc,KAEd;AACF,eAHgB,OAGhB;AA7Lc;AA+LhB,YAAM,aAAY;AAChB,YAAKmJ,SAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA,CAAAA,IAAuBA,SAAAA,CAAAA,CAAAA,EAAAA,EAAAA,EAA5B,EAA4BA,CAA5B,EACE,OAFc,KAEd;AACF,YAAI,QAAQ,OAAR,CAAJ,EACE,OAJc,KAId;AACF,YAAI,QAAQ,OAAR,CAAJ,EACE,OANc,KAMd;AACF,eAPgB,OAOhB;AAtMc;AAAA,KAAlB;AA2MA,QAAI7R,KAAAA,GAAQyR,aAAAA,CAAc5B,IAAAA,CAAAA,OAAAA,CAAAA,MAAAA,EAnYE,EAmYFA,CAAd4B,CAAZ;;AACA,QAAI,EAAE,SAAN,WAAI,CAAJ,EAA6B;AAC3BzZ,MAAAA,OAAAA,CAAAA,IAAAA,CAAa,qCADc,GAC3BA;AACA,aAAO,YAAW;AAAE,eAAF,OAAE;AAFO,OAE3B;AAtY0B;;AAwY5B,WAAO4Z,WAAAA,CAxYqB,KAwYrBA,CAAP;AAxuBsD;;AA4uBxDrD,EAAAA,OAAAA,CAAAA,MAAAA,GAAiB,iCAAgC;AAC/C,QAAI7F,CAAAA,GAAInP,UAAAA,CADuC,KACvCA,CAAR;AACA,QAAIoL,KAAAA,CAAJ,CAAIA,CAAJ,EACE,OAH6C,GAG7C;AAGF,QAAI8L,IAAAA,IAAJ,WACE,OAP6C,GAO7C;;AAGF,QAAI,CAAClC,OAAAA,CAAL,cAA2B;AACzBA,MAAAA,OAAAA,CAAAA,YAAAA,GAAuBuD,cAAAA,CADE,SACFA,CAAvBvD;AAX6C;;AAa/C,QAAIvO,KAAAA,GAAQ,MAAMuO,OAAAA,CAAAA,YAAAA,CAAN,CAAMA,CAAN,GAbmC,GAa/C;;AAGA,QAAI7F,CAAAA,KAAAA,CAAAA,IAAYjT,GAAAA,GAAD,QAACA,IAAhB,WAA8C;AAC5CiD,MAAAA,GAAAA,GAAMyV,SAAAA,CAAU1Y,GAAAA,GAAV0Y,QAAAA,CAAAA,CADsC,IACtCA,CAANzV;AADF,WAEO,IAAIgQ,CAAAA,IAAAA,CAAAA,IAAWjT,GAAAA,GAAD,OAACA,IAAf,WAA4C;AACjDiD,MAAAA,GAAAA,GAAMyV,SAAAA,CAAU1Y,GAAAA,GAAV0Y,OAAAA,CAAAA,CAD2C,IAC3CA,CAANzV;AADK,WAEA,IAAIgQ,CAAAA,IAAAA,CAAAA,IAAWjT,GAAAA,GAAD,OAACA,IAAf,WAA4C;AACjDiD,MAAAA,GAAAA,GAAMyV,SAAAA,CAAU1Y,GAAAA,GAAV0Y,OAAAA,CAAAA,CAD2C,IAC3CA,CAANzV;AADK,WAEA,IAAKjD,GAAAA,GAAD,KAACA,IAAL,WAAgC;AACrCiD,MAAAA,GAAAA,GAAMyV,SAAAA,CAAU1Y,GAAAA,GAAV0Y,KAAAA,CAAAA,CAD+B,IAC/BA,CAANzV;AADK,WAEA,IAAKjD,GAAAA,GAAD,SAACA,IAAL,WAAoC;AACzCiD,MAAAA,GAAAA,GAAMyV,SAAAA,CAAU1Y,GAAAA,GAAV0Y,SAAAA,CAAAA,CADmC,IACnCA,CAANzV;AAzB6C;;AA4B/C,WA5B+C,GA4B/C;AAxwBsD,GA4uBxD6V;;AAqCA,4CAA0C;AACxC,QAAImC,IAAAA,GAAOvC,SAAAA,CAD6B,GAC7BA,CAAX;;AACA,QAAI,CAAJ,MAAW;AACTnW,MAAAA,OAAAA,CAAAA,IAAAA,CAAa,YADJ,gBACTA;;AACA,UAAI,CAAJ,UAAe;AACb,eADa,IACb;AAHO;;AAKT0Y,MAAAA,IAAAA,GALS,QAKTA;AAPsC;;AAexC,QAAIqB,EAAAA,GAfoC,EAexC;;AACA,2BAAuB;AACrB,UAAIrZ,GAAAA,GAAMgY,IAAAA,CADW,IACXA,CAAV;AACAhY,MAAAA,GAAAA,GAAMsZ,YAAAA,CAAAA,GAAAA,EAAAA,IAAAA,EAAAA,GAAAA,EAFe,IAEfA,CAANtZ;AACAA,MAAAA,GAAAA,GAAMuZ,cAAAA,CAAAA,GAAAA,EAAAA,IAAAA,EAHe,GAGfA,CAANvZ;AACAqZ,MAAAA,EAAAA,CAAAA,IAAAA,CAAAA,GAJqB,GAIrBA;AApBsC;;AAsBxC,WAtBwC,EAsBxC;AAvyBsD;;AA2yBxD,8CAA4C;AAC1C,QAAIG,OAAAA,GADsC,0CAC1C;AACA,QAAIC,OAAAA,GAAUD,OAAAA,CAAAA,IAAAA,CAF4B,GAE5BA,CAAd;AACA,QAAI,YAAY,CAACC,OAAAA,CAAjB,QACE,OAJwC,GAIxC;AAIF,QAAIC,SAAAA,GAAYD,OAAAA,CAR0B,CAQ1BA,CAAhB;AACA,QAAIE,SAAAA,GAAYF,OAAAA,CAT0B,CAS1BA,CAAhB;AACA,QAV0C,KAU1C;;AACA,QAAIzc,IAAAA,IAAQ2c,SAAAA,IAAZ,MAA+B;AAC7B9X,MAAAA,KAAAA,GAAQ7E,IAAAA,CADqB,SACrBA,CAAR6E;AADF,WAEO,IAAI8X,SAAAA,IAAJ,WAA4B;AACjC9X,MAAAA,KAAAA,GAAQ4T,SAAAA,CADyB,SACzBA,CAAR5T;AAdwC;;AAkB1C,QAAI6X,SAAAA,IAAJ,SAA0B;AACxB,UAAIE,KAAAA,GAAQ/D,OAAAA,CADY,SACZA,CAAZ;AACA7V,MAAAA,GAAAA,GAAM4Z,KAAAA,CAAAA,GAAAA,EAAAA,KAAAA,EAAAA,GAAAA,EAFkB,IAElBA,CAAN5Z;AApBwC;;AAsB1C,WAtB0C,GAsB1C;AAj0BsD;;AAq0BxD,0CAAwC;AACtC,QAAI6Z,MAAAA,GADkC,sBACtC;AACA,WAAO,oBAAoB,6BAA4B;AACrD,UAAI7c,IAAAA,IAAQ8c,GAAAA,IAAZ,MAAyB;AACvB,eAAO9c,IAAAA,CADgB,GAChBA,CAAP;AAFmD;;AAIrD,UAAI8c,GAAAA,IAAJ,WAAsB;AACpB,eAAOrE,SAAAA,CADa,GACbA,CAAP;AALmD;;AAOrDnW,MAAAA,OAAAA,CAAAA,GAAAA,CAAY,yCAPyC,gBAOrDA;AACA,aARqD,YAQrD;AAVoC,KAE/B,CAAP;AAv0BsD;;AAo1BxD,qCAAmC;AACjC,QAAI5G,IAAAA,GAAOqhB,iBAAAA,CADsB,OACtBA,CAAX;AACA,QAAI,CAACrhB,IAAAA,CAAL,IAFiC;AAMjC,QAAIsf,IAAAA,GAAOgC,WAAAA,CAAYthB,IAAAA,CAAZshB,EAAAA,EAAqBthB,IAAAA,CANC,IAMtBshB,CAAX;;AACA,QAAI,CAAJ,MAAW;AACT1a,MAAAA,OAAAA,CAAAA,IAAAA,CAAa,MAAM5G,IAAAA,CAAN,KADJ,gBACT4G;AADS;AAPsB;;AAajC,QAAI0Y,IAAAA,CAAJ,SAAIA,CAAJ,EAAqB;AACnB,UAAIiC,oBAAAA,CAAAA,OAAAA,CAAAA,KAAJ,GAAyC;AACvCtV,QAAAA,OAAAA,CAAAA,SAAAA,CAAAA,GAAqBqT,IAAAA,CADkB,SAClBA,CAArBrT;AADF,aAEO;AAGL,YAAIuV,QAAAA,GAAWvV,OAAAA,CAHV,UAGL;AACA,YAAIwV,KAAAA,GAJC,KAIL;;AACA,aAAK,IAAIvY,CAAAA,GAAJ,GAAWwY,CAAAA,GAAIF,QAAAA,CAApB,QAAqCtY,CAAAA,GAArC,GAA4CA,CAA5C,IAAiD;AAC/C,cAAIsY,QAAAA,CAAAA,CAAAA,CAAAA,CAAAA,QAAAA,KAAAA,CAAAA,IAA8B,UAAUA,QAAAA,CAAAA,CAAAA,CAAAA,CAA5C,SAAkC,CAAlC,EAAoE;AAClE,uBAAW;AACTA,cAAAA,QAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,GADS,EACTA;AADF,mBAEO;AACLA,cAAAA,QAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,GAAwBlC,IAAAA,CADnB,SACmBA,CAAxBkC;AACAC,cAAAA,KAAAA,GAFK,IAELA;AALgE;AADrB;AAL5C;;AAiBL,YAAI,CAAJ,OAAY;AACV,cAAIE,QAAAA,GAAW3gB,QAAAA,CAAAA,cAAAA,CAAwBse,IAAAA,CAD7B,SAC6BA,CAAxBte,CAAf;AACAiL,UAAAA,OAAAA,CAAAA,YAAAA,CAAAA,QAAAA,EAA+BA,OAAAA,CAFrB,UAEVA;AAnBG;AAHY;;AAyBnB,aAAOqT,IAAAA,CAzBY,SAyBZA,CAAP;AAtC+B;;AAyCjC,wBAAoB;AAClBrT,MAAAA,OAAAA,CAAAA,CAAAA,CAAAA,GAAaqT,IAAAA,CADK,CACLA,CAAbrT;AA1C+B;AAp1BqB;;AAm4BxD,yCAAuC;AACrC,QAAIA,OAAAA,CAAJ,UAAsB;AACpB,aAAOA,OAAAA,CAAAA,QAAAA,CADa,MACpB;AAFmC;;AAIrC,QAAI,OAAOA,OAAAA,CAAP,sBAAJ,aAAsD;AACpD,aAAOA,OAAAA,CAD6C,iBACpD;AALmC;;AAOrC,QAAI2V,KAAAA,GAPiC,CAOrC;;AACA,SAAK,IAAI1Y,CAAAA,GAAT,GAAgBA,CAAAA,GAAI+C,OAAAA,CAAAA,UAAAA,CAApB,QAA+C/C,CAA/C,IAAoD;AAClD0Y,MAAAA,KAAAA,IAAS3V,OAAAA,CAAAA,QAAAA,KAAAA,CAAAA,GAAAA,CAAAA,GADyC,CAClD2V;AATmC;;AAWrC,WAXqC,KAWrC;AA94BsD;;AAk5BxD,sCAAoC;AAClC3V,IAAAA,OAAAA,GAAUA,OAAAA,IAAWjL,QAAAA,CADa,eAClCiL;AAGA,QAAIuV,QAAAA,GAAWK,uBAAAA,CAJmB,OAInBA,CAAf;AACA,QAAIC,YAAAA,GAAeN,QAAAA,CALe,MAKlC;;AACA,SAAK,IAAItY,CAAAA,GAAT,GAAgBA,CAAAA,GAAhB,cAAkCA,CAAlC,IAAuC;AACrC6Y,MAAAA,gBAAAA,CAAiBP,QAAAA,CADoB,CACpBA,CAAjBO,CAAAA;AAPgC;;AAWlCA,IAAAA,gBAAAA,CAXkC,OAWlCA,CAAAA;AA75BsD;;AAg6BxD,SAAO;AAEL7F,IAAAA,GAAAA,EAAK,qCAAoC;AACvC,UAAItN,KAAAA,GAAQvK,GAAAA,CAAAA,WAAAA,CAD2B,GAC3BA,CAAZ;AACA,UAAIgb,IAAAA,GAFmC,SAEvC;;AACA,UAAIzQ,KAAAA,GAAJ,GAAe;AACbyQ,QAAAA,IAAAA,GAAOhb,GAAAA,CAAAA,SAAAA,CAAcuK,KAAAA,GADR,CACNvK,CAAPgb;AACAhb,QAAAA,GAAAA,GAAMA,GAAAA,CAAAA,SAAAA,CAAAA,CAAAA,EAFO,KAEPA,CAANA;AALqC;;AAOvC,UAPuC,QAOvC;;AACA,0BAAoB;AAClBoB,QAAAA,QAAAA,GADkB,EAClBA;AACAA,QAAAA,QAAAA,CAAAA,IAAAA,CAAAA,GAFkB,cAElBA;AAVqC;;AAYvC,UAAI6Z,IAAAA,GAAOgC,WAAAA,CAAAA,GAAAA,EAAAA,IAAAA,EAZ4B,QAY5BA,CAAX;;AACA,UAAIhC,IAAAA,IAAQD,IAAAA,IAAZ,MAA0B;AACxB,eAAOC,IAAAA,CADiB,IACjBA,CAAP;AAdqC;;AAgBvC,aAAO,aAhBgC,IAgBvC;AAlBG;AAsBL0C,IAAAA,OAAAA,EAAS,YAAW;AAAE,aAAF,SAAE;AAtBjB;AAuBLC,IAAAA,OAAAA,EAAS,YAAW;AAAE,aAAF,SAAE;AAvBjB;AA0BLrF,IAAAA,WAAAA,EAAa,YAAW;AAAE,aAAF,SAAE;AA1BrB;AA2BLsF,IAAAA,WAAAA,EAAa,0BAAyB;AACpCC,MAAAA,UAAAA,CAAAA,IAAAA,EAAiB,YAAW;AAC1B,sBACErV,QAFwB;AADQ,OACpCqV,CAAAA;AA5BG;AAmCLtF,IAAAA,YAAAA,EAAc,YAAW;AAGvB,UAAIuF,OAAAA,GAAU,8BAAd;AACA,UAAIC,SAAAA,GAAYnF,SAAAA,CAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,EAJO,CAIPA,CAAhB;AACA,aAAQkF,OAAAA,CAAAA,OAAAA,CAAAA,SAAAA,KAAD,CAACA,GAAD,KAACA,GALe,KAKvB;AAxCG;AA4CLtF,IAAAA,SAAAA,EA5CK;AA+CLwF,IAAAA,aAAAA,EAAe,YAAW;AAAE,aAAF,WAAE;AA/CvB;AAgDLC,IAAAA,KAAAA,EAAO,oBAAmB;AACxB,UAAI,CAAJ,UAAe;AAAA;AAAf,aAEO,IAAInF,WAAAA,IAAAA,UAAAA,IAA6BA,WAAAA,IAAjC,eAA+D;AACpEhc,QAAAA,MAAAA,CAAAA,UAAAA,CAAkB,YAAW;AAC3B0L,UAAAA,QAD2B;AADuC,SACpE1L;AADK,aAIA,IAAIJ,QAAAA,CAAJ,kBAA+B;AACpCA,QAAAA,QAAAA,CAAAA,gBAAAA,CAAAA,WAAAA,EAAuC,gBAAgB;AACrDA,UAAAA,QAAAA,CAAAA,mBAAAA,CAAAA,WAAAA,EADqD,IACrDA;AACA8L,UAAAA,QAFqD;AADnB,SACpC9L;AARsB;AAhDrB;AAAA,GAAP;AAh6BiB,CAAC,CAAD,MAAC,EAApBA,QAAoB,CAApBA;;;;;;;;;;;;;ACpCA;;AAAA;;AAAA;;AAmBA,MAAMwhB,SAAAA,GAAY;AAChBC,EAAAA,KAAAA,EADgB;AAEhBC,EAAAA,SAAAA,EAFgB;AAGhBvX,EAAAA,OAAAA,EAHgB;AAIhBwX,EAAAA,OAAAA,EAJgB;AAAA,CAAlB;;AAOA,MAAMC,YAAAA,GA1BN,GA0BA;AACA,MAAMC,uBAAAA,GAA0B,CA3BhC,EA2BA;AACA,MAAMC,wBAAAA,GAA2B,CA5BjC,GA4BA;AAEA,MAAMC,uBAAAA,GAA0B;AAC9B,YAD8B;AAE9B,YAF8B;AAG9B,YAH8B;AAI9B,YAJ8B;AAK9B,YAL8B;AAM9B,YAN8B;AAO9B,YAP8B;AAQ9B,YAR8B;AAS9B,YAT8B;AAU9B,YAV8B;AAW9B,YAX8B;AAY9B,YAZ8B;AAAA,CAAhC;AAeA,IAAIC,kBAAAA,GA7CJ,IA6CA;;AACA,yBAAyB;AACvB,MAAI,CAAJ,oBAAyB;AAEvB,UAAMC,OAAAA,GAAUhd,MAAAA,CAAAA,IAAAA,CAAAA,uBAAAA,EAAAA,IAAAA,CAFO,EAEPA,CAAhB;AACA+c,IAAAA,kBAAAA,GAAqB,WAAW,WAAX,KAHE,GAGF,CAArBA;AAJqB;;AAMvB,MAAIE,KAAAA,GANmB,IAMvB;AACA,QAAMC,cAAAA,GAAiB,iCAAiC,qBAAqB;AAC3E,UAAMC,YAAAA,GAAeL,uBAAAA,CAArB,EAAqBA,CAArB;AAAA,UACEM,IAAAA,GAAOD,YAAAA,CAAAA,MAAAA,GAAsBE,EAAAA,CAF4C,MAC3E;;AAEA,QAAID,IAAAA,KAAJ,GAAgB;AACb,iBAAD,EAAC,EAAD,IAAC,CAAmB,aAAnB;AAJwE;;AAM3E,WAN2E,YAM3E;AAbqB,GAOA,CAAvB;AASA,SAAO,uBAAP;AA9DF;;AAoEA,sCAAsCH,KAAAA,GAAtC,MAAoD;AAClD,MAAI,CAAJ,OAAY;AACV,WADU,UACV;AAFgD;;AAIlD,MAAIK,SAAAA,GAJ8C,CAIlD;;AACA,aAAW,QAAX,IAAW,CAAX,WAAmC;AACjC,UAAM9V,YAAAA,GAAemB,KAAAA,GADY,SACjC;;AAEA,QAAInB,YAAAA,IAAJ,YAAgC;AAAA;AAHC;;AAMjC,QAAIA,YAAAA,GAAAA,IAAAA,GAAJ,YAAsC;AACpC8V,MAAAA,SAAAA,IAAapM,UAAAA,GADuB,YACpCoM;AADoC;AANL;;AAUjCA,IAAAA,SAAAA,IAViC,IAUjCA;AAfgD;;AAiBlD,SAAOpM,UAAAA,GAjB2C,SAiBlD;AArFF;;AAiGA,wBAAwB;AAItBvX,EAAAA,WAAAA,CAAY;AAAA;AAAZA,IAAAA;AAAY,GAAZA,EAAuC;AACrC,wBADqC,WACrC;AACA,qBAFqC,QAErC;;AAEA,SAJqC,MAIrC;;AACA4jB,IAAAA,QAAAA,CAAAA,GAAAA,CAAAA,cAAAA,EAA6B,0BALQ,IAKR,CAA7BA;AAToB;;AAYtB,MAAIC,gBAAJ,GAAuB;AACrB,WAAO,KADc,iBACrB;AAboB;;AAgBtB,MAAIlM,WAAJ,GAAkB;AAChB,WAAO,KADS,YAChB;AAjBoB;;AAoBtB,MAAIC,iBAAJ,GAAwB;AACtB,WAAO,KADe,kBACtB;AArBoB;;AAwBtB,MAAIkM,QAAJ,GAAe;AACb,WAAO,KADM,SACb;AAzBoB;;AA4BtB,MAAI/W,KAAJ,GAAY;AACV,WAAO,KADG,MACV;AA7BoB;;AAsCtB5G,EAAAA,WAAAA,CAAAA,WAAAA,EAAyB;AACvB,QAAI,KAAJ,cAAuB;AACrB,WADqB,MACrB;AAFqB;;AAIvB,QAAI,CAAJ,aAAkB;AAAA;AAJK;;AAOvB,wBAPuB,WAOvB;;AACA,8BARuB,OAQvB;AA9CoB;;AAiDtB4d,EAAAA,cAAAA,CAAAA,GAAAA,EAAAA,KAAAA,EAA2B;AACzB,QAAI,CAAJ,OAAY;AAAA;AADa;;AAIzB,UAAMC,WAAAA,GAAc,KAJK,YAIzB;;AAEA,QAAI,wBAAwB,4BAA5B,KAA4B,CAA5B,EAAgE;AAC9D,yBAD8D,IAC9D;AAPuB;;AASzB,kBATyB,KASzB;;AACA,QAAIC,GAAAA,KAAJ,0BAAsC;AACpC,0BAAoBrB,SAAAA,CADgB,OACpC;AAXuB;;AAczB,2CAAuC,MAAM;AAG3C,UACE,CAAC,KAAD,gBACCoB,WAAAA,IAAe,sBAFlB,aAGE;AAAA;AANyC;;AAS3C,WAT2C,YAS3C;;AAEA,YAAME,aAAAA,GAAgB,CAAC,KAXoB,iBAW3C;AACA,YAAMC,cAAAA,GAAiB,CAAC,CAAC,KAZkB,YAY3C;;AAEA,UAAI,KAAJ,cAAuB;AACrB/R,QAAAA,YAAAA,CAAa,KADQ,YACrBA,CAAAA;AACA,4BAFqB,IAErB;AAhByC;;AAkB3C,UAAI6R,GAAAA,KAAJ,QAAoB;AAGlB,4BAAoB,WAAW,MAAM;AACnC,eADmC,UACnC;;AACA,8BAFmC,IAEnC;AAFkB,WAHF,YAGE,CAApB;AAHF,aAOO,IAAI,KAAJ,aAAsB;AAG3B,aAH2B,UAG3B;AAHK,aAIA,IAAIA,GAAAA,KAAJ,aAAyB;AAC9B,aAD8B,UAC9B;;AAIA,YAAIC,aAAAA,IAAiB,YAArB,cAA+C;AAC7C,eAD6C,eAC7C;AAN4B;AAAzB,aAQA,IAAID,GAAAA,KAAJ,0BAAsC;AAG3C,4BAAoB;AAClB,eADkB,UAClB;AADF,eAEO;AACL,mCADK,IACL;AANyC;;AAQ3C,aAR2C,eAQ3C;AARK,aASA;AACL,aADK,UACL;AA/CyC;AAdpB,KAczB;AA/DoB;;AAmHtBG,EAAAA,mBAAAA,CAAoB;AAClB/X,IAAAA,OAAAA,GADkB;AAElB8K,IAAAA,YAAAA,GAFkB;AAGlBpQ,IAAAA,SAAAA,GAAY,CAHM;AAIlBwQ,IAAAA,UAAAA,GAAa,CAJf6M;AAAoB,GAApBA,EAKG;AACD,QAAI,CAAC,KAAD,kBAAwB,CAA5B,SAAsC;AAAA;AAAtC,WAEO,IAAI7M,UAAAA,KAAe,CAAfA,CAAAA,IAAqBA,UAAAA,KAAe,eAAxC,UAAiE;AAAA;AAAjE,WAEA,IAAIxQ,SAAAA,KAAc,CAAdA,CAAAA,IAAoBA,SAAAA,KAAc,eAAtC,SAA8D;AAAA;AALpE;;AAQD,0BARC,KAQD;AAEA,UAAM0F,IAAAA,GAAO;AACX8C,MAAAA,GAAAA,EADW;AAEXG,MAAAA,IAAAA,EAAMyH,YAAAA,GAFK;AAAA,KAAb;AAIAkN,kCAAAA,OAAAA,EAAAA,IAAAA,EAdC,IAcDA;AAtIoB;;AAyItBC,EAAAA,MAAAA,GAAS;AACP,6BADO,KACP;AACA,0BAFO,KAEP;AACA,wBAHO,IAGP;AACA,wBAJO,EAIP;AACA,8BALO,EAKP;AACA,kBANO,IAMP;AAEA,qBAAiB;AACfjO,MAAAA,OAAAA,EAAS,CADM;AAEfR,MAAAA,QAAAA,EAAU,CAFK;AAAA,KAAjB;AAKA,mBAAe;AACbQ,MAAAA,OAAAA,EADa;AAEbR,MAAAA,QAAAA,EAFa;AAGb0O,MAAAA,OAAAA,EAHa;AAAA,KAAf;AAKA,gCAlBO,EAkBP;AACA,yBAnBO,EAmBP;AACA,sBApBO,EAoBP;AACA,8BArBO,CAqBP;AACA,0BAtBO,IAsBP;AACA,+BAA2B,IAvBpB,GAuBoB,EAA3B;AACA,0BAxBO,IAwBP;AACA,uBAzBO,KAyBP;AACAnS,IAAAA,YAAAA,CAAa,KA1BN,YA0BPA,CAAAA;AACA,wBA3BO,IA2BP;AAEA,gCA7BO,wCA6BP;AAtKoB;;AA4KtB,MAAIoS,MAAJ,GAAa;AACX,QAAI,sBAAsB,KAA1B,WAA0C;AACxC,uBAAiB,YADuB,KACxC;AACA,OAAC,KAAD,oBAA0BC,SAAAA,CAAU,YAFI,KAEdA,CAA1B;AAHS;;AAKX,WAAO,KALI,gBAKX;AAjLoB;;AAoLtBC,EAAAA,iBAAAA,CAAAA,GAAAA,EAAAA,KAAAA,EAA8B;AAG5B,QAAI3X,KAAAA,CAAAA,KAAAA,KAAgB,YAApB,OAAuC;AACrC,aADqC,IACrC;AAJ0B;;AAM5B;AACE;AACE,cAAMjG,UAAAA,GAAa,yBADrB,CACE;AACA,cAAM7F,WAAAA,GAAc,KAFtB,YAEE;;AASA,YACE6F,UAAAA,IAAAA,CAAAA,IACAA,UAAAA,IAAc7F,WAAAA,CADd6F,UAAAA,IAEAA,UAAAA,KAAe7F,WAAAA,CAFf6F,IAAAA,IAGA,CAAC7F,WAAAA,CAAAA,aAAAA,CAJH,UAIGA,CAJH,EAKE;AACA,iBADA,IACA;AAjBJ;;AAmBE,eApBJ,KAoBI;;AACF;AACE,eAtBJ,KAsBI;AAtBJ;;AAwBA,WA9B4B,IA8B5B;AAlNoB;;AA2NtB0jB,EAAAA,eAAAA,CAAAA,iBAAAA,EAAAA,OAAAA,EAAAA,aAAAA,EAA2D;AACzD,qCAAiC;AAC/B,YAAMC,WAAAA,GAAcC,iBAAAA,CADW,YACXA,CAApB;AACA,YAAMC,QAAAA,GAAWD,iBAAAA,CAAkBhX,YAAAA,GAFJ,CAEdgX,CAAjB;;AAGA,UACEhX,YAAAA,GAAegX,iBAAAA,CAAAA,MAAAA,GAAfhX,CAAAA,IACA+W,WAAAA,CAAAA,KAAAA,KAAsBE,QAAAA,CAFxB,OAGE;AACAF,QAAAA,WAAAA,CAAAA,OAAAA,GADA,IACAA;AACA,eAFA,IAEA;AAV6B;;AAc/B,WAAK,IAAItb,CAAAA,GAAIuE,YAAAA,GAAb,GAA+BvE,CAAAA,IAA/B,GAAuCA,CAAvC,IAA4C;AAC1C,cAAMyb,QAAAA,GAAWF,iBAAAA,CADyB,CACzBA,CAAjB;;AACA,YAAIE,QAAAA,CAAJ,SAAsB;AAAA;AAFoB;;AAK1C,YAAIA,QAAAA,CAAAA,KAAAA,GAAiBA,QAAAA,CAAjBA,WAAAA,GAAwCH,WAAAA,CAA5C,OAA+D;AAAA;AALrB;;AAQ1C,YACEG,QAAAA,CAAAA,KAAAA,GAAiBA,QAAAA,CAAjBA,WAAAA,IACAH,WAAAA,CAAAA,KAAAA,GAAoBA,WAAAA,CAFtB,aAGE;AACAA,UAAAA,WAAAA,CAAAA,OAAAA,GADA,IACAA;AACA,iBAFA,IAEA;AAbwC;AAdb;;AA8B/B,aA9B+B,KA8B/B;AA/BuD;;AAoCzDC,IAAAA,iBAAAA,CAAAA,IAAAA,CAAuB,gBAAgB;AACrC,aAAOzW,CAAAA,CAAAA,KAAAA,KAAYC,CAAAA,CAAZD,KAAAA,GACHA,CAAAA,CAAAA,WAAAA,GAAgBC,CAAAA,CADbD,WAAAA,GAEHA,CAAAA,CAAAA,KAAAA,GAAUC,CAAAA,CAHuB,KACrC;AArCuD,KAoCzDwW;;AAKA,SAAK,IAAIvb,CAAAA,GAAJ,GAAW8K,GAAAA,GAAMyQ,iBAAAA,CAAtB,QAAgDvb,CAAAA,GAAhD,KAAyDA,CAAzD,IAA8D;AAC5D,UAAI0b,SAAAA,CAAJ,CAAIA,CAAJ,EAAkB;AAAA;AAD0C;;AAI5DpP,MAAAA,OAAAA,CAAAA,IAAAA,CAAaiP,iBAAAA,CAAAA,CAAAA,CAAAA,CAJ+C,KAI5DjP;AACAM,MAAAA,aAAAA,CAAAA,IAAAA,CAAmB2O,iBAAAA,CAAAA,CAAAA,CAAAA,CALyC,WAK5D3O;AA9CuD;AA3NrC;;AAiRtB+O,EAAAA,aAAAA,CAAAA,OAAAA,EAAAA,QAAAA,EAAAA,MAAAA,EAAyC;AACvC,QAAIC,QAAAA,GAAJ,GAAkB;AAChB,YAAM/T,KAAAA,GAAQwF,OAAAA,CAAAA,UAAAA,CADE,QACFA,CAAd;AACA,YAAM1I,KAAAA,GAAQ0I,OAAAA,CAAAA,UAAAA,CAAmBuO,QAAAA,GAFjB,CAEFvO,CAAd;;AACA,UAAIwO,sCAAAA,KAAAA,MAA4BA,sCAAhC,KAAgCA,CAAhC,EAAyD;AACvD,eADuD,KACvD;AAJc;AADqB;;AAQvC,UAAMC,MAAAA,GAASF,QAAAA,GAAAA,MAAAA,GARwB,CAQvC;;AACA,QAAIE,MAAAA,GAASzO,OAAAA,CAAAA,MAAAA,GAAb,GAAiC;AAC/B,YAAMvF,IAAAA,GAAOuF,OAAAA,CAAAA,UAAAA,CADkB,MAClBA,CAAb;AACA,YAAM1I,KAAAA,GAAQ0I,OAAAA,CAAAA,UAAAA,CAAmByO,MAAAA,GAFF,CAEjBzO,CAAd;;AACA,UAAIwO,sCAAAA,IAAAA,MAA2BA,sCAA/B,KAA+BA,CAA/B,EAAwD;AACtD,eADsD,KACtD;AAJ6B;AATM;;AAgBvC,WAhBuC,IAgBvC;AAjSoB;;AAoStBE,EAAAA,qBAAAA,CAAAA,KAAAA,EAAAA,SAAAA,EAAAA,WAAAA,EAAAA,SAAAA,EAAAA,UAAAA,EAA4E;AAC1E,UAAMzP,OAAAA,GAAN;AAAA,UACEM,aAAAA,GAFwE,EAC1E;AAEA,UAAMoP,QAAAA,GAAWpd,KAAAA,CAHyD,MAG1E;AAEA,QAAI2N,QAAAA,GAAW,CAL2D,QAK1E;;AACA,iBAAa;AACXA,MAAAA,QAAAA,GAAW0P,WAAAA,CAAAA,OAAAA,CAAAA,KAAAA,EAA2B1P,QAAAA,GAD3B,QACA0P,CAAX1P;;AACA,UAAIA,QAAAA,KAAa,CAAjB,GAAqB;AAAA;AAFV;;AAKX,UAAI2P,UAAAA,IAAc,CAAC,0CAAnB,QAAmB,CAAnB,EAAwE;AAAA;AAL7D;;AAQX,YAAMC,gBAAAA,GAAmBC,gBAAAA,CAAAA,QAAAA,EAAzB,SAAyBA,CAAzB;AAAA,YACEC,QAAAA,GAAW9P,QAAAA,GAAAA,QAAAA,GADb;AAAA,YAEE+P,gBAAAA,GACEF,gBAAAA,CAAAA,QAAAA,EAAAA,SAAAA,CAAAA,GAAAA,gBAAAA,GAXO,CAQX;AAKA9P,MAAAA,OAAAA,CAAAA,IAAAA,CAbW,gBAaXA;AACAM,MAAAA,aAAAA,CAAAA,IAAAA,CAdW,gBAcXA;AApBwE;;AAsB1E,mCAtB0E,OAsB1E;AACA,yCAvB0E,aAuB1E;AA3ToB;;AA8TtB2P,EAAAA,mBAAAA,CAAAA,KAAAA,EAAAA,SAAAA,EAAAA,WAAAA,EAAAA,SAAAA,EAAAA,UAAAA,EAA0E;AACxE,UAAMhB,iBAAAA,GADkE,EACxE;AAGA,UAAMiB,UAAAA,GAAa5d,KAAAA,CAAAA,KAAAA,CAJqD,MAIrDA,CAAnB;;AACA,SAAK,IAAIoB,CAAAA,GAAJ,GAAW8K,GAAAA,GAAM0R,UAAAA,CAAtB,QAAyCxc,CAAAA,GAAzC,KAAkDA,CAAlD,IAAuD;AACrD,YAAMyc,QAAAA,GAAWD,UAAAA,CADoC,CACpCA,CAAjB;AACA,YAAME,WAAAA,GAAcD,QAAAA,CAFiC,MAErD;AAEA,UAAIlQ,QAAAA,GAAW,CAJsC,WAIrD;;AACA,mBAAa;AACXA,QAAAA,QAAAA,GAAW0P,WAAAA,CAAAA,OAAAA,CAAAA,QAAAA,EAA8B1P,QAAAA,GAD9B,WACA0P,CAAX1P;;AACA,YAAIA,QAAAA,KAAa,CAAjB,GAAqB;AAAA;AAFV;;AAKX,YACE2P,UAAAA,IACA,CAAC,0CAFH,WAEG,CAFH,EAGE;AAAA;AARS;;AAWX,cAAMC,gBAAAA,GAAmBC,gBAAAA,CAAAA,QAAAA,EAAzB,SAAyBA,CAAzB;AAAA,cACEC,QAAAA,GAAW9P,QAAAA,GAAAA,WAAAA,GADb;AAAA,cAEE+P,gBAAAA,GACEF,gBAAAA,CAAAA,QAAAA,EAAAA,SAAAA,CAAAA,GAAAA,gBAAAA,GAdO,CAWX;AAMAb,QAAAA,iBAAAA,CAAAA,IAAAA,CAAuB;AACrB/O,UAAAA,KAAAA,EADqB;AAErBmQ,UAAAA,WAAAA,EAFqB;AAGrBC,UAAAA,OAAAA,EAHqB;AAAA,SAAvBrB;AAtBmD;AALiB;;AAoCxE,yCApCwE,EAoCxE;AACA,mCArCwE,EAqCxE;;AAIA,4CAEE,kBAFF,SAEE,CAFF,EAGE,wBA5CsE,SA4CtE,CAHF;AAvWoB;;AA8WtBsB,EAAAA,eAAAA,CAAAA,SAAAA,EAA2B;AACzB,QAAIZ,WAAAA,GAAc,mBADO,SACP,CAAlB;AACA,UAAMa,SAAAA,GAAY,gBAFO,SAEP,CAAlB;AACA,QAAIle,KAAAA,GAAQ,KAHa,MAGzB;AACA,UAAM;AAAA;AAAA;AAAA;AAAA,QAA8C,KAJ3B,MAIzB;;AAEA,QAAIA,KAAAA,CAAAA,MAAAA,KAAJ,GAAwB;AAAA;AANC;;AAWzB,QAAI,CAAJ,eAAoB;AAClBqd,MAAAA,WAAAA,GAAcA,WAAAA,CADI,WACJA,EAAdA;AACArd,MAAAA,KAAAA,GAAQA,KAAAA,CAFU,WAEVA,EAARA;AAbuB;;AAgBzB,sBAAkB;AAChB,2EADgB,UAChB;AADF,WAQO;AACL,yEADK,UACL;AAzBuB;;AAoCzB,QAAI,YAAJ,cAA8B;AAC5B,uBAD4B,SAC5B;AArCuB;;AAuCzB,QAAI,wBAAJ,WAAuC;AACrC,4BADqC,IACrC;;AACA,WAFqC,cAErC;AAzCuB;;AA6CzB,UAAMme,gBAAAA,GAAmB,6BA7CA,MA6CzB;;AACA,QAAIA,gBAAAA,GAAJ,GAA0B;AACxB,iCADwB,gBACxB;;AACA,WAFwB,qBAExB;AAhDuB;AA9WL;;AAkatBC,EAAAA,YAAAA,GAAe;AAEb,QAAI,mCAAJ,GAA0C;AAAA;AAF7B;;AAMb,QAAIC,OAAAA,GAAUC,OAAAA,CAND,OAMCA,EAAd;;AACA,SAAK,IAAIld,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,kBAArB,YAAmDjK,CAAAA,GAAnD,IAA2DA,CAA3D,IAAgE;AAC9D,YAAMmd,qBAAAA,GADwD,wCAC9D;AACA,qCAA+BA,qBAAAA,CAF+B,OAE9D;AAEAF,MAAAA,OAAAA,GAAU,aAAa,MAAM;AAC3B,eAAO,0BACIjd,CAAAA,GADJ,QAECod,OAAAA,IAAW;AACf,iBAAOA,OAAAA,CAAAA,cAAAA,CAAuB;AAC5BC,YAAAA,mBAAAA,EAFa;AACe,WAAvBD,CAAP;AAHG,gBAQHzR,WAAAA,IAAe;AACb,gBAAM2R,SAAAA,GAAY3R,WAAAA,CADL,KACb;AACA,gBAAM4R,MAAAA,GAFO,EAEb;;AAEA,eAAK,IAAIC,CAAAA,GAAJ,GAAWC,EAAAA,GAAKH,SAAAA,CAArB,QAAuCE,CAAAA,GAAvC,IAA+CA,CAA/C,IAAoD;AAClDD,YAAAA,MAAAA,CAAAA,IAAAA,CAAYD,SAAAA,CAAAA,CAAAA,CAAAA,CADsC,GAClDC;AALW;;AASb,WAAC,mBAAD,CAAC,CAAD,EAAwB,gBAAxB,CAAwB,CAAxB,IAA8CpC,SAAAA,CAC5CoC,MAAAA,CAAAA,IAAAA,CAVW,EAUXA,CAD4CpC,CAA9C;AAGAgC,UAAAA,qBAAAA,CAAAA,OAAAA,CAZa,CAYbA;AApBC,WAsBHO,MAAAA,IAAU;AACRhgB,UAAAA,OAAAA,CAAAA,KAAAA,CACE,uCAAuCsC,CAAAA,GAAvC,CADFtC,EAAAA,EADQ,MACRA;AAKA,kCANQ,EAMR;AACA,+BAPQ,IAOR;AACAyf,UAAAA,qBAAAA,CAAAA,OAAAA,CARQ,CAQRA;AA/BqB,SACpB,CAAP;AAL4D,OAIpD,CAAVF;AAXW;AAlaO;;AAmdtBU,EAAAA,WAAAA,CAAAA,KAAAA,EAAmB;AACjB,QAAI,uBAAuB,2BAA3B,OAA6D;AAI3D,+BAAyBjY,KAAAA,GAJkC,CAI3D;AALe;;AAQjB,sDAAkD;AAChD/G,MAAAA,MAAAA,EADgD;AAEhDlB,MAAAA,SAAAA,EAFgD;AAAA,KAAlD;AA3doB;;AAietBmgB,EAAAA,eAAAA,GAAkB;AAChB,sDAAkD;AAChDjf,MAAAA,MAAAA,EADgD;AAEhDlB,MAAAA,SAAAA,EAAW,CAFqC;AAAA,KAAlD;AAleoB;;AAwetBogB,EAAAA,UAAAA,GAAa;AACX,UAAMC,QAAAA,GAAW,YADN,YACX;AACA,UAAMC,gBAAAA,GAAmB,yBAFd,CAEX;AACA,UAAMC,QAAAA,GAAW,kBAHN,UAGX;AAEA,6BALW,IAKX;;AAEA,QAAI,KAAJ,aAAsB;AAEpB,yBAFoB,KAEpB;AACA,+BAAyB,0BAA0B,CAH/B,CAGpB;AACA,6BAJoB,gBAIpB;AACA,8BALoB,IAKpB;AACA,6BANoB,KAMpB;AACA,4BAPoB,IAOpB;AACA,iCARoB,CAQpB;AACA,uCAToB,CASpB;AACA,gCAVoB,CAUpB;;AAEA,WAZoB,eAYpB;;AAEA,WAAK,IAAIhe,CAAAA,GAAT,GAAgBA,CAAAA,GAAhB,UAA8BA,CAA9B,IAAmC;AAEjC,YAAI,6BAAJ,CAAI,CAAJ,EAAqC;AAAA;AAFJ;;AAKjC,qCALiC,CAKjC;;AACA,0CAAkC+M,OAAAA,IAAW;AAC3C,0CAD2C,OAC3C;;AACA,+BAF2C,OAE3C;AAR+B,SAMjC;AApBkB;AAPX;;AAmCX,QAAI,gBAAJ,IAAwB;AACtB,0BAAoBuM,SAAAA,CADE,KACtB;;AADsB;AAnCb;;AAwCX,QAAI,KAAJ,gBAAyB;AAAA;AAxCd;;AA4CX,UAAM3M,MAAAA,GAAS,KA5CJ,OA4CX;AAEA,0BA9CW,QA8CX;;AAGA,QAAIA,MAAAA,CAAAA,QAAAA,KAAJ,MAA8B;AAC5B,YAAMsR,cAAAA,GAAiB,kBAAkBtR,MAAAA,CAAlB,SADK,MAC5B;;AACA,UACG,aAAaA,MAAAA,CAAAA,QAAAA,GAAAA,CAAAA,GAAd,cAAC,IACAmR,QAAAA,IAAYnR,MAAAA,CAAAA,QAAAA,GAFf,GAGE;AAGAA,QAAAA,MAAAA,CAAAA,QAAAA,GAAkBmR,QAAAA,GAAWnR,MAAAA,CAAAA,QAAAA,GAAXmR,CAAAA,GAAiCnR,MAAAA,CAAAA,QAAAA,GAHnD,CAGAA;;AACA,0BAJA,IAIA;;AAJA;AAL0B;;AAc5B,8BAd4B,QAc5B;AA/DS;;AAkEX,SAlEW,cAkEX;AA1iBoB;;AA6iBtBuR,EAAAA,aAAAA,CAAAA,OAAAA,EAAuB;AACrB,UAAMvR,MAAAA,GAAS,KADM,OACrB;AACA,UAAMwR,UAAAA,GAAa7R,OAAAA,CAFE,MAErB;AACA,UAAMwR,QAAAA,GAAW,YAHI,YAGrB;;AAEA,oBAAgB;AAEdnR,MAAAA,MAAAA,CAAAA,QAAAA,GAAkBmR,QAAAA,GAAWK,UAAAA,GAAXL,CAAAA,GAFJ,CAEdnR;;AACA,wBAHc,IAGd;;AACA,aAJc,IAId;AATmB;;AAYrB,4BAZqB,QAYrB;;AACA,QAAIA,MAAAA,CAAJ,SAAoB;AAClBA,MAAAA,MAAAA,CAAAA,QAAAA,GADkB,IAClBA;;AACA,UAAI,sBAAJ,GAA6B;AAE3B,0BAF2B,KAE3B;;AAGA,eAL2B,IAK3B;AAPgB;AAbC;;AAwBrB,WAxBqB,KAwBrB;AArkBoB;;AAwkBtByR,EAAAA,cAAAA,GAAiB;AACf,QAAI,wBAAJ,MAAkC;AAChC1gB,MAAAA,OAAAA,CAAAA,KAAAA,CADgC,qCAChCA;AAFa;;AAKf,QAAI4O,OAAAA,GALW,IAKf;;AACA,OAAG;AACD,YAAMS,OAAAA,GAAU,aADf,OACD;AACAT,MAAAA,OAAAA,GAAU,kBAFT,OAES,CAAVA;;AACA,UAAI,CAAJ,SAAc;AAGZ,8BAHY,OAGZ;AAHY;AAHb;AAAH,aASS,CAAC,mBAfK,OAeL,CATV;AA9kBoB;;AA0lBtB+R,EAAAA,kBAAAA,CAAAA,QAAAA,EAA6B;AAC3B,UAAM1R,MAAAA,GAAS,KADY,OAC3B;AACA,UAAMqR,QAAAA,GAAW,kBAFU,UAE3B;AACArR,IAAAA,MAAAA,CAAAA,OAAAA,GAAiBmR,QAAAA,GAAWnR,MAAAA,CAAAA,OAAAA,GAAXmR,CAAAA,GAAgCnR,MAAAA,CAAAA,OAAAA,GAHtB,CAG3BA;AACAA,IAAAA,MAAAA,CAAAA,QAAAA,GAJ2B,IAI3BA;AAEA,SAN2B,cAM3B;;AAEA,QAAIA,MAAAA,CAAAA,OAAAA,IAAAA,QAAAA,IAA8BA,MAAAA,CAAAA,OAAAA,GAAlC,GAAsD;AACpDA,MAAAA,MAAAA,CAAAA,OAAAA,GAAiBmR,QAAAA,GAAWE,QAAAA,GAAXF,CAAAA,GADmC,CACpDnR;AACAA,MAAAA,MAAAA,CAAAA,OAAAA,GAFoD,IAEpDA;AAVyB;AA1lBP;;AAwmBtB2R,EAAAA,YAAAA,CAAa/F,KAAAA,GAAb+F,KAAAA,EAA4B;AAC1B,QAAI7a,KAAAA,GAAQ6V,SAAAA,CADc,SAC1B;AACA,UAAM2B,OAAAA,GAAU,aAFU,OAE1B;AACA,2BAH0B,KAG1B;;AAEA,eAAW;AACT,YAAMsD,YAAAA,GAAe,eADZ,OACT;AACA,+BAAyB,aAFhB,OAET;AACA,gCAA0B,aAHjB,QAGT;AACA9a,MAAAA,KAAAA,GAAQwX,OAAAA,GAAU3B,SAAAA,CAAV2B,OAAAA,GAA8B3B,SAAAA,CAJ7B,KAIT7V;;AAGA,UAAI8a,YAAAA,KAAiB,CAAjBA,CAAAA,IAAuBA,YAAAA,KAAiB,eAA5C,SAAoE;AAClE,yBADkE,YAClE;AARO;AALe;;AAiB1B,+BAA2B,YAjBD,YAiB1B;;AACA,QAAI,2BAA2B,CAA/B,GAAmC;AAEjC,4BAFiC,IAEjC;;AAEA,uBAAiB,eAJgB,OAIjC;AAtBwB;AAxmBN;;AAkoBtBC,EAAAA,eAAAA,CAAAA,GAAAA,EAAqB;AACnB,UAAM9D,WAAAA,GAAc,KADD,YACnB;;AAIA,2CAAuC,MAAM;AAE3C,UACE,CAAC,KAAD,gBACCA,WAAAA,IAAe,sBAFlB,aAGE;AAAA;AALyC;;AAS3C,UAAI,KAAJ,cAAuB;AACrB5R,QAAAA,YAAAA,CAAa,KADQ,YACrBA,CAAAA;AACA,4BAFqB,IAErB;AAXyC;;AAiB3C,UAAI,KAAJ,gBAAyB;AACvB,8BADuB,IACvB;AACA,2BAFuB,IAEvB;AAnByC;;AAsB3C,0BAAoBwQ,SAAAA,CAtBuB,KAsB3C;;AAEA,+BAxB2C,KAwB3C;;AACA,WAzB2C,eAyB3C;AA9BiB,KAKnB;AAvoBoB;;AAoqBtBmF,EAAAA,oBAAAA,GAAuB;AACrB,UAAM;AAAA;AAAA;AAAA,QAAwB,KADT,SACrB;AACA,QAAIC,OAAAA,GAAJ;AAAA,QACEC,KAAAA,GAAQ,KAHW,kBAErB;;AAEA,QAAIpS,QAAAA,KAAa,CAAjB,GAAqB;AACnB,WAAK,IAAIvM,CAAAA,GAAT,GAAgBA,CAAAA,GAAhB,SAA6BA,CAA7B,IAAkC;AAChC0e,QAAAA,OAAAA,IAAW,gCADqB,CAChCA;AAFiB;;AAInBA,MAAAA,OAAAA,IAAWnS,QAAAA,GAJQ,CAInBmS;AARmB;;AAarB,QAAIA,OAAAA,GAAAA,CAAAA,IAAeA,OAAAA,GAAnB,OAAoC;AAClCA,MAAAA,OAAAA,GAAUC,KAAAA,GADwB,CAClCD;AAdmB;;AAgBrB,WAAO;AAAA;AAAA;AAAA,KAAP;AAprBoB;;AAurBtBE,EAAAA,qBAAAA,GAAwB;AACtB,sDAAkD;AAChDjgB,MAAAA,MAAAA,EADgD;AAEhDkgB,MAAAA,YAAAA,EAAc,KAFkC,oBAElC;AAFkC,KAAlD;AAxrBoB;;AA8rBtBC,EAAAA,cAAAA,CAAAA,KAAAA,EAAAA,QAAAA,EAAgC;AAC9B,sDAAkD;AAChDngB,MAAAA,MAAAA,EADgD;AAAA;AAAA;AAIhDkgB,MAAAA,YAAAA,EAAc,KAJkC,oBAIlC,EAJkC;AAKhDE,MAAAA,QAAAA,EAAU,sBALsC;AAAA,KAAlD;AA/rBoB;;AAAA;;;;;;;;;;;;;;;AClFxB,MAAMC,aAAAA,GAAgB;AACpBC,EAAAA,KAAAA,EADoB;AAEpBC,EAAAA,YAAAA,EAFoB;AAGpBC,EAAAA,KAAAA,EAHoB;AAIpBC,EAAAA,UAAAA,EAJoB;AAKpBC,EAAAA,eAAAA,EALoB;AAMpBC,EAAAA,eAAAA,EANoB;AAOpBC,EAAAA,yBAAAA,EAPoB;AAQpBC,EAAAA,WAAAA,EARoB;AAAA,CAAtB;;;AAWA,wCAAwC;AACtC,SAAOC,QAAAA,GAD+B,MACtC;AA3BF;;AA8BA,2BAA2B;AACzB,SAAQ,YAAD,MAAC,MADiB,CACzB;AA/BF;;AAkCA,gCAAgC;AAC9B,SACGA,QAAAA,IAAAA,IAAAA,IAA8BA,QAAAA,IAA/B,IAACA,IACAA,QAAAA,IAAAA,IAAAA,IAA8BA,QAAAA,IAHH,IAC9B;AAnCF;;AAyCA,gCAAgC;AAC9B,SAAOA,QAAAA,IAAAA,IAAAA,IAA8BA,QAAAA,IADP,IAC9B;AA1CF;;AA6CA,gCAAgC;AAC9B,SACEA,QAAAA,KAAAA,IAAAA,IACAA,QAAAA,KADAA,IAAAA,IAEAA,QAAAA,KAFAA,IAAAA,IAGAA,QAAAA,KAL4B,IAC9B;AA9CF;;AAsDA,yBAAyB;AACvB,SACGA,QAAAA,IAAAA,MAAAA,IAAsBA,QAAAA,IAAvB,MAACA,IACAA,QAAAA,IAAAA,MAAAA,IAAsBA,QAAAA,IAHF,MACvB;AAvDF;;AA6DA,8BAA8B;AAC5B,SAAOA,QAAAA,IAAAA,MAAAA,IAAsBA,QAAAA,IADD,MAC5B;AA9DF;;AAiEA,8BAA8B;AAC5B,SAAOA,QAAAA,IAAAA,MAAAA,IAAsBA,QAAAA,IADD,MAC5B;AAlEF;;AAqEA,uCAAuC;AACrC,SAAOA,QAAAA,IAAAA,MAAAA,IAAsBA,QAAAA,IADQ,MACrC;AAtEF;;AAyEA,0BAA0B;AACxB,SAAQ,YAAD,MAAC,MADgB,MACxB;AA1EF;;AAiFA,oCAAoC;AAClC,MAAIC,oBAAAA,CAAJ,QAAIA,CAAJ,EAAoC;AAClC,QAAIC,OAAAA,CAAJ,QAAIA,CAAJ,EAAuB;AACrB,UAAIC,YAAAA,CAAJ,QAAIA,CAAJ,EAA4B;AAC1B,eAAOZ,aAAAA,CADmB,KAC1B;AADF,aAEO,IACLa,YAAAA,CAAAA,QAAAA,CAAAA,IACAC,YAAAA,CADAD,QACAC,CADAD,IAEAJ,QAAAA,KAHK,MAIL;AACA,eAAOT,aAAAA,CADP,YACA;AARmB;;AAUrB,aAAOA,aAAAA,CAVc,KAUrB;AAVF,WAWO,IAAIe,MAAAA,CAAJ,QAAIA,CAAJ,EAAsB;AAC3B,aAAOf,aAAAA,CADoB,WAC3B;AADK,WAEA,IAAIS,QAAAA,KAAJ,MAAoC;AACzC,aAAOT,aAAAA,CADkC,KACzC;AAfgC;;AAiBlC,WAAOA,aAAAA,CAjB2B,YAiBlC;AAlBgC;;AAqBlC,MAAIgB,KAAAA,CAAJ,QAAIA,CAAJ,EAAqB;AACnB,WAAOhB,aAAAA,CADY,UACnB;AADF,SAEO,IAAIiB,UAAAA,CAAJ,QAAIA,CAAJ,EAA0B;AAC/B,WAAOjB,aAAAA,CADwB,eAC/B;AADK,SAEA,IAAIkB,UAAAA,CAAJ,QAAIA,CAAJ,EAA0B;AAC/B,WAAOlB,aAAAA,CADwB,eAC/B;AADK,SAEA,IAAImB,mBAAAA,CAAJ,QAAIA,CAAJ,EAAmC;AACxC,WAAOnB,aAAAA,CADiC,yBACxC;AA5BgC;;AA8BlC,SAAOA,aAAAA,CA9B2B,YA8BlC;AA/GF;;;;;;;;;;;;;;;ACeA;;AAQA,MAAMoB,mBAAAA,GAvBN,IAuBA;AAEA,MAAMC,0BAAAA,GAzBN,EAyBA;AAEA,MAAMC,uBAAAA,GA3BN,IA2BA;;AAwBA,0BAA0B;AACxB,SAAOxoB,QAAAA,CAAAA,QAAAA,CADiB,IACxB;AApDF;;AAuDA,iBAAiB;AAIfpB,EAAAA,WAAAA,CAAY;AAAA;AAAZA,IAAAA;AAAY,GAAZA,EAAuC;AACrC,uBADqC,WACrC;AACA,oBAFqC,QAErC;AAEA,wBAJqC,KAIrC;AACA,wBALqC,EAKrC;AACA,SANqC,KAMrC;AAEA,wBARqC,IAQrC;AACA,uCATqC,KASrC;;AAGA,iDAA6CsR,GAAAA,IAAO;AAClD,yCACEA,GAAAA,CAAAA,KAAAA,KAAcpH,gCAFkC,MAClD;AAbmC,KAYrC;;AAIA,mCAA+B,MAAM;AACnC,4BADmC,KACnC;;AAEA,uCAEEoH,GAAAA,IAAO;AACL,8BAAsB,CAAC,CAACA,GAAAA,CADnB,UACL;AAHJ,SAKE;AAAEyB,QAAAA,IAAAA,EAR+B;AAQjC,OALF;AAnBmC,KAgBrC;AApBa;;AAsCf8W,EAAAA,UAAAA,CAAW;AAAA;AAAeC,IAAAA,YAAAA,GAAf;AAAqCC,IAAAA,SAAAA,GAAhDF;AAAW,GAAXA,EAAqE;AACnE,QAAI,gBAAgB,uBAApB,UAAqD;AACnD7iB,MAAAA,OAAAA,CAAAA,KAAAA,CADmD,sEACnDA;AADmD;AADc;;AAQnE,QAAI,KAAJ,cAAuB;AACrB,WADqB,KACrB;AATiE;;AAWnE,UAAMgjB,aAAAA,GACJ,4BAA4B,sBAZqC,WAWnE;AAEA,wBAbmE,WAanE;AACA,sBAAkBD,SAAAA,KAdiD,IAcnE;AAEA,wBAhBmE,IAgBnE;;AACA,SAjBmE,WAiBnE;;AACA,UAAMhd,KAAAA,GAAQvL,MAAAA,CAAAA,OAAAA,CAlBqD,KAkBnE;AAEA,+BApBmE,KAoBnE;AACA,4BArBmE,CAqBnE;AACA,wBAAoByoB,cAtB+C,EAsBnE;AACA,+BAvBmE,CAuBnE;AAEA,gBAAY,eAzBuD,CAyBnE;AACA,wBA1BmE,IA0BnE;AACA,qBA3BmE,IA2BnE;;AAEA,QAAI,CAAC,0BAAD,IAAC,CAAD,IAAJ,cAA0E;AACxE,YAAM;AAAA;AAAA;AAAA;AAAA,UAA2B,uBADuC,IACvC,CAAjC;;AAIA,UAAI,0BAAJ,cAA4C;AAE1C,uCAF0C,IAE1C;;AAF0C;AAL4B;;AAYxE,+BACE;AAAA;AAAA;AAAA;AAAA,OADF,EAZwE,IAYxE;;AAZwE;AA7BP;;AAkDnE,UAAMC,WAAAA,GAAcnd,KAAAA,CAlD+C,WAkDnE;;AACA,2CAEEA,KAAAA,CAFF,KAnDmE,IAmDnE;;AAMA,QAAImd,WAAAA,CAAAA,QAAAA,KAAJ,WAAwC;AACtC,8BAAwBA,WAAAA,CADc,QACtC;AA1DiE;;AA4DnE,QAAIA,WAAAA,CAAJ,MAAsB;AACpB,8BAAwBviB,IAAAA,CAAAA,SAAAA,CAAeuiB,WAAAA,CADnB,IACIviB,CAAxB;AAKA,+BANoB,IAMpB;AANF,WAOO,IAAIuiB,WAAAA,CAAJ,MAAsB;AAC3B,8BAAwBA,WAAAA,CADG,IAC3B;AADK,WAEA,IAAIA,WAAAA,CAAJ,MAAsB;AAE3B,8BAAwB,QAAQA,WAAAA,CAAR,IAFG,EAE3B;AAvEiE;AAtCtD;;AAqHfC,EAAAA,KAAAA,GAAQ;AACN,QAAI,KAAJ,cAAuB;AACrB,WADqB,SACrB;;AAEA,0BAHqB,KAGrB;;AACA,WAJqB,aAIrB;AALI;;AAON,QAAI,KAAJ,wBAAiC;AAC/B/X,MAAAA,YAAAA,CAAa,KADkB,sBAC/BA,CAAAA;AACA,oCAF+B,IAE/B;AATI;;AAWN,4BAXM,IAWN;AACA,4BAZM,IAYN;AAjIa;;AAwIfgY,EAAAA,IAAAA,CAAK;AAAEzjB,IAAAA,SAAAA,GAAF;AAAA;AAALyjB,IAAAA;AAAK,GAALA,EAAqD;AACnD,QAAI,CAAC,KAAL,cAAwB;AAAA;AAD2B;;AAInD,QAAIzjB,SAAAA,IAAa,qBAAjB,UAAgD;AAC9CK,MAAAA,OAAAA,CAAAA,KAAAA,CACE,sBACE,aAH0C,uCAC9CA;AAD8C;AAAhD,WAMO,IAAI,CAACI,KAAAA,CAAAA,OAAAA,CAAL,YAAKA,CAAL,EAAkC;AACvCJ,MAAAA,OAAAA,CAAAA,KAAAA,CACE,sBACE,gBAHmC,0CACvCA;AADuC;AAAlC,WAMA,IAAI,CAAC,kBAAL,UAAK,CAAL,EAAoC;AAGzC,UAAIF,UAAAA,KAAAA,IAAAA,IAAuB,KAA3B,cAA8C;AAC5CE,QAAAA,OAAAA,CAAAA,KAAAA,CACE,sBACE,cAHwC,wCAC5CA;AAD4C;AAHL;AAhBQ;;AA4BnD,UAAMc,IAAAA,GAAOnB,SAAAA,IAAagB,IAAAA,CAAAA,SAAAA,CA5ByB,YA4BzBA,CAA1B;;AACA,QAAI,CAAJ,MAAW;AAAA;AA7BwC;;AAmCnD,QAAI0iB,YAAAA,GAnC+C,KAmCnD;;AACA,QACE,sBACC,kBAAkB,kBAAlB,eACCC,iBAAAA,CAAkB,kBAAlBA,IAAAA,EAHJ,YAGIA,CAFF,CADF,EAIE;AAMA,UAAI,kBAAJ,MAA4B;AAAA;AAN5B;;AASAD,MAAAA,YAAAA,GATA,IASAA;AAjDiD;;AAmDnD,QAAI,4BAA4B,CAAhC,cAA+C;AAAA;AAnDI;;AAuDnD,6BACE;AACE7iB,MAAAA,IAAAA,EADF;AAAA;AAGExG,MAAAA,IAAAA,EAHF;AAIEyF,MAAAA,QAAAA,EAAU,iBAJZ;AAAA,KADF,EAvDmD,YAuDnD;;AAUA,QAAI,CAAC,KAAL,qBAA+B;AAG7B,iCAH6B,IAG7B;AAGA+f,MAAAA,OAAAA,CAAAA,OAAAA,GAAAA,IAAAA,CAAuB,MAAM;AAC3B,mCAD2B,KAC3B;AAP2B,OAM7BA;AAvEiD;AAxItC;;AA0Nf+D,EAAAA,QAAAA,CAAAA,UAAAA,EAAqB;AACnB,QAAI,CAAC,KAAL,cAAwB;AAAA;AADL;;AAInB,QAAI,CAAC,kBAAL,UAAK,CAAL,EAAoC;AAClCvjB,MAAAA,OAAAA,CAAAA,KAAAA,CACE,mCAFgC,+BAClCA;AADkC;AAJjB;;AAWnB,QAAI,4BAAJ,YAA4C;AAAA;AAXzB;;AAgBnB,QAAI,KAAJ,qBAA8B;AAAA;AAhBX;;AAoBnB,6BAAyB;AAEvBQ,MAAAA,IAAAA,EAFuB;AAGvBM,MAAAA,IAAAA,EAAM,kBAHiB;AAIvB9G,MAAAA,IAAAA,EAJuB;AAKvByF,MAAAA,QAAAA,EAAU,iBALa;AAAA,KAAzB;;AAQA,QAAI,CAAC,KAAL,qBAA+B;AAG7B,iCAH6B,IAG7B;AAGA+f,MAAAA,OAAAA,CAAAA,OAAAA,GAAAA,IAAAA,CAAuB,MAAM;AAC3B,mCAD2B,KAC3B;AAP2B,OAM7BA;AAlCiB;AA1NN;;AAqQfgE,EAAAA,mBAAAA,GAAsB;AACpB,QAAI,CAAC,KAAD,gBAAsB,KAA1B,qBAAoD;AAAA;AADhC;;AAIpB,SAJoB,uBAIpB;AAzQa;;AAgRfC,EAAAA,IAAAA,GAAO;AACL,QAAI,CAAC,KAAD,gBAAsB,KAA1B,qBAAoD;AAAA;AAD/C;;AAIL,UAAM1d,KAAAA,GAAQvL,MAAAA,CAAAA,OAAAA,CAJT,KAIL;;AACA,QAAI,6BAA6BuL,KAAAA,CAAAA,GAAAA,GAAjC,GAAgD;AAC9CvL,MAAAA,MAAAA,CAAAA,OAAAA,CAD8C,IAC9CA;AANG;AAhRQ;;AA8RfkpB,EAAAA,OAAAA,GAAU;AACR,QAAI,CAAC,KAAD,gBAAsB,KAA1B,qBAAoD;AAAA;AAD5C;;AAIR,UAAM3d,KAAAA,GAAQvL,MAAAA,CAAAA,OAAAA,CAJN,KAIR;;AACA,QAAI,6BAA6BuL,KAAAA,CAAAA,GAAAA,GAAY,KAA7C,SAA2D;AACzDvL,MAAAA,MAAAA,CAAAA,OAAAA,CADyD,OACzDA;AANM;AA9RK;;AA4Sf,MAAImpB,kBAAJ,GAAyB;AACvB,WACE,sBACC,4BAA4B,wBAHR,CAErB,CADF;AA7Sa;;AAmTf,MAAIC,eAAJ,GAAsB;AACpB,WAAO,oBAAoB,KAApB,mBADa,IACpB;AApTa;;AAuTf,MAAIC,eAAJ,GAAsB;AACpB,WAAO,oBAAoB,KAApB,mBADa,IACpB;AAxTa;;AA8TfC,EAAAA,mBAAAA,CAAAA,WAAAA,EAAiCT,YAAAA,GAAjCS,KAAAA,EAAuD;AACrD,UAAMC,aAAAA,GAAgBV,YAAAA,IAAgB,CAAC,KADc,YACrD;AACA,UAAMW,QAAAA,GAAW;AACfC,MAAAA,WAAAA,EAAa,KADE;AAEfC,MAAAA,GAAAA,EAAKH,aAAAA,GAAgB,KAAhBA,IAAAA,GAA4B,YAFlB;AAAA;AAAA,KAAjB;;AAcA,2CAAuCC,QAAAA,CAhBc,GAgBrD;;AAEA,QAlBqD,MAkBrD;;AACA,QAAI,mBAAmBd,WAAAA,EAAvB,MAA0C;AACxC,YAAM9jB,OAAAA,GAAUhF,QAAAA,CAAAA,QAAAA,CAAAA,IAAAA,CAAAA,KAAAA,CAAAA,GAAAA,EADwB,CACxBA,CAAhB;;AAEA,UAAI,CAACgF,OAAAA,CAAAA,UAAAA,CAAL,SAAKA,CAAL,EAAoC;AAClC+kB,QAAAA,MAAAA,GAAS,cAAcjB,WAAAA,CAAd,IADyB,EAClCiB;AAJsC;AAnBW;;AA0BrD,uBAAmB;AACjB3pB,MAAAA,MAAAA,CAAAA,OAAAA,CAAAA,YAAAA,CAAAA,QAAAA,EAAAA,EAAAA,EADiB,MACjBA;AADF,WAEO;AACLA,MAAAA,MAAAA,CAAAA,OAAAA,CAAAA,SAAAA,CAAAA,QAAAA,EAAAA,EAAAA,EADK,MACLA;AA7BmD;AA9TxC;;AA2Wf4pB,EAAAA,uBAAAA,CAAwBC,SAAAA,GAAxBD,KAAAA,EAA2C;AACzC,QAAI,CAAC,KAAL,WAAqB;AAAA;AADoB;;AAIzC,QAAIE,QAAAA,GAAW,KAJ0B,SAIzC;;AACA,mBAAe;AACbA,MAAAA,QAAAA,GAAWjlB,MAAAA,CAAAA,MAAAA,CAAcA,MAAAA,CAAAA,MAAAA,CAAdA,IAAcA,CAAdA,EAAmC,KADjC,SACFA,CAAXilB;AACAA,MAAAA,QAAAA,CAAAA,SAAAA,GAFa,IAEbA;AAPuC;;AAUzC,QAAI,CAAC,KAAL,cAAwB;AACtB,+BADsB,QACtB;;AADsB;AAViB;;AAczC,QAAI,kBAAJ,WAAiC;AAE/B,yCAF+B,IAE/B;;AAF+B;AAdQ;;AAmBzC,QAAI,2BAA2BA,QAAAA,CAA/B,MAA8C;AAAA;AAnBL;;AAsBzC,QACE,CAAC,kBAAD,SACC,mCACC,4BAHJ,0BACE,CADF,EAIE;AAAA;AA1BuC;;AAkCzC,QAAIjB,YAAAA,GAlCqC,KAkCzC;;AACA,QACE,0BAA0BiB,QAAAA,CAA1B,SACA,0BAA0BA,QAAAA,CAF5B,MAGE;AAMA,UAAI,wCAAwC,CAAC,kBAA7C,OAAsE;AAAA;AANtE;;AAUAjB,MAAAA,YAAAA,GAVA,IAUAA;AAhDuC;;AAkDzC,uCAlDyC,YAkDzC;AA7Za;;AAmafkB,EAAAA,YAAAA,CAAAA,GAAAA,EAAkB;AAChB,WACEtkB,MAAAA,CAAAA,SAAAA,CAAAA,GAAAA,KAAyBK,GAAAA,GAAzBL,CAAAA,IAAoCK,GAAAA,IAAO,iBAF7B,UAChB;AApaa;;AA4afkkB,EAAAA,aAAAA,CAAAA,KAAAA,EAAqBC,WAAAA,GAArBD,KAAAA,EAA0C;AACxC,QAAI,CAAJ,OAAY;AACV,aADU,KACV;AAFsC;;AAIxC,QAAIze,KAAAA,CAAAA,WAAAA,KAAsB,KAA1B,cAA6C;AAC3C,uBAAiB;AAGf,YACE,OAAOA,KAAAA,CAAP,4BACAA,KAAAA,CAAAA,WAAAA,CAAAA,MAAAA,KAA6B,kBAF/B,QAGE;AACA,iBADA,KACA;AAPa;;AASf,cAAM,cAAc2e,WAAAA,CAAAA,gBAAAA,CATL,YASKA,CAApB;;AACA,YAAIC,SAAAA,EAAAA,IAAAA,KAAJ,UAAkC;AAChC,iBADgC,KAChC;AAXa;AAAjB,aAaO;AAGL,eAHK,KAGL;AAjByC;AAJL;;AAwBxC,QAAI,CAAC1kB,MAAAA,CAAAA,SAAAA,CAAiB8F,KAAAA,CAAlB,GAAC9F,CAAD,IAAgC8F,KAAAA,CAAAA,GAAAA,GAApC,GAAmD;AACjD,aADiD,KACjD;AAzBsC;;AA2BxC,QAAIA,KAAAA,CAAAA,WAAAA,KAAAA,IAAAA,IAA8B,OAAOA,KAAAA,CAAP,gBAAlC,UAAyE;AACvE,aADuE,KACvE;AA5BsC;;AA8BxC,WA9BwC,IA8BxC;AA1ca;;AAgdf6e,EAAAA,oBAAAA,CAAAA,WAAAA,EAAAA,GAAAA,EAAuCC,eAAAA,GAAvCD,KAAAA,EAAgE;AAC9D,QAAI,KAAJ,wBAAiC;AAI/BxZ,MAAAA,YAAAA,CAAa,KAJkB,sBAI/BA,CAAAA;AACA,oCAL+B,IAK/B;AAN4D;;AAQ9D,QAAIyZ,eAAAA,IAAmB3B,WAAAA,EAAvB,WAA+C;AAG7C,aAAOA,WAAAA,CAHsC,SAG7C;AAX4D;;AAa9D,wBAb8D,WAa9D;AACA,gBAd8D,GAc9D;AACA,mBAAenc,IAAAA,CAAAA,GAAAA,CAAS,KAATA,OAAAA,EAf+C,GAe/CA,CAAf;AAEA,+BAjB8D,CAiB9D;AAjea;;AAuef+d,EAAAA,iBAAAA,CAAkBC,cAAAA,GAAlBD,KAAAA,EAA0C;AACxC,UAAMhkB,IAAAA,GAAOY,QAAAA,CAASuhB,cAATvhB,EAAAA,CAAAA,CAAAA,SAAAA,CAD2B,CAC3BA,CAAb;AACA,UAAMX,MAAAA,GAASC,gCAFyB,IAEzBA,CAAf;AAEA,UAAMgkB,SAAAA,GAAYjkB,MAAAA,CAAAA,GAAAA,CAAAA,WAAAA,KAJsB,EAIxC;AACA,QAAI/G,IAAAA,GAAO+G,MAAAA,CAAAA,GAAAA,CAAAA,MAAAA,IAL6B,CAKxC;;AAEA,QAAI,CAAC,kBAAD,IAAC,CAAD,IAA6BgkB,cAAAA,IAAkBC,SAAAA,CAAAA,MAAAA,GAAnD,GAA0E;AACxEhrB,MAAAA,IAAAA,GADwE,IACxEA;AARsC;;AAUxC,WAAO;AAAA;AAAA;AAAcyF,MAAAA,QAAAA,EAAU,iBAAxB;AAAA,KAAP;AAjfa;;AAuffwlB,EAAAA,eAAAA,CAAgB;AAAhBA,IAAAA;AAAgB,GAAhBA,EAA8B;AAC5B,QAAI,KAAJ,wBAAiC;AAC/B7Z,MAAAA,YAAAA,CAAa,KADkB,sBAC/BA,CAAAA;AACA,oCAF+B,IAE/B;AAH0B;;AAM5B,qBAAiB;AACftK,MAAAA,IAAAA,EAAM,mCACF,QAAQokB,QAAAA,CAAR,UADE,KAEFA,QAAAA,CAAAA,aAAAA,CAAAA,SAAAA,CAHW,CAGXA,CAHW;AAIflrB,MAAAA,IAAAA,EAAM,iBAJS;AAKfmQ,MAAAA,KAAAA,EAAO+a,QAAAA,CALQ;AAMfzlB,MAAAA,QAAAA,EAAUylB,QAAAA,CANK;AAAA,KAAjB;;AASA,QAAI,KAAJ,qBAA8B;AAAA;AAfF;;AAmB5B,QACEvC,0BAAAA,GAAAA,CAAAA,IACA,KADAA,cAAAA,IAEA,KAFAA,YAAAA,IAGA,CAAC,kBAJH,MAKE;AASA,WATA,mBASA;AAjC0B;;AAoC5B,QAAIC,uBAAAA,GAAJ,GAAiC;AAgB/B,oCAA8B,WAAW,MAAM;AAC7C,YAAI,CAAC,KAAL,qBAA+B;AAC7B,uCAD6B,IAC7B;AAF2C;;AAI7C,sCAJ6C,IAI7C;AAJ4B,SAhBC,uBAgBD,CAA9B;AApD0B;AAvff;;AAujBfuC,EAAAA,SAAAA,CAAU;AAAVA,IAAAA;AAAU,GAAVA,EAAqB;AACnB,UAAMC,OAAAA,GAAUnC,cAAhB;AAAA,UACEoC,WAAAA,GAAc,sBAFG,OACnB;AAEA,wBAHmB,OAGnB;;AAEA,QAKE,CALF,OAME;AAEA,WAFA,IAEA;;AAEA,YAAM;AAAA;AAAA;AAAA;AAAA,UAA2B,KAJjC,iBAIiC,EAAjC;;AACA,+BACE;AAAA;AAAA;AAAA;AAAA,OADF,EALA,IAKA;;AALA;AAXiB;;AAsBnB,QAAI,CAAC,mBAAL,KAAK,CAAL,EAAgC;AAAA;AAtBb;;AA8BnB,+BA9BmB,IA8BnB;;AAEA,qBAAiB;AAUf,WAVe,gBAUf;AACAC,0CAAqB;AACnBna,QAAAA,MAAAA,EADmB;AAEnBxM,QAAAA,IAAAA,EAFmB;AAGnBuM,QAAAA,KAAAA,EAHmB;AAAA,OAArBoa,EAAAA,IAAAA,CAIQ,MAAM;AACZ,aADY,gBACZ;AAhBa,OAWfA;AA3CiB;;AAqDnB,UAAMpC,WAAAA,GAAcnd,KAAAA,CArDD,WAqDnB;;AACA,2CAEEA,KAAAA,CAFF,KAtDmB,IAsDnB;;AAMA,QAAIwf,+BAAgBrC,WAAAA,CAApB,QAAIqC,CAAJ,EAA2C;AACzC,kCAA4BrC,WAAAA,CADa,QACzC;AA7DiB;;AA+DnB,QAAIA,WAAAA,CAAJ,MAAsB;AACpB,uCAAiCA,WAAAA,CADb,IACpB;AADF,WAEO,IAAIA,WAAAA,CAAJ,MAAsB;AAC3B,+BAAyBA,WAAAA,CADE,IAC3B;AADK,WAEA,IAAIA,WAAAA,CAAJ,MAAsB;AAE3B,8BAAwBA,WAAAA,CAFG,IAE3B;AArEiB;;AA0EnB1D,IAAAA,OAAAA,CAAAA,OAAAA,GAAAA,IAAAA,CAAuB,MAAM;AAC3B,iCAD2B,KAC3B;AA3EiB,KA0EnBA;AAjoBa;;AAyoBfgG,EAAAA,SAAAA,GAAY;AAMV,QAAI,CAAC,KAAD,gBAAsB,kBAA1B,WAAuD;AACrD,WADqD,uBACrD;AAPQ;AAzoBG;;AAupBfC,EAAAA,WAAAA,GAAc;AACZ,QAAI,KAAJ,cAAuB;AAAA;AADX;;AAIZ,wBAAoB;AAClBC,MAAAA,cAAAA,EAAgB,0BADE,IACF,CADE;AAElBC,MAAAA,QAAAA,EAAU,oBAFQ,IAER,CAFQ;AAGlBC,MAAAA,QAAAA,EAAU,oBAHQ,IAGR;AAHQ,KAApB;;AAMA,wCAAoC,kBAVxB,cAUZ;;AACAprB,IAAAA,MAAAA,CAAAA,gBAAAA,CAAAA,UAAAA,EAAoC,kBAXxB,QAWZA;AACAA,IAAAA,MAAAA,CAAAA,gBAAAA,CAAAA,UAAAA,EAAoC,kBAZxB,QAYZA;AAnqBa;;AAyqBfqrB,EAAAA,aAAAA,GAAgB;AACd,QAAI,CAAC,KAAL,cAAwB;AAAA;AADV;;AAId,yCAAqC,kBAJvB,cAId;;AACArrB,IAAAA,MAAAA,CAAAA,mBAAAA,CAAAA,UAAAA,EAAuC,kBALzB,QAKdA;AACAA,IAAAA,MAAAA,CAAAA,mBAAAA,CAAAA,UAAAA,EAAuC,kBANzB,QAMdA;AAEA,wBARc,IAQd;AAjrBa;;AAAA;;;;AAqrBjB,+CAA+C;AAC7C,MAAI,gCAAgC,oBAApC,UAAkE;AAChE,WADgE,KAChE;AAF2C;;AAI7C,MAAIsrB,QAAAA,KAAJ,UAA2B;AACzB,WADyB,IACzB;AAL2C;;AAO7C,QAAMd,SAAAA,GAAYhkB,gCAAAA,QAAAA,EAAAA,GAAAA,CAP2B,WAO3BA,CAAlB;;AACA,MAAIgkB,SAAAA,KAAJ,UAA4B;AAC1B,WAD0B,IAC1B;AAT2C;;AAW7C,SAX6C,KAW7C;AAvvBF;;AA0vBA,kDAAkD;AAChD,uCAAqC;AACnC,QAAI,iBAAiB,OAArB,QAAoC;AAClC,aADkC,KAClC;AAFiC;;AAInC,QAAI5kB,KAAAA,CAAAA,OAAAA,CAAAA,KAAAA,KAAwBA,KAAAA,CAAAA,OAAAA,CAA5B,MAA4BA,CAA5B,EAAmD;AACjD,aADiD,KACjD;AALiC;;AAOnC,QAAI+J,KAAAA,KAAAA,IAAAA,IAAkB,iBAAlBA,QAAAA,IAA+C4b,MAAAA,KAAnD,MAAoE;AAClE,UAAI1mB,MAAAA,CAAAA,IAAAA,CAAAA,KAAAA,EAAAA,MAAAA,KAA8BA,MAAAA,CAAAA,IAAAA,CAAAA,MAAAA,EAAlC,QAA8D;AAC5D,eAD4D,KAC5D;AAFgE;;AAIlE,+BAAyB;AACvB,YAAI,CAAC2mB,YAAAA,CAAa7b,KAAAA,CAAb6b,GAAa7b,CAAb6b,EAAyBD,MAAAA,CAA9B,GAA8BA,CAAzBC,CAAL,EAA4C;AAC1C,iBAD0C,KAC1C;AAFqB;AAJyC;;AASlE,aATkE,IASlE;AAhBiC;;AAkBnC,WAAO7b,KAAAA,KAAAA,MAAAA,IAAqBlK,MAAAA,CAAAA,KAAAA,CAAAA,KAAAA,KAAuBA,MAAAA,CAAAA,KAAAA,CAlBhB,MAkBgBA,CAAnD;AAnB8C;;AAsBhD,MAAI,EAAE,4BAA4BG,KAAAA,CAAAA,OAAAA,CAAlC,UAAkCA,CAA9B,CAAJ,EAA8D;AAC5D,WAD4D,KAC5D;AAvB8C;;AAyBhD,MAAI6lB,SAAAA,CAAAA,MAAAA,KAAqBC,UAAAA,CAAzB,QAA4C;AAC1C,WAD0C,KAC1C;AA1B8C;;AA4BhD,OAAK,IAAI5jB,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK0Z,SAAAA,CAArB,QAAuC3jB,CAAAA,GAAvC,IAA+CA,CAA/C,IAAoD;AAClD,QAAI,CAAC0jB,YAAAA,CAAaC,SAAAA,CAAbD,CAAaC,CAAbD,EAA2BE,UAAAA,CAAhC,CAAgCA,CAA3BF,CAAL,EAAgD;AAC9C,aAD8C,KAC9C;AAFgD;AA5BJ;;AAiChD,SAjCgD,IAiChD;AA3xBF;;;;;;;;;;;;;ACeA;;AASA;;AAxBA;;AAAA;;AAAA;;AAiEA,MAAMG,iBAAAA,GAAoB1U,iCAAAA,eAAAA,IAjE1B,QAiEA;;AAKA,kBAAkB;AAIhBzY,EAAAA,WAAAA,CAAAA,OAAAA,EAAqB;AACnB,UAAM8T,SAAAA,GAAYjB,OAAAA,CADC,SACnB;AACA,UAAMua,eAAAA,GAAkBva,OAAAA,CAFL,eAEnB;AAEA,cAAUA,OAAAA,CAJS,EAInB;AACA,uBAAmB,SAAS,KALT,EAKnB;AAEA,mBAPmB,IAOnB;AACA,qBARmB,IAQnB;AACA,oBATmB,CASnB;AACA,iBAAaA,OAAAA,CAAAA,KAAAA,IAVM,uBAUnB;AACA,oBAXmB,eAWnB;AACA,yBAAqBua,eAAAA,CAZF,QAYnB;AACA,yCACEva,OAAAA,CAAAA,4BAAAA,IAdiB,IAanB;AAEA,gCAfmB,KAenB;AACA,yBAAqB5L,MAAAA,CAAAA,SAAAA,CAAiB4L,OAAAA,CAAjB5L,aAAAA,IACjB4L,OAAAA,CADiB5L,aAAAA,GAEjB+D,wBAlBe,MAgBnB;AAGA,8BAA0B6H,OAAAA,CAAAA,kBAAAA,IAnBP,EAmBnB;AACA,kCAA8BA,OAAAA,CAAAA,sBAAAA,KApBX,KAoBnB;AACA,0BAAsBA,OAAAA,CAAAA,cAAAA,IArBH,KAqBnB;AACA,2BAAuBA,OAAAA,CAAAA,eAAAA,IAtBJ,iBAsBnB;AAEA,oBAAgBA,OAAAA,CAxBG,QAwBnB;AACA,0BAAsBA,OAAAA,CAzBH,cAyBnB;AACA,4BAAwBA,OAAAA,CA1BL,gBA0BnB;AACA,kCAA8BA,OAAAA,CA3BX,sBA2BnB;AACA,2BAAuBA,OAAAA,CA5BJ,eA4BnB;AACA,kCAA8BA,OAAAA,CA7BX,sBA6BnB;AACA,oBAAgBA,OAAAA,CAAAA,QAAAA,IAAoBhI,uBA9BjB,MA8BnB;AACA,gBAAYgI,OAAAA,CAAAA,IAAAA,IA/BO,oBA+BnB;AAEA,qBAjCmB,IAiCnB;AACA,8BAA0B,IAlCP,OAkCO,EAA1B;AACA,0BAAsBwa,qCAnCH,OAmCnB;AACA,kBApCmB,IAoCnB;AACA,wBArCmB,IAqCnB;AACA,yBAAqB,CAAC,qBAtCH,SAsCG,EAAtB;AAEA,2BAxCmB,IAwCnB;AACA,qBAzCmB,IAyCnB;AACA,qBA1CmB,IA0CnB;AACA,oBA3CmB,IA2CnB;AACA,2BA5CmB,IA4CnB;AAEA,UAAMtsB,GAAAA,GAAMK,QAAAA,CAAAA,aAAAA,CA9CO,KA8CPA,CAAZ;AACAL,IAAAA,GAAAA,CAAAA,SAAAA,GA/CmB,MA+CnBA;AACAA,IAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GAAkBgN,IAAAA,CAAAA,KAAAA,CAAW,cAAXA,KAAAA,IAhDC,IAgDnBhN;AACAA,IAAAA,GAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GAAmBgN,IAAAA,CAAAA,KAAAA,CAAW,cAAXA,MAAAA,IAjDA,IAiDnBhN;AACAA,IAAAA,GAAAA,CAAAA,YAAAA,CAAAA,kBAAAA,EAAqC,KAlDlB,EAkDnBA;AACAA,IAAAA,GAAAA,CAAAA,YAAAA,CAAAA,MAAAA,EAnDmB,QAmDnBA;AACA,mCAA+B;AAAEC,MAAAA,IAAAA,EAAM,KAAvC;AAA+B,KAA/B,OAAuDssB,GAAAA,IAAO;AAC5DvsB,MAAAA,GAAAA,CAAAA,YAAAA,CAAAA,YAAAA,EAD4D,GAC5DA;AArDiB,KAoDnB;AAGA,eAvDmB,GAuDnB;AAEA+S,IAAAA,SAAAA,CAAAA,WAAAA,CAzDmB,GAyDnBA;AA7Dc;;AAgEhByZ,EAAAA,UAAAA,CAAAA,OAAAA,EAAoB;AAClB,mBADkB,OAClB;AACA,yBAAqB7G,OAAAA,CAFH,MAElB;AAEA,UAAM8G,aAAAA,GAAiB,iBAAgB,KAAjB,aAAC,IAJL,GAIlB;AACA,oBAAgB,oBAAoB;AAClCC,MAAAA,KAAAA,EAAO,aAD2B;AAElChnB,MAAAA,QAAAA,EAFkC;AAAA,KAApB,CAAhB;AAIA,SATkB,KASlB;AAzEc;;AA4EhBinB,EAAAA,OAAAA,GAAU;AACR,SADQ,KACR;;AACA,QAAI,KAAJ,SAAkB;AAChB,mBADgB,OAChB;AAHM;AA5EM;;AAsFhB,QAAMC,sBAAN,GAA+B;AAC7B,QAAIC,KAAAA,GADyB,IAC7B;;AACA,QAAI;AACF,YAAM,4BAA4B,KAA5B,UADJ,SACI,CAAN;AADF,MAEE,WAAW;AACXA,MAAAA,KAAAA,GADW,EACXA;AAHF,cAIU;AACR,wDAAkD;AAChD3lB,QAAAA,MAAAA,EADgD;AAEhDnB,QAAAA,UAAAA,EAAY,KAFoC;AAAA;AAAA,OAAlD;AAP2B;AAtFf;;AAwGhB,QAAM+mB,eAAN,GAAwB;AACtB,QAAID,KAAAA,GADkB,IACtB;;AACA,QAAI;AACF,YAAM,qBAAqB,KAArB,UADJ,SACI,CAAN;AADF,MAEE,WAAW;AACXA,MAAAA,KAAAA,GADW,EACXA;AAHF,cAIU;AACR,iDAA2C;AACzC3lB,QAAAA,MAAAA,EADyC;AAEzCnB,QAAAA,UAAAA,EAAY,KAF6B;AAAA;AAAA,OAA3C;AAPoB;AAxGR;;AA0HhBgnB,EAAAA,eAAAA,CAAgBC,aAAAA,GAAhBD,KAAAA,EAAuC;AACrC,QAAI,CAAC,KAAL,WAAqB;AAAA;AADgB;;AAIrC,UAAME,eAAAA,GAAkB,eAJa,UAIrC;AACA,mCALqC,eAKrC;AAGAA,IAAAA,eAAAA,CAAAA,KAAAA,GARqC,CAQrCA;AACAA,IAAAA,eAAAA,CAAAA,MAAAA,GATqC,CASrCA;;AAEA,uBAAmB;AAEjB,qBAFiB,MAEjB;AAbmC;;AAerC,qBAfqC,IAerC;AAzIc;;AA4IhB7D,EAAAA,KAAAA,CAAM;AACJ8D,IAAAA,aAAAA,GADI;AAEJC,IAAAA,mBAAAA,GAFI;AAGJC,IAAAA,YAAAA,GAHI;AAAA,MAANhE,EAAAA,EAIQ;AACN,yBAAqB;AAAA;AAAA;AAAA,KAArB;AACA,0BAAsBkD,qCAFhB,OAEN;AAEA,UAAMtsB,GAAAA,GAAM,KAJN,GAIN;AACAA,IAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GAAkBgN,IAAAA,CAAAA,KAAAA,CAAW,cAAXA,KAAAA,IALZ,IAKNhN;AACAA,IAAAA,GAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GAAmBgN,IAAAA,CAAAA,KAAAA,CAAW,cAAXA,MAAAA,IANb,IAMNhN;AAEA,UAAMqtB,UAAAA,GAAartB,GAAAA,CAAnB;AAAA,UACEstB,aAAAA,GAAiBJ,aAAAA,IAAiB,KAAlB,SAACA,IADnB;AAAA,UAEEK,mBAAAA,GACGJ,mBAAAA,IAAuB,sBAAxB,GAACA,IAHL;AAAA,UAIEK,YAAAA,GAAgBJ,YAAAA,IAAgB,eAAjB,GAACA,IAZZ,IAQN;;AAKA,SAAK,IAAI7kB,CAAAA,GAAI8kB,UAAAA,CAAAA,MAAAA,GAAb,GAAoC9kB,CAAAA,IAApC,GAA4CA,CAA5C,IAAiD;AAC/C,YAAMsN,IAAAA,GAAOwX,UAAAA,CADkC,CAClCA,CAAb;;AACA;AACE,aADF,aACE;AACA,aAFF,mBAEE;AACA;AAHF;AAAA;;AAMArtB,MAAAA,GAAAA,CAAAA,WAAAA,CAR+C,IAQ/CA;AArBI;;AAuBNA,IAAAA,GAAAA,CAAAA,eAAAA,CAvBM,aAuBNA;;AAEA,6BAAyB;AAGvB,2BAHuB,IAGvB;AA5BI;;AA8BN,sBAAkB;AAGhB,oBAHgB,IAGhB;AAjCI;;AAoCN,QAAI,CAAJ,eAAoB;AAClB,UAAI,KAAJ,QAAiB;AACf,uCAA+B,KADhB,MACf;AAGA,4BAJe,CAIf;AACA,6BALe,CAKf;AACA,eAAO,KANQ,MAMf;AAPgB;;AASlB,WATkB,eASlB;AA7CI;;AA+CN,QAAI,KAAJ,KAAc;AACZ,qCAA+B,KADnB,GACZ;AACA,aAAO,KAFK,GAEZ;AAjDI;;AAoDN,0BAAsBK,QAAAA,CAAAA,aAAAA,CApDhB,KAoDgBA,CAAtB;AACA,oCArDM,aAqDN;AACA,6CAtDM,KAsDN;AACA,kCAA8BksB,GAAAA,IAAO;AACnC,sDADmC,GACnC;AAxDI,KAuDN;AAGAvsB,IAAAA,GAAAA,CAAAA,WAAAA,CAAgB,KA1DV,cA0DNA;AA1Mc;;AA6MhBytB,EAAAA,MAAAA,CAAAA,KAAAA,EAAAA,QAAAA,EAAwBC,4BAAAA,GAAxBD,IAAAA,EAA6D;AAC3D,iBAAaf,KAAAA,IAAS,KADqC,KAC3D;;AAEA,QAAI,oBAAJ,aAAqC;AACnC,sBADmC,QACnC;AAJyD;;AAM3D,QAAIgB,4BAAAA,YAAJ,SAAqD;AACnD,2CADmD,4BACnD;AAPyD;;AAS3D,QAAI,KAAJ,eAAwB;AACtB,YAAMxa,GAAAA,GAAM7S,QAAAA,CADU,eACtB;AACA6S,MAAAA,GAAAA,CAAAA,KAAAA,CAAAA,WAAAA,CAAAA,eAAAA,EAAuC,KAFjB,KAEtBA;AAXyD;;AAc3D,UAAMuZ,aAAAA,GAAiB,iBAAgB,KAAjB,aAAC,IAdoC,GAc3D;AACA,oBAAgB,oBAAoB;AAClCC,MAAAA,KAAAA,EAAO,aAD2B;AAElChnB,MAAAA,QAAAA,EAFkC;AAAA,KAApB,CAAhB;;AAKA,QAAI,KAAJ,KAAc;AACZ,wBAAkB;AAChB0L,QAAAA,MAAAA,EAAQ,KADQ;AAEhBuc,QAAAA,qBAAAA,EAFgB;AAGhBC,QAAAA,cAAAA,EAHgB;AAAA,OAAlB;AAMA,6CAAuC;AACrC1mB,QAAAA,MAAAA,EADqC;AAErCnB,QAAAA,UAAAA,EAAY,KAFyB;AAGrC8nB,QAAAA,YAAAA,EAHqC;AAIrCC,QAAAA,SAAAA,EAAWnD,WAAAA,CAJ0B,GAI1BA,EAJ0B;AAKrCkC,QAAAA,KAAAA,EAAO,KAL8B;AAAA,OAAvC;AAPY;AApB6C;;AAqC3D,QAAIkB,mBAAAA,GArCuD,KAqC3D;;AACA,QAAI,eAAe,uBAAnB,GAA6C;AAC3C,YAAMC,WAAAA,GAAc,KADuB,WAC3C;;AACA,UACG,CAAChhB,IAAAA,CAAAA,KAAAA,CAAW,cAAXA,KAAAA,IAAkCghB,WAAAA,CAAnC,EAAChhB,GAAF,CAAC,KACGA,IAAAA,CAAAA,KAAAA,CAAW,cAAXA,MAAAA,IAAmCghB,WAAAA,CAApC,EAAChhB,GADJ,CAAC,IAED,KAHF,iBAIE;AACA+gB,QAAAA,mBAAAA,GADA,IACAA;AAPyC;AAtCc;;AAiD3D,QAAI,KAAJ,QAAiB;AACf,UACE,uBACC,6BAFH,qBAGE;AACA,0BAAkB;AAChB3c,UAAAA,MAAAA,EAAQ,KADQ;AAEhBuc,UAAAA,qBAAAA,EAFgB;AAGhBC,UAAAA,cAAAA,EAHgB;AAAA,SAAlB;AAMA,+CAAuC;AACrC1mB,UAAAA,MAAAA,EADqC;AAErCnB,UAAAA,UAAAA,EAAY,KAFyB;AAGrC8nB,UAAAA,YAAAA,EAHqC;AAIrCC,UAAAA,SAAAA,EAAWnD,WAAAA,CAJ0B,GAI1BA,EAJ0B;AAKrCkC,UAAAA,KAAAA,EAAO,KAL8B;AAAA,SAAvC;AAPA;AAJa;;AAoBf,UAAI,CAAC,KAAD,aAAmB,CAAC,YAAxB,QAA4C;AAC1C,yBAAiB,YADyB,UAC1C;AACA,wCAF0C,UAE1C;AAtBa;AAjD0C;;AA0E3D,QAAI,KAAJ,WAAoB;AAClB,wBAAkB;AAAEzb,QAAAA,MAAAA,EAAQ,eADV;AACA,OAAlB;AA3EyD;;AA6E3D,eAAW;AACT8b,MAAAA,aAAAA,EADS;AAETC,MAAAA,mBAAAA,EAFS;AAGTC,MAAAA,YAAAA,EAHS;AAAA,KAAX;AA1Rc;;AAqShBa,EAAAA,eAAAA,CAAgB;AAAEd,IAAAA,mBAAAA,GAAF;AAA+BC,IAAAA,YAAAA,GAA/B;AAAA,MAAhBa,EAAAA,EAA4E;AAC1E,QAAI,KAAJ,WAAoB;AAClB,qBADkB,MAClB;AACA,uBAFkB,IAElB;AAHwE;;AAK1E,kBAL0E,IAK1E;;AAEA,QAAI,KAAJ,WAAoB;AAClB,qBADkB,MAClB;AACA,uBAFkB,IAElB;AATwE;;AAW1E,QACE,yBACC,wBAAwB,CAAC,qBAF5B,GACE,CADF,EAGE;AACA,2BADA,MACA;AACA,6BAFA,IAEA;AAhBwE;;AAkB1E,QAAI,kBAAkB,iBAAiB,CAAC,cAAxC,GAAI,CAAJ,EAA4D;AAC1D,oBAD0D,MAC1D;AACA,sBAF0D,IAE1D;AApBwE;;AAsB1E,QAAI,KAAJ,sBAA+B;AAC7B,8CAAwC,KADX,oBAC7B;;AACA,kCAF6B,IAE7B;AAxBwE;AArS5D;;AAiUhBJ,EAAAA,YAAAA,CAAa;AAAA;AAEXF,IAAAA,qBAAAA,GAFW;AAGXC,IAAAA,cAAAA,GAHFC;AAAa,GAAbA,EAIG;AAED,UAAM9f,KAAAA,GAAQ,cAFb,KAED;AACA,UAAMC,MAAAA,GAAS,cAHd,MAGD;AACA,UAAMhO,GAAAA,GAAM,KAJX,GAID;AACAoR,IAAAA,MAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GACEA,MAAAA,CAAAA,UAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GACApR,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GACEgN,IAAAA,CAAAA,KAAAA,CAAAA,KAAAA,IARH,IAKDoE;AAIAA,IAAAA,MAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GACEA,MAAAA,CAAAA,UAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GACApR,GAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GACEgN,IAAAA,CAAAA,KAAAA,CAAAA,MAAAA,IAZH,IASDoE;AAKA,UAAM8c,gBAAAA,GACJ,yBAAyB,oCAf1B,QAcD;AAEA,UAAMC,WAAAA,GAAcnhB,IAAAA,CAAAA,GAAAA,CAhBnB,gBAgBmBA,CAApB;AACA,QAAIohB,MAAAA,GAAJ;AAAA,QACEC,MAAAA,GAlBD,CAiBD;;AAEA,QAAIF,WAAAA,KAAAA,EAAAA,IAAsBA,WAAAA,KAA1B,KAA+C;AAE7CC,MAAAA,MAAAA,GAASpgB,MAAAA,GAFoC,KAE7CogB;AACAC,MAAAA,MAAAA,GAAStgB,KAAAA,GAHoC,MAG7CsgB;AAtBD;;AAwBDjd,IAAAA,MAAAA,CAAAA,KAAAA,CAAAA,SAAAA,GAAyB,yDAxBxB,GAwBDA;;AAEA,QAAI,KAAJ,WAAoB;AAKlB,YAAMkd,iBAAAA,GAAoB,eALR,QAKlB;AACA,YAAMC,oBAAAA,GACJ,yBAAyBD,iBAAAA,CAPT,QAMlB;AAEA,YAAME,eAAAA,GAAkBxhB,IAAAA,CAAAA,GAAAA,CARN,oBAQMA,CAAxB;AACA,UAAI0f,KAAAA,GAAQ3e,KAAAA,GAAQugB,iBAAAA,CATF,KASlB;;AACA,UAAIE,eAAAA,KAAAA,EAAAA,IAA0BA,eAAAA,KAA9B,KAAuD;AACrD9B,QAAAA,KAAAA,GAAQ3e,KAAAA,GAAQugB,iBAAAA,CADqC,MACrD5B;AAXgB;;AAalB,YAAM+B,YAAAA,GAAe,eAbH,YAalB;AACA,kBAdkB,MAclB;;AACA;AACE;AACEC,UAAAA,MAAAA,GAASC,MAAAA,GADX,CACED;AAFJ;;AAIE;AACEA,UAAAA,MAAAA,GADF,CACEA;AACAC,UAAAA,MAAAA,GAAS,MAAMF,YAAAA,CAAAA,KAAAA,CAFjB,MAEEE;AANJ;;AAQE;AACED,UAAAA,MAAAA,GAAS,MAAMD,YAAAA,CAAAA,KAAAA,CADjB,KACEC;AACAC,UAAAA,MAAAA,GAAS,MAAMF,YAAAA,CAAAA,KAAAA,CAFjB,MAEEE;AAVJ;;AAYE;AACED,UAAAA,MAAAA,GAAS,MAAMD,YAAAA,CAAAA,KAAAA,CADjB,KACEC;AACAC,UAAAA,MAAAA,GAFF,CAEEA;AAdJ;;AAgBE;AACE1oB,UAAAA,OAAAA,CAAAA,KAAAA,CADF,qBACEA;AAjBJ;AAAA;;AAqBAwoB,MAAAA,YAAAA,CAAAA,KAAAA,CAAAA,SAAAA,GACE,mCACA,cADA,OAEA,8BAvCgB,GAoClBA;AAIAA,MAAAA,YAAAA,CAAAA,KAAAA,CAAAA,eAAAA,GAxCkB,OAwClBA;AAlED;;AAqED,QAAId,qBAAAA,IAAyB,KAA7B,iBAAmD;AACjD,WADiD,sBACjD;AAtED;;AAwED,QAAIC,cAAAA,IAAkB,KAAtB,UAAqC;AACnC,WADmC,eACnC;AAzED;AArUa;;AAkZhB,MAAI7f,KAAJ,GAAY;AACV,WAAO,cADG,KACV;AAnZc;;AAsZhB,MAAIC,MAAJ,GAAa;AACX,WAAO,cADI,MACX;AAvZc;;AA0ZhB4gB,EAAAA,YAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAmB;AACjB,WAAO,mCADU,CACV,CAAP;AA3Zc;;AA8ZhBC,EAAAA,IAAAA,GAAO;AACL,QAAI,wBAAwBvC,qCAA5B,SAAqD;AACnDrmB,MAAAA,OAAAA,CAAAA,KAAAA,CADmD,qCACnDA;AACA,WAFmD,KAEnD;AAHG;;AAKL,UAAM;AAAA;AAAA;AAAA,QALD,IAKL;;AAEA,QAAI,CAAJ,SAAc;AACZ,4BAAsBqmB,qCADV,QACZ;;AAEA,UAAI,KAAJ,gBAAyB;AACvBtsB,QAAAA,GAAAA,CAAAA,WAAAA,CAAgB,KADO,cACvBA;AACA,eAAO,KAFgB,cAEvB;AALU;;AAOZ,aAAOylB,OAAAA,CAAAA,MAAAA,CAAe,UAPV,uBAOU,CAAfA,CAAP;AAdG;;AAiBL,0BAAsB6G,qCAjBjB,OAiBL;AAIA,UAAMwC,aAAAA,GAAgBzuB,QAAAA,CAAAA,aAAAA,CArBjB,KAqBiBA,CAAtB;AACAyuB,IAAAA,aAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GAA4B9uB,GAAAA,CAAAA,KAAAA,CAtBvB,KAsBL8uB;AACAA,IAAAA,aAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GAA6B9uB,GAAAA,CAAAA,KAAAA,CAvBxB,MAuBL8uB;AACAA,IAAAA,aAAAA,CAAAA,SAAAA,CAAAA,GAAAA,CAxBK,eAwBLA;;AAEA,QAAI,sBAAJ,KAA+B;AAE7B9uB,MAAAA,GAAAA,CAAAA,YAAAA,CAAAA,aAAAA,EAAgC,qBAFH,GAE7BA;AAFF,WAGO;AACLA,MAAAA,GAAAA,CAAAA,WAAAA,CADK,aACLA;AA9BG;;AAiCL,QAAI+uB,SAAAA,GAjCC,IAiCL;;AACA,QAAI,uBAAuB9kB,wBAAvB,WAAgD,KAApD,kBAA2E;AACzE,YAAMwkB,YAAAA,GAAepuB,QAAAA,CAAAA,aAAAA,CADoD,KACpDA,CAArB;AACAouB,MAAAA,YAAAA,CAAAA,SAAAA,GAFyE,WAEzEA;AACAA,MAAAA,YAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GAA2BK,aAAAA,CAAAA,KAAAA,CAH8C,KAGzEL;AACAA,MAAAA,YAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GAA4BK,aAAAA,CAAAA,KAAAA,CAJ6C,MAIzEL;;AACA,UAAI,sBAAJ,KAA+B;AAE7BzuB,QAAAA,GAAAA,CAAAA,YAAAA,CAAAA,YAAAA,EAA+B,qBAFF,GAE7BA;AAFF,aAGO;AACLA,QAAAA,GAAAA,CAAAA,WAAAA,CADK,YACLA;AATuE;;AAYzE+uB,MAAAA,SAAAA,GAAY,2DAEV,UAFU,GAGV,KAHU,UAIV,uBAAuB9kB,wBAJb,gBAKV,KAjBuE,QAY7D,CAAZ8kB;AA9CG;;AAsDL,qBAtDK,SAsDL;;AAEA,QAAI,eAAJ,KAAwB;AAEtB/uB,MAAAA,GAAAA,CAAAA,WAAAA,CAAgB,cAFM,GAEtBA;AA1DG;;AA6DL,QAAIgvB,sBAAAA,GA7DC,IA6DL;;AACA,QAAI,KAAJ,gBAAyB;AACvBA,MAAAA,sBAAAA,GAAyBC,IAAAA,IAAQ;AAC/B,YAAI,CAAC,sCAAL,IAAK,CAAL,EAAkD;AAChD,gCAAsB3C,qCAD0B,MAChD;;AACA,wBAAc,MAAM;AAClB,kCAAsBA,qCADJ,OAClB;AACA2C,YAAAA,IAFkB;AAF4B,WAEhD;;AAFgD;AADnB;;AAS/BA,QAAAA,IAT+B;AADV,OACvBD;AA/DG;;AA4EL,UAAME,eAAAA,GAAkB,OAAOrC,KAAAA,GAAP,SAAwB;AAI9C,UAAIsC,SAAAA,KAAc,KAAlB,WAAkC;AAChC,yBADgC,IAChC;AAL4C;;AAQ9C,UAAItC,KAAAA,YAAJ,uCAAkD;AAChD,4BADgD,IAChD;AADgD;AARJ;;AAY9C,0BAZ8C,KAY9C;AAEA,4BAAsBP,qCAdwB,QAc9C;;AAEA,UAAI,KAAJ,gBAAyB;AACvBtsB,QAAAA,GAAAA,CAAAA,WAAAA,CAAgB,KADO,cACvBA;AACA,eAAO,KAFgB,cAEvB;AAlB4C;;AAoB9C,2BApB8C,IAoB9C;;AAEA,6CAAuC;AACrCkH,QAAAA,MAAAA,EADqC;AAErCnB,QAAAA,UAAAA,EAAY,KAFyB;AAGrC8nB,QAAAA,YAAAA,EAHqC;AAIrCC,QAAAA,SAAAA,EAAWnD,WAAAA,CAJ0B,GAI1BA,EAJ0B;AAKrCkC,QAAAA,KAAAA,EAAO,KAL8B;AAAA,OAAvC;;AAQA,iBAAW;AACT,cADS,KACT;AA/B4C;AA5E3C,KA4EL;;AAmCA,UAAMsC,SAAAA,GACJ,kBAAkBrlB,uBAAlB,MACI,gBADJ,aACI,CADJ,GAEI,mBAlHD,aAkHC,CAHN;AAIAqlB,IAAAA,SAAAA,CAAAA,gBAAAA,GAnHK,sBAmHLA;AACA,qBApHK,SAoHL;AAEA,UAAMC,aAAAA,GAAgB,uBACpB,MAAM;AACJ,aAAO,2BAA2B,MAAM;AACtC,uBAAe;AACb,gBAAMC,cAAAA,GAAiB,0BAA0B;AAC/CzJ,YAAAA,mBAAAA,EAD+C;AAE/C0J,YAAAA,oBAAAA,EAF+C;AAAA,WAA1B,CAAvB;AAIAP,UAAAA,SAAAA,CAAAA,oBAAAA,CALa,cAKbA;AACAA,UAAAA,SAAAA,CANa,MAMbA;AAPoC;AADpC,OACG,CAAP;AAFkB,OAapB,kBAAkB;AAChB,aAAOG,eAAAA,CADS,MACTA,CAAP;AApIC,KAsHiB,CAAtB;;AAkBA,QAAI,KAAJ,wBAAiC;AAC/B,UAAI,CAAC,KAAL,iBAA2B;AACzB,+BACE,6EAIE,KAJF,oBAKE,KALF,wBAME,KANF,kBAFuB,IAEvB,CADF;AAF6B;;AAe/B,WAf+B,sBAe/B;AAvJG;;AA0JL,QAAI,KAAJ,iBAA0B;AACxB,UAAI,CAAC,KAAL,UAAoB;AAClB,wBAAgB,yDADE,IACF,CAAhB;AAFsB;;AAQxB,WARwB,eAQxB;AAlKG;;AAuKL,QAAI,+BAA+B,KAA/B,aAAiD,KAArD,QAAkE;AAGhE,kCAA4BK,KAAAA,IAAS;AACnC,YAAIA,KAAAA,CAAAA,UAAAA,KAAqB,KAAzB,IAAkC;AAAA;AADC;;AAInC,gDAAwC,KAJL,oBAInC;;AACA,oCALmC,IAKnC;;AAEA,YAAI,CAAC,KAAL,QAAkB;AAAA;AAPiB;;AAUnC,0CAAkCC,IAAAA,IAAQ;AACxC,cAAI,CAAJ,MAAW;AAAA;AAD6B;;AAIxC,cAAI,CAAC,KAAL,QAAkB;AAAA;AAJsB;;AAOxC,gBAAMC,OAAAA,GAAU,4BAPwB,IAOxB,CAAhB;AACAA,UAAAA,OAAAA,CAAAA,SAAAA,CAAAA,GAAAA,CARwC,YAQxCA;AACA,kCATwC,OASxC;AAnBiC,SAUnC;AAb8D,OAGhE;;AAsBA,6CAAuC,KAzByB,oBAyBhE;;AACA,6BACE,yDA3B8D,OA2B9D,CADF;AAjMG;;AAqMLzvB,IAAAA,GAAAA,CAAAA,YAAAA,CAAAA,aAAAA,EArMK,IAqMLA;AAEA,yCAAqC;AACnCkH,MAAAA,MAAAA,EADmC;AAEnCnB,MAAAA,UAAAA,EAAY,KAFuB;AAAA,KAArC;AAIA,WA3MK,aA2ML;AAzmBc;;AA4mBhB2pB,EAAAA,aAAAA,CAAAA,aAAAA,EAA6B;AAC3B,UAAMC,gBAAAA,GADqB,wCAC3B;AACA,UAAMhiB,MAAAA,GAAS;AACb6X,MAAAA,OAAAA,EAASmK,gBAAAA,CADI;;AAEbC,MAAAA,gBAAAA,CAAAA,IAAAA,EAAuB;AACrBX,QAAAA,IADqB;AAFV;;AAKb3uB,MAAAA,MAAAA,GAAS;AACPuvB,QAAAA,UAAAA,CADO,MACPA;AANW;;AAAA,KAAf;AAUA,UAAM/vB,QAAAA,GAAW,KAZU,QAY3B;AACA,UAAMgwB,MAAAA,GAASzvB,QAAAA,CAAAA,aAAAA,CAbY,QAaZA,CAAf;AAIAyvB,IAAAA,MAAAA,CAAAA,MAAAA,GAjB2B,IAiB3BA;AACA,QAAIC,cAAAA,GAlBuB,IAkB3B;;AACA,UAAMC,UAAAA,GAAa,YAAY;AAC7B,0BAAoB;AAClBF,QAAAA,MAAAA,CAAAA,MAAAA,GADkB,KAClBA;AACAC,QAAAA,cAAAA,GAFkB,KAElBA;AAH2B;AAnBJ,KAmB3B;;AAOAjB,IAAAA,aAAAA,CAAAA,WAAAA,CA1B2B,MA0B3BA;AACA,kBA3B2B,MA2B3B;AAMEgB,IAAAA,MAAAA,CAAAA,SAAAA,GAjCyB,IAiCzBA;AAGF,UAAM/kB,GAAAA,GAAM+kB,MAAAA,CAAAA,UAAAA,CAAAA,IAAAA,EAAwB;AAAEG,MAAAA,KAAAA,EApCX;AAoCS,KAAxBH,CAAZ;AACA,UAAM9B,WAAAA,GAAckC,8BArCO,GAqCPA,CAApB;AACA,uBAtC2B,WAsC3B;;AAEA,QAAI,KAAJ,gBAAyB;AACvB,YAAMC,kBAAAA,GAAqBrwB,QAAAA,CAAAA,KAAAA,CAAe;AAAE4sB,QAAAA,KAAAA,EADrB;AACmB,OAAf5sB,CAA3B;AAGAkuB,MAAAA,WAAAA,CAAAA,EAAAA,IAAkBmC,kBAAAA,CAAAA,KAAAA,GAA2BrwB,QAAAA,CAJtB,KAIvBkuB;AACAA,MAAAA,WAAAA,CAAAA,EAAAA,IAAkBmC,kBAAAA,CAAAA,MAAAA,GAA4BrwB,QAAAA,CALvB,MAKvBkuB;AACAA,MAAAA,WAAAA,CAAAA,MAAAA,GANuB,IAMvBA;AA9CyB;;AAiD3B,QAAI,uBAAJ,GAA8B;AAC5B,YAAMoC,gBAAAA,GAAmBtwB,QAAAA,CAAAA,KAAAA,GAAiBA,QAAAA,CADd,MAC5B;AACA,YAAMuwB,QAAAA,GAAWrjB,IAAAA,CAAAA,IAAAA,CAAU,uBAFC,gBAEXA,CAAjB;;AACA,UAAIghB,WAAAA,CAAAA,EAAAA,GAAAA,QAAAA,IAA6BA,WAAAA,CAAAA,EAAAA,GAAjC,UAA4D;AAC1DA,QAAAA,WAAAA,CAAAA,EAAAA,GAD0D,QAC1DA;AACAA,QAAAA,WAAAA,CAAAA,EAAAA,GAF0D,QAE1DA;AACAA,QAAAA,WAAAA,CAAAA,MAAAA,GAH0D,IAG1DA;AACA,oCAJ0D,IAI1D;AAJF,aAKO;AACL,oCADK,KACL;AAT0B;AAjDH;;AA8D3B,UAAMsC,GAAAA,GAAMC,mCAAoBvC,WAAAA,CA9DL,EA8DfuC,CAAZ;AACA,UAAMC,GAAAA,GAAMD,mCAAoBvC,WAAAA,CA/DL,EA+DfuC,CAAZ;AACAT,IAAAA,MAAAA,CAAAA,KAAAA,GAAeW,6BAAc3wB,QAAAA,CAAAA,KAAAA,GAAiBkuB,WAAAA,CAA/ByC,EAAAA,EAA+CH,GAAAA,CAhEnC,CAgEmCA,CAA/CG,CAAfX;AACAA,IAAAA,MAAAA,CAAAA,MAAAA,GAAgBW,6BAAc3wB,QAAAA,CAAAA,MAAAA,GAAkBkuB,WAAAA,CAAhCyC,EAAAA,EAAgDD,GAAAA,CAjErC,CAiEqCA,CAAhDC,CAAhBX;AACAA,IAAAA,MAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GAAqBW,6BAAc3wB,QAAAA,CAAd2wB,KAAAA,EAA8BH,GAAAA,CAA9BG,CAA8BH,CAA9BG,IAlEM,IAkE3BX;AACAA,IAAAA,MAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GAAsBW,6BAAc3wB,QAAAA,CAAd2wB,MAAAA,EAA+BD,GAAAA,CAA/BC,CAA+BD,CAA/BC,IAnEK,IAmE3BX;AAEA,wCArE2B,QAqE3B;AAGA,UAAMY,SAAAA,GAAY,CAAC1C,WAAAA,CAAD,gBAEd,CAACA,WAAAA,CAAD,UAAuBA,WAAAA,CAAvB,SAFJ;AAGA,UAAM2C,aAAAA,GAAgB;AACpBC,MAAAA,aAAAA,EADoB;AAAA;AAGpB9wB,MAAAA,QAAAA,EAAU,KAHU;AAIpBV,MAAAA,sBAAAA,EAAwB,KAJJ;AAKpBsuB,MAAAA,4BAAAA,EAA8B,KALV;AAAA,KAAtB;AAOA,UAAMmC,UAAAA,GAAa,oBAlFQ,aAkFR,CAAnB;;AACAA,IAAAA,UAAAA,CAAAA,UAAAA,GAAwB,gBAAgB;AACtCG,MAAAA,UADsC;;AAEtC,UAAIriB,MAAAA,CAAJ,kBAA6B;AAC3BA,QAAAA,MAAAA,CAAAA,gBAAAA,CAD2B,IAC3BA;AADF,aAEO;AACLshB,QAAAA,IADK;AAJ+B;AAnFb,KAmF3BY;;AASAA,IAAAA,UAAAA,CAAAA,OAAAA,CAAAA,IAAAA,CACE,YAAY;AACVG,MAAAA,UADU;AAEVL,MAAAA,gBAAAA,CAAAA,OAAAA,CAFU,SAEVA;AAHJE,KAAAA,EAKE,iBAAiB;AACfG,MAAAA,UADe;AAEfL,MAAAA,gBAAAA,CAAAA,MAAAA,CAFe,KAEfA;AAnGuB,KA4F3BE;AAUA,WAtG2B,MAsG3B;AAltBc;;AAqtBhBgB,EAAAA,UAAAA,CAAAA,OAAAA,EAAoB;AAclB,QAAIC,SAAAA,GAdc,KAclB;;AACA,UAAMC,kBAAAA,GAAqB,MAAM;AAC/B,qBAAe;AACb,cAAM,0CACJ,6BAA6B,KAA7B,EADI,IADO,KACP,CAAN;AAF6B;AAff,KAelB;;AASA,UAAMpL,OAAAA,GAAU,KAxBE,OAwBlB;AACA,UAAMwK,kBAAAA,GAAqB,oBAAoB;AAAEzD,MAAAA,KAAAA,EAzB/B;AAyB6B,KAApB,CAA3B;AACA,UAAMlH,OAAAA,GAAU,+BAA+BwL,MAAAA,IAAU;AACvDD,MAAAA,kBADuD;AAEvD,YAAME,MAAAA,GAAS,0BACbtL,OAAAA,CADa,YAEbA,OAAAA,CAFa,MAGWjO,iCAL6B,sBAExC,CAAf;AAKA,aAAO,+CAA+CwZ,GAAAA,IAAO;AAC3DH,QAAAA,kBAD2D;AAE3D,mBAF2D,GAE3D;AACA,yCAH2D,kBAG3D;AAEAG,QAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,GAAkBC,OAAAA,CAAAA,KAAAA,CALyC,KAK3DD;AACAA,QAAAA,GAAAA,CAAAA,KAAAA,CAAAA,MAAAA,GAAmBC,OAAAA,CAAAA,KAAAA,CANwC,MAM3DD;AACA,8BAAsB5E,qCAPqC,QAO3D;AACA6E,QAAAA,OAAAA,CAAAA,WAAAA,CAR2D,GAQ3DA;AAfqD,OAOhD,CAAP;AAjCgB,KA0BF,CAAhB;AAmBA,WAAO;AAAA;;AAELvB,MAAAA,gBAAAA,CAAAA,IAAAA,EAAuB;AACrBX,QAAAA,IADqB;AAFlB;;AAKL3uB,MAAAA,MAAAA,GAAS;AACPwwB,QAAAA,SAAAA,GADO,IACPA;AANG;;AAAA,KAAP;AAlwBc;;AAgxBhBM,EAAAA,YAAAA,CAAAA,KAAAA,EAAoB;AAClB,qBAAiB,oCADC,IAClB;;AAEA,QAAI,mBAAJ,MAA6B;AAC3B,+CAAyC,KADd,SAC3B;AADF,WAEO;AACL,+BADK,iBACL;AANgB;AAhxBJ;;AAAA;;;;;;;;;;;;;;;ACtElB;;AAiBA,MAAMC,eAAAA,GAjBN,KAiBA;AAEA,MAAM/E,eAAAA,GAAkB;AACtBgF,EAAAA,OAAAA,EADsB;AAEtBC,EAAAA,OAAAA,EAFsB;AAGtBC,EAAAA,MAAAA,EAHsB;AAItBC,EAAAA,QAAAA,EAJsB;AAAA,CAAxB;;;AAUA,wBAAwB;AACtBxyB,EAAAA,WAAAA,GAAc;AACZ,qBADY,IACZ;AACA,8BAFY,IAEZ;AACA,kBAHY,IAGZ;AACA,+BAJY,IAIZ;AACA,uBALY,IAKZ;AACA,oBANY,KAMZ;AACA,kCAPY,KAOZ;AARoB;;AActBsG,EAAAA,SAAAA,CAAAA,SAAAA,EAAqB;AACnB,qBADmB,SACnB;AAfoB;;AAqBtBmsB,EAAAA,kBAAAA,CAAAA,kBAAAA,EAAuC;AACrC,8BADqC,kBACrC;AAtBoB;;AA6BtBC,EAAAA,iBAAAA,CAAAA,IAAAA,EAAwB;AACtB,WAAO,6BAA6B/iB,IAAAA,CADd,WACtB;AA9BoB;;AAoCtBgjB,EAAAA,SAAAA,GAAY;AACV,WAAO,CAAC,CAAC,KADC,SACV;AArCoB;;AA2CtBC,EAAAA,qBAAAA,CAAAA,qBAAAA,EAA6C;AAC3C,QAAI,KAAJ,aAAsB;AACpBxgB,MAAAA,YAAAA,CAAa,KADO,WACpBA,CAAAA;AACA,yBAFoB,IAEpB;AAHyC;;AAO3C,QAAI,8BAAJ,qBAAI,CAAJ,EAA0D;AAAA;AAPf;;AAW3C,QAAI,2BAA2B,KAA/B,wBAA4D;AAC1D,UAAI,wBAAJ,cAAI,EAAJ,EAA8C;AAAA;AADY;AAXjB;;AAiB3C,QAAI,KAAJ,UAAmB;AAAA;AAjBwB;;AAsB3C,QAAI,KAAJ,QAAiB;AACf,yBAAmBM,UAAAA,CAAW,iBAAXA,IAAW,CAAXA,EADJ,eACIA,CAAnB;AAvByC;AA3CvB;;AA2EtBmgB,EAAAA,kBAAAA,CAAAA,OAAAA,EAAAA,KAAAA,EAAAA,YAAAA,EAAiD;AAU/C,UAAMC,YAAAA,GAAe/iB,OAAAA,CAV0B,KAU/C;AAEA,UAAMgjB,UAAAA,GAAaD,YAAAA,CAZ4B,MAY/C;;AACA,QAAIC,UAAAA,KAAJ,GAAsB;AACpB,aADoB,IACpB;AAd6C;;AAgB/C,SAAK,IAAIzpB,CAAAA,GAAT,GAAgBA,CAAAA,GAAhB,YAAgC,EAAhC,GAAqC;AACnC,YAAMqG,IAAAA,GAAOmjB,YAAAA,CAAAA,CAAAA,CAAAA,CADsB,IACnC;;AACA,UAAI,CAAC,oBAAL,IAAK,CAAL,EAAgC;AAC9B,eAD8B,IAC9B;AAHiC;AAhBU;;AAwB/C,sBAAkB;AAChB,YAAME,aAAAA,GAAgBjjB,OAAAA,CAAAA,IAAAA,CADN,EAChB;;AAEA,UAAIb,KAAAA,CAAAA,aAAAA,CAAAA,IAAwB,CAAC,oBAAoBA,KAAAA,CAAjD,aAAiDA,CAApB,CAA7B,EAAwE;AACtE,eAAOA,KAAAA,CAD+D,aAC/DA,CAAP;AAJc;AAAlB,WAMO;AACL,YAAM+jB,iBAAAA,GAAoBljB,OAAAA,CAAAA,KAAAA,CAAAA,EAAAA,GADrB,CACL;;AACA,UACEb,KAAAA,CAAAA,iBAAAA,CAAAA,IACA,CAAC,oBAAoBA,KAAAA,CAFvB,iBAEuBA,CAApB,CAFH,EAGE;AACA,eAAOA,KAAAA,CADP,iBACOA,CAAP;AANG;AA9BwC;;AAwC/C,WAxC+C,IAwC/C;AAnHoB;;AA0HtBgkB,EAAAA,cAAAA,CAAAA,IAAAA,EAAqB;AACnB,WAAOvjB,IAAAA,CAAAA,cAAAA,KAAwB0d,eAAAA,CADZ,QACnB;AA3HoB;;AAqItB8F,EAAAA,UAAAA,CAAAA,IAAAA,EAAiB;AACf,YAAQxjB,IAAAA,CAAR;AACE,WAAK0d,eAAAA,CAAL;AACE,eAFJ,KAEI;;AACF,WAAKA,eAAAA,CAAL;AACE,mCAA2B1d,IAAAA,CAD7B,WACE;AACAA,QAAAA,IAAAA,CAFF,MAEEA;AALJ;;AAOE,WAAK0d,eAAAA,CAAL;AACE,mCAA2B1d,IAAAA,CAD7B,WACE;AARJ;;AAUE,WAAK0d,eAAAA,CAAL;AACE,mCAA2B1d,IAAAA,CAD7B,WACE;AACAA,QAAAA,IAAAA,CAAAA,IAAAA,GAAAA,OAAAA,CAEW,MAAM;AACb,eADa,qBACb;AAHJA,SAAAA,EAAAA,KAAAA,CAKSqX,MAAAA,IAAU;AACf,cAAIA,MAAAA,YAAJ,uCAAmD;AAAA;AADpC;;AAIfhgB,UAAAA,OAAAA,CAAAA,KAAAA,CAAc,sBAJC,GAIfA;AAXN,SAEE2I;AAZJ;AAAA;;AAyBA,WA1Be,IA0Bf;AA/JoB;;AAAA;;;;;;;;;;;;;;;ACdxB;;AAfA;;AAAA;;AA+BA,0BAA0B;AAIxB3P,EAAAA,WAAAA,CAAY;AAAA;AAEVozB,IAAAA,gBAAAA,GAFU;AAGVC,IAAAA,gBAAAA,GAHU;AAIVC,IAAAA,mBAAAA,GAJFtzB;AAAY,GAAZA,EAKG;AACD,wBADC,IACD;AACA,sBAFC,IAED;AACA,4BAHC,IAGD;AACA,8BAJC,IAID;AAEA,sBANC,IAMD;AACA,uBAAmBqG,MAAAA,CAAAA,MAAAA,CAPlB,IAOkBA,CAAnB;AACA,kBARC,KAQD;AAEA,qBAVC,QAUD;AACA,6BAXC,gBAWD;AACA,6BAZC,gBAYD;AACA,gCAbC,mBAaD;;AAIA,QAGE,CAAC,KAHH,mBAIE;AACA7E,MAAAA,MAAAA,CAAAA,gBAAAA,CAAAA,mBAAAA,EAA6C8uB,KAAAA,IAAS;AACpD,qDAA6C;AAC3CroB,UAAAA,MAAAA,EAD2C;AAE3CsrB,UAAAA,MAAAA,EAAQjD,KAAAA,CAFmC;AAAA,SAA7C;AAFF,OACA9uB;AAtBD;AATqB;;AAwCxB8E,EAAAA,SAAAA,CAAAA,SAAAA,EAAqB;AACnB,sBADmB,SACnB;AAzCsB;;AA4CxB,QAAMH,WAAN,cAA+B;AAC7B,QAAI,KAAJ,cAAuB;AACrB,YAAM,KADe,iBACf,EAAN;AAF2B;;AAI7B,wBAJ6B,WAI7B;;AAEA,QAAI,CAAJ,aAAkB;AAAA;AANW;;AAS7B,UAAM,0CAA0C,MAAM,YAAY,CAChE6d,WAAAA,CADgE,eAChEA,EADgE,EAEhEA,WAAAA,CAFgE,sBAEhEA,EAFgE,EAGhEA,WAAAA,CAHgE,YAGhEA,EAHgE,CAAZ,CAAtD;;AAMA,QAAI,YAAY,CAAhB,YAA6B;AAE3B,YAAM,KAFqB,iBAErB,EAAN;AAF2B;AAfA;;AAoB7B,QAAIA,WAAAA,KAAgB,KAApB,cAAuC;AAAA;AApBV;;AAuB7B,QAAI;AACF,wBAAkB,KADhB,gBACgB,EAAlB;AADF,MAEE,cAAc;AACdhd,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,qCAAqC4mB,KAAAA,EAArC,OADA,IACd5mB;AAEA,YAAM,KAHQ,iBAGR,EAAN;AAHc;AAzBa;;AAgC7B,kDAA8CspB,KAAAA,IAAS;AACrD,UAAIA,KAAAA,EAAAA,MAAAA,KAAJ,QAA8B;AAAA;AADuB;;AAIrD,8BAAwBA,KAAAA,CAJ6B,MAIrD;AApC2B,KAgC7B;;AAMA,uDAAmDA,KAAAA,IAAS;AAC1D,8CAAwCA,KAAAA,CADkB,MAC1D;AAvC2B,KAsC7B;;AAIA,6CAAyC,CAAC;AAAA;AAAD;AAAC,KAAD,KAA8B;AACrE,UAAIxpB,UAAAA,KAAJ,UAA6B;AAAA;AADwC;;AAIrE,8BAJqE,QAIrE;;AACA,6BALqE,UAKrE;AA/C2B,KA0C7B;;AAOA,6CAAyC,CAAC;AAAD;AAAC,KAAD,KAAoB;AAC3D,UAAI,CAAC,0BAAL,UAAK,CAAL,EAA4C;AAAA;AADe;;AAI3D,UAAIA,UAAAA,KAAe,gBAAnB,mBAAsD;AAAA;AAJK;;AAO3D,6BAP2D,UAO3D;AAxD2B,KAiD7B;;AASA,6CAAyC,eAAe;AACtD,YAAM,wBAAwB,gBADwB,iBAChD,CAAN;AAEA,YAAM,wCAAwC;AAC5CkK,QAAAA,EAAAA,EAD4C;AAE5CrL,QAAAA,IAAAA,EAF4C;AAAA,OAAxC,CAAN;AAKA,6BARsD,OAQtD;AAlE2B,KA0D7B;;AAWA,qCAAiC2qB,KAAAA,IAAS;AACxC,gCADwC,IACxC;AAtE2B,KAqE7B;;AAGA,mCAA+BA,KAAAA,IAAS;AACtC,gCADsC,KACtC;AAzE2B,KAwE7B;;AAIA,eAAW,OAAX,QAAW,CAAX,IAA+B,KAA/B,iBAAqD;AACnD,+BADmD,QACnD;AA7E2B;;AA+E7B,eAAW,OAAX,QAAW,CAAX,IAA+B,KAA/B,YAAgD;AAC9C9uB,MAAAA,MAAAA,CAAAA,gBAAAA,CAAAA,IAAAA,EAD8C,QAC9CA;AAhF2B;;AAmF7B,QAAI;AACF,YAAMgyB,aAAAA,GAAgB,MAAM,KAD1B,iBAC0B,EAA5B;;AACA,UAAIxP,WAAAA,KAAgB,KAApB,cAAuC;AAAA;AAFrC;;AAMF,YAAM,8BAA8B;AAAA;AAAA;AAGlCyP,QAAAA,OAAAA,EAAS;AACPpa,UAAAA,QAAAA,EAAUD,SAAAA,CADH;AAEPsa,UAAAA,QAAAA,EAAUta,SAAAA,CAFH;AAAA,SAHyB;AAOlCua,QAAAA,OAAAA,EAAS,EACP,GADO;AAEPC,UAAAA,OAAAA,EAFO;AAAA;AAPyB,OAA9B,CAAN;;AAaA,gDAA0C;AAAE3rB,QAAAA,MAAAA,EAnB1C;AAmBwC,OAA1C;AAnBF,MAoBE,cAAc;AACdjB,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,qCAAqC4mB,KAAAA,EAArC,OADA,IACd5mB;AAEA,YAAM,KAHQ,iBAGR,EAAN;AAHc;AAvGa;;AA8G7B,UAAM,wCAAwC;AAC5CgK,MAAAA,EAAAA,EAD4C;AAE5CrL,MAAAA,IAAAA,EAF4C;AAAA,KAAxC,CAAN;AAIA,UAAM,uBACJ,gBADI,mBAlHuB,IAkHvB,CAAN;AAMA6gB,IAAAA,OAAAA,CAAAA,OAAAA,GAAAA,IAAAA,CAAuB,MAAM;AAC3B,UAAIxC,WAAAA,KAAgB,KAApB,cAAuC;AACrC,sBADqC,IACrC;AAFyB;AAxHA,KAwH7BwC;AApKsB;;AA2KxB,QAAMqN,gBAAN,SAA+B;AAC7B,WAAO,wCAAwC;AAC7C7iB,MAAAA,EAAAA,EAD6C;AAE7CrL,MAAAA,IAAAA,EAF6C;AAAA,KAAxC,CAAP;AA5KsB;;AAkLxB,QAAMmuB,eAAN,SAA8B;AAC5B,WAAO,wCAAwC;AAC7C9iB,MAAAA,EAAAA,EAD6C;AAE7CrL,MAAAA,IAAAA,EAF6C;AAAA,KAAxC,CAAP;AAnLsB;;AAyLxB,QAAMouB,iBAAN,SAAgC;AAC9B,WAAO,wCAAwC;AAC7C/iB,MAAAA,EAAAA,EAD6C;AAE7CrL,MAAAA,IAAAA,EAF6C;AAAA,KAAxC,CAAP;AA1LsB;;AAgMxB,QAAMquB,gBAAN,SAA+B;AAC7B,WAAO,wCAAwC;AAC7ChjB,MAAAA,EAAAA,EAD6C;AAE7CrL,MAAAA,IAAAA,EAF6C;AAAA,KAAxC,CAAP;AAjMsB;;AAuMxB,MAAIpF,UAAJ,GAAiB;AACf,WAAO,KADQ,WACf;AAxMsB;;AA2MxB,MAAI0zB,cAAJ,GAAqB;AACnB,WAAO,oCADY,IACnB;AA5MsB;;AA+MxB,MAAItR,KAAJ,GAAY;AACV,WAAO,KADG,MACV;AAhNsB;;AAsNxB,MAAIuR,eAAJ,GAAsB;AACpB,WAAOC,sBAAAA,IAAAA,EAAAA,iBAAAA,EAAgC,IADnB,GACmB,EAAhCA,CAAP;AAvNsB;;AA6NxB,MAAIC,UAAJ,GAAiB;AACf,WAAOD,sBAAAA,IAAAA,EAAAA,YAAAA,EAA2B,IADnB,GACmB,EAA3BA,CAAP;AA9NsB;;AAoOxB,MAAIE,gBAAJ,GAAuB;AACrB,WAAOF,sBAAAA,IAAAA,EAAAA,kBAAAA,EAAiC,IADnB,GACmB,EAAjCA,CAAP;AArOsB;;AA2OxB,MAAIG,aAAJ,GAAoB;AAClB,WAAOH,sBAAAA,IAAAA,EAAAA,eAAAA,EAA8B,IADnB,GACmB,EAA9BA,CAAP;AA5OsB;;AAkPxB,QAAMI,kBAAN,SAAiC;AAE/B,UAAMC,oBAAAA,GACJ,wCACA,gBAJ6B,0BAE/B;AAIA,UAAM;AAAA;AAAA;AAAA;AAAA;AAAA,QANyB,MAM/B;;AACA,QAAI,CAAJ,IAAS;AACP;AACE;AACExtB,UAAAA,OAAAA,CADF,KACEA;AAFJ;;AAIE;AACEA,UAAAA,OAAAA,CAAAA,KAAAA,CADF,KACEA;AALJ;;AAOE;AACE,uCAA6BytB,yCAD/B,KAC+BA,CAA7B;AARJ;;AAUE;AACE,8CAAoClnB,KAAAA,GADtC,CACE;AAXJ;;AAaE;AACE,gBAAM,gBADR,YACE;;AACA,2CAAiC;AAAEtF,YAAAA,MAAAA,EAFrC;AAEmC,WAAjC;;AAfJ;;AAiBE;AACEjB,UAAAA,OAAAA,CAAAA,GAAAA,CADF,KACEA;AAlBJ;;AAoBE;AACE,oCAA0B;AAAA;AAD5B;;AAIE,8CAJF,KAIE;AAxBJ;AAAA;;AADO;AAPsB;;AAsC/B,8BAA0B;AACxB,UAAIusB,MAAAA,CAAJ,OAAkB;AAAA;AADM;AAtCK;;AA2C/B,WAAOA,MAAAA,CA3CwB,EA2C/B;AACA,WAAOA,MAAAA,CA5CwB,QA4C/B;AAEA,UAAMmB,GAAAA,GAAM,WAAW,KAAK,GAAL,SAAX,GAA+B,CA9CZ,EA8CY,CAA3C;;AACA,iCAA6B;AAC3B,YAAMroB,OAAAA,GAAUjL,QAAAA,CAAAA,cAAAA,CADW,SACXA,CAAhB;;AACA,mBAAa;AACXiL,QAAAA,OAAAA,CAAAA,aAAAA,CAAsB,qCAAqC;AADhD;AACgD,SAArC,CAAtBA;AADF,aAEO;AAEL,iEAFK,MAEL;AANyB;AA/CE;AAlPT;;AA+SxB,QAAMsoB,iBAAN,aAAoC9K,UAAAA,GAApC,OAAwD;AACtD,UAAM7F,WAAAA,GAAc,KAApB;AAAA,UACE4Q,YAAAA,GAAe,KAFqC,aACtD;;AAGA,oBAAgB;AACd,8BADc,wCACd;AALoD;;AAOtD,QAAI,CAAC,KAAL,kBAA4B;AAAA;AAP0B;;AAUtD,UAAMC,QAAAA,GAAW,4BAA0C/tB,UAAAA,GAVL,CAUrC,CAAjB;;AAEA,QAAI+tB,QAAAA,EAAAA,cAAAA,KAA6BxH,qCAAjC,UAA2D;AACzD,gCADyD,UACzD;;AADyD;AAZL;;AAgBtD,iCAhBsD,UAgBtD;;AAEA,UAAMyH,cAAAA,GAAkB,aAAY;AAElC,YAAMlB,OAAAA,GAAU,OAAO,CAACgB,YAAAA,CAAAA,GAAAA,CAAD,UAACA,CAAD,GACnBC,QAAAA,CAAAA,OAAAA,EADmB,YACnBA,EADmB,GAFW,IAElB,CAAhB;;AAGA,UAAI7Q,WAAAA,KAAgB,KAApB,cAAuC;AAAA;AALL;;AASlC,YAAM,wCAAwC;AAC5ChT,QAAAA,EAAAA,EAD4C;AAE5CrL,QAAAA,IAAAA,EAF4C;AAAA;AAAA;AAAA,OAAxC,CAAN;AA3BoD,KAkB9B,GAAxB;;AAgBAivB,IAAAA,YAAAA,CAAAA,GAAAA,CAAAA,UAAAA,EAlCsD,cAkCtDA;AAjVsB;;AAuVxB,QAAMG,kBAAN,aAAqC;AACnC,UAAM/Q,WAAAA,GAAc,KAApB;AAAA,UACE4Q,YAAAA,GAAe,KAFkB,aACnC;;AAGA,QAAI,CAAC,KAAL,kBAA4B;AAAA;AAJO;;AAOnC,QAAI,0BAAJ,UAAI,CAAJ,EAA2C;AAAA;AAPR;;AAUnC,UAAME,cAAAA,GAAiBF,YAAAA,CAAAA,GAAAA,CAVY,UAUZA,CAAvB;;AACA,QAAI,CAAJ,gBAAqB;AAAA;AAXc;;AAcnCA,IAAAA,YAAAA,CAAAA,GAAAA,CAAAA,UAAAA,EAdmC,IAcnCA;AAGA,UAjBmC,cAiBnC;;AACA,QAAI5Q,WAAAA,KAAgB,KAApB,cAAuC;AAAA;AAlBJ;;AAsBnC,UAAM,wCAAwC;AAC5ChT,MAAAA,EAAAA,EAD4C;AAE5CrL,MAAAA,IAAAA,EAF4C;AAAA;AAAA,KAAxC,CAAN;AA7WsB;;AA0XxB,QAAMqvB,iBAAN,GAA0B;AACxB,QAAI,KAAJ,sBAA+B;AAC7B,aAAO,0BAA0B,KADJ,YACtB,CAAP;AAFsB;;AAKtB,UAAM;AAAA;AAAA,QAA0BC,mBAAAA,CALV,EAKUA,CAAhC;;AAEA,WAAO3B,mBAAAA,CAAoB,KAPL,YAOfA,CAAP;AAjYoB;;AAyYxB4B,EAAAA,gBAAAA,GAAmB;AACjB,8BADiB,wCACjB;;AAEA,QAAI,KAAJ,YAAqB;AACnB,YAAM,UADa,6CACb,CAAN;AAJe;;AAMjB,QAAI,KAAJ,mBAA4B;AAC1B,aAAO,uCAAuC;AAC5C9B,QAAAA,gBAAAA,EAAkB,KAFM;AACoB,OAAvC,CAAP;AAPe;;AAYf,UAAM;AAAA;AAAA,QAAuB6B,mBAAAA,CAZd,EAYcA,CAA7B;;AAEA,WAAO,qBAAqB,KAdb,iBAcR,CAAP;AAvZoB;;AA+ZxB,QAAME,iBAAN,GAA0B;AACxB,QAAI,CAAC,KAAL,YAAsB;AACpB,0BADoB,IACpB;AAEA,+BAHoB,OAGpB;AAHoB;AADE;;AAOxB,QAAI,KAAJ,kBAA2B;AACzB,YAAM,aAAa,CACjB,sBADiB,SAEjB,YAAY9iB,OAAAA,IAAW;AAErBK,QAAAA,UAAAA,CAAAA,OAAAA,EAFqB,IAErBA,CAAAA;AAJe,OAEjB,CAFiB,CAAb,QAMGsU,MAAAA,IAAU,CAPM,CACnB,CAAN;AASA,8BAVyB,IAUzB;AAjBsB;;AAmBxB,wBAnBwB,IAmBxB;;AAEA,QAAI;AACF,YAAM,gBADJ,cACI,EAAN;AADF,MAEE,WAAW,CAvBW;;AAyBxB,eAAW,OAAX,QAAW,CAAX,IAA+B,KAA/B,iBAAqD;AACnD,gCADmD,QACnD;AA1BsB;;AA4BxB,yBA5BwB,KA4BxB;;AAEA,eAAW,OAAX,QAAW,CAAX,IAA+B,KAA/B,YAAgD;AAC9CxlB,MAAAA,MAAAA,CAAAA,mBAAAA,CAAAA,IAAAA,EAD8C,QAC9CA;AA/BsB;;AAiCxB,oBAjCwB,KAiCxB;;AAEA,0BAnCwB,KAmCxB;;AACA,uBApCwB,KAoCxB;;AAEA,sBAtCwB,IAsCxB;AACA,WAAO,iBAvCiB,MAuCxB;AACA,kBAxCwB,KAwCxB;AAEA,6BA1CwB,OA0CxB;AAzcsB;;AAAA;;;;;;;;;;;;;;;;AChB1B;;AAEA,gDAAgD;AAC9C,QAAM6W,GAAAA,GAAN;AAAA,QACEjS,OAAAA,GAAUiS,GAAAA,CAAAA,KAAAA,CAAAA,GAAAA,EAFkC,CAElCA,CADZ;AAGA,MAAI;AAAA;AAAA;AAAA;AAAA;AAAA,MACF,MAAM2L,WAAAA,CALsC,WAKtCA,EADR;;AAGA,MAAI,CAAJ,eAAoB;AAClB,UAAM;AAAA;AAAA,QAAa,MAAMA,WAAAA,CADP,eACOA,EAAzB;AACAoR,IAAAA,aAAAA,GAFkB,MAElBA;AAT4C;;AAY9C,SAAO,EACL,GADK;AAELpX,IAAAA,OAAAA,EAFK;AAGLqX,IAAAA,QAAAA,EAHK;AAILC,IAAAA,QAAAA,EAAUC,0BAAAA,IAA8BC,qCAJnC,GAImCA,CAJnC;AAKLC,IAAAA,QAAAA,EAAUA,QAAAA,EALL,MAKKA,EALL;AAMLC,IAAAA,OAAAA,EAASD,QAAAA,EAAAA,GAAAA,CANJ,YAMIA,CANJ;AAOLnO,IAAAA,QAAAA,EAAUtD,WAAAA,CAPL;AAQLlL,IAAAA,GAAAA,EARK;AAAA,GAAP;AA7BF;;AAyCA,uBAAuB;AACrB9Y,EAAAA,WAAAA,CAAAA,gBAAAA,EAA8B;AAC5B,kBAAc,uDAGP,MAAM;AACX,aAAOwB,MAAAA,CAAAA,YAAAA,CADI,cACJA,EAAP;AAL0B,KACd,CAAd;AAFmB;;AAUrB,QAAMm0B,aAAN,OAA0B;AACxB,UAAMC,OAAAA,GAAU,MAAM,KADE,MACxB;AACAA,IAAAA,OAAAA,CAAAA,MAAAA,CAFwB,IAExBA;AAZmB;;AAerB,QAAMC,sBAAN,QAAoC;AAClC,UAAMD,OAAAA,GAAU,MAAM,KADY,MAClC;AACAA,IAAAA,OAAAA,CAAAA,aAAAA,CAFkC,KAElCA;AAjBmB;;AAoBrB,QAAME,cAAN,GAAuB;AACrB,UAAMF,OAAAA,GAAU,MAAM,KADD,MACrB;AACAA,IAAAA,OAAAA,CAFqB,WAErBA;AAtBmB;;AAAA;;;;;;;;;;;;;;;ACzCvB;;AAAA;;AAkBA,0DAA6C;AAC3C51B,EAAAA,WAAAA,CAAAA,OAAAA,EAAqB;AACnB,UADmB,OACnB;;AAEA,mCAA+BsR,GAAAA,IAAO;AAGpC,WAHoC,sBAGpC;AANiB,KAGnB;AAJyC;;AAW3C,MAAIykB,cAAJ,GAAqB;AAKnB,WAAO5B,sBAAAA,IAAAA,EAAAA,gBAAAA,EAA+B,KALnB,aAKZA,CAAP;AAhByC;;AAmB3C,MAAI6B,qBAAJ,GAA4B;AAC1B,WAD0B,CAC1B;AApByC;;AAuB3CC,EAAAA,UAAAA,GAAa;AACX,UADW,UACX;;AACA,+BAFW,CAEX;AACA,yBAAqB70B,QAAAA,CAHV,sBAGUA,EAArB;AACA,6BAJW,IAIX;AA3ByC;;AA8B3C80B,EAAAA,sBAAAA,GAAyB;AACvB,UAAMrB,QAAAA,GAAW,YAAY,0BADN,CACN,CAAjB;AACA,UAAMsB,gBAAAA,GAAmB,YAAY,2BAFd,CAEE,CAAzB;AAEA,UAAMC,WAAAA,GAAc,YAJG,UAIvB;;AACA,YAAQA,WAAAA,CAAR;AACE;AACE,gCAAwBvB,QAAAA,CAD1B,GACE;AAFJ;;AAIE;AACE,YAAIuB,WAAAA,CAAAA,CAAAA,CAAAA,KAAmBD,gBAAAA,CAAvB,KAA6C;AAC3C,gBAAM,UADqC,6DACrC,CAAN;AAFJ;;AAME,YAAItB,QAAAA,KAAJ,kBAAmC;AAAA;AANrC;;AAUE,uCAA+BsB,gBAAAA,CAVjC,GAUE;;AACA,gCAAwBtB,QAAAA,CAX1B,GAWE;AAEA,mCAbF,CAaE;AAjBJ;;AAmBE;AACE,cAAM,UApBV,oEAoBU,CAAN;AApBJ;;AAwBA,+BAA2B,KA7BJ,kBA6BvB;AA3DyC;;AA8D3CwB,EAAAA,aAAAA,GAAgB;AACd,QAAI,KAAJ,mBAA4B;AAC1B,WAD0B,iBAC1B;AAFY;;AAId,UAJc,aAId;AAlEyC;;AAqE3CC,EAAAA,eAAAA,CAAgB;AAAA;AAAWC,IAAAA,QAAAA,GAAX;AAA4BzvB,IAAAA,UAAAA,GAA5CwvB;AAAgB,GAAhBA,EAAiE;AAC/D,oBAAgB;AAEd,iCAFc,UAEd;AAH6D;;AAK/D,UAAME,YAAAA,GAAe,2BAA2B,KALe,mBAK/D;;AAEA,SAP+D,sBAO/D;;AAGA,SAV+D,MAU/D;;AAEA,0BAAsB;AAAA;AAAA;AAAA;AAAA,KAAtB;;AAIA,6BAAyB,MAAM;AAC7B,yBAD6B,YAC7B;AACA,+BAF6B,IAE7B;AAlB6D,KAgB/D;AArFyC;;AA2F3CC,EAAAA,gBAAAA,GAAmB;AACjB,WAAO,KADU,sBACV,EAAP;AA5FyC;;AA+F3CC,EAAAA,aAAAA,CAAAA,YAAAA,EAA4B,CA/Fe;;AAiG3C,MAAIC,uBAAJ,GAA8B;AAE5B,WAAOxC,sBAAAA,IAAAA,EAAAA,yBAAAA,EAFqB,KAErBA,CAAP;AAnGyC;;AAsG3CyC,EAAAA,iBAAAA,GAAoB,CAtGuB;;AAwG3CC,EAAAA,iBAAAA,GAAoB,CAxGuB;;AA0G3CC,EAAAA,eAAAA,GAAkB;AAChB,WADgB,CAChB;AA3GyC;;AAAA;;;;;;;;;;;;;;;ACH7C;;AACA;;AAsBA;;AAtCA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA+CA,MAAMC,kBAAAA,GA/CN,EA+CA;;AAoCA,iCAAiC;AAC/B,QAAMrX,IAAAA,GADyB,EAC/B;;AACA,cAAY,gBAAgB;AAC1B,UAAMpW,CAAAA,GAAIoW,IAAAA,CAAAA,OAAAA,CADgB,IAChBA,CAAV;;AACA,QAAIpW,CAAAA,IAAJ,GAAY;AACVoW,MAAAA,IAAAA,CAAAA,MAAAA,CAAAA,CAAAA,EADU,CACVA;AAHwB;;AAK1BA,IAAAA,IAAAA,CAAAA,IAAAA,CAL0B,IAK1BA;;AACA,QAAIA,IAAAA,CAAAA,MAAAA,GAAJ,MAAwB;AACtBA,MAAAA,IAAAA,CAAAA,KAAAA,GADsB,OACtBA;AAPwB;AAFG,GAE/B;;AAiBA,gBAAc,gCAAgC;AAC5C5N,IAAAA,IAAAA,GAD4C,OAC5CA;;AACA,qBAAiB;AACf,YAAMklB,aAAAA,GAAgB,IADP,GACO,EAAtB;;AACA,WAAK,IAAI1tB,CAAAA,GAAJ,GAAW2tB,IAAAA,GAAOC,WAAAA,CAAvB,QAA2C5tB,CAAAA,GAA3C,MAAqD,EAArD,GAA0D;AACxD0tB,QAAAA,aAAAA,CAAAA,GAAAA,CAAkBE,WAAAA,CAAAA,CAAAA,CAAAA,CADsC,EACxDF;AAHa;;AAKfG,sCAAAA,IAAAA,EAAuB,gBAAgB;AACrC,eAAOH,aAAAA,CAAAA,GAAAA,CAAkBh2B,IAAAA,CADY,EAC9Bg2B,CAAP;AANa,OAKfG;AAP0C;;AAW5C,WAAOzX,IAAAA,CAAAA,MAAAA,GAAP,MAA2B;AACzBA,MAAAA,IAAAA,CAAAA,KAAAA,GADyB,OACzBA;AAZ0C;AAnBf,GAmB/B;;AAgBA,aAAW,gBAAgB;AACzB,WAAOA,IAAAA,CAAAA,QAAAA,CADkB,IAClBA,CAAP;AApC6B,GAmC/B;AAtHF;;AA2HA,yCAAyC;AACvC,MAAI0X,QAAAA,KAAJ,UAA2B;AACzB,WADyB,IACzB;AAFqC;;AAIvC,MAAIrpB,IAAAA,CAAAA,GAAAA,CAASqpB,QAAAA,GAATrpB,QAAAA,IAAJ,OAA2C;AAGzC,WAHyC,IAGzC;AAPqC;;AASvC,SATuC,KASvC;AApIF;;AA2IA,iBAAiB;AAIf/N,EAAAA,WAAAA,CAAAA,OAAAA,EAAqB;AACnB,QAAI,qBAAJ,YAAqC;AACnC,YAAM,UAD6B,+BAC7B,CAAN;AAFiB;;AAInB,UAAMq3B,aAAAA,GAJa,UAInB;;AAEA,QAAIC,sBAAJ,eAA+B;AAC7B,YAAM,UACJ,0FAF2B,IACvB,CAAN;AAPiB;;AAWnB,qBAAiBzkB,OAAAA,CAXE,SAWnB;AACA,kBAAcA,OAAAA,CAAAA,MAAAA,IAAkBA,OAAAA,CAAAA,SAAAA,CAZb,iBAYnB;;AAME,QACE,EACE,mDACA,uCAHJ,KACE,CADF,EAKE;AACA,YAAM,UADN,6CACM,CAAN;AAxBe;;AA2BjB,QACE,+BACArG,gBAAAA,CAAiB,KAAjBA,SAAAA,CAAAA,CAAAA,QAAAA,KAFF,YAGE;AACA,YAAM,UADN,gDACM,CAAN;AA/Be;;AAkCnB,oBAAgBqG,OAAAA,CAlCG,QAkCnB;AACA,uBAAmBA,OAAAA,CAAAA,WAAAA,IAAuB,IAnCvB,mCAmCuB,EAA1C;AACA,2BAAuBA,OAAAA,CAAAA,eAAAA,IApCJ,IAoCnB;AACA,0BAAsBA,OAAAA,CAAAA,cAAAA,IArCH,IAqCnB;AACA,6BAAyBA,OAAAA,CAAAA,gBAAAA,IAtCN,IAsCnB;AACA,6BAAyBA,OAAAA,CAAAA,iBAAAA,IAvCN,KAuCnB;AACA,yBAAqB5L,MAAAA,CAAAA,SAAAA,CAAiB4L,OAAAA,CAAjB5L,aAAAA,IACjB4L,OAAAA,CADiB5L,aAAAA,GAEjB+D,wBA1Ce,MAwCnB;AAGA,8BAA0B6H,OAAAA,CAAAA,kBAAAA,IA3CP,EA2CnB;AACA,kCAA8BA,OAAAA,CAAAA,sBAAAA,KA5CX,KA4CnB;AACA,iCAA6BA,OAAAA,CAAAA,qBAAAA,IA7CV,KA6CnB;AACA,oBAAgBA,OAAAA,CAAAA,QAAAA,IAAoBhI,uBA9CjB,MA8CnB;AACA,0BAAsBgI,OAAAA,CAAAA,cAAAA,IA/CH,KA+CnB;AACA,2BAAuBA,OAAAA,CAhDJ,eAgDnB;AACA,gBAAYA,OAAAA,CAAAA,IAAAA,IAjDO,oBAiDnB;AAEA,iCAA6B,CAACA,OAAAA,CAnDX,cAmDnB;;AACA,QAAI,KAAJ,uBAAgC;AAE9B,4BAAsB,IAFQ,sCAER,EAAtB;AACA,oCAH8B,IAG9B;AAHF,WAIO;AACL,4BAAsBA,OAAAA,CADjB,cACL;AAzDiB;;AA2DnB,gBAAYzR,QAAAA,CA3DO,eA2DnB;AAEA,kBAAcm2B,2BAAY,KAAZA,SAAAA,EAA4B,wBA7DvB,IA6DuB,CAA5BA,CAAd;AACA,iCAA6BrtB,gCA9DV,OA8DnB;AACA,yBAAqB,oBA/DF,IA+DnB;;AACA,SAhEmB,UAgEnB;;AAEA,QAAI,KAAJ,mBAA4B;AAC1B,gCAD0B,mBAC1B;AAnEiB;;AAuEnBsc,IAAAA,OAAAA,CAAAA,OAAAA,GAAAA,IAAAA,CAAuB,MAAM;AAC3B,+CAAyC;AAAEve,QAAAA,MAAAA,EADhB;AACc,OAAzC;AAxEiB,KAuEnBue;AA3Ea;;AAgFf,MAAIhgB,UAAJ,GAAiB;AACf,WAAO,YADQ,MACf;AAjFa;;AAoFfgxB,EAAAA,WAAAA,CAAAA,KAAAA,EAAmB;AACjB,WAAO,YADU,KACV,CAAP;AArFa;;AA2Ff,MAAIC,cAAJ,GAAqB;AACnB,QAAI,CAAC,sBAAL,SAAoC;AAClC,aADkC,KAClC;AAFiB;;AAMnB,WAAO,kBAAkB,oBAAoB;AAC3C,aAAO5C,QAAAA,EADoC,OAC3C;AAPiB,KAMZ,CAAP;AAjGa;;AAyGf,MAAIx0B,eAAJ,GAAsB;AACpB,WAAO,CAAC,CAAC,KADW,iBACpB;AA1Ga;;AAgHf,MAAIq3B,iBAAJ,GAAwB;AACtB,WAAO,KADe,kBACtB;AAjHa;;AAuHf,MAAIA,iBAAJ,MAA2B;AACzB,QAAI,CAACzwB,MAAAA,CAAAA,SAAAA,CAAL,GAAKA,CAAL,EAA4B;AAC1B,YAAM,UADoB,sBACpB,CAAN;AAFuB;;AAIzB,QAAI,CAAC,KAAL,aAAuB;AAAA;AAJE;;AAQzB,QAAI,CAAC,gCAAL,IAAK,CAAL,EAAyE;AACvED,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,0BADyD,wBACvEA;AATuB;AAvHZ;;AAwIf2wB,EAAAA,qBAAAA,CAAAA,GAAAA,EAA2BC,oBAAAA,GAA3BD,KAAAA,EAAyD;AACvD,QAAI,4BAAJ,KAAqC;AACnC,gCAA0B;AACxB,aADwB,qBACxB;AAFiC;;AAInC,aAJmC,IAInC;AALqD;;AAQvD,QAAI,EAAE,WAAWrwB,GAAAA,IAAO,KAAxB,UAAI,CAAJ,EAA0C;AACxC,aADwC,KACxC;AATqD;;AAWvD,UAAM8f,QAAAA,GAAW,KAXsC,kBAWvD;AACA,8BAZuD,GAYvD;AAEA,2CAAuC;AACrCnf,MAAAA,MAAAA,EADqC;AAErCnB,MAAAA,UAAAA,EAFqC;AAGrC+wB,MAAAA,SAAAA,EAAW,mBAAmBvwB,GAAAA,GAAnB,MAH0B;AAAA;AAAA,KAAvC;;AAOA,8BAA0B;AACxB,WADwB,qBACxB;AAtBqD;;AAwBvD,WAxBuD,IAwBvD;AAhKa;;AAuKf,MAAIwwB,gBAAJ,GAAuB;AACrB,WAAO,mBAAmB,0BAAnB,MADc,IACrB;AAxKa;;AA8Kf,MAAIA,gBAAJ,MAA0B;AACxB,QAAI,CAAC,KAAL,aAAuB;AAAA;AADC;;AAIxB,QAAI92B,IAAAA,GAAOsG,GAAAA,GAJa,CAIxB;;AACA,QAAI,KAAJ,aAAsB;AACpB,YAAMgC,CAAAA,GAAI,yBADU,GACV,CAAV;;AACA,UAAIA,CAAAA,IAAJ,GAAY;AACVtI,QAAAA,IAAAA,GAAOsI,CAAAA,GADG,CACVtI;AAHkB;AALE;;AAYxB,QAAI,CAAC,iCAAL,IAAK,CAAL,EAA0E;AACxEgG,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,yBAD0D,wBACxEA;AAbsB;AA9KX;;AAkMf,MAAI+wB,YAAJ,GAAmB;AACjB,WAAO,iDACH,KADG,gBADU,uBACjB;AAnMa;;AA2Mf,MAAIA,YAAJ,MAAsB;AACpB,QAAIpkB,KAAAA,CAAJ,GAAIA,CAAJ,EAAgB;AACd,YAAM,UADQ,wBACR,CAAN;AAFkB;;AAIpB,QAAI,CAAC,KAAL,aAAuB;AAAA;AAJH;;AAOpB,wBAPoB,KAOpB;AAlNa;;AAwNf,MAAIqkB,iBAAJ,GAAwB;AACtB,WAAO,KADe,kBACtB;AAzNa;;AA+Nf,MAAIA,iBAAJ,MAA2B;AACzB,QAAI,CAAC,KAAL,aAAuB;AAAA;AADE;;AAIzB,wBAJyB,KAIzB;AAnOa;;AAyOf,MAAIC,aAAJ,GAAoB;AAClB,WAAO,KADW,cAClB;AA1Oa;;AAgPf,MAAIA,aAAJ,WAA4B;AAC1B,QAAI,CAAC1L,+BAAL,QAAKA,CAAL,EAAgC;AAC9B,YAAM,UADwB,+BACxB,CAAN;AAFwB;;AAI1B,QAAI,CAAC,KAAL,aAAuB;AAAA;AAJG;;AAQ1B9lB,IAAAA,QAAAA,IAR0B,GAQ1BA;;AACA,QAAIA,QAAAA,GAAJ,GAAkB;AAChBA,MAAAA,QAAAA,IADgB,GAChBA;AAVwB;;AAY1B,QAAI,wBAAJ,UAAsC;AAAA;AAZZ;;AAe1B,0BAf0B,QAe1B;AAEA,UAAMK,UAAAA,GAAa,KAjBO,kBAiB1B;;AAEA,SAAK,IAAIwC,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,YAArB,QAAyCjK,CAAAA,GAAzC,IAAiDA,CAAjD,IAAsD;AACpD,YAAMurB,QAAAA,GAAW,YADmC,CACnC,CAAjB;AACAA,MAAAA,QAAAA,CAAAA,MAAAA,CAAgBA,QAAAA,CAAhBA,KAAAA,EAFoD,QAEpDA;AArBwB;;AAyB1B,QAAI,KAAJ,oBAA6B;AAC3B,qBAAe,KAAf,oBAD2B,IAC3B;AA1BwB;;AA6B1B,+CAA2C;AACzC5sB,MAAAA,MAAAA,EADyC;AAEzCgwB,MAAAA,aAAAA,EAFyC;AAAA;AAAA,KAA3C;;AAMA,QAAI,KAAJ,uBAAgC;AAC9B,WAD8B,MAC9B;AApCwB;AAhPb;;AAwRf,MAAIC,gBAAJ,GAAuB;AACrB,WAAO,mBAAmB,0BAAnB,UADc,IACrB;AAzRa;;AA4Rf,MAAIC,eAAJ,GAAsB;AACpB,WAAO,mBAAmB,gCAAnB,UADa,IACpB;AA7Ra;;AAgSf,MAAIC,YAAJ,GAAmB;AACjB,WAAO,mBAAmB,sBAAnB,UADU,IACjB;AAjSa;;AAuSf,MAAIrC,cAAJ,GAAqB;AAEnB,UAAM,UAFa,iCAEb,CAAN;AAzSa;;AA+SfsC,EAAAA,4BAAAA,GAA+B;AAS7B,QACE,CAAC,eAAD,gBACA,yCAFF,GAGE;AACA,aAAO7R,OAAAA,CADP,OACOA,EAAP;AAb2B;;AAe7B,WAAO,gCAfsB,OAe7B;AA9Ta;;AAoUfrgB,EAAAA,WAAAA,CAAAA,WAAAA,EAAyB;AACvB,QAAI,KAAJ,aAAsB;AACpB,6CAAuC;AAAE8B,QAAAA,MAAAA,EADrB;AACmB,OAAvC;;AAEA,WAHoB,gBAGpB;;AACA,WAJoB,UAIpB;;AAEA,UAAI,KAAJ,gBAAyB;AACvB,wCADuB,IACvB;AAPkB;;AASpB,UAAI,KAAJ,mBAA4B;AAC1B,2CAD0B,IAC1B;AAVkB;AADC;;AAevB,uBAfuB,WAevB;;AACA,QAAI,CAAJ,aAAkB;AAAA;AAhBK;;AAmBvB,UAAMqwB,SAAAA,GAAYtU,WAAAA,CAnBK,SAmBvB;AACA,UAAMxd,UAAAA,GAAawd,WAAAA,CApBI,QAoBvB;AACA,UAAMkU,gBAAAA,GAAmBlU,WAAAA,CAAAA,OAAAA,CArBF,CAqBEA,CAAzB;AAEA,UAAMyK,4BAAAA,GAA+BzK,WAAAA,CAvBd,wBAuBcA,EAArC;;AAEA,uCAAmC,MAAM;AACvC,4CAAsC;AACpC/b,QAAAA,MAAAA,EADoC;AAAA;AAAA,OAAtC;AA1BqB,KAyBvB;;AAOA,yBAAqBqJ,GAAAA,IAAO;AAC1B,YAAMujB,QAAAA,GAAW,YAAYvjB,GAAAA,CAAAA,UAAAA,GADH,CACT,CAAjB;;AACA,UAAI,CAAJ,UAAe;AAAA;AAFW;;AAO1B,wBAP0B,QAO1B;AAvCqB,KAgCvB;;AASA,oCAAgC,KAzCT,aAyCvB;;AAEA,wBAAoBA,GAAAA,IAAO;AACzB,UAAIA,GAAAA,CAAAA,YAAAA,IAAoB,gCAAxB,SAAiE;AAAA;AADxC;;AAIzB,sCAJyB,OAIzB;;AAEA,yCAAmC,KANV,YAMzB;;AACA,0BAPyB,IAOzB;AAlDqB,KA2CvB;;AASA,sCAAkC,KApDX,YAoDvB;;AAIA4mB,IAAAA,gBAAAA,CAAAA,IAAAA,CACQK,YAAAA,IAAgB;AACpB,wCADoB,YACpB;;AACA,2CAFoB,4BAEpB;AAEA,YAAM9K,KAAAA,GAAQ,KAJM,YAIpB;AACA,YAAM5sB,QAAAA,GAAW03B,YAAAA,CAAAA,WAAAA,CAAyB;AAAE9K,QAAAA,KAAAA,EAAOA,KAAAA,GAL/B;AAKsB,OAAzB8K,CAAjB;AACA,YAAMC,gBAAAA,GACJ,uBAAuBxtB,wBAAvB,iBAPkB,IAMpB;AAEA,YAAMytB,eAAAA,GAAkBH,SAAAA,GAAAA,IAAAA,GARJ,IAQpB;;AAEA,WAAK,IAAII,OAAAA,GAAT,GAAsBA,OAAAA,IAAtB,YAA6C,EAA7C,SAAwD;AACtD,cAAM7D,QAAAA,GAAW,+BAAgB;AAC/B/gB,UAAAA,SAAAA,EAAW,KADoB;AAE/B8P,UAAAA,QAAAA,EAAU,KAFqB;AAG/B5S,UAAAA,EAAAA,EAH+B;AAAA;AAK/Boc,UAAAA,eAAAA,EAAiBvsB,QAAAA,CALc,KAKdA,EALc;AAAA;AAO/B83B,UAAAA,cAAAA,EAAgB,KAPe;AAAA;AAS/B1d,UAAAA,aAAAA,EAAe,KATgB;AAU/B2d,UAAAA,sBAAAA,EAV+B;AAAA;AAY/BC,UAAAA,sBAAAA,EAZ+B;AAa/B34B,UAAAA,kBAAAA,EAAoB,KAbW;AAc/BC,UAAAA,sBAAAA,EAAwB,KAdO;AAe/B0a,UAAAA,QAAAA,EAAU,KAfqB;AAgB/BK,UAAAA,cAAAA,EAAgB,KAhBe;AAiB/BT,UAAAA,eAAAA,EAAiB,KAjBc;AAkB/Bra,UAAAA,IAAAA,EAAM,KAlByB;AAAA,SAAhB,CAAjB;;AAoBA,yBArBsD,QAqBtD;AA/BkB;;AAoCpB,YAAM04B,aAAAA,GAAgB,YApCF,CAoCE,CAAtB;;AACA,yBAAmB;AACjBA,QAAAA,aAAAA,CAAAA,UAAAA,CADiB,YACjBA;AACA,yCAAiCP,YAAAA,CAFhB,GAEjB;AAvCkB;;AAyCpB,UAAI,qBAAqB/sB,qBAAzB,MAA0C;AACxC,aADwC,iBACxC;AA1CkB;;AAgDpB,+CAAyC,MAAM;AAC7C,YAAI,KAAJ,gBAAyB;AACvB,0CADuB,WACvB;AAF2C;;AAI7C,YAAI,KAAJ,mBAA4B;AAC1B,6CAD0B,WAC1B;AAL2C;;AAU7C,YAAIwY,WAAAA,CAAAA,aAAAA,CAAAA,gBAAAA,IAA8Cxd,UAAAA,GAAlD,MAAqE;AAEnE,gCAFmE,OAEnE;;AAFmE;AAVxB;;AAe7C,YAAIuyB,YAAAA,GAAevyB,UAAAA,GAf0B,CAe7C;;AAEA,YAAIuyB,YAAAA,IAAJ,GAAuB;AACrB,gCADqB,OACrB;;AADqB;AAjBsB;;AAqB7C,aAAK,IAAIL,OAAAA,GAAT,GAAsBA,OAAAA,IAAtB,YAA6C,EAA7C,SAAwD;AACtD1U,UAAAA,WAAAA,CAAAA,OAAAA,CAAAA,OAAAA,EAAAA,IAAAA,CACE0C,OAAAA,IAAW;AACT,kBAAMmO,QAAAA,GAAW,YAAY6D,OAAAA,GADpB,CACQ,CAAjB;;AACA,gBAAI,CAAC7D,QAAAA,CAAL,SAAuB;AACrBA,cAAAA,QAAAA,CAAAA,UAAAA,CADqB,OACrBA;AAHO;;AAKT,mDAAuCnO,OAAAA,CAL9B,GAKT;;AACA,gBAAI,mBAAJ,GAA0B;AACxB,oCADwB,OACxB;AAPO;AADb1C,WAAAA,EAWEgD,MAAAA,IAAU;AACRhgB,YAAAA,OAAAA,CAAAA,KAAAA,CACE,6BADFA,uBAAAA,EADQ,MACRA;;AAIA,gBAAI,mBAAJ,GAA0B;AACxB,oCADwB,OACxB;AANM;AAZ0C,WACtDgd;AAtB2C;AAhD3B,OAgDpB;;AA8CA,0CAAoC;AAAE/b,QAAAA,MAAAA,EA9FlB;AA8FgB,OAApC;;AAEA,UAAI,KAAJ,uBAAgC;AAC9B,aAD8B,MAC9B;AAjGkB;AADxBiwB,KAAAA,EAAAA,KAAAA,CAqGSlR,MAAAA,IAAU;AACfhgB,MAAAA,OAAAA,CAAAA,KAAAA,CAAAA,6BAAAA,EADe,MACfA;AA9JmB,KAwDvBkxB;AA5Xa;;AAyefc,EAAAA,aAAAA,CAAAA,MAAAA,EAAsB;AACpB,QAAI,CAAC,KAAL,aAAuB;AAAA;AADH;;AAIpB,QAAI,CAAJ,QAAa;AACX,yBADW,IACX;AADF,WAEO,IACL,EAAE,yBAAyB,8BAA8BC,MAAAA,CADpD,MACL,CADK,EAEL;AACA,yBADA,IACA;AACAjyB,MAAAA,OAAAA,CAAAA,KAAAA,CAFA,qCAEAA;AAJK,WAKA;AACL,yBADK,MACL;AAZkB;;AAepB,SAAK,IAAIsC,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,YAArB,QAAyCjK,CAAAA,GAAzC,IAAiDA,CAAjD,IAAsD;AACpD,kCAA4B,yBADwB,IACpD;AAhBkB;AAzeP;;AA6ff2sB,EAAAA,UAAAA,GAAa;AACX,kBADW,EACX;AACA,8BAFW,CAEX;AACA,yBAHW,uBAGX;AACA,8BAJW,IAIX;AACA,uBALW,IAKX;AACA,mBAAe,sBANJ,kBAMI,CAAf;AACA,qBAPW,IAOX;AACA,0BARW,CAQX;AACA,yCATW,IASX;AACA,0BAAsB,IAVX,OAUW,EAAtB;AACA,gCAXW,wCAWX;AACA,sCAZW,wCAYX;AACA,4BAbW,wCAaX;AACA,uBAAmB7qB,qBAdR,QAcX;AACA,uBAAmBI,qBAfR,IAeX;;AAEA,QAAI,KAAJ,eAAwB;AACtB,uCAAiC,KADX,aACtB;;AACA,2BAFsB,IAEtB;AAnBS;;AAqBX,QAAI,KAAJ,cAAuB;AACrB,yCAAmC,KADd,YACrB;;AACA,0BAFqB,IAErB;AAvBS;;AA0BX,8BA1BW,EA0BX;;AAEA,SA5BW,iBA4BX;AAzhBa;;AA4hBf6qB,EAAAA,aAAAA,GAAgB;AACd,QAAI,oBAAJ,GAA2B;AAAA;AADb;;AAId,SAJc,MAId;AAhiBa;;AAmiBfC,EAAAA,eAAAA,CAAgB;AAAA;AAAWC,IAAAA,QAAAA,GAAX;AAA4BzvB,IAAAA,UAAAA,GAA5CwvB;AAAgB,GAAhBA,EAAiE;AAC/DjS,kCAAAA,OAAAA,EAD+D,QAC/DA;AApiBa;;AAuiBf6U,EAAAA,oBAAAA,CAAAA,QAAAA,EAAAA,QAAAA,EAAyCC,QAAAA,GAAzCD,KAAAA,EAA2DE,MAAAA,GAA3DF,KAAAA,EAA2E;AACzE,8BAA0BG,QAAAA,CAD+C,QAC/CA,EAA1B;;AAEA,QAAIC,WAAAA,CAAY,KAAZA,aAAAA,EAAJ,QAAIA,CAAJ,EAA+C;AAC7C,kBAAY;AACV,gDAAwC;AACtCrxB,UAAAA,MAAAA,EADsC;AAEtCwlB,UAAAA,KAAAA,EAFsC;AAGtC8L,UAAAA,WAAAA,EAHsC;AAAA,SAAxC;AAF2C;;AAAA;AAH0B;;AAazE,iDAbyE,QAazE;;AAEA,SAAK,IAAIjwB,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,YAArB,QAAyCjK,CAAAA,GAAzC,IAAiDA,CAAjD,IAAsD;AACpD,4BADoD,QACpD;AAhBuE;;AAkBzE,yBAlByE,QAkBzE;;AAEA,QAAI,CAAJ,UAAe;AACb,UAAItI,IAAAA,GAAO,KAAX;AAAA,UADa,IACb;;AAEA,UACE,kBACA,EAAE,6BAA6B,KAFjC,0BAEE,CAFF,EAGE;AACAA,QAAAA,IAAAA,GAAO,eADP,UACAA;AACAwG,QAAAA,IAAAA,GAAO,OAEL;AAAE7B,UAAAA,IAAAA,EAFG;AAEL,SAFK,EAGL,eAHK,MAIL,eAJK,UAAP6B;AARW;;AAgBb,8BAAwB;AACtBV,QAAAA,UAAAA,EADsB;AAEtBI,QAAAA,SAAAA,EAFsB;AAGtBsB,QAAAA,mBAAAA,EAHsB;AAAA,OAAxB;AApCuE;;AA2CzE,4CAAwC;AACtCP,MAAAA,MAAAA,EADsC;AAEtCwlB,MAAAA,KAAAA,EAFsC;AAGtC8L,MAAAA,WAAAA,EAAaH,MAAAA,GAAAA,QAAAA,GAHyB;AAAA,KAAxC;;AAMA,QAAI,KAAJ,uBAAgC;AAC9B,WAD8B,MAC9B;AAlDuE;AAviB5D;;AAgmBf,MAAIpD,qBAAJ,GAA4B;AAC1B,QACE,qBAAqBxqB,qBAArB,QACA,qBAAqBJ,qBADrB,cAEA,CAAC,KAHH,sBAIE;AACA,aADA,CACA;AANwB;;AAQ1B,WAR0B,CAQ1B;AAxmBa;;AA2mBfouB,EAAAA,SAAAA,CAAAA,KAAAA,EAAiBL,QAAAA,GAAjBK,KAAAA,EAAmC;AACjC,QAAI/L,KAAAA,GAAQllB,UAAAA,CADqB,KACrBA,CAAZ;;AAEA,QAAIklB,KAAAA,GAAJ,GAAe;AACb,wDADa,KACb;AADF,WAEO;AACL,YAAMgM,WAAAA,GAAc,YAAY,0BAD3B,CACe,CAApB;;AACA,UAAI,CAAJ,aAAkB;AAAA;AAFb;;AAKL,YAAMC,SAAAA,GAAY,6BAA6B,KAL1C,iBAKL;AACA,UAAIC,QAAAA,GAAWD,SAAAA,GAAAA,CAAAA,GANV,2BAML;AACA,UAAIE,QAAAA,GAAWF,SAAAA,GAAAA,CAAAA,GAPV,0BAOL;;AAEA,UAAI,cAAc,KAAlB,yBAAgD;AAC9C,+BAAuB,oBAAvB;AAVG;;AAYL,YAAMG,cAAAA,GACD,8BAAD,QAAC,IAAyCJ,WAAAA,CAA3C,KAAE,GACDA,WAAAA,CADF,KAAG,GAEH,KAfG,qBAYL;AAIA,YAAMK,eAAAA,GACF,+BAAD,QAAC,IAA0CL,WAAAA,CAA5C,MAAE,GACFA,WAAAA,CAlBG,KAgBL;;AAGA;AACE;AACEhM,UAAAA,KAAAA,GADF,CACEA;AAFJ;;AAIE;AACEA,UAAAA,KAAAA,GADF,cACEA;AALJ;;AAOE;AACEA,UAAAA,KAAAA,GADF,eACEA;AARJ;;AAUE;AACEA,UAAAA,KAAAA,GAAQ1f,IAAAA,CAAAA,GAAAA,CAAAA,cAAAA,EADV,eACUA,CAAR0f;AAXJ;;AAaE;AAGE,gBAAMsM,eAAAA,GAAkBC,qCAAAA,WAAAA,IAAAA,cAAAA,GAEpBjsB,IAAAA,CAAAA,GAAAA,CAAAA,eAAAA,EALN,cAKMA,CAFJ;AAGA0f,UAAAA,KAAAA,GAAQ1f,IAAAA,CAAAA,GAAAA,CAAAA,wBAAAA,EANV,eAMUA,CAAR0f;AAnBJ;;AAqBE;AACEzmB,UAAAA,OAAAA,CAAAA,KAAAA,CAAc,oBADhB,6BACEA;AAtBJ;AAAA;;AAyBA,wDA5CK,IA4CL;AAjD+B;AA3mBpB;;AAoqBfizB,EAAAA,qBAAAA,GAAwB;AACtB,QAAI,KAAJ,sBAA+B;AAE7B,qBAAe,KAAf,oBAF6B,IAE7B;AAHoB;;AAMtB,UAAMpF,QAAAA,GAAW,YAAY,0BANP,CAML,CAAjB;;AACA,yBAAqB;AAAEqF,MAAAA,OAAAA,EAASrF,QAAAA,CAPV;AAOD,KAArB;AA3qBa;;AAmrBfsF,EAAAA,qBAAAA,CAAAA,KAAAA,EAA6B;AAC3B,QAAI,CAAC,KAAL,aAAuB;AACrB,aADqB,IACrB;AAFyB;;AAI3B,UAAM7wB,CAAAA,GAAI,yBAJiB,KAIjB,CAAV;;AACA,QAAIA,CAAAA,GAAJ,GAAW;AACT,aADS,IACT;AANyB;;AAQ3B,WAAOA,CAAAA,GARoB,CAQ3B;AA3rBa;;AA6sBf8wB,EAAAA,kBAAAA,CAAmB;AAAA;AAEjBlzB,IAAAA,SAAAA,GAFiB;AAGjBsB,IAAAA,mBAAAA,GAHiB;AAIjBtC,IAAAA,qBAAAA,GAJFk0B;AAAmB,GAAnBA,EAKG;AACD,QAAI,CAAC,KAAL,aAAuB;AAAA;AADtB;;AAID,UAAMvF,QAAAA,GACJ5tB,MAAAA,CAAAA,SAAAA,CAAAA,UAAAA,KAAgC,YAAYH,UAAAA,GAL7C,CAKiC,CADlC;;AAEA,QAAI,CAAJ,UAAe;AACbE,MAAAA,OAAAA,CAAAA,KAAAA,CACE,kCAFW,wCACbA;AADa;AANd;;AAaD,QAAI,6BAA6B,CAAjC,WAA6C;AAC3C,6CAD2C,IAC3C;;AAD2C;AAb5C;;AAiBD,QAAImH,CAAAA,GAAJ;AAAA,QACE8C,CAAAA,GAlBD,CAiBD;AAEA,QAAInC,KAAAA,GAAJ;AAAA,QACEC,MAAAA,GADF;AAAA;AAAA,QAnBC,WAmBD;AAIA,UAAMH,iBAAAA,GAAoBimB,QAAAA,CAAAA,QAAAA,GAAAA,GAAAA,KAvBzB,CAuBD;AACA,UAAMwF,SAAAA,GACH,qBAAoBxF,QAAAA,CAApB,SAAsCA,QAAAA,CAAvC,KAAC,IACDA,QAAAA,CADA,KAAC,GAzBF,mBAwBD;AAIA,UAAMyF,UAAAA,GACH,qBAAoBzF,QAAAA,CAApB,QAAqCA,QAAAA,CAAtC,MAAC,IACDA,QAAAA,CADA,KAAC,GA7BF,mBA4BD;AAIA,QAAIpH,KAAAA,GAhCH,CAgCD;;AACA,YAAQvmB,SAAAA,CAAAA,CAAAA,CAAAA,CAAR;AACE;AACEiH,QAAAA,CAAAA,GAAIjH,SAAAA,CADN,CACMA,CAAJiH;AACA8C,QAAAA,CAAAA,GAAI/J,SAAAA,CAFN,CAEMA,CAAJ+J;AACAwc,QAAAA,KAAAA,GAAQvmB,SAAAA,CAHV,CAGUA,CAARumB;AAKAtf,QAAAA,CAAAA,GAAIA,CAAAA,KAAAA,IAAAA,GAAAA,CAAAA,GARN,CAQEA;AACA8C,QAAAA,CAAAA,GAAIA,CAAAA,KAAAA,IAAAA,GAAAA,CAAAA,GATN,UASEA;AAVJ;;AAYE,WAZF,KAYE;AACA;AACEwc,QAAAA,KAAAA,GADF,UACEA;AAdJ;;AAgBE,WAhBF,MAgBE;AACA;AACExc,QAAAA,CAAAA,GAAI/J,SAAAA,CADN,CACMA,CAAJ+J;AACAwc,QAAAA,KAAAA,GAFF,YAEEA;;AAGA,YAAIxc,CAAAA,KAAAA,IAAAA,IAAc,KAAlB,WAAkC;AAChC9C,UAAAA,CAAAA,GAAI,eAD4B,IAChCA;AACA8C,UAAAA,CAAAA,GAAI,eAF4B,GAEhCA;AAFF,eAGO,IAAI,aAAJ,UAA2B;AAGhCA,UAAAA,CAAAA,GAHgC,UAGhCA;AAXJ;;AAjBF;;AA+BE,WA/BF,MA+BE;AACA;AACE9C,QAAAA,CAAAA,GAAIjH,SAAAA,CADN,CACMA,CAAJiH;AACAW,QAAAA,KAAAA,GAFF,SAEEA;AACAC,QAAAA,MAAAA,GAHF,UAGEA;AACA0e,QAAAA,KAAAA,GAJF,aAIEA;AApCJ;;AAsCE;AACEtf,QAAAA,CAAAA,GAAIjH,SAAAA,CADN,CACMA,CAAJiH;AACA8C,QAAAA,CAAAA,GAAI/J,SAAAA,CAFN,CAEMA,CAAJ+J;AACAnC,QAAAA,KAAAA,GAAQ5H,SAAAA,CAAAA,CAAAA,CAAAA,GAHV,CAGE4H;AACAC,QAAAA,MAAAA,GAAS7H,SAAAA,CAAAA,CAAAA,CAAAA,GAJX,CAIE6H;AACA,cAAM4qB,QAAAA,GAAW,6BALnB,2BAKE;AACA,cAAMC,QAAAA,GAAW,6BANnB,0BAME;AAEAW,QAAAA,UAAAA,GACG,8BAAD,QAAC,IAAD,KAAC,GATL,mBAQEA;AAEAC,QAAAA,WAAAA,GACG,+BAAD,QAAC,IAAD,MAAC,GAXL,mBAUEA;AAEA/M,QAAAA,KAAAA,GAAQ1f,IAAAA,CAAAA,GAAAA,CAASA,IAAAA,CAAAA,GAAAA,CAATA,UAASA,CAATA,EAA+BA,IAAAA,CAAAA,GAAAA,CAZzC,WAYyCA,CAA/BA,CAAR0f;AAlDJ;;AAoDE;AACEzmB,QAAAA,OAAAA,CAAAA,KAAAA,CACE,wBAAwBE,SAAAA,CAAAA,CAAAA,CAAAA,CAAxB,IAFJ,oCACEF;AArDJ;AAAA;;AA2DA,QAAI,CAAJ,uBAA4B;AAC1B,UAAIymB,KAAAA,IAASA,KAAAA,KAAU,KAAvB,eAA2C;AACzC,iCADyC,KACzC;AADF,aAEO,IAAI,uBAAJ,yBAA0C;AAC/C,iCAD+C,6BAC/C;AAJwB;AA5F3B;;AAoGD,QAAIA,KAAAA,KAAAA,UAAAA,IAAwB,CAACvmB,SAAAA,CAA7B,CAA6BA,CAA7B,EAA2C;AACzC,2BAAqB;AACnBgzB,QAAAA,OAAAA,EAASrF,QAAAA,CADU;AAAA;AAAA,OAArB;;AADyC;AApG1C;;AA4GD,UAAM4F,YAAAA,GAAe,CACnB5F,QAAAA,CAAAA,QAAAA,CAAAA,sBAAAA,CAAAA,CAAAA,EADmB,CACnBA,CADmB,EAEnBA,QAAAA,CAAAA,QAAAA,CAAAA,sBAAAA,CAAyC1mB,CAAAA,GAAzC0mB,KAAAA,EAAoD5jB,CAAAA,GAFjC,MAEnB4jB,CAFmB,CAArB;AAIA,QAAInlB,IAAAA,GAAO3B,IAAAA,CAAAA,GAAAA,CAAS0sB,YAAAA,CAAAA,CAAAA,CAAAA,CAAT1sB,CAAS0sB,CAAT1sB,EAA6B0sB,YAAAA,CAAAA,CAAAA,CAAAA,CAhHvC,CAgHuCA,CAA7B1sB,CAAX;AACA,QAAIwB,GAAAA,GAAMxB,IAAAA,CAAAA,GAAAA,CAAS0sB,YAAAA,CAAAA,CAAAA,CAAAA,CAAT1sB,CAAS0sB,CAAT1sB,EAA6B0sB,YAAAA,CAAAA,CAAAA,CAAAA,CAjHtC,CAiHsCA,CAA7B1sB,CAAV;;AAEA,QAAI,CAAJ,qBAA0B;AAIxB2B,MAAAA,IAAAA,GAAO3B,IAAAA,CAAAA,GAAAA,CAAAA,IAAAA,EAJiB,CAIjBA,CAAP2B;AACAH,MAAAA,GAAAA,GAAMxB,IAAAA,CAAAA,GAAAA,CAAAA,GAAAA,EALkB,CAKlBA,CAANwB;AAxHD;;AA0HD,yBAAqB;AACnB2qB,MAAAA,OAAAA,EAASrF,QAAAA,CADU;AAEnB0B,MAAAA,QAAAA,EAAU;AAAA;AAAA;AAAA,OAFS;AAAA;AAAA,KAArB;AA50Ba;;AAm1BfmE,EAAAA,eAAAA,CAAAA,SAAAA,EAA2B;AACzB,UAAM3C,YAAAA,GAAe,KADI,aACzB;AACA,UAAMC,iBAAAA,GAAoB,KAFD,kBAEzB;AACA,UAAM2C,oBAAAA,GACJpyB,UAAAA,CAAAA,iBAAAA,CAAAA,KAAAA,YAAAA,GACIwF,IAAAA,CAAAA,KAAAA,CAAWgqB,YAAAA,GAAXhqB,KAAAA,IADJxF,GAAAA,GAJuB,iBAGzB;AAKA,UAAMzB,UAAAA,GAAa8zB,SAAAA,CARM,EAQzB;AACA,QAAIC,aAAAA,GAAgB,WATK,UASzB;AACAA,IAAAA,aAAAA,IAAiB,WAVQ,oBAUzBA;AACA,UAAMC,eAAAA,GAAkB,YAAYh0B,UAAAA,GAXX,CAWD,CAAxB;AACA,UAAMgN,SAAAA,GAAY,KAZO,SAYzB;AACA,UAAMinB,OAAAA,GAAUD,eAAAA,CAAAA,YAAAA,CACdhnB,SAAAA,CAAAA,UAAAA,GAAuB8mB,SAAAA,CADTE,CAAAA,EAEdhnB,SAAAA,CAAAA,SAAAA,GAAsB8mB,SAAAA,CAfC,CAaTE,CAAhB;AAIA,UAAME,OAAAA,GAAUjtB,IAAAA,CAAAA,KAAAA,CAAWgtB,OAAAA,CAjBF,CAiBEA,CAAXhtB,CAAhB;AACA,UAAMktB,MAAAA,GAASltB,IAAAA,CAAAA,KAAAA,CAAWgtB,OAAAA,CAlBD,CAkBCA,CAAXhtB,CAAf;AACA8sB,IAAAA,aAAAA,IAAiB,sBAnBQ,MAmBzBA;AAEA,qBAAiB;AAAA;AAEfpN,MAAAA,KAAAA,EAFe;AAGfle,MAAAA,GAAAA,EAHe;AAIfG,MAAAA,IAAAA,EAJe;AAKfjJ,MAAAA,QAAAA,EAAU,KALK;AAAA;AAAA,KAAjB;AAx2Ba;;AAk3BfiwB,EAAAA,aAAAA,CAAAA,YAAAA,EAA4B;AAC1B,UAAM,UADoB,gCACpB,CAAN;AAn3Ba;;AAs3BflI,EAAAA,MAAAA,GAAS;AACP,UAAMze,OAAAA,GAAU,KADT,gBACS,EAAhB;;AACA,UAAMmrB,YAAAA,GAAenrB,OAAAA,CAArB;AAAA,UACEorB,eAAAA,GAAkBD,YAAAA,CAHb,MAEP;;AAGA,QAAIC,eAAAA,KAAJ,GAA2B;AAAA;AALpB;;AAQP,UAAMC,YAAAA,GAAertB,IAAAA,CAAAA,GAAAA,CAAAA,kBAAAA,EAA6B,sBAR3C,CAQcA,CAArB;;AACA,sCATO,YASP;;AAEA,8CAXO,OAWP;;AAEA,uBAbO,YAaP;;AAEA,yBAAqBgC,OAAAA,CAfd,KAeP;;AACA,6CAAyC;AACvC9H,MAAAA,MAAAA,EADuC;AAEvCikB,MAAAA,QAAAA,EAAU,KAF6B;AAAA,KAAzC;AAt4Ba;;AA44BfmP,EAAAA,eAAAA,CAAAA,OAAAA,EAAyB;AACvB,WAAO,wBADgB,OAChB,CAAP;AA74Ba;;AAg5BfC,EAAAA,KAAAA,GAAQ;AACN,mBADM,KACN;AAj5Ba;;AAo5Bf,MAAI3E,uBAAJ,GAA8B;AAG5B,WAAO,oCAEH,qBAAqBvrB,qBALG,UAG5B;AAv5Ba;;AA45Bf,MAAImwB,eAAJ,GAAsB;AACpB,WAAO/uB,gBAAAA,CAAiB,KAAjBA,SAAAA,CAAAA,CAAAA,SAAAA,KADa,KACpB;AA75Ba;;AAg6Bf,MAAIgoB,oBAAJ,GAA2B;AACzB,WAAO,+BAA+BtqB,gCADb,UACzB;AAj6Ba;;AAo6Bf,MAAIsxB,0BAAJ,GAAiC;AAC/B,WAAO,+BAA+BtxB,gCADP,QAC/B;AAr6Ba;;AAw6Bf,MAAIuxB,4BAAJ,GAAmC;AACjC,WAAO,oCAEH,6BAA6B,eAHA,WACjC;AAz6Ba;;AA86Bf,MAAIC,0BAAJ,GAAiC;AAC/B,WAAO,oCAEH,8BAA8B,eAHH,YAC/B;AA/6Ba;;AA07BfC,EAAAA,sBAAAA,GAAyB;AACvB,QAAI,CAAC,KAAL,YAAsB;AACpB,aAAO;AAAEzsB,QAAAA,KAAAA,EADW;AACb,OAAP;AAFqB;;AAIvB,UAAM2lB,QAAAA,GAAW,YAAY,0BAJN,CAIN,CAAjB;AAGA,UAAMxoB,OAAAA,GAAUwoB,QAAAA,CAPO,GAOvB;AAEA,UAAMllB,IAAAA,GAAO;AACXqB,MAAAA,EAAAA,EAAI6jB,QAAAA,CADO;AAEX1mB,MAAAA,CAAAA,EAAG9B,OAAAA,CAAAA,UAAAA,GAAqBA,OAAAA,CAFb;AAGX4E,MAAAA,CAAAA,EAAG5E,OAAAA,CAAAA,SAAAA,GAAoBA,OAAAA,CAHZ;AAIXsD,MAAAA,IAAAA,EAJW;AAAA,KAAb;AAMA,WAAO;AAAEwB,MAAAA,KAAAA,EAAF;AAAeC,MAAAA,IAAAA,EAAf;AAA2BlC,MAAAA,KAAAA,EAAO,CAAlC,IAAkC;AAAlC,KAAP;AAz8Ba;;AA48BfunB,EAAAA,gBAAAA,GAAmB;AACjB,WAAO,kCAAmB;AACxBjnB,MAAAA,QAAAA,EAAU,KADc;AAExBN,MAAAA,KAAAA,EAAO,KAFiB;AAGxBE,MAAAA,gBAAAA,EAHwB;AAIxBC,MAAAA,UAAAA,EAAY,KAJY;AAKxBC,MAAAA,GAAAA,EAAK,gCAAgC,KALb;AAAA,KAAnB,CAAP;AA78Ba;;AAy9BfrG,EAAAA,aAAAA,CAAAA,UAAAA,EAA0B;AACxB,QAAI,CAAC,KAAL,aAAuB;AACrB,aADqB,KACrB;AAFsB;;AAIxB,QACE,EACE,gCACAnC,UAAAA,GADA,KAEAA,UAAAA,IAAc,KAJlB,UACE,CADF,EAME;AACAE,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,6BADd,wBACAA;AACA,aAFA,KAEA;AAZsB;;AAcxB,WAAO,mCAAmC,gBAAgB;AACxD,aAAO2I,IAAAA,CAAAA,EAAAA,KADiD,UACxD;AAfsB,KAcjB,CAAP;AAv+Ba;;AA++BfzG,EAAAA,YAAAA,CAAAA,UAAAA,EAAyB;AACvB,QAAI,CAAC,KAAD,eAAqB,CAAC,KAA1B,SAAwC;AACtC,aADsC,KACtC;AAFqB;;AAIvB,QACE,EACE,gCACApC,UAAAA,GADA,KAEAA,UAAAA,IAAc,KAJlB,UACE,CADF,EAME;AACAE,MAAAA,OAAAA,CAAAA,KAAAA,CAAc,4BADd,wBACAA;AACA,aAFA,KAEA;AAZqB;;AAcvB,UAAM6tB,QAAAA,GAAW,YAAY/tB,UAAAA,GAdN,CAcN,CAAjB;;AACA,QAAI,CAAJ,UAAe;AACb,aADa,KACb;AAhBqB;;AAkBvB,WAAO,iBAlBgB,QAkBhB,CAAP;AAjgCa;;AAogCf80B,EAAAA,OAAAA,GAAU;AACR,SAAK,IAAItyB,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,YAArB,QAAyCjK,CAAAA,GAAzC,IAAiDA,CAAjD,IAAsD;AACpD,UACE,kBACA,kCAAkC+jB,qCAFpC,UAGE;AACA,uBADA,KACA;AALkD;AAD9C;AApgCK;;AAkhCfwO,EAAAA,gBAAAA,GAAmB;AACjB,SAAK,IAAIvyB,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,YAArB,QAAyCjK,CAAAA,GAAzC,IAAiDA,CAAjD,IAAsD;AACpD,UAAI,YAAJ,CAAI,CAAJ,EAAoB;AAClB,uBADkB,eAClB;AAFkD;AADrC;AAlhCJ;;AA+hCfwyB,EAAAA,oBAAAA,CAAAA,QAAAA,EAA+B;AAC7B,QAAIjH,QAAAA,CAAJ,SAAsB;AACpB,aAAOrO,OAAAA,CAAAA,OAAAA,CAAgBqO,QAAAA,CADH,OACbrO,CAAP;AAF2B;;AAI7B,QAAI,wBAAJ,QAAI,CAAJ,EAAuC;AACrC,aAAO,wBAD8B,QAC9B,CAAP;AAL2B;;AAO7B,UAAMD,OAAAA,GAAU,yBACLsO,QAAAA,CADK,SAERnO,OAAAA,IAAW;AACf,UAAI,CAACmO,QAAAA,CAAL,SAAuB;AACrBA,QAAAA,QAAAA,CAAAA,UAAAA,CADqB,OACrBA;AAFa;;AAIf,iCAJe,QAIf;;AACA,aALe,OAKf;AAPY,aASP7N,MAAAA,IAAU;AACfhgB,MAAAA,OAAAA,CAAAA,KAAAA,CAAAA,kCAAAA,EADe,MACfA;;AAEA,iCAHe,QAGf;AAnByB,KAOb,CAAhB;;AAcA,sCArB6B,OAqB7B;;AACA,WAtB6B,OAsB7B;AArjCa;;AAwjCf+0B,EAAAA,cAAAA,CAAAA,qBAAAA,EAAsC;AACpC,UAAMb,YAAAA,GAAec,qBAAAA,IAAyB,KADV,gBACU,EAA9C;;AACA,UAAMC,WAAAA,GAAc,+BAChB,YADgB,QAEhB,YAJgC,IAEpC;AAGA,UAAMpH,QAAAA,GAAW,qDAEf,KAFe,QALmB,WAKnB,CAAjB;;AAKA,kBAAc;AACZ,+CAAyC,MAAM;AAC7C,uCAD6C,QAC7C;AAFU,OACZ;;AAGA,aAJY,IAIZ;AAdkC;;AAgBpC,WAhBoC,KAgBpC;AAxkCa;;AAmlCf5c,EAAAA,sBAAAA,CAAAA,YAAAA,EAAAA,SAAAA,EAAAA,QAAAA,EAIErD,oBAAAA,GAJFqD,KAAAA,EAAAA,QAAAA,EAME;AACA,WAAO,yCAAqB;AAAA;AAAA;AAAA;AAAA;AAK1BtD,MAAAA,cAAAA,EAAgB,mCAAmC,KALzB;AAM1BC,MAAAA,oBAAAA,EAAsB,oCANI;AAAA,KAArB,CAAP;AA1lCa;;AAonCfrT,EAAAA,4BAAAA,CAAAA,OAAAA,EAAAA,OAAAA,EAGEtB,iBAAAA,GAHFsB,IAAAA,EAIErB,kBAAAA,GAJFqB,EAAAA,EAKEpB,sBAAAA,GALFoB,KAAAA,EAMEnB,IAAAA,GANFmB,oBAAAA,EAOElB,eAAAA,GAPFkB,IAAAA,EAQEjB,mBAAAA,GARFiB,IAAAA,EASEhB,UAAAA,GATFgB,IAAAA,EAUE;AACA,WAAO,qDAA2B;AAAA;AAAA;AAGhCtB,MAAAA,iBAAAA,EACEA,iBAAAA,IAAqB,kBAJS;AAAA;AAAA;AAOhCgB,MAAAA,WAAAA,EAAa,KAPmB;AAQhCC,MAAAA,eAAAA,EAAiB,KARe;AAAA;AAUhCb,MAAAA,eAAAA,EAAiBA,eAAAA,IAAmB,KAVJ;AAWhCC,MAAAA,mBAAAA,EACEA,mBAAAA,IAAuB,kBAZO,YAYP,EAZO;AAahCC,MAAAA,UAAAA,EAAYA,UAAAA,IAAc,wBAbM;AAAA,KAA3B,CAAP;AA/nCa;;AAupCf27B,EAAAA,qBAAAA,CAAAA,OAAAA,EAAAA,OAAAA,EAAwCj8B,iBAAAA,GAAxCi8B,IAAAA,EAAkE;AAChE,WAAO,uCAAoB;AAAA;AAAA;AAGzBj8B,MAAAA,iBAAAA,EACEA,iBAAAA,IAAqB,kBAJE;AAAA,KAApB,CAAP;AAxpCa;;AAoqCfk8B,EAAAA,4BAAAA,CAAAA,OAAAA,EAAsC;AACpC,WAAO,sDAA2B;AADE;AACF,KAA3B,CAAP;AArqCa;;AA8qCf,MAAIC,iBAAJ,GAAwB;AACtB,UAAMtD,aAAAA,GAAgB,YADA,CACA,CAAtB;;AACA,SAAK,IAAIxvB,CAAAA,GAAJ,GAAWiK,EAAAA,GAAK,YAArB,QAAyCjK,CAAAA,GAAzC,IAAiD,EAAjD,GAAsD;AACpD,YAAMurB,QAAAA,GAAW,YADmC,CACnC,CAAjB;;AACA,UACEA,QAAAA,CAAAA,KAAAA,KAAmBiE,aAAAA,CAAnBjE,KAAAA,IACAA,QAAAA,CAAAA,MAAAA,KAAoBiE,aAAAA,CAFtB,QAGE;AACA,eADA,KACA;AANkD;AAFhC;;AAWtB,WAXsB,IAWtB;AAzrCa;;AAgsCfuD,EAAAA,gBAAAA,GAAmB;AACjB,WAAO,gBAAgBxH,QAAAA,IAAY;AACjC,YAAMh0B,QAAAA,GAAWg0B,QAAAA,CAAAA,OAAAA,CAAAA,WAAAA,CAA6B;AAAEpH,QAAAA,KAAAA,EADf;AACa,OAA7BoH,CAAjB;;AAEA,UAAI,CAAC,KAAD,yBAA+BmF,qCAAnC,QAAmCA,CAAnC,EAAoE;AAClE,eAAO;AACLlrB,UAAAA,KAAAA,EAAOjO,QAAAA,CADF;AAELkO,UAAAA,MAAAA,EAAQlO,QAAAA,CAFH;AAGL4F,UAAAA,QAAAA,EAAU5F,QAAAA,CAHL;AAAA,SAAP;AAJ+B;;AAWjC,aAAO;AACLiO,QAAAA,KAAAA,EAAOjO,QAAAA,CADF;AAELkO,QAAAA,MAAAA,EAAQlO,QAAAA,CAFH;AAGL4F,QAAAA,QAAAA,EAAW,qBAAD,EAAC,IAHN;AAAA,OAAP;AAZe,KACV,CAAP;AAjsCa;;AAutCf,MAAIgoB,4BAAJ,GAAmC;AACjC,QAAI,CAAC,KAAL,aAAuB;AACrB,aAAOjI,OAAAA,CAAAA,OAAAA,CADc,IACdA,CAAP;AAF+B;;AAIjC,QAAI,CAAC,KAAL,+BAAyC;AAGvC,aAAO,iBAHgC,wBAGhC,EAAP;AAP+B;;AASjC,WAAO,KAT0B,6BASjC;AAhuCa;;AAuuCf,MAAIiI,4BAAJ,UAA0C;AACxC,QAAI,EAAE,mBAAN,OAAI,CAAJ,EAAmC;AACjC,YAAM,UAAU,gDADiB,EAC3B,CAAN;AAFsC;;AAIxC,QAAI,CAAC,KAAL,aAAuB;AAAA;AAJiB;;AAOxC,QAAI,CAAC,KAAL,+BAAyC;AAAA;AAPD;;AAYxC,yCAZwC,OAYxC;;AAEA,2BAAuB,KAAvB,QAAoC;AAClCoG,MAAAA,QAAAA,CAAAA,MAAAA,CAAgBA,QAAAA,CAAhBA,KAAAA,EAAgCA,QAAAA,CAAhCA,QAAAA,EADkC,OAClCA;AAfsC;;AAiBxC,SAjBwC,MAiBxC;AAEA,2DAAuD;AACrD5sB,MAAAA,MAAAA,EADqD;AAAA;AAAA,KAAvD;AA1vCa;;AAmwCf,MAAIq0B,UAAJ,GAAiB;AACf,WAAO,KADQ,WACf;AApwCa;;AA4wCf,MAAIA,UAAJ,OAAqB;AACnB,QAAI,qBAAJ,MAA+B;AAAA;AADZ;;AAInB,QAAI,CAACC,iCAAL,IAAKA,CAAL,EAA8B;AAC5B,YAAM,UAAU,4BADY,EACtB,CAAN;AALiB;;AAOnB,uBAPmB,IAOnB;AACA,gDAA4C;AAAEt0B,MAAAA,MAAAA,EAAF;AAAA;AAAA,KAA5C;;AAEA,2BAA0C,KAVvB,kBAUnB;AAtxCa;;AAyxCf2uB,EAAAA,iBAAAA,CAAkB9vB,UAAAA,GAAlB8vB,IAAAA,EAAqC;AACnC,UAAM0F,UAAAA,GAAa,KAAnB;AAAA,UACEvoB,MAAAA,GAAS,KAFwB,MACnC;AAGAA,IAAAA,MAAAA,CAAAA,SAAAA,CAAAA,MAAAA,CAAAA,kBAAAA,EAEEuoB,UAAAA,KAAelxB,qBANkB,UAInC2I;AAIAA,IAAAA,MAAAA,CAAAA,SAAAA,CAAAA,MAAAA,CAAAA,eAAAA,EAAyCuoB,UAAAA,KAAelxB,qBARrB,OAQnC2I;;AAEA,QAAI,CAAC,KAAD,eAAqB,CAAzB,YAAsC;AAAA;AAVH;;AAgBnC,QAAI,2BAA2BJ,KAAAA,CAAM,KAArC,kBAA+BA,CAA/B,EAA+D;AAC7D,qBAAe,KAAf,oBAD6D,IAC7D;AAjBiC;;AAmBnC,2CAnBmC,IAmBnC;;AACA,SApBmC,MAoBnC;AA7yCa;;AAmzCf,MAAI6oB,UAAJ,GAAiB;AACf,WAAO,KADQ,WACf;AApzCa;;AA4zCf,MAAIA,UAAJ,OAAqB;AACnB,QAAI,qBAAJ,MAA+B;AAAA;AADZ;;AAInB,QAAI,CAACC,iCAAL,IAAKA,CAAL,EAA8B;AAC5B,YAAM,UAAU,4BADY,EACtB,CAAN;AALiB;;AAOnB,uBAPmB,IAOnB;AACA,gDAA4C;AAAEx0B,MAAAA,MAAAA,EAAF;AAAA;AAAA,KAA5C;;AAEA,2BAA0C,KAVvB,kBAUnB;AAt0Ca;;AAy0Cf4uB,EAAAA,iBAAAA,CAAkB/vB,UAAAA,GAAlB+vB,IAAAA,EAAqC;AACnC,QAAI,CAAC,KAAL,aAAuB;AAAA;AADY;;AAInC,UAAM9iB,MAAAA,GAAS,KAAf;AAAA,UACE2oB,KAAAA,GAAQ,KALyB,MAInC;AAGA3oB,IAAAA,MAAAA,CAAAA,WAAAA,GAPmC,EAOnCA;;AAEA,QAAI,qBAAqBvI,qBAAzB,MAA0C;AACxC,WAAK,IAAIlC,CAAAA,GAAJ,GAAW2tB,IAAAA,GAAOyF,KAAAA,CAAvB,QAAqCpzB,CAAAA,GAArC,MAA+C,EAA/C,GAAoD;AAClDyK,QAAAA,MAAAA,CAAAA,WAAAA,CAAmB2oB,KAAAA,CAAAA,CAAAA,CAAAA,CAD+B,GAClD3oB;AAFsC;AAA1C,WAIO;AACL,YAAM4oB,MAAAA,GAAS,mBADV,CACL;AACA,UAAIC,MAAAA,GAFC,IAEL;;AACA,WAAK,IAAItzB,CAAAA,GAAJ,GAAW2tB,IAAAA,GAAOyF,KAAAA,CAAvB,QAAqCpzB,CAAAA,GAArC,MAA+C,EAA/C,GAAoD;AAClD,YAAIszB,MAAAA,KAAJ,MAAqB;AACnBA,UAAAA,MAAAA,GAASx7B,QAAAA,CAAAA,aAAAA,CADU,KACVA,CAATw7B;AACAA,UAAAA,MAAAA,CAAAA,SAAAA,GAFmB,QAEnBA;AACA7oB,UAAAA,MAAAA,CAAAA,WAAAA,CAHmB,MAGnBA;AAHF,eAIO,IAAIzK,CAAAA,GAAAA,CAAAA,KAAJ,QAAsB;AAC3BszB,UAAAA,MAAAA,GAASA,MAAAA,CAAAA,SAAAA,CADkB,KAClBA,CAATA;AACA7oB,UAAAA,MAAAA,CAAAA,WAAAA,CAF2B,MAE3BA;AAPgD;;AASlD6oB,QAAAA,MAAAA,CAAAA,WAAAA,CAAmBF,KAAAA,CAAAA,CAAAA,CAAAA,CAT+B,GASlDE;AAZG;AAb4B;;AA6BnC,QAAI,CAAJ,YAAiB;AAAA;AA7BkB;;AAgCnC,QAAI,2BAA2BjpB,KAAAA,CAAM,KAArC,kBAA+BA,CAA/B,EAA+D;AAC7D,qBAAe,KAAf,oBAD6D,IAC7D;AAjCiC;;AAmCnC,2CAnCmC,IAmCnC;;AACA,SApCmC,MAoCnC;AA72Ca;;AAm3CfmjB,EAAAA,eAAAA,CAAAA,iBAAAA,EAAmC1P,QAAAA,GAAnC0P,KAAAA,EAAqD;AACnD,QAAI,KAAJ,sBAA+B;AAC7B,aAD6B,CAC7B;AAFiD;;AAInD,YAAQ,KAAR;AACE,WAAK1rB,qBAAL;AAAyB;AACvB,gBAAM;AAAA;AAAA,cAAY,KAAlB,gBAAkB,EAAlB;AAAA,gBACEyxB,UAAAA,GAAa,IAFQ,GAER,EADf;;AAIA,qBAAW;AAAA;AAAA;AAAA;AAAX;AAAW,WAAX,WAAsD;AACpD,gBAAI9rB,OAAAA,KAAAA,CAAAA,IAAiBG,YAAAA,GAArB,KAAyC;AAAA;AADW;;AAIpD,gBAAI4rB,MAAAA,GAASD,UAAAA,CAAAA,GAAAA,CAJuC,CAIvCA,CAAb;;AACA,gBAAI,CAAJ,QAAa;AACXA,cAAAA,UAAAA,CAAAA,GAAAA,CAAAA,CAAAA,EAAmBC,MAAAA,KADR,EACXD;AANkD;;AAQpDC,YAAAA,MAAAA,CAAAA,IAAAA,CARoD,EAQpDA;AAbqB;;AAgBvB,+BAAqBD,UAAAA,CAArB,MAAqBA,EAArB,EAA0C;AACxC,kBAAMhvB,YAAAA,GAAeivB,MAAAA,CAAAA,OAAAA,CADmB,iBACnBA,CAArB;;AACA,gBAAIjvB,YAAAA,KAAiB,CAArB,GAAyB;AAAA;AAFe;;AAKxC,kBAAMyZ,QAAAA,GAAWwV,MAAAA,CALuB,MAKxC;;AACA,gBAAIxV,QAAAA,KAAJ,GAAoB;AAAA;AANoB;;AAUxC,0BAAc;AACZ,mBAAK,IAAIhe,CAAAA,GAAIuE,YAAAA,GAAR,GAA0B0F,EAAAA,GAA/B,GAAuCjK,CAAAA,IAAvC,IAAgDA,CAAhD,IAAqD;AACnD,sBAAMyzB,SAAAA,GAAYD,MAAAA,CAAlB,CAAkBA,CAAlB;AAAA,sBACEE,UAAAA,GAAaF,MAAAA,CAAOxzB,CAAAA,GAAPwzB,CAAAA,CAAAA,GAFoC,CACnD;;AAEA,oBAAIC,SAAAA,GAAJ,YAA4B;AAC1B,yBAAOrF,iBAAAA,GADmB,UAC1B;AAJiD;AADzC;AAAd,mBAQO;AACL,mBAAK,IAAIpuB,CAAAA,GAAIuE,YAAAA,GAAR,GAA0B0F,EAAAA,GAA/B,UAA8CjK,CAAAA,GAA9C,IAAsDA,CAAtD,IAA2D;AACzD,sBAAMyzB,SAAAA,GAAYD,MAAAA,CAAlB,CAAkBA,CAAlB;AAAA,sBACEE,UAAAA,GAAaF,MAAAA,CAAOxzB,CAAAA,GAAPwzB,CAAAA,CAAAA,GAF0C,CACzD;;AAEA,oBAAIC,SAAAA,GAAJ,YAA4B;AAC1B,yBAAOC,UAAAA,GADmB,iBAC1B;AAJuD;AADtD;AAlBiC;;AA4BxC,0BAAc;AACZ,oBAAMC,OAAAA,GAAUH,MAAAA,CADJ,CACIA,CAAhB;;AACA,kBAAIG,OAAAA,GAAJ,mBAAiC;AAC/B,uBAAOvF,iBAAAA,GAAAA,OAAAA,GADwB,CAC/B;AAHU;AAAd,mBAKO;AACL,oBAAMwF,MAAAA,GAASJ,MAAAA,CAAOxV,QAAAA,GADjB,CACUwV,CAAf;;AACA,kBAAII,MAAAA,GAAJ,mBAAgC;AAC9B,uBAAOA,MAAAA,GAAAA,iBAAAA,GADuB,CAC9B;AAHG;AAjCiC;;AAAA;AAhBnB;;AAAA;AAD3B;;AA4DE,WAAK9xB,qBAAL;AAA4B;AAAA;AA5D9B;;AA+DE,WAAKA,qBAAL;AAA0B;AACxB,cAAI,qBAAqBI,qBAAzB,MAA0C;AAAA;AADlB;;AAIxB,gBAAMmxB,MAAAA,GAAS,mBAJS,CAIxB;;AAEA,cAAIvV,QAAAA,IAAYsQ,iBAAAA,GAAAA,CAAAA,KAAhB,QAAkD;AAAA;AAAlD,iBAEO,IAAI,aAAaA,iBAAAA,GAAAA,CAAAA,KAAjB,QAAmD;AAAA;AARlC;;AAWxB,gBAAM;AAAA;AAAA,cAAY,KAAlB,gBAAkB,EAAlB;AAAA,gBACEsF,UAAAA,GAAa5V,QAAAA,GAAWsQ,iBAAAA,GAAXtQ,CAAAA,GAAmCsQ,iBAAAA,GAZ1B,CAWxB;;AAGA,qBAAW;AAAA;AAAA;AAAX;AAAW,WAAX,WAAmD;AACjD,gBAAI1mB,EAAAA,KAAJ,YAAuB;AAAA;AAD0B;;AAIjD,gBAAID,OAAAA,GAAAA,CAAAA,IAAeG,YAAAA,KAAnB,KAAyC;AACvC,qBADuC,CACvC;AAL+C;;AAAA;AAd3B;;AAAA;AA/D5B;AAAA;;AAyFA,WA7FmD,CA6FnD;AAh9Ca;;AAu9CfisB,EAAAA,QAAAA,GAAW;AACT,UAAMzF,iBAAAA,GAAoB,KAA1B;AAAA,UACElxB,UAAAA,GAAa,KAFN,UACT;;AAGA,QAAIkxB,iBAAAA,IAAJ,YAAqC;AACnC,aADmC,KACnC;AALO;;AAOT,UAAM0F,OAAAA,GACJ,kDARO,CAOT;AAGA,6BAAyBrvB,IAAAA,CAAAA,GAAAA,CAAS2pB,iBAAAA,GAAT3pB,OAAAA,EAVhB,UAUgBA,CAAzB;AACA,WAXS,IAWT;AAl+Ca;;AAy+Cf8Z,EAAAA,YAAAA,GAAe;AACb,UAAM6P,iBAAAA,GAAoB,KADb,kBACb;;AAEA,QAAIA,iBAAAA,IAAJ,GAA4B;AAC1B,aAD0B,KAC1B;AAJW;;AAMb,UAAM0F,OAAAA,GACJ,iDAPW,CAMb;AAGA,6BAAyBrvB,IAAAA,CAAAA,GAAAA,CAAS2pB,iBAAAA,GAAT3pB,OAAAA,EATZ,CASYA,CAAzB;AACA,WAVa,IAUb;AAn/Ca;;AAAA;;;;;;;;;;;;;;AC5HjB,MAAMsvB,qBAAAA,GAAwB;AAE5BC,EAAAA,QAAAA,EAF4B;AAG5BC,EAAAA,gBAAAA,EAH4B;AAK5BC,EAAAA,IAAAA,EAL4B;AAM5BC,EAAAA,IAAAA,EAN4B;AAO5BC,EAAAA,GAAAA,EAP4B;AAQ5BC,EAAAA,KAAAA,EAR4B;AAS5BC,EAAAA,SAAAA,EAT4B;AAW5BC,EAAAA,CAAAA,EAX4B;AAa5BC,EAAAA,CAAAA,EAb4B;AAc5BC,EAAAA,KAAAA,EAd4B;AAe5BC,EAAAA,MAAAA,EAf4B;AAiB5BC,EAAAA,GAAAA,EAjB4B;AAmB5BC,EAAAA,GAAAA,EAnB4B;AAoB5BC,EAAAA,IAAAA,EApB4B;AAqB5BC,EAAAA,EAAAA,EArB4B;AAsB5BC,EAAAA,MAAAA,EAtB4B;AAuB5BC,EAAAA,IAAAA,EAvB4B;AAwB5BC,EAAAA,KAAAA,EAxB4B;AAyB5BC,EAAAA,IAAAA,EAzB4B;AA2B5BC,EAAAA,IAAAA,EA3B4B;AA4B5BC,EAAAA,EAAAA,EA5B4B;AA6B5BC,EAAAA,EAAAA,EA7B4B;AA8B5BC,EAAAA,EAAAA,EA9B4B;AA+B5BC,EAAAA,OAAAA,EA/B4B;AAgC5BC,EAAAA,EAAAA,EAhC4B;AAiC5BC,EAAAA,EAAAA,EAjC4B;AAmC5BC,EAAAA,CAAAA,EAnC4B;AAoC5BC,EAAAA,EAAAA,EApC4B;AAqC5BC,EAAAA,KAAAA,EArC4B;AAuC5BC,EAAAA,KAAAA,EAvC4B;AAwC5BC,EAAAA,EAAAA,EAxC4B;AAyC5BC,EAAAA,EAAAA,EAzC4B;AA0C5BC,EAAAA,EAAAA,EA1C4B;AA2C5BC,EAAAA,KAAAA,EA3C4B;AA4C5BC,EAAAA,KAAAA,EA5C4B;AA6C5BC,EAAAA,KAAAA,EA7C4B;AA+C5BC,EAAAA,OAAAA,EA/C4B;AAiD5BC,EAAAA,MAAAA,EAjD4B;AAmD5BC,EAAAA,OAAAA,EAnD4B;AAqD5BC,EAAAA,QAAAA,EArD4B;AAAA,CAA9B;AAwDA,MAAMC,eAAAA,GAvEN,UAuEA;;AAOA,6BAA6B;AAI3B9/B,EAAAA,WAAAA,CAAY;AAAZA,IAAAA;AAAY,GAAZA,EAAyB;AACvB,mBADuB,OACvB;AALyB;;AAQ3BQ,EAAAA,MAAAA,CAAAA,UAAAA,EAAmB;AACjB,WAAO,WADU,UACV,CAAP;AATyB;;AAY3Bu/B,EAAAA,cAAAA,CAAAA,aAAAA,EAAAA,WAAAA,EAA2C;AACzC,QAAIC,aAAAA,CAAAA,GAAAA,KAAJ,WAAqC;AACnCC,MAAAA,WAAAA,CAAAA,YAAAA,CAAAA,YAAAA,EAAuCD,aAAAA,CADJ,GACnCC;AAFuC;;AAIzC,QAAID,aAAAA,CAAAA,EAAAA,KAAJ,WAAoC;AAClCC,MAAAA,WAAAA,CAAAA,YAAAA,CAAAA,WAAAA,EAAsCD,aAAAA,CADJ,EAClCC;AALuC;AAZhB;;AAqB3BC,EAAAA,KAAAA,CAAAA,IAAAA,EAAY;AACV,QAAI,CAAJ,MAAW;AACT,aADS,IACT;AAFQ;;AAKV,UAAM7zB,OAAAA,GAAUjL,QAAAA,CAAAA,aAAAA,CALN,MAKMA,CAAhB;;AACA,QAAI,UAAJ,MAAoB;AAClB,YAAM;AAAA;AAAA,UADY,IAClB;AACA,YAAM0U,KAAAA,GAAQqqB,IAAAA,CAAAA,KAAAA,CAFI,eAEJA,CAAd;;AACA,iBAAW;AACT9zB,QAAAA,OAAAA,CAAAA,YAAAA,CAAAA,MAAAA,EADS,SACTA;AACAA,QAAAA,OAAAA,CAAAA,YAAAA,CAAAA,YAAAA,EAAmCyJ,KAAAA,CAF1B,CAE0BA,CAAnCzJ;AAFF,aAGO,IAAIgxB,qBAAAA,CAAJ,IAAIA,CAAJ,EAAiC;AACtChxB,QAAAA,OAAAA,CAAAA,YAAAA,CAAAA,MAAAA,EAA6BgxB,qBAAAA,CADS,IACTA,CAA7BhxB;AAPgB;AANV;;AAiBV,8BAjBU,OAiBV;;AAEA,QAAIuK,IAAAA,CAAJ,UAAmB;AACjB,UAAIA,IAAAA,CAAAA,QAAAA,CAAAA,MAAAA,KAAAA,CAAAA,IAA8B,QAAQA,IAAAA,CAAAA,QAAAA,CAA1C,CAA0CA,CAA1C,EAA4D;AAG1D,4BAAoBA,IAAAA,CAAAA,QAAAA,CAApB,CAAoBA,CAApB,EAH0D,OAG1D;AAHF,aAIO;AACL,0BAAkBA,IAAAA,CAAlB,UAAiC;AAC/BvK,UAAAA,OAAAA,CAAAA,WAAAA,CAAoB,WADW,GACX,CAApBA;AAFG;AALU;AAnBT;;AA8BV,WA9BU,OA8BV;AAnDyB;;AAAA;;;;AA0D7B,oCAAoC;AAKlC8vB,EAAAA,4BAAAA,CAAAA,OAAAA,EAAsC;AACpC,WAAO,2BAA2B;AADE;AACF,KAA3B,CAAP;AANgC;;AAAA;;;;;;;;;;;;;;;ACxIpC;;AAwBA,sBAAsB;AAIpBn8B,EAAAA,WAAAA,CAAY;AAAA;AAAA;AAAA;AAAZA,IAAAA;AAAY,GAAZA,EAA8D;AAC5D,mBAD4D,OAC5D;AACA,mBAF4D,OAE5D;AACA,mBAH4D,OAG5D;AACA,6BAJ4D,iBAI5D;AAEA,eAN4D,IAM5D;AACA,sBAP4D,KAO5D;AAXkB;;AAoBpBQ,EAAAA,MAAAA,CAAAA,QAAAA,EAAiBC,MAAAA,GAAjBD,SAAAA,EAAqC;AACnC,QAAIC,MAAAA,KAAJ,SAAwB;AACtB,YAAMG,UAAAA,GAAa;AACjBC,QAAAA,QAAAA,EAAUA,QAAAA,CAAAA,KAAAA,CAAe;AAAEC,UAAAA,QAAAA,EADV;AACQ,SAAfD,CADO;AAEjBE,QAAAA,GAAAA,EAAK,KAFY;AAGjBq/B,QAAAA,GAAAA,EAAK,KAHY;AAIjBp/B,QAAAA,IAAAA,EAJiB;AAKjBf,QAAAA,iBAAAA,EAAmB,KALF;AAAA;AAAA,OAAnB;AAUA,YAAMc,GAAAA,GAAMK,QAAAA,CAAAA,aAAAA,CAXU,KAWVA,CAAZ;AACA,+BAZsB,GAYtB;AACAR,MAAAA,UAAAA,CAAAA,GAAAA,GAbsB,GAatBA;;AAEAy/B,yBAAAA,MAAAA,CAfsB,UAetBA;;AACA,aAAO7Z,OAAAA,CAhBe,OAgBfA,EAAP;AAjBiC;;AAqBnC,WAAO,2BAEC4Z,GAAAA,IAAO;AACX,UAAI,KAAJ,YAAqB;AAAA;AADV;;AAIX,YAAMx/B,UAAAA,GAAa;AACjBC,QAAAA,QAAAA,EAAUA,QAAAA,CAAAA,KAAAA,CAAe;AAAEC,UAAAA,QAAAA,EADV;AACQ,SAAfD,CADO;AAEjBE,QAAAA,GAAAA,EAAK,KAFY;AAAA;AAIjBC,QAAAA,IAAAA,EAAM,KAJW;AAKjBf,QAAAA,iBAAAA,EAAmB,KALF;AAAA;AAAA,OAAnB;;AASA,UAAI,KAAJ,KAAc;AACZogC,2BAAAA,MAAAA,CADY,UACZA;AADF,aAEO;AAEL,mBAAWj/B,QAAAA,CAAAA,aAAAA,CAFN,KAEMA,CAAX;AACA,iCAAyB,KAHpB,GAGL;AACAR,QAAAA,UAAAA,CAAAA,GAAAA,GAAiB,KAJZ,GAILA;;AAEAy/B,2BAAAA,MAAAA,CANK,UAMLA;AArBS;AAFR,aA0BEzS,KAAAA,IAAS;AACd5mB,MAAAA,OAAAA,CAAAA,KAAAA,CADc,KACdA;AAhD+B,KAqB5B,CAAP;AAzCkB;;AAwEpB3F,EAAAA,MAAAA,GAAS;AACP,sBADO,IACP;AAzEkB;;AA4EpBC,EAAAA,IAAAA,GAAO;AACL,QAAI,CAAC,KAAL,KAAe;AAAA;AADV;;AAIL,sBAJK,IAIL;AAhFkB;;AAAA;;;;AAuFtB,6BAA6B;AAO3B46B,EAAAA,qBAAAA,CAAAA,OAAAA,EAAAA,OAAAA,EAGEj8B,iBAAAA,GAHFi8B,IAAAA,EAIEoE,OAAAA,GAJFpE,IAAAA,EAKE;AACA,WAAO,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,KAApB,CAAP;AAbyB;;AAAA;;;;;;;;;;;;;;;AChG7B;;AAfA;;AAAA;;AAmBA,gDAAmC;AACjC,MAAInG,cAAJ,GAAqB;AACnB,WAAO5B,sBAAAA,IAAAA,EAAAA,gBAAAA,EAA+B,KADnB,MACZA,CAAP;AAF+B;;AAKjCmC,EAAAA,eAAAA,CAAgB;AAAA;AAAWC,IAAAA,QAAAA,GAAX;AAA4BzvB,IAAAA,UAAAA,GAA5CwvB;AAAgB,GAAhBA,EAAiE;AAC/D,QAAI,aAAa,CAAC,KAAlB,sBAA6C;AAC3C,YAAM5mB,IAAAA,GAAOwqB,OAAAA,CAAAA,UAAAA,GAAqBA,OAAAA,CADS,UAC3C;AACA,YAAM/sB,KAAAA,GAAQuC,IAAAA,GAAOwqB,OAAAA,CAFsB,WAE3C;AACA,YAAM;AAAA;AAAA;AAAA,UAA8B,KAHO,SAG3C;;AACA,UACE,gCACAxqB,IAAAA,GADA,cAEAvC,KAAAA,GAAQozB,UAAAA,GAHV,aAIE;AACAhK,QAAAA,QAAAA,GAAW;AAAE7mB,UAAAA,IAAAA,EAAF;AAAWH,UAAAA,GAAAA,EAAX;AAAA,SAAXgnB;AATyC;AADkB;;AAa/D,0BAAsB;AAAA;AAAA;AAAA;AAAA,KAAtB;AAlB+B;;AAqBjCE,EAAAA,gBAAAA,GAAmB;AACjB,QAAI,KAAJ,sBAA+B;AAG7B,aAAO,KAHsB,sBAGtB,EAAP;AAJe;;AAMjB,WAAO,MANU,gBAMV,EAAP;AA3B+B;;AA8BjCC,EAAAA,aAAAA,CAAAA,YAAAA,EAA4B;AAC1B,QAAI,KAAJ,sBAA+B;AAAA;AADL;;AAI1B,QAAIqG,SAAAA,GAAY,KAJU,kBAI1B;AACA,QAAIyD,iBAAAA,GALsB,KAK1B;;AAEA,qCAAiC;AAC/B,UAAIx/B,IAAAA,CAAAA,OAAAA,GAAJ,KAAwB;AAAA;AADO;;AAI/B,UACEA,IAAAA,CAAAA,EAAAA,KAAAA,SAAAA,IACA,qBAAqBoK,qBADrBpK,QAAAA,IAEA,qBAAqBwK,qBAHvB,MAIE;AACAg1B,QAAAA,iBAAAA,GADA,IACAA;AADA;AAR6B;AAPP;;AAoB1B,QAAI,CAAJ,mBAAwB;AACtBzD,MAAAA,SAAAA,GAAY7B,YAAAA,CAAAA,CAAAA,CAAAA,CADU,EACtB6B;AArBwB;;AAuB1B,+BAvB0B,SAuB1B;AArD+B;;AAAA;;;;;;;UCnBnC;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPA;;AAIA;;AAIA;;AACA;;AAxBA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAoCA,MAAM0D,YAAAA,GApCN,UAoCA;AAEA,MAAMC,UAAAA,GAtCN,WAsCA", "sources": ["webpack://pdfjs-dist/web/pdf_viewer/webpack/universalModuleDefinition", "webpack://pdfjs-dist/web/pdf_viewer/web/annotation_layer_builder.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdfjs.js", "webpack://pdfjs-dist/web/pdf_viewer/web/l10n_utils.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_link_service.js", "webpack://pdfjs-dist/web/pdf_viewer/web/ui_utils.js", "webpack://pdfjs-dist/web/pdf_viewer/web/text_layer_builder.js", "webpack://pdfjs-dist/web/pdf_viewer/web/download_manager.js", "webpack://pdfjs-dist/web/pdf_viewer/web/app_options.js", "webpack://pdfjs-dist/web/pdf_viewer/web/genericl10n.js", "webpack://pdfjs-dist/web/pdf_viewer/external/webL10n/l10n.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_find_controller.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_find_utils.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_history.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_page_view.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_rendering_queue.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_scripting_manager.js", "webpack://pdfjs-dist/web/pdf_viewer/web/generic_scripting.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_single_page_viewer.js", "webpack://pdfjs-dist/web/pdf_viewer/web/base_viewer.js", "webpack://pdfjs-dist/web/pdf_viewer/web/struct_tree_layer_builder.js", "webpack://pdfjs-dist/web/pdf_viewer/web/xfa_layer_builder.js", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_viewer.js", "webpack://pdfjs-dist/web/pdf_viewer/webpack/bootstrap", "webpack://pdfjs-dist/web/pdf_viewer/web/pdf_viewer.component.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"pdfjs-dist/web/pdf_viewer\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"pdfjs-dist/web/pdf_viewer\"] = factory();\n\telse\n\t\troot[\"pdfjs-dist/web/pdf_viewer\"] = factory();\n})(this, function() {\nreturn ", "/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AnnotationLayer } from \"pdfjs-lib\";\nimport { NullL10n } from \"./l10n_utils.js\";\nimport { SimpleLinkService } from \"./pdf_link_service.js\";\n\n/**\n * @typedef {Object} AnnotationLayerBuilderOptions\n * @property {HTMLDivElement} pageDiv\n * @property {PDFPage} pdfPage\n * @property {AnnotationStorage} [annotationStorage]\n * @property {string} [imageResourcesPath] - Path for image resources, mainly\n *   for annotation icons. Include trailing slash.\n * @property {boolean} renderInteractiveForms\n * @property {IPDFLinkService} linkService\n * @property {DownloadManager} downloadManager\n * @property {IL10n} l10n - Localization service.\n * @property {boolean} [enableScripting]\n * @property {Promise<boolean>} [hasJSActionsPromise]\n * @property {Object} [mouseState]\n */\n\nclass AnnotationLayerBuilder {\n  /**\n   * @param {AnnotationLayerBuilderOptions} options\n   */\n  constructor({\n    pageDiv,\n    pdfPage,\n    linkService,\n    downloadManager,\n    annotationStorage = null,\n    imageResourcesPath = \"\",\n    renderInteractiveForms = true,\n    l10n = NullL10n,\n    enableScripting = false,\n    hasJSActionsPromise = null,\n    mouseState = null,\n  }) {\n    this.pageDiv = pageDiv;\n    this.pdfPage = pdfPage;\n    this.linkService = linkService;\n    this.downloadManager = downloadManager;\n    this.imageResourcesPath = imageResourcesPath;\n    this.renderInteractiveForms = renderInteractiveForms;\n    this.l10n = l10n;\n    this.annotationStorage = annotationStorage;\n    this.enableScripting = enableScripting;\n    this._hasJSActionsPromise = hasJSActionsPromise;\n    this._mouseState = mouseState;\n\n    this.div = null;\n    this._cancelled = false;\n  }\n\n  /**\n   * @param {PageViewport} viewport\n   * @param {string} intent (default value is 'display')\n   * @returns {Promise<void>} A promise that is resolved when rendering of the\n   *   annotations is complete.\n   */\n  render(viewport, intent = \"display\") {\n    return Promise.all([\n      this.pdfPage.getAnnotations({ intent }),\n      this._hasJSActionsPromise,\n    ]).then(([annotations, hasJSActions = false]) => {\n      if (this._cancelled) {\n        return;\n      }\n      if (annotations.length === 0) {\n        return;\n      }\n\n      const parameters = {\n        viewport: viewport.clone({ dontFlip: true }),\n        div: this.div,\n        annotations,\n        page: this.pdfPage,\n        imageResourcesPath: this.imageResourcesPath,\n        renderInteractiveForms: this.renderInteractiveForms,\n        linkService: this.linkService,\n        downloadManager: this.downloadManager,\n        annotationStorage: this.annotationStorage,\n        enableScripting: this.enableScripting,\n        hasJSActions,\n        mouseState: this._mouseState,\n      };\n\n      if (this.div) {\n        // If an annotationLayer already exists, refresh its children's\n        // transformation matrices.\n        AnnotationLayer.update(parameters);\n      } else {\n        // Create an annotation layer div and render the annotations\n        // if there is at least one annotation.\n        this.div = document.createElement(\"div\");\n        this.div.className = \"annotationLayer\";\n        this.pageDiv.appendChild(this.div);\n        parameters.div = this.div;\n\n        AnnotationLayer.render(parameters);\n        this.l10n.translate(this.div);\n      }\n    });\n  }\n\n  cancel() {\n    this._cancelled = true;\n  }\n\n  hide() {\n    if (!this.div) {\n      return;\n    }\n    this.div.hidden = true;\n  }\n}\n\n/**\n * @implements IPDFAnnotationLayerFactory\n */\nclass DefaultAnnotationLayerFactory {\n  /**\n   * @param {HTMLDivElement} pageDiv\n   * @param {PDFPage} pdfPage\n   * @param {AnnotationStorage} [annotationStorage]\n   * @param {string} [imageResourcesPath] - Path for image resources, mainly\n   *   for annotation icons. Include trailing slash.\n   * @param {boolean} renderInteractiveForms\n   * @param {IL10n} l10n\n   * @param {boolean} [enableScripting]\n   * @param {Promise<boolean>} [hasJSActionsPromise]\n   * @param {Object} [mouseState]\n   * @returns {AnnotationLayerBuilder}\n   */\n  createAnnotationLayerBuilder(\n    pageDiv,\n    pdfPage,\n    annotationStorage = null,\n    imageResourcesPath = \"\",\n    renderInteractiveForms = true,\n    l10n = NullL10n,\n    enableScripting = false,\n    hasJSActionsPromise = null,\n    mouseState = null\n  ) {\n    return new AnnotationLayerBuilder({\n      pageDiv,\n      pdfPage,\n      imageResourcesPath,\n      renderInteractiveForms,\n      linkService: new SimpleLinkService(),\n      l10n,\n      annotationStorage,\n      enableScripting,\n      hasJSActionsPromise,\n      mouseState,\n    });\n  }\n}\n\nexport { AnnotationLayerBuilder, DefaultAnnotationLayerFactory };\n", "/* Copyright 2016 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/* globals module, __non_webpack_require__ */\n\n\"use strict\";\n\nlet pdfjsLib;\nif (typeof window !== \"undefined\" && window[\"pdfjs-dist/build/pdf\"]) {\n  pdfjsLib = window[\"pdfjs-dist/build/pdf\"];\n} else {\n  pdfjsLib = __non_webpack_require__(\"../build/pdf.js\");\n}\nmodule.exports = pdfjsLib;\n", "/* Copyright 2021 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * A subset of the l10n strings in the `l10n/en-US/viewer.properties` file.\n */\nconst DEFAULT_L10N_STRINGS = {\n  of_pages: \"of {{pagesCount}}\",\n  page_of_pages: \"({{pageNumber}} of {{pagesCount}})\",\n\n  document_properties_kb: \"{{size_kb}} KB ({{size_b}} bytes)\",\n  document_properties_mb: \"{{size_mb}} MB ({{size_b}} bytes)\",\n  document_properties_date_string: \"{{date}}, {{time}}\",\n  document_properties_page_size_unit_inches: \"in\",\n  document_properties_page_size_unit_millimeters: \"mm\",\n  document_properties_page_size_orientation_portrait: \"portrait\",\n  document_properties_page_size_orientation_landscape: \"landscape\",\n  document_properties_page_size_name_a3: \"A3\",\n  document_properties_page_size_name_a4: \"A4\",\n  document_properties_page_size_name_letter: \"Letter\",\n  document_properties_page_size_name_legal: \"Legal\",\n  document_properties_page_size_dimension_string:\n    \"{{width}} × {{height}} {{unit}} ({{orientation}})\",\n  document_properties_page_size_dimension_name_string:\n    \"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})\",\n  document_properties_linearized_yes: \"Yes\",\n  document_properties_linearized_no: \"No\",\n\n  print_progress_percent: \"{{progress}}%\",\n\n  \"toggle_sidebar.title\": \"Toggle Sidebar\",\n  \"toggle_sidebar_notification2.title\":\n    \"Toggle Sidebar (document contains outline/attachments/layers)\",\n\n  additional_layers: \"Additional Layers\",\n  page_landmark: \"Page {{page}}\",\n  thumb_page_title: \"Page {{page}}\",\n  thumb_page_canvas: \"Thumbnail of Page {{page}}\",\n\n  find_reached_top: \"Reached top of document, continued from bottom\",\n  find_reached_bottom: \"Reached end of document, continued from top\",\n  \"find_match_count[one]\": \"{{current}} of {{total}} match\",\n  \"find_match_count[other]\": \"{{current}} of {{total}} matches\",\n  \"find_match_count_limit[one]\": \"More than {{limit}} match\",\n  \"find_match_count_limit[other]\": \"More than {{limit}} matches\",\n  find_not_found: \"Phrase not found\",\n\n  error_version_info: \"PDF.js v{{version}} (build: {{build}})\",\n  error_message: \"Message: {{message}}\",\n  error_stack: \"Stack: {{stack}}\",\n  error_file: \"File: {{file}}\",\n  error_line: \"Line: {{line}}\",\n  rendering_error: \"An error occurred while rendering the page.\",\n\n  page_scale_width: \"Page Width\",\n  page_scale_fit: \"Page Fit\",\n  page_scale_auto: \"Automatic Zoom\",\n  page_scale_actual: \"Actual Size\",\n  page_scale_percent: \"{{scale}}%\",\n\n  loading: \"Loading…\",\n  loading_error: \"An error occurred while loading the PDF.\",\n  invalid_file_error: \"Invalid or corrupted PDF file.\",\n  missing_file_error: \"Missing PDF file.\",\n  unexpected_response_error: \"Unexpected server response.\",\n\n  printing_not_supported:\n    \"Warning: Printing is not fully supported by this browser.\",\n  printing_not_ready: \"Warning: The PDF is not fully loaded for printing.\",\n  web_fonts_disabled:\n    \"Web fonts are disabled: unable to use embedded PDF fonts.\",\n};\n\nfunction getL10nFallback(key, args) {\n  switch (key) {\n    case \"find_match_count\":\n      key = `find_match_count[${args.total === 1 ? \"one\" : \"other\"}]`;\n      break;\n    case \"find_match_count_limit\":\n      key = `find_match_count_limit[${args.limit === 1 ? \"one\" : \"other\"}]`;\n      break;\n  }\n  return DEFAULT_L10N_STRINGS[key] || \"\";\n}\n\nconst PARTIAL_LANG_CODES = {\n  en: \"en-US\",\n  es: \"es-ES\",\n  fy: \"fy-NL\",\n  ga: \"ga-IE\",\n  gu: \"gu-IN\",\n  hi: \"hi-IN\",\n  hy: \"hy-AM\",\n  nb: \"nb-NO\",\n  ne: \"ne-NP\",\n  nn: \"nn-NO\",\n  pa: \"pa-IN\",\n  pt: \"pt-PT\",\n  sv: \"sv-SE\",\n  zh: \"zh-CN\",\n};\n\n// Try to support \"incompletely\" specified language codes (see issue 13689).\nfunction fixupLangCode(langCode) {\n  return PARTIAL_LANG_CODES[langCode?.toLowerCase()] || langCode;\n}\n\n// Replaces {{arguments}} with their values.\nfunction formatL10nValue(text, args) {\n  if (!args) {\n    return text;\n  }\n  return text.replace(/\\{\\{\\s*(\\w+)\\s*\\}\\}/g, (all, name) => {\n    return name in args ? args[name] : \"{{\" + name + \"}}\";\n  });\n}\n\n/**\n * No-op implementation of the localization service.\n * @implements {IL10n}\n */\nconst NullL10n = {\n  async getLanguage() {\n    return \"en-us\";\n  },\n\n  async getDirection() {\n    return \"ltr\";\n  },\n\n  async get(key, args = null, fallback = getL10nFallback(key, args)) {\n    return formatL10nValue(fallback, args);\n  },\n\n  async translate(element) {},\n};\n\nexport { fixupLangCode, getL10nFallback, NullL10n };\n", "/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { parseQueryString } from \"./ui_utils.js\";\n\n/**\n * @typedef {Object} PDFLinkServiceOptions\n * @property {EventBus} eventBus - The application event bus.\n * @property {number} [externalLinkTarget] - Specifies the `target` attribute\n *   for external links. Must use one of the values from {LinkTarget}.\n *   Defaults to using no target.\n * @property {string} [externalLinkRel] - Specifies the `rel` attribute for\n *   external links. Defaults to stripping the referrer.\n * @property {boolean} [ignoreDestinationZoom] - Ignores the zoom argument,\n *   thus preserving the current zoom level in the viewer, when navigating\n *   to internal destinations. The default value is `false`.\n */\n\n/**\n * Performs navigation functions inside PDF, such as opening specified page,\n * or destination.\n * @implements {IPDFLinkService}\n */\nclass PDFLinkService {\n  /**\n   * @param {PDFLinkServiceOptions} options\n   */\n  constructor({\n    eventBus,\n    externalLinkTarget = null,\n    externalLinkRel = null,\n    ignoreDestinationZoom = false,\n  } = {}) {\n    this.eventBus = eventBus;\n    this.externalLinkTarget = externalLinkTarget;\n    this.externalLinkRel = externalLinkRel;\n    this.externalLinkEnabled = true;\n    this._ignoreDestinationZoom = ignoreDestinationZoom;\n\n    this.baseUrl = null;\n    this.pdfDocument = null;\n    this.pdfViewer = null;\n    this.pdfHistory = null;\n\n    this._pagesRefCache = null;\n  }\n\n  setDocument(pdfDocument, baseUrl = null) {\n    this.baseUrl = baseUrl;\n    this.pdfDocument = pdfDocument;\n    this._pagesRefCache = Object.create(null);\n  }\n\n  setViewer(pdfViewer) {\n    this.pdfViewer = pdfViewer;\n  }\n\n  setHistory(pdfHistory) {\n    this.pdfHistory = pdfHistory;\n  }\n\n  /**\n   * @type {number}\n   */\n  get pagesCount() {\n    return this.pdfDocument ? this.pdfDocument.numPages : 0;\n  }\n\n  /**\n   * @type {number}\n   */\n  get page() {\n    return this.pdfViewer.currentPageNumber;\n  }\n\n  /**\n   * @param {number} value\n   */\n  set page(value) {\n    this.pdfViewer.currentPageNumber = value;\n  }\n\n  /**\n   * @type {number}\n   */\n  get rotation() {\n    return this.pdfViewer.pagesRotation;\n  }\n\n  /**\n   * @param {number} value\n   */\n  set rotation(value) {\n    this.pdfViewer.pagesRotation = value;\n  }\n\n  /**\n   * @private\n   */\n  _goToDestinationHelper(rawDest, namedDest = null, explicitDest) {\n    // Dest array looks like that: <page-ref> </XYZ|/FitXXX> <args..>\n    const destRef = explicitDest[0];\n    let pageNumber;\n\n    if (typeof destRef === \"object\" && destRef !== null) {\n      pageNumber = this._cachedPageNumber(destRef);\n\n      if (pageNumber === null) {\n        // Fetch the page reference if it's not yet available. This could\n        // only occur during loading, before all pages have been resolved.\n        this.pdfDocument\n          .getPageIndex(destRef)\n          .then(pageIndex => {\n            this.cachePageRef(pageIndex + 1, destRef);\n            this._goToDestinationHelper(rawDest, namedDest, explicitDest);\n          })\n          .catch(() => {\n            console.error(\n              `PDFLinkService._goToDestinationHelper: \"${destRef}\" is not ` +\n                `a valid page reference, for dest=\"${rawDest}\".`\n            );\n          });\n        return;\n      }\n    } else if (Number.isInteger(destRef)) {\n      pageNumber = destRef + 1;\n    } else {\n      console.error(\n        `PDFLinkService._goToDestinationHelper: \"${destRef}\" is not ` +\n          `a valid destination reference, for dest=\"${rawDest}\".`\n      );\n      return;\n    }\n    if (!pageNumber || pageNumber < 1 || pageNumber > this.pagesCount) {\n      console.error(\n        `PDFLinkService._goToDestinationHelper: \"${pageNumber}\" is not ` +\n          `a valid page number, for dest=\"${rawDest}\".`\n      );\n      return;\n    }\n\n    if (this.pdfHistory) {\n      // Update the browser history before scrolling the new destination into\n      // view, to be able to accurately capture the current document position.\n      this.pdfHistory.pushCurrentPosition();\n      this.pdfHistory.push({ namedDest, explicitDest, pageNumber });\n    }\n\n    this.pdfViewer.scrollPageIntoView({\n      pageNumber,\n      destArray: explicitDest,\n      ignoreDestinationZoom: this._ignoreDestinationZoom,\n    });\n  }\n\n  /**\n   * This method will, when available, also update the browser history.\n   *\n   * @param {string|Array} dest - The named, or explicit, PDF destination.\n   */\n  async goToDestination(dest) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    let namedDest, explicitDest;\n    if (typeof dest === \"string\") {\n      namedDest = dest;\n      explicitDest = await this.pdfDocument.getDestination(dest);\n    } else {\n      namedDest = null;\n      explicitDest = await dest;\n    }\n    if (!Array.isArray(explicitDest)) {\n      console.error(\n        `PDFLinkService.goToDestination: \"${explicitDest}\" is not ` +\n          `a valid destination array, for dest=\"${dest}\".`\n      );\n      return;\n    }\n    this._goToDestinationHelper(dest, namedDest, explicitDest);\n  }\n\n  /**\n   * This method will, when available, also update the browser history.\n   *\n   * @param {number|string} val - The page number, or page label.\n   */\n  goToPage(val) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    const pageNumber =\n      (typeof val === \"string\" && this.pdfViewer.pageLabelToPageNumber(val)) ||\n      val | 0;\n    if (\n      !(\n        Number.isInteger(pageNumber) &&\n        pageNumber > 0 &&\n        pageNumber <= this.pagesCount\n      )\n    ) {\n      console.error(`PDFLinkService.goToPage: \"${val}\" is not a valid page.`);\n      return;\n    }\n\n    if (this.pdfHistory) {\n      // Update the browser history before scrolling the new page into view,\n      // to be able to accurately capture the current document position.\n      this.pdfHistory.pushCurrentPosition();\n      this.pdfHistory.pushPage(pageNumber);\n    }\n\n    this.pdfViewer.scrollPageIntoView({ pageNumber });\n  }\n\n  /**\n   * @param {string|Array} dest - The PDF destination object.\n   * @returns {string} The hyperlink to the PDF object.\n   */\n  getDestinationHash(dest) {\n    if (typeof dest === \"string\") {\n      if (dest.length > 0) {\n        return this.getAnchorUrl(\"#\" + escape(dest));\n      }\n    } else if (Array.isArray(dest)) {\n      const str = JSON.stringify(dest);\n      if (str.length > 0) {\n        return this.getAnchorUrl(\"#\" + escape(str));\n      }\n    }\n    return this.getAnchorUrl(\"\");\n  }\n\n  /**\n   * Prefix the full url on anchor links to make sure that links are resolved\n   * relative to the current URL instead of the one defined in <base href>.\n   * @param {string} anchor - The anchor hash, including the #.\n   * @returns {string} The hyperlink to the PDF object.\n   */\n  getAnchorUrl(anchor) {\n    return (this.baseUrl || \"\") + anchor;\n  }\n\n  /**\n   * @param {string} hash\n   */\n  setHash(hash) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    let pageNumber, dest;\n    if (hash.includes(\"=\")) {\n      const params = parseQueryString(hash);\n      if (params.has(\"search\")) {\n        this.eventBus.dispatch(\"findfromurlhash\", {\n          source: this,\n          query: params.get(\"search\").replace(/\"/g, \"\"),\n          phraseSearch: params.get(\"phrase\") === \"true\",\n        });\n      }\n      // borrowing syntax from \"Parameters for Opening PDF Files\"\n      if (params.has(\"page\")) {\n        pageNumber = params.get(\"page\") | 0 || 1;\n      }\n      if (params.has(\"zoom\")) {\n        // Build the destination array.\n        const zoomArgs = params.get(\"zoom\").split(\",\"); // scale,left,top\n        const zoomArg = zoomArgs[0];\n        const zoomArgNumber = parseFloat(zoomArg);\n\n        if (!zoomArg.includes(\"Fit\")) {\n          // If the zoomArg is a number, it has to get divided by 100. If it's\n          // a string, it should stay as it is.\n          dest = [\n            null,\n            { name: \"XYZ\" },\n            zoomArgs.length > 1 ? zoomArgs[1] | 0 : null,\n            zoomArgs.length > 2 ? zoomArgs[2] | 0 : null,\n            zoomArgNumber ? zoomArgNumber / 100 : zoomArg,\n          ];\n        } else {\n          if (zoomArg === \"Fit\" || zoomArg === \"FitB\") {\n            dest = [null, { name: zoomArg }];\n          } else if (\n            zoomArg === \"FitH\" ||\n            zoomArg === \"FitBH\" ||\n            zoomArg === \"FitV\" ||\n            zoomArg === \"FitBV\"\n          ) {\n            dest = [\n              null,\n              { name: zoomArg },\n              zoomArgs.length > 1 ? zoomArgs[1] | 0 : null,\n            ];\n          } else if (zoomArg === \"FitR\") {\n            if (zoomArgs.length !== 5) {\n              console.error(\n                'PDFLinkService.setHash: Not enough parameters for \"FitR\".'\n              );\n            } else {\n              dest = [\n                null,\n                { name: zoomArg },\n                zoomArgs[1] | 0,\n                zoomArgs[2] | 0,\n                zoomArgs[3] | 0,\n                zoomArgs[4] | 0,\n              ];\n            }\n          } else {\n            console.error(\n              `PDFLinkService.setHash: \"${zoomArg}\" is not ` +\n                \"a valid zoom value.\"\n            );\n          }\n        }\n      }\n      if (dest) {\n        this.pdfViewer.scrollPageIntoView({\n          pageNumber: pageNumber || this.page,\n          destArray: dest,\n          allowNegativeOffset: true,\n        });\n      } else if (pageNumber) {\n        this.page = pageNumber; // simple page\n      }\n      if (params.has(\"pagemode\")) {\n        this.eventBus.dispatch(\"pagemode\", {\n          source: this,\n          mode: params.get(\"pagemode\"),\n        });\n      }\n      // Ensure that this parameter is *always* handled last, in order to\n      // guarantee that it won't be overridden (e.g. by the \"page\" parameter).\n      if (params.has(\"nameddest\")) {\n        this.goToDestination(params.get(\"nameddest\"));\n      }\n    } else {\n      // Named (or explicit) destination.\n      dest = unescape(hash);\n      try {\n        dest = JSON.parse(dest);\n\n        if (!Array.isArray(dest)) {\n          // Avoid incorrectly rejecting a valid named destination, such as\n          // e.g. \"4.3\" or \"true\", because `JSON.parse` converted its type.\n          dest = dest.toString();\n        }\n      } catch (ex) {}\n\n      if (typeof dest === \"string\" || isValidExplicitDestination(dest)) {\n        this.goToDestination(dest);\n        return;\n      }\n      console.error(\n        `PDFLinkService.setHash: \"${unescape(hash)}\" is not ` +\n          \"a valid destination.\"\n      );\n    }\n  }\n\n  /**\n   * @param {string} action\n   */\n  executeNamedAction(action) {\n    // See PDF reference, table 8.45 - Named action\n    switch (action) {\n      case \"GoBack\":\n        if (this.pdfHistory) {\n          this.pdfHistory.back();\n        }\n        break;\n\n      case \"GoForward\":\n        if (this.pdfHistory) {\n          this.pdfHistory.forward();\n        }\n        break;\n\n      case \"NextPage\":\n        this.pdfViewer.nextPage();\n        break;\n\n      case \"PrevPage\":\n        this.pdfViewer.previousPage();\n        break;\n\n      case \"LastPage\":\n        this.page = this.pagesCount;\n        break;\n\n      case \"FirstPage\":\n        this.page = 1;\n        break;\n\n      default:\n        break; // No action according to spec\n    }\n\n    this.eventBus.dispatch(\"namedaction\", {\n      source: this,\n      action,\n    });\n  }\n\n  /**\n   * @param {number} pageNum - page number.\n   * @param {Object} pageRef - reference to the page.\n   */\n  cachePageRef(pageNum, pageRef) {\n    if (!pageRef) {\n      return;\n    }\n    const refStr =\n      pageRef.gen === 0 ? `${pageRef.num}R` : `${pageRef.num}R${pageRef.gen}`;\n    this._pagesRefCache[refStr] = pageNum;\n  }\n\n  /**\n   * @private\n   */\n  _cachedPageNumber(pageRef) {\n    const refStr =\n      pageRef.gen === 0 ? `${pageRef.num}R` : `${pageRef.num}R${pageRef.gen}`;\n    return this._pagesRefCache?.[refStr] || null;\n  }\n\n  /**\n   * @param {number} pageNumber\n   */\n  isPageVisible(pageNumber) {\n    return this.pdfViewer.isPageVisible(pageNumber);\n  }\n\n  /**\n   * @param {number} pageNumber\n   */\n  isPageCached(pageNumber) {\n    return this.pdfViewer.isPageCached(pageNumber);\n  }\n}\n\nfunction isValidExplicitDestination(dest) {\n  if (!Array.isArray(dest)) {\n    return false;\n  }\n  const destLength = dest.length;\n  if (destLength < 2) {\n    return false;\n  }\n  const page = dest[0];\n  if (\n    !(\n      typeof page === \"object\" &&\n      Number.isInteger(page.num) &&\n      Number.isInteger(page.gen)\n    ) &&\n    !(Number.isInteger(page) && page >= 0)\n  ) {\n    return false;\n  }\n  const zoom = dest[1];\n  if (!(typeof zoom === \"object\" && typeof zoom.name === \"string\")) {\n    return false;\n  }\n  let allowNull = true;\n  switch (zoom.name) {\n    case \"XYZ\":\n      if (destLength !== 5) {\n        return false;\n      }\n      break;\n    case \"Fit\":\n    case \"FitB\":\n      return destLength === 2;\n    case \"FitH\":\n    case \"FitBH\":\n    case \"FitV\":\n    case \"FitBV\":\n      if (destLength !== 3) {\n        return false;\n      }\n      break;\n    case \"FitR\":\n      if (destLength !== 6) {\n        return false;\n      }\n      allowNull = false;\n      break;\n    default:\n      return false;\n  }\n  for (let i = 2; i < destLength; i++) {\n    const param = dest[i];\n    if (!(typeof param === \"number\" || (allowNull && param === null))) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * @implements {IPDFLinkService}\n */\nclass SimpleLinkService {\n  constructor() {\n    this.externalLinkTarget = null;\n    this.externalLinkRel = null;\n    this.externalLinkEnabled = true;\n    this._ignoreDestinationZoom = false;\n  }\n\n  /**\n   * @type {number}\n   */\n  get pagesCount() {\n    return 0;\n  }\n\n  /**\n   * @type {number}\n   */\n  get page() {\n    return 0;\n  }\n\n  /**\n   * @param {number} value\n   */\n  set page(value) {}\n\n  /**\n   * @type {number}\n   */\n  get rotation() {\n    return 0;\n  }\n\n  /**\n   * @param {number} value\n   */\n  set rotation(value) {}\n\n  /**\n   * @param {string|Array} dest - The named, or explicit, PDF destination.\n   */\n  async goToDestination(dest) {}\n\n  /**\n   * @param {number|string} val - The page number, or page label.\n   */\n  goToPage(val) {}\n\n  /**\n   * @param dest - The PDF destination object.\n   * @returns {string} The hyperlink to the PDF object.\n   */\n  getDestinationHash(dest) {\n    return \"#\";\n  }\n\n  /**\n   * @param hash - The PDF parameters/hash.\n   * @returns {string} The hyperlink to the PDF object.\n   */\n  getAnchorUrl(hash) {\n    return \"#\";\n  }\n\n  /**\n   * @param {string} hash\n   */\n  setHash(hash) {}\n\n  /**\n   * @param {string} action\n   */\n  executeNamedAction(action) {}\n\n  /**\n   * @param {number} pageNum - page number.\n   * @param {Object} pageRef - reference to the page.\n   */\n  cachePageRef(pageNum, pageRef) {}\n\n  /**\n   * @param {number} pageNumber\n   */\n  isPageVisible(pageNumber) {\n    return true;\n  }\n\n  /**\n   * @param {number} pageNumber\n   */\n  isPageCached(pageNumber) {\n    return true;\n  }\n}\n\nexport { PDFLinkService, SimpleLinkService };\n", "/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst CSS_UNITS = 96.0 / 72.0;\nconst DEFAULT_SCALE_VALUE = \"auto\";\nconst DEFAULT_SCALE = 1.0;\nconst MIN_SCALE = 0.1;\nconst MAX_SCALE = 10.0;\nconst UNKNOWN_SCALE = 0;\nconst MAX_AUTO_SCALE = 1.25;\nconst SCROLLBAR_PADDING = 40;\nconst VERTICAL_PADDING = 5;\n\nconst LOADINGBAR_END_OFFSET_VAR = \"--loadingBar-end-offset\";\n\nconst PresentationModeState = {\n  UNKNOWN: 0,\n  NORMAL: 1,\n  CHANGING: 2,\n  FULLSCREEN: 3,\n};\n\nconst SidebarView = {\n  UNKNOWN: -1,\n  NONE: 0,\n  THUMBS: 1, // Default value.\n  OUTLINE: 2,\n  ATTACHMENTS: 3,\n  LAYERS: 4,\n};\n\nconst RendererType = {\n  CANVAS: \"canvas\",\n  SVG: \"svg\",\n};\n\nconst TextLayerMode = {\n  DISABLE: 0,\n  ENABLE: 1,\n  ENABLE_ENHANCE: 2,\n};\n\nconst ScrollMode = {\n  UNKNOWN: -1,\n  VERTICAL: 0, // Default value.\n  HORIZONTAL: 1,\n  WRAPPED: 2,\n};\n\nconst SpreadMode = {\n  UNKNOWN: -1,\n  NONE: 0, // Default value.\n  ODD: 1,\n  EVEN: 2,\n};\n\n// Used by `PDFViewerApplication`, and by the API unit-tests.\nconst AutoPrintRegExp = /\\bprint\\s*\\(/;\n\n/**\n * Returns scale factor for the canvas. It makes sense for the HiDPI displays.\n * @returns {Object} The object with horizontal (sx) and vertical (sy)\n *                   scales. The scaled property is set to false if scaling is\n *                   not required, true otherwise.\n */\nfunction getOutputScale(ctx) {\n  const devicePixelRatio = window.devicePixelRatio || 1;\n  const backingStoreRatio =\n    ctx.webkitBackingStorePixelRatio ||\n    ctx.mozBackingStorePixelRatio ||\n    ctx.backingStorePixelRatio ||\n    1;\n  const pixelRatio = devicePixelRatio / backingStoreRatio;\n  return {\n    sx: pixelRatio,\n    sy: pixelRatio,\n    scaled: pixelRatio !== 1,\n  };\n}\n\n/**\n * Scrolls specified element into view of its parent.\n * @param {Object} element - The element to be visible.\n * @param {Object} spot - An object with optional top and left properties,\n *   specifying the offset from the top left edge.\n * @param {boolean} [scrollMatches] - When scrolling search results into view,\n *   ignore elements that either: Contains marked content identifiers,\n *   or have the CSS-rule `overflow: hidden;` set. The default value is `false`.\n */\nfunction scrollIntoView(element, spot, scrollMatches = false) {\n  // Assuming offsetParent is available (it's not available when viewer is in\n  // hidden iframe or object). We have to scroll: if the offsetParent is not set\n  // producing the error. See also animationStarted.\n  let parent = element.offsetParent;\n  if (!parent) {\n    console.error(\"offsetParent is not set -- cannot scroll\");\n    return;\n  }\n  let offsetY = element.offsetTop + element.clientTop;\n  let offsetX = element.offsetLeft + element.clientLeft;\n  while (\n    (parent.clientHeight === parent.scrollHeight &&\n      parent.clientWidth === parent.scrollWidth) ||\n    (scrollMatches &&\n      (parent.classList.contains(\"markedContent\") ||\n        getComputedStyle(parent).overflow === \"hidden\"))\n  ) {\n    offsetY += parent.offsetTop;\n    offsetX += parent.offsetLeft;\n\n    parent = parent.offsetParent;\n    if (!parent) {\n      return; // no need to scroll\n    }\n  }\n  if (spot) {\n    if (spot.top !== undefined) {\n      offsetY += spot.top;\n    }\n    if (spot.left !== undefined) {\n      offsetX += spot.left;\n      parent.scrollLeft = offsetX;\n    }\n  }\n  parent.scrollTop = offsetY;\n}\n\n/**\n * Helper function to start monitoring the scroll event and converting them into\n * PDF.js friendly one: with scroll debounce and scroll direction.\n */\nfunction watchScroll(viewAreaElement, callback) {\n  const debounceScroll = function (evt) {\n    if (rAF) {\n      return;\n    }\n    // schedule an invocation of scroll for next animation frame.\n    rAF = window.requestAnimationFrame(function viewAreaElementScrolled() {\n      rAF = null;\n\n      const currentX = viewAreaElement.scrollLeft;\n      const lastX = state.lastX;\n      if (currentX !== lastX) {\n        state.right = currentX > lastX;\n      }\n      state.lastX = currentX;\n      const currentY = viewAreaElement.scrollTop;\n      const lastY = state.lastY;\n      if (currentY !== lastY) {\n        state.down = currentY > lastY;\n      }\n      state.lastY = currentY;\n      callback(state);\n    });\n  };\n\n  const state = {\n    right: true,\n    down: true,\n    lastX: viewAreaElement.scrollLeft,\n    lastY: viewAreaElement.scrollTop,\n    _eventHandler: debounceScroll,\n  };\n\n  let rAF = null;\n  viewAreaElement.addEventListener(\"scroll\", debounceScroll, true);\n  return state;\n}\n\n/**\n * Helper function to parse query string (e.g. ?param1=value&param2=...).\n * @param {string}\n * @returns {Map}\n */\nfunction parseQueryString(query) {\n  const params = new Map();\n  for (const part of query.split(\"&\")) {\n    const param = part.split(\"=\"),\n      key = param[0].toLowerCase(),\n      value = param.length > 1 ? param[1] : \"\";\n    params.set(decodeURIComponent(key), decodeURIComponent(value));\n  }\n  return params;\n}\n\n/**\n * Use binary search to find the index of the first item in a given array which\n * passes a given condition. The items are expected to be sorted in the sense\n * that if the condition is true for one item in the array, then it is also true\n * for all following items.\n *\n * @returns {number} Index of the first array element to pass the test,\n *                   or |items.length| if no such element exists.\n */\nfunction binarySearchFirstItem(items, condition) {\n  let minIndex = 0;\n  let maxIndex = items.length - 1;\n\n  if (maxIndex < 0 || !condition(items[maxIndex])) {\n    return items.length;\n  }\n  if (condition(items[minIndex])) {\n    return minIndex;\n  }\n\n  while (minIndex < maxIndex) {\n    const currentIndex = (minIndex + maxIndex) >> 1;\n    const currentItem = items[currentIndex];\n    if (condition(currentItem)) {\n      maxIndex = currentIndex;\n    } else {\n      minIndex = currentIndex + 1;\n    }\n  }\n  return minIndex; /* === maxIndex */\n}\n\n/**\n *  Approximates float number as a fraction using Farey sequence (max order\n *  of 8).\n *  @param {number} x - Positive float number.\n *  @returns {Array} Estimated fraction: the first array item is a numerator,\n *                   the second one is a denominator.\n */\nfunction approximateFraction(x) {\n  // Fast paths for int numbers or their inversions.\n  if (Math.floor(x) === x) {\n    return [x, 1];\n  }\n  const xinv = 1 / x;\n  const limit = 8;\n  if (xinv > limit) {\n    return [1, limit];\n  } else if (Math.floor(xinv) === xinv) {\n    return [1, xinv];\n  }\n\n  const x_ = x > 1 ? xinv : x;\n  // a/b and c/d are neighbours in Farey sequence.\n  let a = 0,\n    b = 1,\n    c = 1,\n    d = 1;\n  // Limiting search to order 8.\n  while (true) {\n    // Generating next term in sequence (order of q).\n    const p = a + c,\n      q = b + d;\n    if (q > limit) {\n      break;\n    }\n    if (x_ <= p / q) {\n      c = p;\n      d = q;\n    } else {\n      a = p;\n      b = q;\n    }\n  }\n  let result;\n  // Select closest of the neighbours to x.\n  if (x_ - a / b < c / d - x_) {\n    result = x_ === x ? [a, b] : [b, a];\n  } else {\n    result = x_ === x ? [c, d] : [d, c];\n  }\n  return result;\n}\n\nfunction roundToDivide(x, div) {\n  const r = x % div;\n  return r === 0 ? x : Math.round(x - r + div);\n}\n\n/**\n * Gets the size of the specified page, converted from PDF units to inches.\n * @param {Object} An Object containing the properties: {Array} `view`,\n *   {number} `userUnit`, and {number} `rotate`.\n * @returns {Object} An Object containing the properties: {number} `width`\n *   and {number} `height`, given in inches.\n */\nfunction getPageSizeInches({ view, userUnit, rotate }) {\n  const [x1, y1, x2, y2] = view;\n  // We need to take the page rotation into account as well.\n  const changeOrientation = rotate % 180 !== 0;\n\n  const width = ((x2 - x1) / 72) * userUnit;\n  const height = ((y2 - y1) / 72) * userUnit;\n\n  return {\n    width: changeOrientation ? height : width,\n    height: changeOrientation ? width : height,\n  };\n}\n\n/**\n * Helper function for getVisibleElements.\n *\n * @param {number} index - initial guess at the first visible element\n * @param {Array} views - array of pages, into which `index` is an index\n * @param {number} top - the top of the scroll pane\n * @returns {number} less than or equal to `index` that is definitely at or\n *   before the first visible element in `views`, but not by too much. (Usually,\n *   this will be the first element in the first partially visible row in\n *   `views`, although sometimes it goes back one row further.)\n */\nfunction backtrackBeforeAllVisibleElements(index, views, top) {\n  // binarySearchFirstItem's assumption is that the input is ordered, with only\n  // one index where the conditions flips from false to true: [false ...,\n  // true...]. With vertical scrolling and spreads, it is possible to have\n  // [false ..., true, false, true ...]. With wrapped scrolling we can have a\n  // similar sequence, with many more mixed true and false in the middle.\n  //\n  // So there is no guarantee that the binary search yields the index of the\n  // first visible element. It could have been any of the other visible elements\n  // that were preceded by a hidden element.\n\n  // Of course, if either this element or the previous (hidden) element is also\n  // the first element, there's nothing to worry about.\n  if (index < 2) {\n    return index;\n  }\n\n  // That aside, the possible cases are represented below.\n  //\n  //     ****  = fully hidden\n  //     A*B*  = mix of partially visible and/or hidden pages\n  //     CDEF  = fully visible\n  //\n  // (1) Binary search could have returned A, in which case we can stop.\n  // (2) Binary search could also have returned B, in which case we need to\n  // check the whole row.\n  // (3) Binary search could also have returned C, in which case we need to\n  // check the whole previous row.\n  //\n  // There's one other possibility:\n  //\n  //     ****  = fully hidden\n  //     ABCD  = mix of fully and/or partially visible pages\n  //\n  // (4) Binary search could only have returned A.\n\n  // Initially assume that we need to find the beginning of the current row\n  // (case 1, 2, or 4), which means finding a page that is above the current\n  // page's top. If the found page is partially visible, we're definitely not in\n  // case 3, and this assumption is correct.\n  let elt = views[index].div;\n  let pageTop = elt.offsetTop + elt.clientTop;\n\n  if (pageTop >= top) {\n    // The found page is fully visible, so we're actually either in case 3 or 4,\n    // and unfortunately we can't tell the difference between them without\n    // scanning the entire previous row, so we just conservatively assume that\n    // we do need to backtrack to that row. In both cases, the previous page is\n    // in the previous row, so use its top instead.\n    elt = views[index - 1].div;\n    pageTop = elt.offsetTop + elt.clientTop;\n  }\n\n  // Now we backtrack to the first page that still has its bottom below\n  // `pageTop`, which is the top of a page in the first visible row (unless\n  // we're in case 4, in which case it's the row before that).\n  // `index` is found by binary search, so the page at `index - 1` is\n  // invisible and we can start looking for potentially visible pages from\n  // `index - 2`. (However, if this loop terminates on its first iteration,\n  // which is the case when pages are stacked vertically, `index` should remain\n  // unchanged, so we use a distinct loop variable.)\n  for (let i = index - 2; i >= 0; --i) {\n    elt = views[i].div;\n    if (elt.offsetTop + elt.clientTop + elt.clientHeight <= pageTop) {\n      // We have reached the previous row, so stop now.\n      // This loop is expected to terminate relatively quickly because the\n      // number of pages per row is expected to be small.\n      break;\n    }\n    index = i;\n  }\n  return index;\n}\n\n/**\n * @typedef {Object} GetVisibleElementsParameters\n * @property {HTMLElement} scrollEl - A container that can possibly scroll.\n * @property {Array} views - Objects with a `div` property that contains an\n *   HTMLElement, which should all be descendants of `scrollEl` satisfying the\n *   relevant layout assumptions.\n * @property {boolean} sortByVisibility - If `true`, the returned elements are\n *   sorted in descending order of the percent of their padding box that is\n *   visible. The default value is `false`.\n * @property {boolean} horizontal - If `true`, the elements are assumed to be\n *   laid out horizontally instead of vertically. The default value is `false`.\n * @property {boolean} rtl - If `true`, the `scrollEl` container is assumed to\n *   be in right-to-left mode. The default value is `false`.\n */\n\n/**\n * Generic helper to find out what elements are visible within a scroll pane.\n *\n * Well, pretty generic. There are some assumptions placed on the elements\n * referenced by `views`:\n *   - If `horizontal`, no left of any earlier element is to the right of the\n *     left of any later element.\n *   - Otherwise, `views` can be split into contiguous rows where, within a row,\n *     no top of any element is below the bottom of any other element, and\n *     between rows, no bottom of any element in an earlier row is below the\n *     top of any element in a later row.\n *\n * (Here, top, left, etc. all refer to the padding edge of the element in\n * question. For pages, that ends up being equivalent to the bounding box of the\n * rendering canvas. Earlier and later refer to index in `views`, not page\n * layout.)\n *\n * @param {GetVisibleElementsParameters}\n * @returns {Object} `{ first, last, views: [{ id, x, y, view, percent }] }`\n */\nfunction getVisibleElements({\n  scrollEl,\n  views,\n  sortByVisibility = false,\n  horizontal = false,\n  rtl = false,\n}) {\n  const top = scrollEl.scrollTop,\n    bottom = top + scrollEl.clientHeight;\n  const left = scrollEl.scrollLeft,\n    right = left + scrollEl.clientWidth;\n\n  // Throughout this \"generic\" function, comments will assume we're working with\n  // PDF document pages, which is the most important and complex case. In this\n  // case, the visible elements we're actually interested is the page canvas,\n  // which is contained in a wrapper which adds no padding/border/margin, which\n  // is itself contained in `view.div` which adds no padding (but does add a\n  // border). So, as specified in this function's doc comment, this function\n  // does all of its work on the padding edge of the provided views, starting at\n  // offsetLeft/Top (which includes margin) and adding clientLeft/Top (which is\n  // the border). Adding clientWidth/Height gets us the bottom-right corner of\n  // the padding edge.\n  function isElementBottomAfterViewTop(view) {\n    const element = view.div;\n    const elementBottom =\n      element.offsetTop + element.clientTop + element.clientHeight;\n    return elementBottom > top;\n  }\n  function isElementNextAfterViewHorizontally(view) {\n    const element = view.div;\n    const elementLeft = element.offsetLeft + element.clientLeft;\n    const elementRight = elementLeft + element.clientWidth;\n    return rtl ? elementLeft < right : elementRight > left;\n  }\n\n  const visible = [],\n    numViews = views.length;\n  let firstVisibleElementInd = binarySearchFirstItem(\n    views,\n    horizontal\n      ? isElementNextAfterViewHorizontally\n      : isElementBottomAfterViewTop\n  );\n\n  // Please note the return value of the `binarySearchFirstItem` function when\n  // no valid element is found (hence the `firstVisibleElementInd` check below).\n  if (\n    firstVisibleElementInd > 0 &&\n    firstVisibleElementInd < numViews &&\n    !horizontal\n  ) {\n    // In wrapped scrolling (or vertical scrolling with spreads), with some page\n    // sizes, isElementBottomAfterViewTop doesn't satisfy the binary search\n    // condition: there can be pages with bottoms above the view top between\n    // pages with bottoms below. This function detects and corrects that error;\n    // see it for more comments.\n    firstVisibleElementInd = backtrackBeforeAllVisibleElements(\n      firstVisibleElementInd,\n      views,\n      top\n    );\n  }\n\n  // lastEdge acts as a cutoff for us to stop looping, because we know all\n  // subsequent pages will be hidden.\n  //\n  // When using wrapped scrolling or vertical scrolling with spreads, we can't\n  // simply stop the first time we reach a page below the bottom of the view;\n  // the tops of subsequent pages on the same row could still be visible. In\n  // horizontal scrolling, we don't have that issue, so we can stop as soon as\n  // we pass `right`, without needing the code below that handles the -1 case.\n  let lastEdge = horizontal ? right : -1;\n\n  for (let i = firstVisibleElementInd; i < numViews; i++) {\n    const view = views[i],\n      element = view.div;\n    const currentWidth = element.offsetLeft + element.clientLeft;\n    const currentHeight = element.offsetTop + element.clientTop;\n    const viewWidth = element.clientWidth,\n      viewHeight = element.clientHeight;\n    const viewRight = currentWidth + viewWidth;\n    const viewBottom = currentHeight + viewHeight;\n\n    if (lastEdge === -1) {\n      // As commented above, this is only needed in non-horizontal cases.\n      // Setting lastEdge to the bottom of the first page that is partially\n      // visible ensures that the next page fully below lastEdge is on the\n      // next row, which has to be fully hidden along with all subsequent rows.\n      if (viewBottom >= bottom) {\n        lastEdge = viewBottom;\n      }\n    } else if ((horizontal ? currentWidth : currentHeight) > lastEdge) {\n      break;\n    }\n\n    if (\n      viewBottom <= top ||\n      currentHeight >= bottom ||\n      viewRight <= left ||\n      currentWidth >= right\n    ) {\n      continue;\n    }\n\n    const hiddenHeight =\n      Math.max(0, top - currentHeight) + Math.max(0, viewBottom - bottom);\n    const hiddenWidth =\n      Math.max(0, left - currentWidth) + Math.max(0, viewRight - right);\n\n    const fractionHeight = (viewHeight - hiddenHeight) / viewHeight,\n      fractionWidth = (viewWidth - hiddenWidth) / viewWidth;\n    const percent = (fractionHeight * fractionWidth * 100) | 0;\n\n    visible.push({\n      id: view.id,\n      x: currentWidth,\n      y: currentHeight,\n      view,\n      percent,\n      widthPercent: (fractionWidth * 100) | 0,\n    });\n  }\n\n  const first = visible[0],\n    last = visible[visible.length - 1];\n\n  if (sortByVisibility) {\n    visible.sort(function (a, b) {\n      const pc = a.percent - b.percent;\n      if (Math.abs(pc) > 0.001) {\n        return -pc;\n      }\n      return a.id - b.id; // ensure stability\n    });\n  }\n  return { first, last, views: visible };\n}\n\n/**\n * Event handler to suppress context menu.\n */\nfunction noContextMenuHandler(evt) {\n  evt.preventDefault();\n}\n\nfunction normalizeWheelEventDirection(evt) {\n  let delta = Math.hypot(evt.deltaX, evt.deltaY);\n  const angle = Math.atan2(evt.deltaY, evt.deltaX);\n  if (-0.25 * Math.PI < angle && angle < 0.75 * Math.PI) {\n    // All that is left-up oriented has to change the sign.\n    delta = -delta;\n  }\n  return delta;\n}\n\nfunction normalizeWheelEventDelta(evt) {\n  let delta = normalizeWheelEventDirection(evt);\n\n  const MOUSE_DOM_DELTA_PIXEL_MODE = 0;\n  const MOUSE_DOM_DELTA_LINE_MODE = 1;\n  const MOUSE_PIXELS_PER_LINE = 30;\n  const MOUSE_LINES_PER_PAGE = 30;\n\n  // Converts delta to per-page units\n  if (evt.deltaMode === MOUSE_DOM_DELTA_PIXEL_MODE) {\n    delta /= MOUSE_PIXELS_PER_LINE * MOUSE_LINES_PER_PAGE;\n  } else if (evt.deltaMode === MOUSE_DOM_DELTA_LINE_MODE) {\n    delta /= MOUSE_LINES_PER_PAGE;\n  }\n  return delta;\n}\n\nfunction isValidRotation(angle) {\n  return Number.isInteger(angle) && angle % 90 === 0;\n}\n\nfunction isValidScrollMode(mode) {\n  return (\n    Number.isInteger(mode) &&\n    Object.values(ScrollMode).includes(mode) &&\n    mode !== ScrollMode.UNKNOWN\n  );\n}\n\nfunction isValidSpreadMode(mode) {\n  return (\n    Number.isInteger(mode) &&\n    Object.values(SpreadMode).includes(mode) &&\n    mode !== SpreadMode.UNKNOWN\n  );\n}\n\nfunction isPortraitOrientation(size) {\n  return size.width <= size.height;\n}\n\nconst WaitOnType = {\n  EVENT: \"event\",\n  TIMEOUT: \"timeout\",\n};\n\n/**\n * @typedef {Object} WaitOnEventOrTimeoutParameters\n * @property {Object} target - The event target, can for example be:\n *   `window`, `document`, a DOM element, or an {EventBus} instance.\n * @property {string} name - The name of the event.\n * @property {number} delay - The delay, in milliseconds, after which the\n *   timeout occurs (if the event wasn't already dispatched).\n */\n\n/**\n * Allows waiting for an event or a timeout, whichever occurs first.\n * Can be used to ensure that an action always occurs, even when an event\n * arrives late or not at all.\n *\n * @param {WaitOnEventOrTimeoutParameters}\n * @returns {Promise} A promise that is resolved with a {WaitOnType} value.\n */\nfunction waitOnEventOrTimeout({ target, name, delay = 0 }) {\n  return new Promise(function (resolve, reject) {\n    if (\n      typeof target !== \"object\" ||\n      !(name && typeof name === \"string\") ||\n      !(Number.isInteger(delay) && delay >= 0)\n    ) {\n      throw new Error(\"waitOnEventOrTimeout - invalid parameters.\");\n    }\n\n    function handler(type) {\n      if (target instanceof EventBus) {\n        target._off(name, eventHandler);\n      } else {\n        target.removeEventListener(name, eventHandler);\n      }\n\n      if (timeout) {\n        clearTimeout(timeout);\n      }\n      resolve(type);\n    }\n\n    const eventHandler = handler.bind(null, WaitOnType.EVENT);\n    if (target instanceof EventBus) {\n      target._on(name, eventHandler);\n    } else {\n      target.addEventListener(name, eventHandler);\n    }\n\n    const timeoutHandler = handler.bind(null, WaitOnType.TIMEOUT);\n    const timeout = setTimeout(timeoutHandler, delay);\n  });\n}\n\n/**\n * Promise that is resolved when DOM window becomes visible.\n */\nconst animationStarted = new Promise(function (resolve) {\n  if (\n    typeof PDFJSDev !== \"undefined\" &&\n    PDFJSDev.test(\"LIB\") &&\n    typeof window === \"undefined\"\n  ) {\n    // Prevent \"ReferenceError: window is not defined\" errors when running the\n    // unit-tests in Node.js environments.\n    setTimeout(resolve, 20);\n    return;\n  }\n  window.requestAnimationFrame(resolve);\n});\n\n/**\n * NOTE: Only used to support various PDF viewer tests in `mozilla-central`.\n */\nfunction dispatchDOMEvent(eventName, args = null) {\n  if (typeof PDFJSDev !== \"undefined\" && !PDFJSDev.test(\"MOZCENTRAL\")) {\n    throw new Error(\"Not implemented: dispatchDOMEvent\");\n  }\n  const details = Object.create(null);\n  if (args?.length > 0) {\n    const obj = args[0];\n    for (const key in obj) {\n      const value = obj[key];\n      if (key === \"source\") {\n        if (value === window || value === document) {\n          return; // No need to re-dispatch (already) global events.\n        }\n        continue; // Ignore the `source` property.\n      }\n      details[key] = value;\n    }\n  }\n  const event = document.createEvent(\"CustomEvent\");\n  event.initCustomEvent(eventName, true, true, details);\n  document.dispatchEvent(event);\n}\n\n/**\n * Simple event bus for an application. Listeners are attached using the `on`\n * and `off` methods. To raise an event, the `dispatch` method shall be used.\n */\nclass EventBus {\n  constructor(options) {\n    this._listeners = Object.create(null);\n\n    if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"MOZCENTRAL\")) {\n      this._isInAutomation = options?.isInAutomation === true;\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} listener\n   * @param {Object} [options]\n   */\n  on(eventName, listener, options = null) {\n    this._on(eventName, listener, {\n      external: true,\n      once: options?.once,\n    });\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} listener\n   * @param {Object} [options]\n   */\n  off(eventName, listener, options = null) {\n    this._off(eventName, listener, {\n      external: true,\n      once: options?.once,\n    });\n  }\n\n  dispatch(eventName) {\n    const eventListeners = this._listeners[eventName];\n    if (!eventListeners || eventListeners.length === 0) {\n      if (\n        (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"MOZCENTRAL\")) &&\n        this._isInAutomation\n      ) {\n        const args = Array.prototype.slice.call(arguments, 1);\n        dispatchDOMEvent(eventName, args);\n      }\n      return;\n    }\n    // Passing all arguments after the eventName to the listeners.\n    const args = Array.prototype.slice.call(arguments, 1);\n    let externalListeners;\n    // Making copy of the listeners array in case if it will be modified\n    // during dispatch.\n    for (const { listener, external, once } of eventListeners.slice(0)) {\n      if (once) {\n        this._off(eventName, listener);\n      }\n      if (external) {\n        (externalListeners ||= []).push(listener);\n        continue;\n      }\n      listener.apply(null, args);\n    }\n    // Dispatch any \"external\" listeners *after* the internal ones, to give the\n    // viewer components time to handle events and update their state first.\n    if (externalListeners) {\n      for (const listener of externalListeners) {\n        listener.apply(null, args);\n      }\n      externalListeners = null;\n    }\n    if (\n      (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"MOZCENTRAL\")) &&\n      this._isInAutomation\n    ) {\n      dispatchDOMEvent(eventName, args);\n    }\n  }\n\n  /**\n   * @ignore\n   */\n  _on(eventName, listener, options = null) {\n    const eventListeners = (this._listeners[eventName] ||= []);\n    eventListeners.push({\n      listener,\n      external: options?.external === true,\n      once: options?.once === true,\n    });\n  }\n\n  /**\n   * @ignore\n   */\n  _off(eventName, listener, options = null) {\n    const eventListeners = this._listeners[eventName];\n    if (!eventListeners) {\n      return;\n    }\n    for (let i = 0, ii = eventListeners.length; i < ii; i++) {\n      if (eventListeners[i].listener === listener) {\n        eventListeners.splice(i, 1);\n        return;\n      }\n    }\n  }\n}\n\nfunction clamp(v, min, max) {\n  return Math.min(Math.max(v, min), max);\n}\n\nclass ProgressBar {\n  constructor(id, { height, width, units } = {}) {\n    this.visible = true;\n\n    // Fetch the sub-elements for later.\n    this.div = document.querySelector(id + \" .progress\");\n    // Get the loading bar element, so it can be resized to fit the viewer.\n    this.bar = this.div.parentNode;\n\n    // Get options, with sensible defaults.\n    this.height = height || 100;\n    this.width = width || 100;\n    this.units = units || \"%\";\n\n    // Initialize heights.\n    this.div.style.height = this.height + this.units;\n    this.percent = 0;\n  }\n\n  _updateBar() {\n    if (this._indeterminate) {\n      this.div.classList.add(\"indeterminate\");\n      this.div.style.width = this.width + this.units;\n      return;\n    }\n\n    this.div.classList.remove(\"indeterminate\");\n    const progressSize = (this.width * this._percent) / 100;\n    this.div.style.width = progressSize + this.units;\n  }\n\n  get percent() {\n    return this._percent;\n  }\n\n  set percent(val) {\n    this._indeterminate = isNaN(val);\n    this._percent = clamp(val, 0, 100);\n    this._updateBar();\n  }\n\n  setWidth(viewer) {\n    if (!viewer) {\n      return;\n    }\n    const container = viewer.parentNode;\n    const scrollbarWidth = container.offsetWidth - viewer.offsetWidth;\n    if (scrollbarWidth > 0) {\n      const doc = document.documentElement;\n      doc.style.setProperty(LOADINGBAR_END_OFFSET_VAR, `${scrollbarWidth}px`);\n    }\n  }\n\n  hide() {\n    if (!this.visible) {\n      return;\n    }\n    this.visible = false;\n    this.bar.classList.add(\"hidden\");\n  }\n\n  show() {\n    if (this.visible) {\n      return;\n    }\n    this.visible = true;\n    this.bar.classList.remove(\"hidden\");\n  }\n}\n\n/**\n * Moves all elements of an array that satisfy condition to the end of the\n * array, preserving the order of the rest.\n */\nfunction moveToEndOfArray(arr, condition) {\n  const moved = [],\n    len = arr.length;\n  let write = 0;\n  for (let read = 0; read < len; ++read) {\n    if (condition(arr[read])) {\n      moved.push(arr[read]);\n    } else {\n      arr[write] = arr[read];\n      ++write;\n    }\n  }\n  for (let read = 0; write < len; ++read, ++write) {\n    arr[write] = moved[read];\n  }\n}\n\n/**\n * Get the active or focused element in current DOM.\n *\n * Recursively search for the truly active or focused element in case there are\n * shadow DOMs.\n *\n * @returns {Element} the truly active or focused element.\n */\nfunction getActiveOrFocusedElement() {\n  let curRoot = document;\n  let curActiveOrFocused =\n    curRoot.activeElement || curRoot.querySelector(\":focus\");\n\n  while (curActiveOrFocused?.shadowRoot) {\n    curRoot = curActiveOrFocused.shadowRoot;\n    curActiveOrFocused =\n      curRoot.activeElement || curRoot.querySelector(\":focus\");\n  }\n\n  return curActiveOrFocused;\n}\n\n/**\n * Converts API PageLayout values to the format used by `BaseViewer`.\n * NOTE: This is supported to the extent that the viewer implements the\n *       necessary Scroll/Spread modes (since SinglePage, TwoPageLeft,\n *       and TwoPageRight all suggests using non-continuous scrolling).\n * @param {string} mode - The API PageLayout value.\n * @returns {number} A value from {SpreadMode}.\n */\nfunction apiPageLayoutToSpreadMode(layout) {\n  switch (layout) {\n    case \"SinglePage\":\n    case \"OneColumn\":\n      return SpreadMode.NONE;\n    case \"TwoColumnLeft\":\n    case \"TwoPageLeft\":\n      return SpreadMode.ODD;\n    case \"TwoColumnRight\":\n    case \"TwoPageRight\":\n      return SpreadMode.EVEN;\n  }\n  return SpreadMode.NONE; // Default value.\n}\n\n/**\n * Converts API PageMode values to the format used by `PDFSidebar`.\n * NOTE: There's also a \"FullScreen\" parameter which is not possible to support,\n *       since the Fullscreen API used in browsers requires that entering\n *       fullscreen mode only occurs as a result of a user-initiated event.\n * @param {string} mode - The API PageMode value.\n * @returns {number} A value from {SidebarView}.\n */\nfunction apiPageModeToSidebarView(mode) {\n  switch (mode) {\n    case \"UseNone\":\n      return SidebarView.NONE;\n    case \"UseThumbs\":\n      return SidebarView.THUMBS;\n    case \"UseOutlines\":\n      return SidebarView.OUTLINE;\n    case \"UseAttachments\":\n      return SidebarView.ATTACHMENTS;\n    case \"UseOC\":\n      return SidebarView.LAYERS;\n  }\n  return SidebarView.NONE; // Default value.\n}\n\nexport {\n  animationStarted,\n  apiPageLayoutToSpreadMode,\n  apiPageModeToSidebarView,\n  approximateFraction,\n  AutoPrintRegExp,\n  backtrackBeforeAllVisibleElements, // only exported for testing\n  binarySearchFirstItem,\n  CSS_UNITS,\n  DEFAULT_SCALE,\n  DEFAULT_SCALE_VALUE,\n  EventBus,\n  getActiveOrFocusedElement,\n  getOutputScale,\n  getPageSizeInches,\n  getVisibleElements,\n  isPortraitOrientation,\n  isValidRotation,\n  isValidScrollMode,\n  isValidSpreadMode,\n  MAX_AUTO_SCALE,\n  MAX_SCALE,\n  MIN_SCALE,\n  moveToEndOfArray,\n  noContextMenuHandler,\n  normalizeWheelEventDelta,\n  normalizeWheelEventDirection,\n  parseQueryString,\n  PresentationModeState,\n  ProgressBar,\n  RendererType,\n  roundToDivide,\n  SCROLLBAR_PADDING,\n  scrollIntoView,\n  ScrollMode,\n  SidebarView,\n  SpreadMode,\n  TextLayerMode,\n  UNKNOWN_SCALE,\n  VERTICAL_PADDING,\n  waitOnEventOrTimeout,\n  WaitOnType,\n  watchScroll,\n};\n", "/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { renderTextLayer } from \"pdfjs-lib\";\n\nconst EXPAND_DIVS_TIMEOUT = 300; // ms\n\n/**\n * @typedef {Object} TextLayerBuilderOptions\n * @property {HTMLDivElement} textLayerDiv - The text layer container.\n * @property {EventBus} eventBus - The application event bus.\n * @property {number} pageIndex - The page index.\n * @property {PageViewport} viewport - The viewport of the text layer.\n * @property {PDFFindController} findController\n * @property {boolean} enhanceTextSelection - Option to turn on improved\n *   text selection.\n */\n\n/**\n * The text layer builder provides text selection functionality for the PDF.\n * It does this by creating overlay divs over the PDF's text. These divs\n * contain text that matches the PDF text they are overlaying. This object\n * also provides a way to highlight text that is being searched for.\n */\nclass TextLayerBuilder {\n  constructor({\n    textLayerDiv,\n    eventBus,\n    pageIndex,\n    viewport,\n    findController = null,\n    enhanceTextSelection = false,\n  }) {\n    this.textLayerDiv = textLayerDiv;\n    this.eventBus = eventBus;\n    this.textContent = null;\n    this.textContentItemsStr = [];\n    this.textContentStream = null;\n    this.renderingDone = false;\n    this.pageIdx = pageIndex;\n    this.pageNumber = this.pageIdx + 1;\n    this.matches = [];\n    this.viewport = viewport;\n    this.textDivs = [];\n    this.findController = findController;\n    this.textLayerRenderTask = null;\n    this.enhanceTextSelection = enhanceTextSelection;\n\n    this._onUpdateTextLayerMatches = null;\n    this._bindMouse();\n  }\n\n  /**\n   * @private\n   */\n  _finishRendering() {\n    this.renderingDone = true;\n\n    if (!this.enhanceTextSelection) {\n      const endOfContent = document.createElement(\"div\");\n      endOfContent.className = \"endOfContent\";\n      this.textLayerDiv.appendChild(endOfContent);\n    }\n\n    this.eventBus.dispatch(\"textlayerrendered\", {\n      source: this,\n      pageNumber: this.pageNumber,\n      numTextDivs: this.textDivs.length,\n    });\n  }\n\n  /**\n   * Renders the text layer.\n   *\n   * @param {number} [timeout] - Wait for a specified amount of milliseconds\n   *                             before rendering.\n   */\n  render(timeout = 0) {\n    if (!(this.textContent || this.textContentStream) || this.renderingDone) {\n      return;\n    }\n    this.cancel();\n\n    this.textDivs = [];\n    const textLayerFrag = document.createDocumentFragment();\n    this.textLayerRenderTask = renderTextLayer({\n      textContent: this.textContent,\n      textContentStream: this.textContentStream,\n      container: textLayerFrag,\n      viewport: this.viewport,\n      textDivs: this.textDivs,\n      textContentItemsStr: this.textContentItemsStr,\n      timeout,\n      enhanceTextSelection: this.enhanceTextSelection,\n    });\n    this.textLayerRenderTask.promise.then(\n      () => {\n        this.textLayerDiv.appendChild(textLayerFrag);\n        this._finishRendering();\n        this._updateMatches();\n      },\n      function (reason) {\n        // Cancelled or failed to render text layer; skipping errors.\n      }\n    );\n\n    if (!this._onUpdateTextLayerMatches) {\n      this._onUpdateTextLayerMatches = evt => {\n        if (evt.pageIndex === this.pageIdx || evt.pageIndex === -1) {\n          this._updateMatches();\n        }\n      };\n      this.eventBus._on(\n        \"updatetextlayermatches\",\n        this._onUpdateTextLayerMatches\n      );\n    }\n  }\n\n  /**\n   * Cancel rendering of the text layer.\n   */\n  cancel() {\n    if (this.textLayerRenderTask) {\n      this.textLayerRenderTask.cancel();\n      this.textLayerRenderTask = null;\n    }\n    if (this._onUpdateTextLayerMatches) {\n      this.eventBus._off(\n        \"updatetextlayermatches\",\n        this._onUpdateTextLayerMatches\n      );\n      this._onUpdateTextLayerMatches = null;\n    }\n  }\n\n  setTextContentStream(readableStream) {\n    this.cancel();\n    this.textContentStream = readableStream;\n  }\n\n  setTextContent(textContent) {\n    this.cancel();\n    this.textContent = textContent;\n  }\n\n  _convertMatches(matches, matchesLength) {\n    // Early exit if there is nothing to convert.\n    if (!matches) {\n      return [];\n    }\n    const { textContentItemsStr } = this;\n\n    let i = 0,\n      iIndex = 0;\n    const end = textContentItemsStr.length - 1;\n    const result = [];\n\n    for (let m = 0, mm = matches.length; m < mm; m++) {\n      // Calculate the start position.\n      let matchIdx = matches[m];\n\n      // Loop over the divIdxs.\n      while (i !== end && matchIdx >= iIndex + textContentItemsStr[i].length) {\n        iIndex += textContentItemsStr[i].length;\n        i++;\n      }\n\n      if (i === textContentItemsStr.length) {\n        console.error(\"Could not find a matching mapping\");\n      }\n\n      const match = {\n        begin: {\n          divIdx: i,\n          offset: matchIdx - iIndex,\n        },\n      };\n\n      // Calculate the end position.\n      matchIdx += matchesLength[m];\n\n      // Somewhat the same array as above, but use > instead of >= to get\n      // the end position right.\n      while (i !== end && matchIdx > iIndex + textContentItemsStr[i].length) {\n        iIndex += textContentItemsStr[i].length;\n        i++;\n      }\n\n      match.end = {\n        divIdx: i,\n        offset: matchIdx - iIndex,\n      };\n      result.push(match);\n    }\n    return result;\n  }\n\n  _renderMatches(matches) {\n    // Early exit if there is nothing to render.\n    if (matches.length === 0) {\n      return;\n    }\n    const { findController, pageIdx, textContentItemsStr, textDivs } = this;\n\n    const isSelectedPage = pageIdx === findController.selected.pageIdx;\n    const selectedMatchIdx = findController.selected.matchIdx;\n    const highlightAll = findController.state.highlightAll;\n    let prevEnd = null;\n    const infinity = {\n      divIdx: -1,\n      offset: undefined,\n    };\n\n    function beginText(begin, className) {\n      const divIdx = begin.divIdx;\n      textDivs[divIdx].textContent = \"\";\n      return appendTextToDiv(divIdx, 0, begin.offset, className);\n    }\n\n    function appendTextToDiv(divIdx, fromOffset, toOffset, className) {\n      const div = textDivs[divIdx];\n      const content = textContentItemsStr[divIdx].substring(\n        fromOffset,\n        toOffset\n      );\n      const node = document.createTextNode(content);\n      if (className) {\n        const span = document.createElement(\"span\");\n        span.className = `${className} appended`;\n        span.appendChild(node);\n        div.appendChild(span);\n        return className.includes(\"selected\") ? span.offsetLeft : 0;\n      }\n      div.appendChild(node);\n      return 0;\n    }\n\n    let i0 = selectedMatchIdx,\n      i1 = i0 + 1;\n    if (highlightAll) {\n      i0 = 0;\n      i1 = matches.length;\n    } else if (!isSelectedPage) {\n      // Not highlighting all and this isn't the selected page, so do nothing.\n      return;\n    }\n\n    for (let i = i0; i < i1; i++) {\n      const match = matches[i];\n      const begin = match.begin;\n      const end = match.end;\n      const isSelected = isSelectedPage && i === selectedMatchIdx;\n      const highlightSuffix = isSelected ? \" selected\" : \"\";\n      let selectedLeft = 0;\n\n      // Match inside new div.\n      if (!prevEnd || begin.divIdx !== prevEnd.divIdx) {\n        // If there was a previous div, then add the text at the end.\n        if (prevEnd !== null) {\n          appendTextToDiv(prevEnd.divIdx, prevEnd.offset, infinity.offset);\n        }\n        // Clear the divs and set the content until the starting point.\n        beginText(begin);\n      } else {\n        appendTextToDiv(prevEnd.divIdx, prevEnd.offset, begin.offset);\n      }\n\n      if (begin.divIdx === end.divIdx) {\n        selectedLeft = appendTextToDiv(\n          begin.divIdx,\n          begin.offset,\n          end.offset,\n          \"highlight\" + highlightSuffix\n        );\n      } else {\n        selectedLeft = appendTextToDiv(\n          begin.divIdx,\n          begin.offset,\n          infinity.offset,\n          \"highlight begin\" + highlightSuffix\n        );\n        for (let n0 = begin.divIdx + 1, n1 = end.divIdx; n0 < n1; n0++) {\n          textDivs[n0].className = \"highlight middle\" + highlightSuffix;\n        }\n        beginText(end, \"highlight end\" + highlightSuffix);\n      }\n      prevEnd = end;\n\n      if (isSelected) {\n        // Attempt to scroll the selected match into view.\n        findController.scrollMatchIntoView({\n          element: textDivs[begin.divIdx],\n          selectedLeft,\n          pageIndex: pageIdx,\n          matchIndex: selectedMatchIdx,\n        });\n      }\n    }\n\n    if (prevEnd) {\n      appendTextToDiv(prevEnd.divIdx, prevEnd.offset, infinity.offset);\n    }\n  }\n\n  _updateMatches() {\n    // Only show matches when all rendering is done.\n    if (!this.renderingDone) {\n      return;\n    }\n    const { findController, matches, pageIdx, textContentItemsStr, textDivs } =\n      this;\n    let clearedUntilDivIdx = -1;\n\n    // Clear all current matches.\n    for (let i = 0, ii = matches.length; i < ii; i++) {\n      const match = matches[i];\n      const begin = Math.max(clearedUntilDivIdx, match.begin.divIdx);\n      for (let n = begin, end = match.end.divIdx; n <= end; n++) {\n        const div = textDivs[n];\n        div.textContent = textContentItemsStr[n];\n        div.className = \"\";\n      }\n      clearedUntilDivIdx = match.end.divIdx + 1;\n    }\n\n    if (!findController?.highlightMatches) {\n      return;\n    }\n    // Convert the matches on the `findController` into the match format\n    // used for the textLayer.\n    const pageMatches = findController.pageMatches[pageIdx] || null;\n    const pageMatchesLength = findController.pageMatchesLength[pageIdx] || null;\n\n    this.matches = this._convertMatches(pageMatches, pageMatchesLength);\n    this._renderMatches(this.matches);\n  }\n\n  /**\n   * Improves text selection by adding an additional div where the mouse was\n   * clicked. This reduces flickering of the content if the mouse is slowly\n   * dragged up or down.\n   *\n   * @private\n   */\n  _bindMouse() {\n    const div = this.textLayerDiv;\n    let expandDivsTimer = null;\n\n    div.addEventListener(\"mousedown\", evt => {\n      if (this.enhanceTextSelection && this.textLayerRenderTask) {\n        this.textLayerRenderTask.expandTextDivs(true);\n        if (\n          (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) &&\n          expandDivsTimer\n        ) {\n          clearTimeout(expandDivsTimer);\n          expandDivsTimer = null;\n        }\n        return;\n      }\n\n      const end = div.querySelector(\".endOfContent\");\n      if (!end) {\n        return;\n      }\n      if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n        // On non-Firefox browsers, the selection will feel better if the height\n        // of the `endOfContent` div is adjusted to start at mouse click\n        // location. This avoids flickering when the selection moves up.\n        // However it does not work when selection is started on empty space.\n        let adjustTop = evt.target !== div;\n        if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"GENERIC\")) {\n          adjustTop =\n            adjustTop &&\n            window\n              .getComputedStyle(end)\n              .getPropertyValue(\"-moz-user-select\") !== \"none\";\n        }\n        if (adjustTop) {\n          const divBounds = div.getBoundingClientRect();\n          const r = Math.max(0, (evt.pageY - divBounds.top) / divBounds.height);\n          end.style.top = (r * 100).toFixed(2) + \"%\";\n        }\n      }\n      end.classList.add(\"active\");\n    });\n\n    div.addEventListener(\"mouseup\", () => {\n      if (this.enhanceTextSelection && this.textLayerRenderTask) {\n        if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n          expandDivsTimer = setTimeout(() => {\n            if (this.textLayerRenderTask) {\n              this.textLayerRenderTask.expandTextDivs(false);\n            }\n            expandDivsTimer = null;\n          }, EXPAND_DIVS_TIMEOUT);\n        } else {\n          this.textLayerRenderTask.expandTextDivs(false);\n        }\n        return;\n      }\n\n      const end = div.querySelector(\".endOfContent\");\n      if (!end) {\n        return;\n      }\n      if (typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"MOZCENTRAL\")) {\n        end.style.top = \"\";\n      }\n      end.classList.remove(\"active\");\n    });\n  }\n}\n\n/**\n * @implements IPDFTextLayerFactory\n */\nclass DefaultTextLayerFactory {\n  /**\n   * @param {HTMLDivElement} textLayerDiv\n   * @param {number} pageIndex\n   * @param {PageViewport} viewport\n   * @param {boolean} enhanceTextSelection\n   * @param {EventBus} eventBus\n   * @returns {TextLayerBuilder}\n   */\n  createTextLayerBuilder(\n    textLayerDiv,\n    pageIndex,\n    viewport,\n    enhanceTextSelection = false,\n    eventBus\n  ) {\n    return new TextLayerBuilder({\n      textLayerDiv,\n      pageIndex,\n      viewport,\n      enhanceTextSelection,\n      eventBus,\n    });\n  }\n}\n\nexport { DefaultTextLayerFactory, TextLayerBuilder };\n", "/* Copyright 2013 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createObjectURL, createValidAbsoluteUrl, isPdfFile } from \"pdfjs-lib\";\nimport { compatibilityParams } from \"./app_options.js\";\n\nif (typeof PDFJSDev !== \"undefined\" && !PDFJSDev.test(\"CHROME || GENERIC\")) {\n  throw new Error(\n    'Module \"pdfjs-web/download_manager\" shall not be used ' +\n      \"outside CHROME and GENERIC builds.\"\n  );\n}\n\nfunction download(blobUrl, filename) {\n  const a = document.createElement(\"a\");\n  if (!a.click) {\n    throw new Error('DownloadManager: \"a.click()\" is not supported.');\n  }\n  a.href = blobUrl;\n  a.target = \"_parent\";\n  // Use a.download if available. This increases the likelihood that\n  // the file is downloaded instead of opened by another PDF plugin.\n  if (\"download\" in a) {\n    a.download = filename;\n  }\n  // <a> must be in the document for recent Firefox versions,\n  // otherwise .click() is ignored.\n  (document.body || document.documentElement).appendChild(a);\n  a.click();\n  a.remove();\n}\n\nclass DownloadManager {\n  constructor() {\n    this._openBlobUrls = new WeakMap();\n  }\n\n  downloadUrl(url, filename) {\n    if (!createValidAbsoluteUrl(url, \"http://example.com\")) {\n      return; // restricted/invalid URL\n    }\n    download(url + \"#pdfjs.action=download\", filename);\n  }\n\n  downloadData(data, filename, contentType) {\n    const blobUrl = createObjectURL(\n      data,\n      contentType,\n      compatibilityParams.disableCreateObjectURL\n    );\n    download(blobUrl, filename);\n  }\n\n  /**\n   * @returns {boolean} Indicating if the data was opened.\n   */\n  openOrDownloadData(element, data, filename) {\n    const isPdfData = isPdfFile(filename);\n    const contentType = isPdfData ? \"application/pdf\" : \"\";\n\n    if (isPdfData && !compatibilityParams.disableCreateObjectURL) {\n      let blobUrl = this._openBlobUrls.get(element);\n      if (!blobUrl) {\n        blobUrl = URL.createObjectURL(new Blob([data], { type: contentType }));\n        this._openBlobUrls.set(element, blobUrl);\n      }\n      let viewerUrl;\n      if (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"GENERIC\")) {\n        // The current URL is the viewer, let's use it and append the file.\n        viewerUrl = \"?file=\" + encodeURIComponent(blobUrl + \"#\" + filename);\n      } else if (PDFJSDev.test(\"CHROME\")) {\n        // In the Chrome extension, the URL is rewritten using the history API\n        // in viewer.js, so an absolute URL must be generated.\n        viewerUrl =\n          // eslint-disable-next-line no-undef\n          chrome.runtime.getURL(\"/content/web/viewer.html\") +\n          \"?file=\" +\n          encodeURIComponent(blobUrl + \"#\" + filename);\n      }\n\n      try {\n        window.open(viewerUrl);\n        return true;\n      } catch (ex) {\n        console.error(`openOrDownloadData: ${ex}`);\n        // Release the `blobUrl`, since opening it failed, and fallback to\n        // downloading the PDF file.\n        URL.revokeObjectURL(blobUrl);\n        this._openBlobUrls.delete(element);\n      }\n    }\n\n    this.downloadData(data, filename, contentType);\n    return false;\n  }\n\n  /**\n   * @param sourceEventType {string} Used to signal what triggered the download.\n   *   The version of PDF.js integrated with Firefox uses this to to determine\n   *   which dialog to show. \"save\" triggers \"save as\" and \"download\" triggers\n   *   the \"open with\" dialog.\n   */\n  download(blob, url, filename, sourceEventType = \"download\") {\n    if (compatibilityParams.disableCreateObjectURL) {\n      // URL.createObjectURL is not supported\n      this.downloadUrl(url, filename);\n      return;\n    }\n    const blobUrl = URL.createObjectURL(blob);\n    download(blobUrl, filename);\n  }\n}\n\nexport { DownloadManager };\n", "/* Copyright 2018 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst compatibilityParams = Object.create(null);\nif (typeof PDFJSDev === \"undefined\" || PDFJSDev.test(\"GENERIC\")) {\n  const userAgent =\n    (typeof navigator !== \"undefined\" && navigator.userAgent) || \"\";\n  const platform =\n    (typeof navigator !== \"undefined\" && navigator.platform) || \"\";\n  const maxTouchPoints =\n    (typeof navigator !== \"undefined\" && navigator.maxTouchPoints) || 1;\n\n  const isAndroid = /Android/.test(userAgent);\n  const isIOS =\n    /\\b(iPad|iPhone|iPod)(?=;)/.test(userAgent) ||\n    (platform === \"MacIntel\" && maxTouchPoints > 1);\n  const isIOSChrome = /CriOS/.test(userAgent);\n\n  // Disables URL.createObjectURL() usage in some environments.\n  // Support: Chrome on iOS\n  (function checkOnBlobSupport() {\n    // Sometimes Chrome on iOS loses data created with createObjectURL(),\n    // see issue 8081.\n    if (isIOSChrome) {\n      compatibilityParams.disableCreateObjectURL = true;\n    }\n  })();\n\n  // Limit canvas size to 5 mega-pixels on mobile.\n  // Support: Android, iOS\n  (function checkCanvasSizeLimitation() {\n    if (isIOS || isAndroid) {\n      compatibilityParams.maxCanvasPixels = 5242880;\n    }\n  })();\n}\n\nconst OptionKind = {\n  VIEWER: 0x02,\n  API: 0x04,\n  WORKER: 0x08,\n  PREFERENCE: 0x80,\n};\n\n/**\n * PLEASE NOTE: To avoid introducing unnecessary dependencies, we specify the\n *              values below *explicitly* rather than relying on imported types.\n */\nconst defaultOptions = {\n  cursorToolOnLoad: {\n    /** @type {number} */\n    value: 0,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  defaultUrl: {\n    /** @type {string} */\n    value: \"compressed.tracemonkey-pldi-09.pdf\",\n    kind: OptionKind.VIEWER,\n  },\n  defaultZoomValue: {\n    /** @type {string} */\n    value: \"\",\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  disableHistory: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER,\n  },\n  disablePageLabels: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  enablePermissions: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  enablePrintAutoRotate: {\n    /** @type {boolean} */\n    value: true,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  enableScripting: {\n    /** @type {boolean} */\n    value: true,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  externalLinkRel: {\n    /** @type {string} */\n    value: \"noopener noreferrer nofollow\",\n    kind: OptionKind.VIEWER,\n  },\n  externalLinkTarget: {\n    /** @type {number} */\n    value: 0,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  historyUpdateUrl: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  ignoreDestinationZoom: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  imageResourcesPath: {\n    /** @type {string} */\n    value: \"./images/\",\n    kind: OptionKind.VIEWER,\n  },\n  maxCanvasPixels: {\n    /** @type {number} */\n    value: 16777216,\n    compatibility: compatibilityParams.maxCanvasPixels,\n    kind: OptionKind.VIEWER,\n  },\n  pdfBugEnabled: {\n    /** @type {boolean} */\n    value: typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"PRODUCTION\"),\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  printResolution: {\n    /** @type {number} */\n    value: 150,\n    kind: OptionKind.VIEWER,\n  },\n  renderer: {\n    /** @type {string} */\n    value: \"canvas\",\n    kind: OptionKind.VIEWER,\n  },\n  renderInteractiveForms: {\n    /** @type {boolean} */\n    value: true,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  sidebarViewOnLoad: {\n    /** @type {number} */\n    value: -1,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  scrollModeOnLoad: {\n    /** @type {number} */\n    value: -1,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  spreadModeOnLoad: {\n    /** @type {number} */\n    value: -1,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  textLayerMode: {\n    /** @type {number} */\n    value: 1,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  useOnlyCssZoom: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  viewerCssTheme: {\n    /** @type {number} */\n    value: typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"CHROME\") ? 2 : 0,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n  viewOnLoad: {\n    /** @type {boolean} */\n    value: 0,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  },\n\n  cMapPacked: {\n    /** @type {boolean} */\n    value: true,\n    kind: OptionKind.API,\n  },\n  cMapUrl: {\n    /** @type {string} */\n    value:\n      typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"PRODUCTION\")\n        ? \"../external/bcmaps/\"\n        : \"../web/cmaps/\",\n    kind: OptionKind.API,\n  },\n  disableAutoFetch: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.API + OptionKind.PREFERENCE,\n  },\n  disableFontFace: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.API + OptionKind.PREFERENCE,\n  },\n  disableRange: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.API + OptionKind.PREFERENCE,\n  },\n  disableStream: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.API + OptionKind.PREFERENCE,\n  },\n  docBaseUrl: {\n    /** @type {string} */\n    value: \"\",\n    kind: OptionKind.API,\n  },\n  enableXfa: {\n    /** @type {boolean} */\n    value:\n      typeof PDFJSDev === \"undefined\" ||\n      PDFJSDev.test(\"!PRODUCTION || TESTING\"),\n    kind: OptionKind.API + OptionKind.PREFERENCE,\n  },\n  fontExtraProperties: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.API,\n  },\n  isEvalSupported: {\n    /** @type {boolean} */\n    value: true,\n    kind: OptionKind.API,\n  },\n  maxImageSize: {\n    /** @type {number} */\n    value: -1,\n    kind: OptionKind.API,\n  },\n  pdfBug: {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.API,\n  },\n  standardFontDataUrl: {\n    /** @type {string} */\n    value:\n      typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"PRODUCTION\")\n        ? \"../external/standard_fonts/\"\n        : \"../web/standard_fonts/\",\n    kind: OptionKind.API,\n  },\n  verbosity: {\n    /** @type {number} */\n    value: 1,\n    kind: OptionKind.API,\n  },\n\n  workerPort: {\n    /** @type {Object} */\n    value: null,\n    kind: OptionKind.WORKER,\n  },\n  workerSrc: {\n    /** @type {string} */\n    value:\n      typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"PRODUCTION\")\n        ? \"../src/worker_loader.js\"\n        : \"../build/pdf.worker.js\",\n    kind: OptionKind.WORKER,\n  },\n};\nif (\n  typeof PDFJSDev === \"undefined\" ||\n  PDFJSDev.test(\"!PRODUCTION || GENERIC\")\n) {\n  defaultOptions.disablePreferences = {\n    /** @type {boolean} */\n    value: typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"TESTING\"),\n    kind: OptionKind.VIEWER,\n  };\n  defaultOptions.locale = {\n    /** @type {string} */\n    value: typeof navigator !== \"undefined\" ? navigator.language : \"en-US\",\n    kind: OptionKind.VIEWER,\n  };\n  defaultOptions.sandboxBundleSrc = {\n    /** @type {string} */\n    value:\n      typeof PDFJSDev === \"undefined\" || !PDFJSDev.test(\"PRODUCTION\")\n        ? \"../build/dev-sandbox/pdf.sandbox.js\"\n        : \"../build/pdf.sandbox.js\",\n    kind: OptionKind.VIEWER,\n  };\n\n  defaultOptions.renderer.kind += OptionKind.PREFERENCE;\n} else if (PDFJSDev.test(\"CHROME\")) {\n  defaultOptions.disableTelemetry = {\n    /** @type {boolean} */\n    value: false,\n    kind: OptionKind.VIEWER + OptionKind.PREFERENCE,\n  };\n  defaultOptions.sandboxBundleSrc = {\n    /** @type {string} */\n    value: \"../build/pdf.sandbox.js\",\n    kind: OptionKind.VIEWER,\n  };\n}\n\nconst userOptions = Object.create(null);\n\nclass AppOptions {\n  constructor() {\n    throw new Error(\"Cannot initialize AppOptions.\");\n  }\n\n  static get(name) {\n    const userOption = userOptions[name];\n    if (userOption !== undefined) {\n      return userOption;\n    }\n    const defaultOption = defaultOptions[name];\n    if (defaultOption !== undefined) {\n      return defaultOption.compatibility ?? defaultOption.value;\n    }\n    return undefined;\n  }\n\n  static getAll(kind = null) {\n    const options = Object.create(null);\n    for (const name in defaultOptions) {\n      const defaultOption = defaultOptions[name];\n      if (kind) {\n        if ((kind & defaultOption.kind) === 0) {\n          continue;\n        }\n        if (kind === OptionKind.PREFERENCE) {\n          const value = defaultOption.value,\n            valueType = typeof value;\n\n          if (\n            valueType === \"boolean\" ||\n            valueType === \"string\" ||\n            (valueType === \"number\" && Number.isInteger(value))\n          ) {\n            options[name] = value;\n            continue;\n          }\n          throw new Error(`Invalid type for preference: ${name}`);\n        }\n      }\n      const userOption = userOptions[name];\n      options[name] =\n        userOption !== undefined\n          ? userOption\n          : defaultOption.compatibility ?? defaultOption.value;\n    }\n    return options;\n  }\n\n  static set(name, value) {\n    userOptions[name] = value;\n  }\n\n  static setAll(options) {\n    for (const name in options) {\n      userOptions[name] = options[name];\n    }\n  }\n\n  static remove(name) {\n    delete userOptions[name];\n  }\n}\n\nexport { AppOptions, compatibilityParams, OptionKind };\n", "/* Copyright 2017 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport \"../external/webL10n/l10n.js\";\nimport { fixupLangCode, getL10nFallback } from \"./l10n_utils.js\";\n\nconst webL10n = document.webL10n;\n\nclass GenericL10n {\n  constructor(lang) {\n    this._lang = lang;\n    this._ready = new Promise((resolve, reject) => {\n      webL10n.setLanguage(fixupLangCode(lang), () => {\n        resolve(webL10n);\n      });\n    });\n  }\n\n  async getLanguage() {\n    const l10n = await this._ready;\n    return l10n.getLanguage();\n  }\n\n  async getDirection() {\n    const l10n = await this._ready;\n    return l10n.getDirection();\n  }\n\n  async get(key, args = null, fallback = getL10nFallback(key, args)) {\n    const l10n = await this._ready;\n    return l10n.get(key, args, fallback);\n  }\n\n  async translate(element) {\n    const l10n = await this._ready;\n    return l10n.translate(element);\n  }\n}\n\nexport { GenericL10n };\n", "/**\n * Copyright (c) 2011-2013 <PERSON><PERSON><PERSON>, Mozilla.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to\n * deal in the Software without restriction, including without limitation the\n * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n * sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n * IN THE SOFTWARE.\n */\n/*\n  Additional modifications for PDF.js project:\n    - Disables language initialization on page loading.\n    - Disables document translation on page loading.\n    - Removes consoleWarn and consoleLog and use console.log/warn directly.\n    - Removes window._ assignment.\n    - Remove compatibility code for OldIE.\n    - Replaces `String.prototype.substr()` with `String.prototype.substring()`.\n    - Removes `fireL10nReadyEvent` since the \"localized\" event it dispatches\n      is unused and may clash with an identically named event in the viewer.\n*/\n\n/*jshint browser: true, devel: true, es5: true, globalstrict: true */\n'use strict';\n\ndocument.webL10n = (function(window, document, undefined) {\n  var gL10nData = {};\n  var gTextData = '';\n  var gTextProp = 'textContent';\n  var gLanguage = '';\n  var gMacros = {};\n  var gReadyState = 'loading';\n\n\n  /**\n   * Synchronously loading l10n resources significantly minimizes flickering\n   * from displaying the app with non-localized strings and then updating the\n   * strings. Although this will block all script execution on this page, we\n   * expect that the l10n resources are available locally on flash-storage.\n   *\n   * As synchronous XHR is generally considered as a bad idea, we're still\n   * loading l10n resources asynchronously -- but we keep this in a setting,\n   * just in case... and applications using this library should hide their\n   * content until the `localized' event happens.\n   */\n\n  var gAsyncResourceLoading = true; // read-only\n\n\n  /**\n   * DOM helpers for the so-called \"HTML API\".\n   *\n   * These functions are written for modern browsers. For old versions of IE,\n   * they're overridden in the 'startup' section at the end of this file.\n   */\n\n  function getL10nResourceLinks() {\n    return document.querySelectorAll('link[type=\"application/l10n\"]');\n  }\n\n  function getL10nDictionary() {\n    var script = document.querySelector('script[type=\"application/l10n\"]');\n    // TODO: support multiple and external JSON dictionaries\n    return script ? JSON.parse(script.innerHTML) : null;\n  }\n\n  function getTranslatableChildren(element) {\n    return element ? element.querySelectorAll('*[data-l10n-id]') : [];\n  }\n\n  function getL10nAttributes(element) {\n    if (!element)\n      return {};\n\n    var l10nId = element.getAttribute('data-l10n-id');\n    var l10nArgs = element.getAttribute('data-l10n-args');\n    var args = {};\n    if (l10nArgs) {\n      try {\n        args = JSON.parse(l10nArgs);\n      } catch (e) {\n        console.warn('could not parse arguments for #' + l10nId);\n      }\n    }\n    return { id: l10nId, args: args };\n  }\n\n  function xhrLoadText(url, onSuccess, onFailure) {\n    onSuccess = onSuccess || function _onSuccess(data) {};\n    onFailure = onFailure || function _onFailure() {};\n\n    var xhr = new XMLHttpRequest();\n    xhr.open('GET', url, gAsyncResourceLoading);\n    if (xhr.overrideMimeType) {\n      xhr.overrideMimeType('text/plain; charset=utf-8');\n    }\n    xhr.onreadystatechange = function() {\n      if (xhr.readyState == 4) {\n        if (xhr.status == 200 || xhr.status === 0) {\n          onSuccess(xhr.responseText);\n        } else {\n          onFailure();\n        }\n      }\n    };\n    xhr.onerror = onFailure;\n    xhr.ontimeout = onFailure;\n\n    // in Firefox OS with the app:// protocol, trying to XHR a non-existing\n    // URL will raise an exception here -- hence this ugly try...catch.\n    try {\n      xhr.send(null);\n    } catch (e) {\n      onFailure();\n    }\n  }\n\n\n  /**\n   * l10n resource parser:\n   *  - reads (async XHR) the l10n resource matching `lang';\n   *  - imports linked resources (synchronously) when specified;\n   *  - parses the text data (fills `gL10nData' and `gTextData');\n   *  - triggers success/failure callbacks when done.\n   *\n   * @param {string} href\n   *    URL of the l10n resource to parse.\n   *\n   * @param {string} lang\n   *    locale (language) to parse. Must be a lowercase string.\n   *\n   * @param {Function} successCallback\n   *    triggered when the l10n resource has been successfully parsed.\n   *\n   * @param {Function} failureCallback\n   *    triggered when the an error has occurred.\n   *\n   * @return {void}\n   *    uses the following global variables: gL10nData, gTextData, gTextProp.\n   */\n\n  function parseResource(href, lang, successCallback, failureCallback) {\n    var baseURL = href.replace(/[^\\/]*$/, '') || './';\n\n    // handle escaped characters (backslashes) in a string\n    function evalString(text) {\n      if (text.lastIndexOf('\\\\') < 0)\n        return text;\n      return text.replace(/\\\\\\\\/g, '\\\\')\n                 .replace(/\\\\n/g, '\\n')\n                 .replace(/\\\\r/g, '\\r')\n                 .replace(/\\\\t/g, '\\t')\n                 .replace(/\\\\b/g, '\\b')\n                 .replace(/\\\\f/g, '\\f')\n                 .replace(/\\\\{/g, '{')\n                 .replace(/\\\\}/g, '}')\n                 .replace(/\\\\\"/g, '\"')\n                 .replace(/\\\\'/g, \"'\");\n    }\n\n    // parse *.properties text data into an l10n dictionary\n    // If gAsyncResourceLoading is false, then the callback will be called\n    // synchronously. Otherwise it is called asynchronously.\n    function parseProperties(text, parsedPropertiesCallback) {\n      var dictionary = {};\n\n      // token expressions\n      var reBlank = /^\\s*|\\s*$/;\n      var reComment = /^\\s*#|^\\s*$/;\n      var reSection = /^\\s*\\[(.*)\\]\\s*$/;\n      var reImport = /^\\s*@import\\s+url\\((.*)\\)\\s*$/i;\n      var reSplit = /^([^=\\s]*)\\s*=\\s*(.+)$/; // TODO: escape EOLs with '\\'\n\n      // parse the *.properties file into an associative array\n      function parseRawLines(rawText, extendedSyntax, parsedRawLinesCallback) {\n        var entries = rawText.replace(reBlank, '').split(/[\\r\\n]+/);\n        var currentLang = '*';\n        var genericLang = lang.split('-', 1)[0];\n        var skipLang = false;\n        var match = '';\n\n        function nextEntry() {\n          // Use infinite loop instead of recursion to avoid reaching the\n          // maximum recursion limit for content with many lines.\n          while (true) {\n            if (!entries.length) {\n              parsedRawLinesCallback();\n              return;\n            }\n            var line = entries.shift();\n\n            // comment or blank line?\n            if (reComment.test(line))\n              continue;\n\n            // the extended syntax supports [lang] sections and @import rules\n            if (extendedSyntax) {\n              match = reSection.exec(line);\n              if (match) { // section start?\n                // RFC 4646, section 4.4, \"All comparisons MUST be performed\n                // in a case-insensitive manner.\"\n\n                currentLang = match[1].toLowerCase();\n                skipLang = (currentLang !== '*') &&\n                    (currentLang !== lang) && (currentLang !== genericLang);\n                continue;\n              } else if (skipLang) {\n                continue;\n              }\n              match = reImport.exec(line);\n              if (match) { // @import rule?\n                loadImport(baseURL + match[1], nextEntry);\n                return;\n              }\n            }\n\n            // key-value pair\n            var tmp = line.match(reSplit);\n            if (tmp && tmp.length == 3) {\n              dictionary[tmp[1]] = evalString(tmp[2]);\n            }\n          }\n        }\n        nextEntry();\n      }\n\n      // import another *.properties file\n      function loadImport(url, callback) {\n        xhrLoadText(url, function(content) {\n          parseRawLines(content, false, callback); // don't allow recursive imports\n        }, function () {\n          console.warn(url + ' not found.');\n          callback();\n        });\n      }\n\n      // fill the dictionary\n      parseRawLines(text, true, function() {\n        parsedPropertiesCallback(dictionary);\n      });\n    }\n\n    // load and parse l10n data (warning: global variables are used here)\n    xhrLoadText(href, function(response) {\n      gTextData += response; // mostly for debug\n\n      // parse *.properties text data into an l10n dictionary\n      parseProperties(response, function(data) {\n\n        // find attribute descriptions, if any\n        for (var key in data) {\n          var id, prop, index = key.lastIndexOf('.');\n          if (index > 0) { // an attribute has been specified\n            id = key.substring(0, index);\n            prop = key.substring(index + 1);\n          } else { // no attribute: assuming text content by default\n            id = key;\n            prop = gTextProp;\n          }\n          if (!gL10nData[id]) {\n            gL10nData[id] = {};\n          }\n          gL10nData[id][prop] = data[key];\n        }\n\n        // trigger callback\n        if (successCallback) {\n          successCallback();\n        }\n      });\n    }, failureCallback);\n  }\n\n  // load and parse all resources for the specified locale\n  function loadLocale(lang, callback) {\n    // RFC 4646, section 2.1 states that language tags have to be treated as\n    // case-insensitive. Convert to lowercase for case-insensitive comparisons.\n    if (lang) {\n      lang = lang.toLowerCase();\n    }\n\n    callback = callback || function _callback() {};\n\n    clear();\n    gLanguage = lang;\n\n    // check all <link type=\"application/l10n\" href=\"...\" /> nodes\n    // and load the resource files\n    var langLinks = getL10nResourceLinks();\n    var langCount = langLinks.length;\n    if (langCount === 0) {\n      // we might have a pre-compiled dictionary instead\n      var dict = getL10nDictionary();\n      if (dict && dict.locales && dict.default_locale) {\n        console.log('using the embedded JSON directory, early way out');\n        gL10nData = dict.locales[lang];\n        if (!gL10nData) {\n          var defaultLocale = dict.default_locale.toLowerCase();\n          for (var anyCaseLang in dict.locales) {\n            anyCaseLang = anyCaseLang.toLowerCase();\n            if (anyCaseLang === lang) {\n              gL10nData = dict.locales[lang];\n              break;\n            } else if (anyCaseLang === defaultLocale) {\n              gL10nData = dict.locales[defaultLocale];\n            }\n          }\n        }\n        callback();\n      } else {\n        console.log('no resource to load, early way out');\n      }\n      // early way out\n      gReadyState = 'complete';\n      return;\n    }\n\n    // start the callback when all resources are loaded\n    var onResourceLoaded = null;\n    var gResourceCount = 0;\n    onResourceLoaded = function() {\n      gResourceCount++;\n      if (gResourceCount >= langCount) {\n        callback();\n        gReadyState = 'complete';\n      }\n    };\n\n    // load all resource files\n    function L10nResourceLink(link) {\n      var href = link.href;\n      // Note: If |gAsyncResourceLoading| is false, then the following callbacks\n      // are synchronously called.\n      this.load = function(lang, callback) {\n        parseResource(href, lang, callback, function() {\n          console.warn(href + ' not found.');\n          // lang not found, used default resource instead\n          console.warn('\"' + lang + '\" resource not found');\n          gLanguage = '';\n          // Resource not loaded, but we still need to call the callback.\n          callback();\n        });\n      };\n    }\n\n    for (var i = 0; i < langCount; i++) {\n      var resource = new L10nResourceLink(langLinks[i]);\n      resource.load(lang, onResourceLoaded);\n    }\n  }\n\n  // clear all l10n data\n  function clear() {\n    gL10nData = {};\n    gTextData = '';\n    gLanguage = '';\n    // TODO: clear all non predefined macros.\n    // There's no such macro /yet/ but we're planning to have some...\n  }\n\n\n  /**\n   * Get rules for plural forms (shared with JetPack), see:\n   * http://unicode.org/repos/cldr-tmp/trunk/diff/supplemental/language_plural_rules.html\n   * https://github.com/mozilla/addon-sdk/blob/master/python-lib/plural-rules-generator.p\n   *\n   * @param {string} lang\n   *    locale (language) used.\n   *\n   * @return {Function}\n   *    returns a function that gives the plural form name for a given integer:\n   *       var fun = getPluralRules('en');\n   *       fun(1)    -> 'one'\n   *       fun(0)    -> 'other'\n   *       fun(1000) -> 'other'.\n   */\n\n  function getPluralRules(lang) {\n    var locales2rules = {\n      'af': 3,\n      'ak': 4,\n      'am': 4,\n      'ar': 1,\n      'asa': 3,\n      'az': 0,\n      'be': 11,\n      'bem': 3,\n      'bez': 3,\n      'bg': 3,\n      'bh': 4,\n      'bm': 0,\n      'bn': 3,\n      'bo': 0,\n      'br': 20,\n      'brx': 3,\n      'bs': 11,\n      'ca': 3,\n      'cgg': 3,\n      'chr': 3,\n      'cs': 12,\n      'cy': 17,\n      'da': 3,\n      'de': 3,\n      'dv': 3,\n      'dz': 0,\n      'ee': 3,\n      'el': 3,\n      'en': 3,\n      'eo': 3,\n      'es': 3,\n      'et': 3,\n      'eu': 3,\n      'fa': 0,\n      'ff': 5,\n      'fi': 3,\n      'fil': 4,\n      'fo': 3,\n      'fr': 5,\n      'fur': 3,\n      'fy': 3,\n      'ga': 8,\n      'gd': 24,\n      'gl': 3,\n      'gsw': 3,\n      'gu': 3,\n      'guw': 4,\n      'gv': 23,\n      'ha': 3,\n      'haw': 3,\n      'he': 2,\n      'hi': 4,\n      'hr': 11,\n      'hu': 0,\n      'id': 0,\n      'ig': 0,\n      'ii': 0,\n      'is': 3,\n      'it': 3,\n      'iu': 7,\n      'ja': 0,\n      'jmc': 3,\n      'jv': 0,\n      'ka': 0,\n      'kab': 5,\n      'kaj': 3,\n      'kcg': 3,\n      'kde': 0,\n      'kea': 0,\n      'kk': 3,\n      'kl': 3,\n      'km': 0,\n      'kn': 0,\n      'ko': 0,\n      'ksb': 3,\n      'ksh': 21,\n      'ku': 3,\n      'kw': 7,\n      'lag': 18,\n      'lb': 3,\n      'lg': 3,\n      'ln': 4,\n      'lo': 0,\n      'lt': 10,\n      'lv': 6,\n      'mas': 3,\n      'mg': 4,\n      'mk': 16,\n      'ml': 3,\n      'mn': 3,\n      'mo': 9,\n      'mr': 3,\n      'ms': 0,\n      'mt': 15,\n      'my': 0,\n      'nah': 3,\n      'naq': 7,\n      'nb': 3,\n      'nd': 3,\n      'ne': 3,\n      'nl': 3,\n      'nn': 3,\n      'no': 3,\n      'nr': 3,\n      'nso': 4,\n      'ny': 3,\n      'nyn': 3,\n      'om': 3,\n      'or': 3,\n      'pa': 3,\n      'pap': 3,\n      'pl': 13,\n      'ps': 3,\n      'pt': 3,\n      'rm': 3,\n      'ro': 9,\n      'rof': 3,\n      'ru': 11,\n      'rwk': 3,\n      'sah': 0,\n      'saq': 3,\n      'se': 7,\n      'seh': 3,\n      'ses': 0,\n      'sg': 0,\n      'sh': 11,\n      'shi': 19,\n      'sk': 12,\n      'sl': 14,\n      'sma': 7,\n      'smi': 7,\n      'smj': 7,\n      'smn': 7,\n      'sms': 7,\n      'sn': 3,\n      'so': 3,\n      'sq': 3,\n      'sr': 11,\n      'ss': 3,\n      'ssy': 3,\n      'st': 3,\n      'sv': 3,\n      'sw': 3,\n      'syr': 3,\n      'ta': 3,\n      'te': 3,\n      'teo': 3,\n      'th': 0,\n      'ti': 4,\n      'tig': 3,\n      'tk': 3,\n      'tl': 4,\n      'tn': 3,\n      'to': 0,\n      'tr': 0,\n      'ts': 3,\n      'tzm': 22,\n      'uk': 11,\n      'ur': 3,\n      've': 3,\n      'vi': 0,\n      'vun': 3,\n      'wa': 4,\n      'wae': 3,\n      'wo': 0,\n      'xh': 3,\n      'xog': 3,\n      'yo': 0,\n      'zh': 0,\n      'zu': 3\n    };\n\n    // utility functions for plural rules methods\n    function isIn(n, list) {\n      return list.indexOf(n) !== -1;\n    }\n    function isBetween(n, start, end) {\n      return start <= n && n <= end;\n    }\n\n    // list of all plural rules methods:\n    // map an integer to the plural form name to use\n    var pluralRules = {\n      '0': function(n) {\n        return 'other';\n      },\n      '1': function(n) {\n        if ((isBetween((n % 100), 3, 10)))\n          return 'few';\n        if (n === 0)\n          return 'zero';\n        if ((isBetween((n % 100), 11, 99)))\n          return 'many';\n        if (n == 2)\n          return 'two';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '2': function(n) {\n        if (n !== 0 && (n % 10) === 0)\n          return 'many';\n        if (n == 2)\n          return 'two';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '3': function(n) {\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '4': function(n) {\n        if ((isBetween(n, 0, 1)))\n          return 'one';\n        return 'other';\n      },\n      '5': function(n) {\n        if ((isBetween(n, 0, 2)) && n != 2)\n          return 'one';\n        return 'other';\n      },\n      '6': function(n) {\n        if (n === 0)\n          return 'zero';\n        if ((n % 10) == 1 && (n % 100) != 11)\n          return 'one';\n        return 'other';\n      },\n      '7': function(n) {\n        if (n == 2)\n          return 'two';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '8': function(n) {\n        if ((isBetween(n, 3, 6)))\n          return 'few';\n        if ((isBetween(n, 7, 10)))\n          return 'many';\n        if (n == 2)\n          return 'two';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '9': function(n) {\n        if (n === 0 || n != 1 && (isBetween((n % 100), 1, 19)))\n          return 'few';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '10': function(n) {\n        if ((isBetween((n % 10), 2, 9)) && !(isBetween((n % 100), 11, 19)))\n          return 'few';\n        if ((n % 10) == 1 && !(isBetween((n % 100), 11, 19)))\n          return 'one';\n        return 'other';\n      },\n      '11': function(n) {\n        if ((isBetween((n % 10), 2, 4)) && !(isBetween((n % 100), 12, 14)))\n          return 'few';\n        if ((n % 10) === 0 ||\n            (isBetween((n % 10), 5, 9)) ||\n            (isBetween((n % 100), 11, 14)))\n          return 'many';\n        if ((n % 10) == 1 && (n % 100) != 11)\n          return 'one';\n        return 'other';\n      },\n      '12': function(n) {\n        if ((isBetween(n, 2, 4)))\n          return 'few';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '13': function(n) {\n        if ((isBetween((n % 10), 2, 4)) && !(isBetween((n % 100), 12, 14)))\n          return 'few';\n        if (n != 1 && (isBetween((n % 10), 0, 1)) ||\n            (isBetween((n % 10), 5, 9)) ||\n            (isBetween((n % 100), 12, 14)))\n          return 'many';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '14': function(n) {\n        if ((isBetween((n % 100), 3, 4)))\n          return 'few';\n        if ((n % 100) == 2)\n          return 'two';\n        if ((n % 100) == 1)\n          return 'one';\n        return 'other';\n      },\n      '15': function(n) {\n        if (n === 0 || (isBetween((n % 100), 2, 10)))\n          return 'few';\n        if ((isBetween((n % 100), 11, 19)))\n          return 'many';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '16': function(n) {\n        if ((n % 10) == 1 && n != 11)\n          return 'one';\n        return 'other';\n      },\n      '17': function(n) {\n        if (n == 3)\n          return 'few';\n        if (n === 0)\n          return 'zero';\n        if (n == 6)\n          return 'many';\n        if (n == 2)\n          return 'two';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '18': function(n) {\n        if (n === 0)\n          return 'zero';\n        if ((isBetween(n, 0, 2)) && n !== 0 && n != 2)\n          return 'one';\n        return 'other';\n      },\n      '19': function(n) {\n        if ((isBetween(n, 2, 10)))\n          return 'few';\n        if ((isBetween(n, 0, 1)))\n          return 'one';\n        return 'other';\n      },\n      '20': function(n) {\n        if ((isBetween((n % 10), 3, 4) || ((n % 10) == 9)) && !(\n            isBetween((n % 100), 10, 19) ||\n            isBetween((n % 100), 70, 79) ||\n            isBetween((n % 100), 90, 99)\n            ))\n          return 'few';\n        if ((n % 1000000) === 0 && n !== 0)\n          return 'many';\n        if ((n % 10) == 2 && !isIn((n % 100), [12, 72, 92]))\n          return 'two';\n        if ((n % 10) == 1 && !isIn((n % 100), [11, 71, 91]))\n          return 'one';\n        return 'other';\n      },\n      '21': function(n) {\n        if (n === 0)\n          return 'zero';\n        if (n == 1)\n          return 'one';\n        return 'other';\n      },\n      '22': function(n) {\n        if ((isBetween(n, 0, 1)) || (isBetween(n, 11, 99)))\n          return 'one';\n        return 'other';\n      },\n      '23': function(n) {\n        if ((isBetween((n % 10), 1, 2)) || (n % 20) === 0)\n          return 'one';\n        return 'other';\n      },\n      '24': function(n) {\n        if ((isBetween(n, 3, 10) || isBetween(n, 13, 19)))\n          return 'few';\n        if (isIn(n, [2, 12]))\n          return 'two';\n        if (isIn(n, [1, 11]))\n          return 'one';\n        return 'other';\n      }\n    };\n\n    // return a function that gives the plural form name for a given integer\n    var index = locales2rules[lang.replace(/-.*$/, '')];\n    if (!(index in pluralRules)) {\n      console.warn('plural form unknown for [' + lang + ']');\n      return function() { return 'other'; };\n    }\n    return pluralRules[index];\n  }\n\n  // pre-defined 'plural' macro\n  gMacros.plural = function(str, param, key, prop) {\n    var n = parseFloat(param);\n    if (isNaN(n))\n      return str;\n\n    // TODO: support other properties (l20n still doesn't...)\n    if (prop != gTextProp)\n      return str;\n\n    // initialize _pluralRules\n    if (!gMacros._pluralRules) {\n      gMacros._pluralRules = getPluralRules(gLanguage);\n    }\n    var index = '[' + gMacros._pluralRules(n) + ']';\n\n    // try to find a [zero|one|two] key if it's defined\n    if (n === 0 && (key + '[zero]') in gL10nData) {\n      str = gL10nData[key + '[zero]'][prop];\n    } else if (n == 1 && (key + '[one]') in gL10nData) {\n      str = gL10nData[key + '[one]'][prop];\n    } else if (n == 2 && (key + '[two]') in gL10nData) {\n      str = gL10nData[key + '[two]'][prop];\n    } else if ((key + index) in gL10nData) {\n      str = gL10nData[key + index][prop];\n    } else if ((key + '[other]') in gL10nData) {\n      str = gL10nData[key + '[other]'][prop];\n    }\n\n    return str;\n  };\n\n\n  /**\n   * l10n dictionary functions\n   */\n\n  // fetch an l10n object, warn if not found, apply `args' if possible\n  function getL10nData(key, args, fallback) {\n    var data = gL10nData[key];\n    if (!data) {\n      console.warn('#' + key + ' is undefined.');\n      if (!fallback) {\n        return null;\n      }\n      data = fallback;\n    }\n\n    /** This is where l10n expressions should be processed.\n      * The plan is to support C-style expressions from the l20n project;\n      * until then, only two kinds of simple expressions are supported:\n      *   {[ index ]} and {{ arguments }}.\n      */\n    var rv = {};\n    for (var prop in data) {\n      var str = data[prop];\n      str = substIndexes(str, args, key, prop);\n      str = substArguments(str, args, key);\n      rv[prop] = str;\n    }\n    return rv;\n  }\n\n  // replace {[macros]} with their values\n  function substIndexes(str, args, key, prop) {\n    var reIndex = /\\{\\[\\s*([a-zA-Z]+)\\(([a-zA-Z]+)\\)\\s*\\]\\}/;\n    var reMatch = reIndex.exec(str);\n    if (!reMatch || !reMatch.length)\n      return str;\n\n    // an index/macro has been found\n    // Note: at the moment, only one parameter is supported\n    var macroName = reMatch[1];\n    var paramName = reMatch[2];\n    var param;\n    if (args && paramName in args) {\n      param = args[paramName];\n    } else if (paramName in gL10nData) {\n      param = gL10nData[paramName];\n    }\n\n    // there's no macro parser yet: it has to be defined in gMacros\n    if (macroName in gMacros) {\n      var macro = gMacros[macroName];\n      str = macro(str, param, key, prop);\n    }\n    return str;\n  }\n\n  // replace {{arguments}} with their values\n  function substArguments(str, args, key) {\n    var reArgs = /\\{\\{\\s*(.+?)\\s*\\}\\}/g;\n    return str.replace(reArgs, function(matched_text, arg) {\n      if (args && arg in args) {\n        return args[arg];\n      }\n      if (arg in gL10nData) {\n        return gL10nData[arg];\n      }\n      console.log('argument {{' + arg + '}} for #' + key + ' is undefined.');\n      return matched_text;\n    });\n  }\n\n  // translate an HTML element\n  function translateElement(element) {\n    var l10n = getL10nAttributes(element);\n    if (!l10n.id)\n      return;\n\n    // get the related l10n object\n    var data = getL10nData(l10n.id, l10n.args);\n    if (!data) {\n      console.warn('#' + l10n.id + ' is undefined.');\n      return;\n    }\n\n    // translate element (TODO: security checks?)\n    if (data[gTextProp]) { // XXX\n      if (getChildElementCount(element) === 0) {\n        element[gTextProp] = data[gTextProp];\n      } else {\n        // this element has element children: replace the content of the first\n        // (non-empty) child textNode and clear other child textNodes\n        var children = element.childNodes;\n        var found = false;\n        for (var i = 0, l = children.length; i < l; i++) {\n          if (children[i].nodeType === 3 && /\\S/.test(children[i].nodeValue)) {\n            if (found) {\n              children[i].nodeValue = '';\n            } else {\n              children[i].nodeValue = data[gTextProp];\n              found = true;\n            }\n          }\n        }\n        // if no (non-empty) textNode is found, insert a textNode before the\n        // first element child.\n        if (!found) {\n          var textNode = document.createTextNode(data[gTextProp]);\n          element.insertBefore(textNode, element.firstChild);\n        }\n      }\n      delete data[gTextProp];\n    }\n\n    for (var k in data) {\n      element[k] = data[k];\n    }\n  }\n\n  // webkit browsers don't currently support 'children' on SVG elements...\n  function getChildElementCount(element) {\n    if (element.children) {\n      return element.children.length;\n    }\n    if (typeof element.childElementCount !== 'undefined') {\n      return element.childElementCount;\n    }\n    var count = 0;\n    for (var i = 0; i < element.childNodes.length; i++) {\n      count += element.nodeType === 1 ? 1 : 0;\n    }\n    return count;\n  }\n\n  // translate an HTML subtree\n  function translateFragment(element) {\n    element = element || document.documentElement;\n\n    // check all translatable children (= w/ a `data-l10n-id' attribute)\n    var children = getTranslatableChildren(element);\n    var elementCount = children.length;\n    for (var i = 0; i < elementCount; i++) {\n      translateElement(children[i]);\n    }\n\n    // translate element itself if necessary\n    translateElement(element);\n  }\n\n  return {\n    // get a localized string\n    get: function(key, args, fallbackString) {\n      var index = key.lastIndexOf('.');\n      var prop = gTextProp;\n      if (index > 0) { // An attribute has been specified\n        prop = key.substring(index + 1);\n        key = key.substring(0, index);\n      }\n      var fallback;\n      if (fallbackString) {\n        fallback = {};\n        fallback[prop] = fallbackString;\n      }\n      var data = getL10nData(key, args, fallback);\n      if (data && prop in data) {\n        return data[prop];\n      }\n      return '{{' + key + '}}';\n    },\n\n    // debug\n    getData: function() { return gL10nData; },\n    getText: function() { return gTextData; },\n\n    // get|set the document language\n    getLanguage: function() { return gLanguage; },\n    setLanguage: function(lang, callback) {\n      loadLocale(lang, function() {\n        if (callback)\n          callback();\n      });\n    },\n\n    // get the direction (ltr|rtl) of the current language\n    getDirection: function() {\n      // http://www.w3.org/International/questions/qa-scripts\n      // Arabic, Hebrew, Farsi, Pashto, Urdu\n      var rtlList = ['ar', 'he', 'fa', 'ps', 'ur'];\n      var shortCode = gLanguage.split('-', 1)[0];\n      return (rtlList.indexOf(shortCode) >= 0) ? 'rtl' : 'ltr';\n    },\n\n    // translate an element or document fragment\n    translate: translateFragment,\n\n    // this can be used to prevent race conditions\n    getReadyState: function() { return gReadyState; },\n    ready: function(callback) {\n      if (!callback) {\n        return;\n      } else if (gReadyState == 'complete' || gReadyState == 'interactive') {\n        window.setTimeout(function() {\n          callback();\n        });\n      } else if (document.addEventListener) {\n        document.addEventListener('localized', function once() {\n          document.removeEventListener('localized', once);\n          callback();\n        });\n      }\n    }\n  };\n}) (window, document);\n", "/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createPromiseCapability } from \"pdfjs-lib\";\nimport { getCharacterType } from \"./pdf_find_utils.js\";\nimport { scrollIntoView } from \"./ui_utils.js\";\n\nconst FindState = {\n  FOUND: 0,\n  NOT_FOUND: 1,\n  WRAPPED: 2,\n  PENDING: 3,\n};\n\nconst FIND_TIMEOUT = 250; // ms\nconst MATCH_SCROLL_OFFSET_TOP = -50; // px\nconst MATCH_SCROLL_OFFSET_LEFT = -400; // px\n\nconst CHARACTERS_TO_NORMALIZE = {\n  \"\\u2010\": \"-\", // Hyphen\n  \"\\u2018\": \"'\", // Left single quotation mark\n  \"\\u2019\": \"'\", // Right single quotation mark\n  \"\\u201A\": \"'\", // Single low-9 quotation mark\n  \"\\u201B\": \"'\", // Single high-reversed-9 quotation mark\n  \"\\u201C\": '\"', // Left double quotation mark\n  \"\\u201D\": '\"', // Right double quotation mark\n  \"\\u201E\": '\"', // Double low-9 quotation mark\n  \"\\u201F\": '\"', // Double high-reversed-9 quotation mark\n  \"\\u00BC\": \"1/4\", // Vulgar fraction one quarter\n  \"\\u00BD\": \"1/2\", // Vulgar fraction one half\n  \"\\u00BE\": \"3/4\", // Vulgar fraction three quarters\n};\n\nlet normalizationRegex = null;\nfunction normalize(text) {\n  if (!normalizationRegex) {\n    // Compile the regular expression for text normalization once.\n    const replace = Object.keys(CHARACTERS_TO_NORMALIZE).join(\"\");\n    normalizationRegex = new RegExp(`[${replace}]`, \"g\");\n  }\n  let diffs = null;\n  const normalizedText = text.replace(normalizationRegex, function (ch, index) {\n    const normalizedCh = CHARACTERS_TO_NORMALIZE[ch],\n      diff = normalizedCh.length - ch.length;\n    if (diff !== 0) {\n      (diffs ||= []).push([index, diff]);\n    }\n    return normalizedCh;\n  });\n\n  return [normalizedText, diffs];\n}\n\n// Determine the original, non-normalized, match index such that highlighting of\n// search results is correct in the `textLayer` for strings containing e.g. \"½\"\n// characters; essentially \"inverting\" the result of the `normalize` function.\nfunction getOriginalIndex(matchIndex, diffs = null) {\n  if (!diffs) {\n    return matchIndex;\n  }\n  let totalDiff = 0;\n  for (const [index, diff] of diffs) {\n    const currentIndex = index + totalDiff;\n\n    if (currentIndex >= matchIndex) {\n      break;\n    }\n    if (currentIndex + diff > matchIndex) {\n      totalDiff += matchIndex - currentIndex;\n      break;\n    }\n    totalDiff += diff;\n  }\n  return matchIndex - totalDiff;\n}\n\n/**\n * @typedef {Object} PDFFindControllerOptions\n * @property {IPDFLinkService} linkService - The navigation/linking service.\n * @property {EventBus} eventBus - The application event bus.\n */\n\n/**\n * Provides search functionality to find a given string in a PDF document.\n */\nclass PDFFindController {\n  /**\n   * @param {PDFFindControllerOptions} options\n   */\n  constructor({ linkService, eventBus }) {\n    this._linkService = linkService;\n    this._eventBus = eventBus;\n\n    this._reset();\n    eventBus._on(\"findbarclose\", this._onFindBarClose.bind(this));\n  }\n\n  get highlightMatches() {\n    return this._highlightMatches;\n  }\n\n  get pageMatches() {\n    return this._pageMatches;\n  }\n\n  get pageMatchesLength() {\n    return this._pageMatchesLength;\n  }\n\n  get selected() {\n    return this._selected;\n  }\n\n  get state() {\n    return this._state;\n  }\n\n  /**\n   * Set a reference to the PDF document in order to search it.\n   * Note that searching is not possible if this method is not called.\n   *\n   * @param {PDFDocumentProxy} pdfDocument - The PDF document to search.\n   */\n  setDocument(pdfDocument) {\n    if (this._pdfDocument) {\n      this._reset();\n    }\n    if (!pdfDocument) {\n      return;\n    }\n    this._pdfDocument = pdfDocument;\n    this._firstPageCapability.resolve();\n  }\n\n  executeCommand(cmd, state) {\n    if (!state) {\n      return;\n    }\n    const pdfDocument = this._pdfDocument;\n\n    if (this._state === null || this._shouldDirtyMatch(cmd, state)) {\n      this._dirtyMatch = true;\n    }\n    this._state = state;\n    if (cmd !== \"findhighlightallchange\") {\n      this._updateUIState(FindState.PENDING);\n    }\n\n    this._firstPageCapability.promise.then(() => {\n      // If the document was closed before searching began, or if the search\n      // operation was relevant for a previously opened document, do nothing.\n      if (\n        !this._pdfDocument ||\n        (pdfDocument && this._pdfDocument !== pdfDocument)\n      ) {\n        return;\n      }\n      this._extractText();\n\n      const findbarClosed = !this._highlightMatches;\n      const pendingTimeout = !!this._findTimeout;\n\n      if (this._findTimeout) {\n        clearTimeout(this._findTimeout);\n        this._findTimeout = null;\n      }\n      if (cmd === \"find\") {\n        // Trigger the find action with a small delay to avoid starting the\n        // search when the user is still typing (saving resources).\n        this._findTimeout = setTimeout(() => {\n          this._nextMatch();\n          this._findTimeout = null;\n        }, FIND_TIMEOUT);\n      } else if (this._dirtyMatch) {\n        // Immediately trigger searching for non-'find' operations, when the\n        // current state needs to be reset and matches re-calculated.\n        this._nextMatch();\n      } else if (cmd === \"findagain\") {\n        this._nextMatch();\n\n        // When the findbar was previously closed, and `highlightAll` is set,\n        // ensure that the matches on all active pages are highlighted again.\n        if (findbarClosed && this._state.highlightAll) {\n          this._updateAllPages();\n        }\n      } else if (cmd === \"findhighlightallchange\") {\n        // If there was a pending search operation, synchronously trigger a new\n        // search *first* to ensure that the correct matches are highlighted.\n        if (pendingTimeout) {\n          this._nextMatch();\n        } else {\n          this._highlightMatches = true;\n        }\n        this._updateAllPages(); // Update the highlighting on all active pages.\n      } else {\n        this._nextMatch();\n      }\n    });\n  }\n\n  scrollMatchIntoView({\n    element = null,\n    selectedLeft = 0,\n    pageIndex = -1,\n    matchIndex = -1,\n  }) {\n    if (!this._scrollMatches || !element) {\n      return;\n    } else if (matchIndex === -1 || matchIndex !== this._selected.matchIdx) {\n      return;\n    } else if (pageIndex === -1 || pageIndex !== this._selected.pageIdx) {\n      return;\n    }\n    this._scrollMatches = false; // Ensure that scrolling only happens once.\n\n    const spot = {\n      top: MATCH_SCROLL_OFFSET_TOP,\n      left: selectedLeft + MATCH_SCROLL_OFFSET_LEFT,\n    };\n    scrollIntoView(element, spot, /* scrollMatches = */ true);\n  }\n\n  _reset() {\n    this._highlightMatches = false;\n    this._scrollMatches = false;\n    this._pdfDocument = null;\n    this._pageMatches = [];\n    this._pageMatchesLength = [];\n    this._state = null;\n    // Currently selected match.\n    this._selected = {\n      pageIdx: -1,\n      matchIdx: -1,\n    };\n    // Where the find algorithm currently is in the document.\n    this._offset = {\n      pageIdx: null,\n      matchIdx: null,\n      wrapped: false,\n    };\n    this._extractTextPromises = [];\n    this._pageContents = []; // Stores the normalized text for each page.\n    this._pageDiffs = [];\n    this._matchesCountTotal = 0;\n    this._pagesToSearch = null;\n    this._pendingFindMatches = new Set();\n    this._resumePageIdx = null;\n    this._dirtyMatch = false;\n    clearTimeout(this._findTimeout);\n    this._findTimeout = null;\n\n    this._firstPageCapability = createPromiseCapability();\n  }\n\n  /**\n   * @type {string} The (current) normalized search query.\n   */\n  get _query() {\n    if (this._state.query !== this._rawQuery) {\n      this._rawQuery = this._state.query;\n      [this._normalizedQuery] = normalize(this._state.query);\n    }\n    return this._normalizedQuery;\n  }\n\n  _shouldDirtyMatch(cmd, state) {\n    // When the search query changes, regardless of the actual search command\n    // used, always re-calculate matches to avoid errors (fixes bug 1030622).\n    if (state.query !== this._state.query) {\n      return true;\n    }\n    switch (cmd) {\n      case \"findagain\":\n        const pageNumber = this._selected.pageIdx + 1;\n        const linkService = this._linkService;\n        // Only treat a 'findagain' event as a new search operation when it's\n        // *absolutely* certain that the currently selected match is no longer\n        // visible, e.g. as a result of the user scrolling in the document.\n        //\n        // NOTE: If only a simple `this._linkService.page` check was used here,\n        // there's a risk that consecutive 'findagain' operations could \"skip\"\n        // over matches at the top/bottom of pages thus making them completely\n        // inaccessible when there's multiple pages visible in the viewer.\n        if (\n          pageNumber >= 1 &&\n          pageNumber <= linkService.pagesCount &&\n          pageNumber !== linkService.page &&\n          !linkService.isPageVisible(pageNumber)\n        ) {\n          return true;\n        }\n        return false;\n      case \"findhighlightallchange\":\n        return false;\n    }\n    return true;\n  }\n\n  /**\n   * Helper for multi-term search that fills the `matchesWithLength` array\n   * and handles cases where one search term includes another search term (for\n   * example, \"tamed tame\" or \"this is\"). It looks for intersecting terms in\n   * the `matches` and keeps elements with a longer match length.\n   */\n  _prepareMatches(matchesWithLength, matches, matchesLength) {\n    function isSubTerm(currentIndex) {\n      const currentElem = matchesWithLength[currentIndex];\n      const nextElem = matchesWithLength[currentIndex + 1];\n\n      // Check for cases like \"TAMEd TAME\".\n      if (\n        currentIndex < matchesWithLength.length - 1 &&\n        currentElem.match === nextElem.match\n      ) {\n        currentElem.skipped = true;\n        return true;\n      }\n\n      // Check for cases like \"thIS IS\".\n      for (let i = currentIndex - 1; i >= 0; i--) {\n        const prevElem = matchesWithLength[i];\n        if (prevElem.skipped) {\n          continue;\n        }\n        if (prevElem.match + prevElem.matchLength < currentElem.match) {\n          break;\n        }\n        if (\n          prevElem.match + prevElem.matchLength >=\n          currentElem.match + currentElem.matchLength\n        ) {\n          currentElem.skipped = true;\n          return true;\n        }\n      }\n      return false;\n    }\n\n    // Sort the array of `{ match: <match>, matchLength: <matchLength> }`\n    // objects on increasing index first and on the length otherwise.\n    matchesWithLength.sort(function (a, b) {\n      return a.match === b.match\n        ? a.matchLength - b.matchLength\n        : a.match - b.match;\n    });\n    for (let i = 0, len = matchesWithLength.length; i < len; i++) {\n      if (isSubTerm(i)) {\n        continue;\n      }\n      matches.push(matchesWithLength[i].match);\n      matchesLength.push(matchesWithLength[i].matchLength);\n    }\n  }\n\n  /**\n   * Determine if the search query constitutes a \"whole word\", by comparing the\n   * first/last character type with the preceding/following character type.\n   */\n  _isEntireWord(content, startIdx, length) {\n    if (startIdx > 0) {\n      const first = content.charCodeAt(startIdx);\n      const limit = content.charCodeAt(startIdx - 1);\n      if (getCharacterType(first) === getCharacterType(limit)) {\n        return false;\n      }\n    }\n    const endIdx = startIdx + length - 1;\n    if (endIdx < content.length - 1) {\n      const last = content.charCodeAt(endIdx);\n      const limit = content.charCodeAt(endIdx + 1);\n      if (getCharacterType(last) === getCharacterType(limit)) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  _calculatePhraseMatch(query, pageIndex, pageContent, pageDiffs, entireWord) {\n    const matches = [],\n      matchesLength = [];\n    const queryLen = query.length;\n\n    let matchIdx = -queryLen;\n    while (true) {\n      matchIdx = pageContent.indexOf(query, matchIdx + queryLen);\n      if (matchIdx === -1) {\n        break;\n      }\n      if (entireWord && !this._isEntireWord(pageContent, matchIdx, queryLen)) {\n        continue;\n      }\n      const originalMatchIdx = getOriginalIndex(matchIdx, pageDiffs),\n        matchEnd = matchIdx + queryLen - 1,\n        originalQueryLen =\n          getOriginalIndex(matchEnd, pageDiffs) - originalMatchIdx + 1;\n\n      matches.push(originalMatchIdx);\n      matchesLength.push(originalQueryLen);\n    }\n    this._pageMatches[pageIndex] = matches;\n    this._pageMatchesLength[pageIndex] = matchesLength;\n  }\n\n  _calculateWordMatch(query, pageIndex, pageContent, pageDiffs, entireWord) {\n    const matchesWithLength = [];\n\n    // Divide the query into pieces and search for text in each piece.\n    const queryArray = query.match(/\\S+/g);\n    for (let i = 0, len = queryArray.length; i < len; i++) {\n      const subquery = queryArray[i];\n      const subqueryLen = subquery.length;\n\n      let matchIdx = -subqueryLen;\n      while (true) {\n        matchIdx = pageContent.indexOf(subquery, matchIdx + subqueryLen);\n        if (matchIdx === -1) {\n          break;\n        }\n        if (\n          entireWord &&\n          !this._isEntireWord(pageContent, matchIdx, subqueryLen)\n        ) {\n          continue;\n        }\n        const originalMatchIdx = getOriginalIndex(matchIdx, pageDiffs),\n          matchEnd = matchIdx + subqueryLen - 1,\n          originalQueryLen =\n            getOriginalIndex(matchEnd, pageDiffs) - originalMatchIdx + 1;\n\n        // Other searches do not, so we store the length.\n        matchesWithLength.push({\n          match: originalMatchIdx,\n          matchLength: originalQueryLen,\n          skipped: false,\n        });\n      }\n    }\n\n    // Prepare arrays for storing the matches.\n    this._pageMatchesLength[pageIndex] = [];\n    this._pageMatches[pageIndex] = [];\n\n    // Sort `matchesWithLength`, remove intersecting terms and put the result\n    // into the two arrays.\n    this._prepareMatches(\n      matchesWithLength,\n      this._pageMatches[pageIndex],\n      this._pageMatchesLength[pageIndex]\n    );\n  }\n\n  _calculateMatch(pageIndex) {\n    let pageContent = this._pageContents[pageIndex];\n    const pageDiffs = this._pageDiffs[pageIndex];\n    let query = this._query;\n    const { caseSensitive, entireWord, phraseSearch } = this._state;\n\n    if (query.length === 0) {\n      // Do nothing: the matches should be wiped out already.\n      return;\n    }\n\n    if (!caseSensitive) {\n      pageContent = pageContent.toLowerCase();\n      query = query.toLowerCase();\n    }\n\n    if (phraseSearch) {\n      this._calculatePhraseMatch(\n        query,\n        pageIndex,\n        pageContent,\n        pageDiffs,\n        entireWord\n      );\n    } else {\n      this._calculateWordMatch(\n        query,\n        pageIndex,\n        pageContent,\n        pageDiffs,\n        entireWord\n      );\n    }\n\n    // When `highlightAll` is set, ensure that the matches on previously\n    // rendered (and still active) pages are correctly highlighted.\n    if (this._state.highlightAll) {\n      this._updatePage(pageIndex);\n    }\n    if (this._resumePageIdx === pageIndex) {\n      this._resumePageIdx = null;\n      this._nextPageMatch();\n    }\n\n    // Update the match count.\n    const pageMatchesCount = this._pageMatches[pageIndex].length;\n    if (pageMatchesCount > 0) {\n      this._matchesCountTotal += pageMatchesCount;\n      this._updateUIResultsCount();\n    }\n  }\n\n  _extractText() {\n    // Perform text extraction once if this method is called multiple times.\n    if (this._extractTextPromises.length > 0) {\n      return;\n    }\n\n    let promise = Promise.resolve();\n    for (let i = 0, ii = this._linkService.pagesCount; i < ii; i++) {\n      const extractTextCapability = createPromiseCapability();\n      this._extractTextPromises[i] = extractTextCapability.promise;\n\n      promise = promise.then(() => {\n        return this._pdfDocument\n          .getPage(i + 1)\n          .then(pdfPage => {\n            return pdfPage.getTextContent({\n              normalizeWhitespace: true,\n            });\n          })\n          .then(\n            textContent => {\n              const textItems = textContent.items;\n              const strBuf = [];\n\n              for (let j = 0, jj = textItems.length; j < jj; j++) {\n                strBuf.push(textItems[j].str);\n              }\n\n              // Store the normalized page content (text items) as one string.\n              [this._pageContents[i], this._pageDiffs[i]] = normalize(\n                strBuf.join(\"\")\n              );\n              extractTextCapability.resolve(i);\n            },\n            reason => {\n              console.error(\n                `Unable to get text content for page ${i + 1}`,\n                reason\n              );\n              // Page error -- assuming no text content.\n              this._pageContents[i] = \"\";\n              this._pageDiffs[i] = null;\n              extractTextCapability.resolve(i);\n            }\n          );\n      });\n    }\n  }\n\n  _updatePage(index) {\n    if (this._scrollMatches && this._selected.pageIdx === index) {\n      // If the page is selected, scroll the page into view, which triggers\n      // rendering the page, which adds the text layer. Once the text layer\n      // is built, it will attempt to scroll the selected match into view.\n      this._linkService.page = index + 1;\n    }\n\n    this._eventBus.dispatch(\"updatetextlayermatches\", {\n      source: this,\n      pageIndex: index,\n    });\n  }\n\n  _updateAllPages() {\n    this._eventBus.dispatch(\"updatetextlayermatches\", {\n      source: this,\n      pageIndex: -1,\n    });\n  }\n\n  _nextMatch() {\n    const previous = this._state.findPrevious;\n    const currentPageIndex = this._linkService.page - 1;\n    const numPages = this._linkService.pagesCount;\n\n    this._highlightMatches = true;\n\n    if (this._dirtyMatch) {\n      // Need to recalculate the matches, reset everything.\n      this._dirtyMatch = false;\n      this._selected.pageIdx = this._selected.matchIdx = -1;\n      this._offset.pageIdx = currentPageIndex;\n      this._offset.matchIdx = null;\n      this._offset.wrapped = false;\n      this._resumePageIdx = null;\n      this._pageMatches.length = 0;\n      this._pageMatchesLength.length = 0;\n      this._matchesCountTotal = 0;\n\n      this._updateAllPages(); // Wipe out any previously highlighted matches.\n\n      for (let i = 0; i < numPages; i++) {\n        // Start finding the matches as soon as the text is extracted.\n        if (this._pendingFindMatches.has(i)) {\n          continue;\n        }\n        this._pendingFindMatches.add(i);\n        this._extractTextPromises[i].then(pageIdx => {\n          this._pendingFindMatches.delete(pageIdx);\n          this._calculateMatch(pageIdx);\n        });\n      }\n    }\n\n    // If there's no query there's no point in searching.\n    if (this._query === \"\") {\n      this._updateUIState(FindState.FOUND);\n      return;\n    }\n    // If we're waiting on a page, we return since we can't do anything else.\n    if (this._resumePageIdx) {\n      return;\n    }\n\n    const offset = this._offset;\n    // Keep track of how many pages we should maximally iterate through.\n    this._pagesToSearch = numPages;\n    // If there's already a `matchIdx` that means we are iterating through a\n    // page's matches.\n    if (offset.matchIdx !== null) {\n      const numPageMatches = this._pageMatches[offset.pageIdx].length;\n      if (\n        (!previous && offset.matchIdx + 1 < numPageMatches) ||\n        (previous && offset.matchIdx > 0)\n      ) {\n        // The simple case; we just have advance the matchIdx to select\n        // the next match on the page.\n        offset.matchIdx = previous ? offset.matchIdx - 1 : offset.matchIdx + 1;\n        this._updateMatch(/* found = */ true);\n        return;\n      }\n      // We went beyond the current page's matches, so we advance to\n      // the next page.\n      this._advanceOffsetPage(previous);\n    }\n    // Start searching through the page.\n    this._nextPageMatch();\n  }\n\n  _matchesReady(matches) {\n    const offset = this._offset;\n    const numMatches = matches.length;\n    const previous = this._state.findPrevious;\n\n    if (numMatches) {\n      // There were matches for the page, so initialize `matchIdx`.\n      offset.matchIdx = previous ? numMatches - 1 : 0;\n      this._updateMatch(/* found = */ true);\n      return true;\n    }\n    // No matches, so attempt to search the next page.\n    this._advanceOffsetPage(previous);\n    if (offset.wrapped) {\n      offset.matchIdx = null;\n      if (this._pagesToSearch < 0) {\n        // No point in wrapping again, there were no matches.\n        this._updateMatch(/* found = */ false);\n        // While matches were not found, searching for a page\n        // with matches should nevertheless halt.\n        return true;\n      }\n    }\n    // Matches were not found (and searching is not done).\n    return false;\n  }\n\n  _nextPageMatch() {\n    if (this._resumePageIdx !== null) {\n      console.error(\"There can only be one pending page.\");\n    }\n\n    let matches = null;\n    do {\n      const pageIdx = this._offset.pageIdx;\n      matches = this._pageMatches[pageIdx];\n      if (!matches) {\n        // The matches don't exist yet for processing by `_matchesReady`,\n        // so set a resume point for when they do exist.\n        this._resumePageIdx = pageIdx;\n        break;\n      }\n    } while (!this._matchesReady(matches));\n  }\n\n  _advanceOffsetPage(previous) {\n    const offset = this._offset;\n    const numPages = this._linkService.pagesCount;\n    offset.pageIdx = previous ? offset.pageIdx - 1 : offset.pageIdx + 1;\n    offset.matchIdx = null;\n\n    this._pagesToSearch--;\n\n    if (offset.pageIdx >= numPages || offset.pageIdx < 0) {\n      offset.pageIdx = previous ? numPages - 1 : 0;\n      offset.wrapped = true;\n    }\n  }\n\n  _updateMatch(found = false) {\n    let state = FindState.NOT_FOUND;\n    const wrapped = this._offset.wrapped;\n    this._offset.wrapped = false;\n\n    if (found) {\n      const previousPage = this._selected.pageIdx;\n      this._selected.pageIdx = this._offset.pageIdx;\n      this._selected.matchIdx = this._offset.matchIdx;\n      state = wrapped ? FindState.WRAPPED : FindState.FOUND;\n\n      // Update the currently selected page to wipe out any selected matches.\n      if (previousPage !== -1 && previousPage !== this._selected.pageIdx) {\n        this._updatePage(previousPage);\n      }\n    }\n\n    this._updateUIState(state, this._state.findPrevious);\n    if (this._selected.pageIdx !== -1) {\n      // Ensure that the match will be scrolled into view.\n      this._scrollMatches = true;\n\n      this._updatePage(this._selected.pageIdx);\n    }\n  }\n\n  _onFindBarClose(evt) {\n    const pdfDocument = this._pdfDocument;\n    // Since searching is asynchronous, ensure that the removal of highlighted\n    // matches (from the UI) is async too such that the 'updatetextlayermatches'\n    // events will always be dispatched in the expected order.\n    this._firstPageCapability.promise.then(() => {\n      // Only update the UI if the document is open, and is the current one.\n      if (\n        !this._pdfDocument ||\n        (pdfDocument && this._pdfDocument !== pdfDocument)\n      ) {\n        return;\n      }\n      // Ensure that a pending, not yet started, search operation is aborted.\n      if (this._findTimeout) {\n        clearTimeout(this._findTimeout);\n        this._findTimeout = null;\n      }\n      // Abort any long running searches, to avoid a match being scrolled into\n      // view *after* the findbar has been closed. In this case `this._offset`\n      // will most likely differ from `this._selected`, hence we also ensure\n      // that any new search operation will always start with a clean slate.\n      if (this._resumePageIdx) {\n        this._resumePageIdx = null;\n        this._dirtyMatch = true;\n      }\n      // Avoid the UI being in a pending state when the findbar is re-opened.\n      this._updateUIState(FindState.FOUND);\n\n      this._highlightMatches = false;\n      this._updateAllPages(); // Wipe out any previously highlighted matches.\n    });\n  }\n\n  _requestMatchesCount() {\n    const { pageIdx, matchIdx } = this._selected;\n    let current = 0,\n      total = this._matchesCountTotal;\n    if (matchIdx !== -1) {\n      for (let i = 0; i < pageIdx; i++) {\n        current += this._pageMatches[i]?.length || 0;\n      }\n      current += matchIdx + 1;\n    }\n    // When searching starts, this method may be called before the `pageMatches`\n    // have been counted (in `_calculateMatch`). Ensure that the UI won't show\n    // temporarily broken state when the active find result doesn't make sense.\n    if (current < 1 || current > total) {\n      current = total = 0;\n    }\n    return { current, total };\n  }\n\n  _updateUIResultsCount() {\n    this._eventBus.dispatch(\"updatefindmatchescount\", {\n      source: this,\n      matchesCount: this._requestMatchesCount(),\n    });\n  }\n\n  _updateUIState(state, previous) {\n    this._eventBus.dispatch(\"updatefindcontrolstate\", {\n      source: this,\n      state,\n      previous,\n      matchesCount: this._requestMatchesCount(),\n      rawQuery: this._state?.query ?? null,\n    });\n  }\n}\n\nexport { FindState, PDFFindController };\n", "/* Copyright 2018 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst CharacterType = {\n  SPACE: 0,\n  ALPHA_LETTER: 1,\n  PUNCT: 2,\n  HAN_LETTER: 3,\n  KATAKANA_LETTER: 4,\n  HIRAGANA_LETTER: 5,\n  HALFWIDTH_KATAKANA_LETTER: 6,\n  THAI_LETTER: 7,\n};\n\nfunction isAlphabeticalScript(charCode) {\n  return charCode < 0x2e80;\n}\n\nfunction isAscii(charCode) {\n  return (charCode & 0xff80) === 0;\n}\n\nfunction isAsciiAlpha(charCode) {\n  return (\n    (charCode >= /* a = */ 0x61 && charCode <= /* z = */ 0x7a) ||\n    (charCode >= /* A = */ 0x41 && charCode <= /* Z = */ 0x5a)\n  );\n}\n\nfunction isAsciiDigit(charCode) {\n  return charCode >= /* 0 = */ 0x30 && charCode <= /* 9 = */ 0x39;\n}\n\nfunction isAsciiSpace(charCode) {\n  return (\n    charCode === /* SPACE = */ 0x20 ||\n    charCode === /* TAB = */ 0x09 ||\n    charCode === /* CR = */ 0x0d ||\n    charCode === /* LF = */ 0x0a\n  );\n}\n\nfunction isHan(charCode) {\n  return (\n    (charCode >= 0x3400 && charCode <= 0x9fff) ||\n    (charCode >= 0xf900 && charCode <= 0xfaff)\n  );\n}\n\nfunction isKatakana(charCode) {\n  return charCode >= 0x30a0 && charCode <= 0x30ff;\n}\n\nfunction isHiragana(charCode) {\n  return charCode >= 0x3040 && charCode <= 0x309f;\n}\n\nfunction isHalfwidthKatakana(charCode) {\n  return charCode >= 0xff60 && charCode <= 0xff9f;\n}\n\nfunction isThai(charCode) {\n  return (charCode & 0xff80) === 0x0e00;\n}\n\n/**\n * This function is based on the word-break detection implemented in:\n * https://hg.mozilla.org/mozilla-central/file/tip/intl/lwbrk/WordBreaker.cpp\n */\nfunction getCharacterType(charCode) {\n  if (isAlphabeticalScript(charCode)) {\n    if (isAscii(charCode)) {\n      if (isAsciiSpace(charCode)) {\n        return CharacterType.SPACE;\n      } else if (\n        isAsciiAlpha(charCode) ||\n        isAsciiDigit(charCode) ||\n        charCode === /* UNDERSCORE = */ 0x5f\n      ) {\n        return CharacterType.ALPHA_LETTER;\n      }\n      return CharacterType.PUNCT;\n    } else if (isThai(charCode)) {\n      return CharacterType.THAI_LETTER;\n    } else if (charCode === /* NBSP = */ 0xa0) {\n      return CharacterType.SPACE;\n    }\n    return CharacterType.ALPHA_LETTER;\n  }\n\n  if (isHan(charCode)) {\n    return CharacterType.HAN_LETTER;\n  } else if (isKatakana(charCode)) {\n    return CharacterType.KATAKANA_LETTER;\n  } else if (isHiragana(charCode)) {\n    return CharacterType.HIRAGANA_LETTER;\n  } else if (isHalfwidthKatakana(charCode)) {\n    return CharacterType.HALFWIDTH_KATAKANA_LETTER;\n  }\n  return CharacterType.ALPHA_LETTER;\n}\n\nexport { CharacterType, getCharacterType };\n", "/* Copyright 2017 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  isValidRotation,\n  parseQueryString,\n  PresentationModeState,\n  waitOnEventOrTimeout,\n} from \"./ui_utils.js\";\n\n// Heuristic value used when force-resetting `this._blockHashChange`.\nconst HASH_CHANGE_TIMEOUT = 1000; // milliseconds\n// Heuristic value used when adding the current position to the browser history.\nconst POSITION_UPDATED_THRESHOLD = 50;\n// Heuristic value used when adding a temporary position to the browser history.\nconst UPDATE_VIEWAREA_TIMEOUT = 1000; // milliseconds\n\n/**\n * @typedef {Object} PDFHistoryOptions\n * @property {IPDFLinkService} linkService - The navigation/linking service.\n * @property {EventBus} eventBus - The application event bus.\n */\n\n/**\n * @typedef {Object} InitializeParameters\n * @property {string} fingerprint - The PDF document's unique fingerprint.\n * @property {boolean} [resetHistory] - Reset the browsing history.\n * @property {boolean} [updateUrl] - Attempt to update the document URL, with\n *   the current hash, when pushing/replacing browser history entries.\n */\n\n/**\n * @typedef {Object} PushParameters\n * @property {string} [namedDest] - The named destination. If absent, a\n *   stringified version of `explicitDest` is used.\n * @property {Array} explicitDest - The explicit destination array.\n * @property {number} pageNumber - The page to which the destination points.\n */\n\nfunction getCurrentHash() {\n  return document.location.hash;\n}\n\nclass PDFHistory {\n  /**\n   * @param {PDFHistoryOptions} options\n   */\n  constructor({ linkService, eventBus }) {\n    this.linkService = linkService;\n    this.eventBus = eventBus;\n\n    this._initialized = false;\n    this._fingerprint = \"\";\n    this.reset();\n\n    this._boundEvents = null;\n    this._isViewerInPresentationMode = false;\n    // Ensure that we don't miss either a 'presentationmodechanged' or a\n    // 'pagesinit' event, by registering the listeners immediately.\n    this.eventBus._on(\"presentationmodechanged\", evt => {\n      this._isViewerInPresentationMode =\n        evt.state !== PresentationModeState.NORMAL;\n    });\n    this.eventBus._on(\"pagesinit\", () => {\n      this._isPagesLoaded = false;\n\n      this.eventBus._on(\n        \"pagesloaded\",\n        evt => {\n          this._isPagesLoaded = !!evt.pagesCount;\n        },\n        { once: true }\n      );\n    });\n  }\n\n  /**\n   * Initialize the history for the PDF document, using either the current\n   * browser history entry or the document hash, whichever is present.\n   * @param {InitializeParameters} params\n   */\n  initialize({ fingerprint, resetHistory = false, updateUrl = false }) {\n    if (!fingerprint || typeof fingerprint !== \"string\") {\n      console.error(\n        'PDFHistory.initialize: The \"fingerprint\" must be a non-empty string.'\n      );\n      return;\n    }\n    // Ensure that any old state is always reset upon initialization.\n    if (this._initialized) {\n      this.reset();\n    }\n    const reInitialized =\n      this._fingerprint !== \"\" && this._fingerprint !== fingerprint;\n    this._fingerprint = fingerprint;\n    this._updateUrl = updateUrl === true;\n\n    this._initialized = true;\n    this._bindEvents();\n    const state = window.history.state;\n\n    this._popStateInProgress = false;\n    this._blockHashChange = 0;\n    this._currentHash = getCurrentHash();\n    this._numPositionUpdates = 0;\n\n    this._uid = this._maxUid = 0;\n    this._destination = null;\n    this._position = null;\n\n    if (!this._isValidState(state, /* checkReload = */ true) || resetHistory) {\n      const { hash, page, rotation } = this._parseCurrentHash(\n        /* checkNameddest = */ true\n      );\n\n      if (!hash || reInitialized || resetHistory) {\n        // Ensure that the browser history is reset on PDF document load.\n        this._pushOrReplaceState(null, /* forceReplace = */ true);\n        return;\n      }\n      // Ensure that the browser history is initialized correctly when\n      // the document hash is present on PDF document load.\n      this._pushOrReplaceState(\n        { hash, page, rotation },\n        /* forceReplace = */ true\n      );\n      return;\n    }\n\n    // The browser history contains a valid entry, ensure that the history is\n    // initialized correctly on PDF document load.\n    const destination = state.destination;\n    this._updateInternalState(\n      destination,\n      state.uid,\n      /* removeTemporary = */ true\n    );\n\n    if (destination.rotation !== undefined) {\n      this._initialRotation = destination.rotation;\n    }\n    if (destination.dest) {\n      this._initialBookmark = JSON.stringify(destination.dest);\n\n      // If the history is updated, e.g. through the user changing the hash,\n      // before the initial destination has become visible, then we do *not*\n      // want to potentially add `this._position` to the browser history.\n      this._destination.page = null;\n    } else if (destination.hash) {\n      this._initialBookmark = destination.hash;\n    } else if (destination.page) {\n      // Fallback case; shouldn't be necessary, but better safe than sorry.\n      this._initialBookmark = `page=${destination.page}`;\n    }\n  }\n\n  /**\n   * Reset the current `PDFHistory` instance, and consequently prevent any\n   * further updates and/or navigation of the browser history.\n   */\n  reset() {\n    if (this._initialized) {\n      this._pageHide(); // Simulate a 'pagehide' event when resetting.\n\n      this._initialized = false;\n      this._unbindEvents();\n    }\n    if (this._updateViewareaTimeout) {\n      clearTimeout(this._updateViewareaTimeout);\n      this._updateViewareaTimeout = null;\n    }\n    this._initialBookmark = null;\n    this._initialRotation = null;\n  }\n\n  /**\n   * Push an internal destination to the browser history.\n   * @param {PushParameters}\n   */\n  push({ namedDest = null, explicitDest, pageNumber }) {\n    if (!this._initialized) {\n      return;\n    }\n    if (namedDest && typeof namedDest !== \"string\") {\n      console.error(\n        \"PDFHistory.push: \" +\n          `\"${namedDest}\" is not a valid namedDest parameter.`\n      );\n      return;\n    } else if (!Array.isArray(explicitDest)) {\n      console.error(\n        \"PDFHistory.push: \" +\n          `\"${explicitDest}\" is not a valid explicitDest parameter.`\n      );\n      return;\n    } else if (!this._isValidPage(pageNumber)) {\n      // Allow an unset `pageNumber` if and only if the history is still empty;\n      // please refer to the `this._destination.page = null;` comment above.\n      if (pageNumber !== null || this._destination) {\n        console.error(\n          \"PDFHistory.push: \" +\n            `\"${pageNumber}\" is not a valid pageNumber parameter.`\n        );\n        return;\n      }\n    }\n\n    const hash = namedDest || JSON.stringify(explicitDest);\n    if (!hash) {\n      // The hash *should* never be undefined, but if that were to occur,\n      // avoid any possible issues by not updating the browser history.\n      return;\n    }\n\n    let forceReplace = false;\n    if (\n      this._destination &&\n      (isDestHashesEqual(this._destination.hash, hash) ||\n        isDestArraysEqual(this._destination.dest, explicitDest))\n    ) {\n      // When the new destination is identical to `this._destination`, and\n      // its `page` is undefined, replace the current browser history entry.\n      // NOTE: This can only occur if `this._destination` was set either:\n      //  - through the document hash being specified on load.\n      //  - through the user changing the hash of the document.\n      if (this._destination.page) {\n        return;\n      }\n      forceReplace = true;\n    }\n    if (this._popStateInProgress && !forceReplace) {\n      return;\n    }\n\n    this._pushOrReplaceState(\n      {\n        dest: explicitDest,\n        hash,\n        page: pageNumber,\n        rotation: this.linkService.rotation,\n      },\n      forceReplace\n    );\n\n    if (!this._popStateInProgress) {\n      // Prevent the browser history from updating while the new destination is\n      // being scrolled into view, to avoid potentially inconsistent state.\n      this._popStateInProgress = true;\n      // We defer the resetting of `this._popStateInProgress`, to account for\n      // e.g. zooming occurring when the new destination is being navigated to.\n      Promise.resolve().then(() => {\n        this._popStateInProgress = false;\n      });\n    }\n  }\n\n  /**\n   * Push a page to the browser history; generally the `push` method should be\n   * used instead.\n   * @param {number} pageNumber\n   */\n  pushPage(pageNumber) {\n    if (!this._initialized) {\n      return;\n    }\n    if (!this._isValidPage(pageNumber)) {\n      console.error(\n        `PDFHistory.pushPage: \"${pageNumber}\" is not a valid page number.`\n      );\n      return;\n    }\n\n    if (this._destination?.page === pageNumber) {\n      // When the new page is identical to the one in `this._destination`, we\n      // don't want to add a potential duplicate entry in the browser history.\n      return;\n    }\n    if (this._popStateInProgress) {\n      return;\n    }\n\n    this._pushOrReplaceState({\n      // Simulate an internal destination, for `this._tryPushCurrentPosition`:\n      dest: null,\n      hash: `page=${pageNumber}`,\n      page: pageNumber,\n      rotation: this.linkService.rotation,\n    });\n\n    if (!this._popStateInProgress) {\n      // Prevent the browser history from updating while the new page is\n      // being scrolled into view, to avoid potentially inconsistent state.\n      this._popStateInProgress = true;\n      // We defer the resetting of `this._popStateInProgress`, to account for\n      // e.g. zooming occurring when the new page is being navigated to.\n      Promise.resolve().then(() => {\n        this._popStateInProgress = false;\n      });\n    }\n  }\n\n  /**\n   * Push the current position to the browser history.\n   */\n  pushCurrentPosition() {\n    if (!this._initialized || this._popStateInProgress) {\n      return;\n    }\n    this._tryPushCurrentPosition();\n  }\n\n  /**\n   * Go back one step in the browser history.\n   * NOTE: Avoids navigating away from the document, useful for \"named actions\".\n   */\n  back() {\n    if (!this._initialized || this._popStateInProgress) {\n      return;\n    }\n    const state = window.history.state;\n    if (this._isValidState(state) && state.uid > 0) {\n      window.history.back();\n    }\n  }\n\n  /**\n   * Go forward one step in the browser history.\n   * NOTE: Avoids navigating away from the document, useful for \"named actions\".\n   */\n  forward() {\n    if (!this._initialized || this._popStateInProgress) {\n      return;\n    }\n    const state = window.history.state;\n    if (this._isValidState(state) && state.uid < this._maxUid) {\n      window.history.forward();\n    }\n  }\n\n  /**\n   * @type {boolean} Indicating if the user is currently moving through the\n   *   browser history, useful e.g. for skipping the next 'hashchange' event.\n   */\n  get popStateInProgress() {\n    return (\n      this._initialized &&\n      (this._popStateInProgress || this._blockHashChange > 0)\n    );\n  }\n\n  get initialBookmark() {\n    return this._initialized ? this._initialBookmark : null;\n  }\n\n  get initialRotation() {\n    return this._initialized ? this._initialRotation : null;\n  }\n\n  /**\n   * @private\n   */\n  _pushOrReplaceState(destination, forceReplace = false) {\n    const shouldReplace = forceReplace || !this._destination;\n    const newState = {\n      fingerprint: this._fingerprint,\n      uid: shouldReplace ? this._uid : this._uid + 1,\n      destination,\n    };\n\n    if (\n      typeof PDFJSDev !== \"undefined\" &&\n      PDFJSDev.test(\"CHROME\") &&\n      window.history.state?.chromecomState\n    ) {\n      // history.state.chromecomState is managed by chromecom.js.\n      newState.chromecomState = window.history.state.chromecomState;\n    }\n    this._updateInternalState(destination, newState.uid);\n\n    let newUrl;\n    if (this._updateUrl && destination?.hash) {\n      const baseUrl = document.location.href.split(\"#\")[0];\n      // Prevent errors in Firefox.\n      if (!baseUrl.startsWith(\"file://\")) {\n        newUrl = `${baseUrl}#${destination.hash}`;\n      }\n    }\n    if (shouldReplace) {\n      window.history.replaceState(newState, \"\", newUrl);\n    } else {\n      window.history.pushState(newState, \"\", newUrl);\n    }\n\n    if (\n      typeof PDFJSDev !== \"undefined\" &&\n      PDFJSDev.test(\"CHROME\") &&\n      top === window\n    ) {\n      // eslint-disable-next-line no-undef\n      chrome.runtime.sendMessage(\"showPageAction\");\n    }\n  }\n\n  /**\n   * @private\n   */\n  _tryPushCurrentPosition(temporary = false) {\n    if (!this._position) {\n      return;\n    }\n    let position = this._position;\n    if (temporary) {\n      position = Object.assign(Object.create(null), this._position);\n      position.temporary = true;\n    }\n\n    if (!this._destination) {\n      this._pushOrReplaceState(position);\n      return;\n    }\n    if (this._destination.temporary) {\n      // Always replace a previous *temporary* position.\n      this._pushOrReplaceState(position, /* forceReplace = */ true);\n      return;\n    }\n    if (this._destination.hash === position.hash) {\n      return; // The current document position has not changed.\n    }\n    if (\n      !this._destination.page &&\n      (POSITION_UPDATED_THRESHOLD <= 0 ||\n        this._numPositionUpdates <= POSITION_UPDATED_THRESHOLD)\n    ) {\n      // `this._destination` was set through the user changing the hash of\n      // the document. Do not add `this._position` to the browser history,\n      // to avoid \"flooding\" it with lots of (nearly) identical entries,\n      // since we cannot ensure that the document position has changed.\n      return;\n    }\n\n    let forceReplace = false;\n    if (\n      this._destination.page >= position.first &&\n      this._destination.page <= position.page\n    ) {\n      // When the `page` of `this._destination` is still visible, do not\n      // update the browsing history when `this._destination` either:\n      //  - contains an internal destination, since in this case we\n      //    cannot ensure that the document position has actually changed.\n      //  - was set through the user changing the hash of the document.\n      if (this._destination.dest !== undefined || !this._destination.first) {\n        return;\n      }\n      // To avoid \"flooding\" the browser history, replace the current entry.\n      forceReplace = true;\n    }\n    this._pushOrReplaceState(position, forceReplace);\n  }\n\n  /**\n   * @private\n   */\n  _isValidPage(val) {\n    return (\n      Number.isInteger(val) && val > 0 && val <= this.linkService.pagesCount\n    );\n  }\n\n  /**\n   * @private\n   */\n  _isValidState(state, checkReload = false) {\n    if (!state) {\n      return false;\n    }\n    if (state.fingerprint !== this._fingerprint) {\n      if (checkReload) {\n        // Potentially accept the history entry, even if the fingerprints don't\n        // match, when the viewer was reloaded (see issue 6847).\n        if (\n          typeof state.fingerprint !== \"string\" ||\n          state.fingerprint.length !== this._fingerprint.length\n        ) {\n          return false;\n        }\n        const [perfEntry] = performance.getEntriesByType(\"navigation\");\n        if (perfEntry?.type !== \"reload\") {\n          return false;\n        }\n      } else {\n        // This should only occur in viewers with support for opening more than\n        // one PDF document, e.g. the GENERIC viewer.\n        return false;\n      }\n    }\n    if (!Number.isInteger(state.uid) || state.uid < 0) {\n      return false;\n    }\n    if (state.destination === null || typeof state.destination !== \"object\") {\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * @private\n   */\n  _updateInternalState(destination, uid, removeTemporary = false) {\n    if (this._updateViewareaTimeout) {\n      // When updating `this._destination`, make sure that we always wait for\n      // the next 'updateviewarea' event before (potentially) attempting to\n      // push the current position to the browser history.\n      clearTimeout(this._updateViewareaTimeout);\n      this._updateViewareaTimeout = null;\n    }\n    if (removeTemporary && destination?.temporary) {\n      // When the `destination` comes from the browser history,\n      // we no longer treat it as a *temporary* position.\n      delete destination.temporary;\n    }\n    this._destination = destination;\n    this._uid = uid;\n    this._maxUid = Math.max(this._maxUid, uid);\n    // This should always be reset when `this._destination` is updated.\n    this._numPositionUpdates = 0;\n  }\n\n  /**\n   * @private\n   */\n  _parseCurrentHash(checkNameddest = false) {\n    const hash = unescape(getCurrentHash()).substring(1);\n    const params = parseQueryString(hash);\n\n    const nameddest = params.get(\"nameddest\") || \"\";\n    let page = params.get(\"page\") | 0;\n\n    if (!this._isValidPage(page) || (checkNameddest && nameddest.length > 0)) {\n      page = null;\n    }\n    return { hash, page, rotation: this.linkService.rotation };\n  }\n\n  /**\n   * @private\n   */\n  _updateViewarea({ location }) {\n    if (this._updateViewareaTimeout) {\n      clearTimeout(this._updateViewareaTimeout);\n      this._updateViewareaTimeout = null;\n    }\n\n    this._position = {\n      hash: this._isViewerInPresentationMode\n        ? `page=${location.pageNumber}`\n        : location.pdfOpenParams.substring(1),\n      page: this.linkService.page,\n      first: location.pageNumber,\n      rotation: location.rotation,\n    };\n\n    if (this._popStateInProgress) {\n      return;\n    }\n\n    if (\n      POSITION_UPDATED_THRESHOLD > 0 &&\n      this._isPagesLoaded &&\n      this._destination &&\n      !this._destination.page\n    ) {\n      // If the current destination was set through the user changing the hash\n      // of the document, we will usually not try to push the current position\n      // to the browser history; see `this._tryPushCurrentPosition()`.\n      //\n      // To prevent `this._tryPushCurrentPosition()` from effectively being\n      // reduced to a no-op in this case, we will assume that the position\n      // *did* in fact change if the 'updateviewarea' event was dispatched\n      // more than `POSITION_UPDATED_THRESHOLD` times.\n      this._numPositionUpdates++;\n    }\n\n    if (UPDATE_VIEWAREA_TIMEOUT > 0) {\n      // When closing the browser, a 'pagehide' event will be dispatched which\n      // *should* allow us to push the current position to the browser history.\n      // In practice, it seems that the event is arriving too late in order for\n      // the session history to be successfully updated.\n      // (For additional details, please refer to the discussion in\n      //  https://bugzilla.mozilla.org/show_bug.cgi?id=1153393.)\n      //\n      // To workaround this we attempt to *temporarily* add the current position\n      // to the browser history only when the viewer is *idle*,\n      // i.e. when scrolling and/or zooming does not occur.\n      //\n      // PLEASE NOTE: It's absolutely imperative that the browser history is\n      // *not* updated too often, since that would render the viewer more or\n      // less unusable. Hence the use of a timeout to delay the update until\n      // the viewer has been idle for `UPDATE_VIEWAREA_TIMEOUT` milliseconds.\n      this._updateViewareaTimeout = setTimeout(() => {\n        if (!this._popStateInProgress) {\n          this._tryPushCurrentPosition(/* temporary = */ true);\n        }\n        this._updateViewareaTimeout = null;\n      }, UPDATE_VIEWAREA_TIMEOUT);\n    }\n  }\n\n  /**\n   * @private\n   */\n  _popState({ state }) {\n    const newHash = getCurrentHash(),\n      hashChanged = this._currentHash !== newHash;\n    this._currentHash = newHash;\n\n    if (\n      (typeof PDFJSDev !== \"undefined\" &&\n        PDFJSDev.test(\"CHROME\") &&\n        state?.chromecomState &&\n        !this._isValidState(state)) ||\n      !state\n    ) {\n      // This case corresponds to the user changing the hash of the document.\n      this._uid++;\n\n      const { hash, page, rotation } = this._parseCurrentHash();\n      this._pushOrReplaceState(\n        { hash, page, rotation },\n        /* forceReplace = */ true\n      );\n      return;\n    }\n    if (!this._isValidState(state)) {\n      // This should only occur in viewers with support for opening more than\n      // one PDF document, e.g. the GENERIC viewer.\n      return;\n    }\n\n    // Prevent the browser history from updating until the new destination,\n    // as stored in the browser history, has been scrolled into view.\n    this._popStateInProgress = true;\n\n    if (hashChanged) {\n      // When the hash changed, implying that the 'popstate' event will be\n      // followed by a 'hashchange' event, then we do *not* want to update the\n      // browser history when handling the 'hashchange' event (in web/app.js)\n      // since that would *overwrite* the new destination navigated to below.\n      //\n      // To avoid accidentally disabling all future user-initiated hash changes,\n      // if there's e.g. another 'hashchange' listener that stops the event\n      // propagation, we make sure to always force-reset `this._blockHashChange`\n      // after `HASH_CHANGE_TIMEOUT` milliseconds have passed.\n      this._blockHashChange++;\n      waitOnEventOrTimeout({\n        target: window,\n        name: \"hashchange\",\n        delay: HASH_CHANGE_TIMEOUT,\n      }).then(() => {\n        this._blockHashChange--;\n      });\n    }\n\n    // Navigate to the new destination.\n    const destination = state.destination;\n    this._updateInternalState(\n      destination,\n      state.uid,\n      /* removeTemporary = */ true\n    );\n\n    if (isValidRotation(destination.rotation)) {\n      this.linkService.rotation = destination.rotation;\n    }\n    if (destination.dest) {\n      this.linkService.goToDestination(destination.dest);\n    } else if (destination.hash) {\n      this.linkService.setHash(destination.hash);\n    } else if (destination.page) {\n      // Fallback case; shouldn't be necessary, but better safe than sorry.\n      this.linkService.page = destination.page;\n    }\n\n    // Since `PDFLinkService.goToDestination` is asynchronous, we thus defer the\n    // resetting of `this._popStateInProgress` slightly.\n    Promise.resolve().then(() => {\n      this._popStateInProgress = false;\n    });\n  }\n\n  /**\n   * @private\n   */\n  _pageHide() {\n    // Attempt to push the `this._position` into the browser history when\n    // navigating away from the document. This is *only* done if the history\n    // is empty/temporary, since otherwise an existing browser history entry\n    // will end up being overwritten (given that new entries cannot be pushed\n    // into the browser history when the 'unload' event has already fired).\n    if (!this._destination || this._destination.temporary) {\n      this._tryPushCurrentPosition();\n    }\n  }\n\n  /**\n   * @private\n   */\n  _bindEvents() {\n    if (this._boundEvents) {\n      return; // The event listeners were already added.\n    }\n    this._boundEvents = {\n      updateViewarea: this._updateViewarea.bind(this),\n      popState: this._popState.bind(this),\n      pageHide: this._pageHide.bind(this),\n    };\n\n    this.eventBus._on(\"updateviewarea\", this._boundEvents.updateViewarea);\n    window.addEventListener(\"popstate\", this._boundEvents.popState);\n    window.addEventListener(\"pagehide\", this._boundEvents.pageHide);\n  }\n\n  /**\n   * @private\n   */\n  _unbindEvents() {\n    if (!this._boundEvents) {\n      return; // The event listeners were already removed.\n    }\n    this.eventBus._off(\"updateviewarea\", this._boundEvents.updateViewarea);\n    window.removeEventListener(\"popstate\", this._boundEvents.popState);\n    window.removeEventListener(\"pagehide\", this._boundEvents.pageHide);\n\n    this._boundEvents = null;\n  }\n}\n\nfunction isDestHashesEqual(destHash, pushHash) {\n  if (typeof destHash !== \"string\" || typeof pushHash !== \"string\") {\n    return false;\n  }\n  if (destHash === pushHash) {\n    return true;\n  }\n  const nameddest = parseQueryString(destHash).get(\"nameddest\");\n  if (nameddest === pushHash) {\n    return true;\n  }\n  return false;\n}\n\nfunction isDestArraysEqual(firstDest, secondDest) {\n  function isEntryEqual(first, second) {\n    if (typeof first !== typeof second) {\n      return false;\n    }\n    if (Array.isArray(first) || Array.isArray(second)) {\n      return false;\n    }\n    if (first !== null && typeof first === \"object\" && second !== null) {\n      if (Object.keys(first).length !== Object.keys(second).length) {\n        return false;\n      }\n      for (const key in first) {\n        if (!isEntryEqual(first[key], second[key])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    return first === second || (Number.isNaN(first) && Number.isNaN(second));\n  }\n\n  if (!(Array.isArray(firstDest) && Array.isArray(secondDest))) {\n    return false;\n  }\n  if (firstDest.length !== secondDest.length) {\n    return false;\n  }\n  for (let i = 0, ii = firstDest.length; i < ii; i++) {\n    if (!isEntryEqual(firstDest[i], secondDest[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport { isDestArraysEqual, isDestHashesEqual, PDFHistory };\n", "/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  approximateFraction,\n  CSS_UNITS,\n  DEFAULT_SCALE,\n  getOutputScale,\n  RendererType,\n  roundToDivide,\n  TextLayerMode,\n} from \"./ui_utils.js\";\nimport {\n  createPromiseCapability,\n  RenderingCancelledException,\n  SVGGraphics,\n} from \"pdfjs-lib\";\nimport { compatibilityParams } from \"./app_options.js\";\nimport { NullL10n } from \"./l10n_utils.js\";\nimport { RenderingStates } from \"./pdf_rendering_queue.js\";\n\n/**\n * @typedef {Object} PDFPageViewOptions\n * @property {HTMLDivElement} container - The viewer element.\n * @property {EventBus} eventBus - The application event bus.\n * @property {number} id - The page unique ID (normally its number).\n * @property {number} scale - The page scale display.\n * @property {PageViewport} defaultViewport - The page viewport.\n * @property {Promise<OptionalContentConfig>} [optionalContentConfigPromise] -\n *   A promise that is resolved with an {@link OptionalContentConfig} instance.\n *   The default value is `null`.\n * @property {PDFRenderingQueue} renderingQueue - The rendering queue object.\n * @property {IPDFTextLayerFactory} textLayerFactory\n * @property {number} [textLayerMode] - Controls if the text layer used for\n *   selection and searching is created, and if the improved text selection\n *   behaviour is enabled. The constants from {TextLayerMode} should be used.\n *   The default value is `TextLayerMode.ENABLE`.\n * @property {IPDFAnnotationLayerFactory} annotationLayerFactory\n * @property {IPDFXfaLayerFactory} xfaLayerFactory\n * @property {IPDFStructTreeLayerFactory} structTreeLayerFactory\n * @property {string} [imageResourcesPath] - Path for image resources, mainly\n *   for annotation icons. Include trailing slash.\n * @property {boolean} renderInteractiveForms - Turns on rendering of\n *   interactive form elements. The default value is `true`.\n * @property {string} renderer - 'canvas' or 'svg'. The default is 'canvas'.\n * @property {boolean} [useOnlyCssZoom] - Enables CSS only zooming. The default\n *   value is `false`.\n * @property {number} [maxCanvasPixels] - The maximum supported canvas size in\n *   total pixels, i.e. width * height. Use -1 for no limit. The default value\n *   is 4096 * 4096 (16 mega-pixels).\n * @property {IL10n} l10n - Localization service.\n */\n\nconst MAX_CANVAS_PIXELS = compatibilityParams.maxCanvasPixels || 16777216;\n\n/**\n * @implements {IRenderableView}\n */\nclass PDFPageView {\n  /**\n   * @param {PDFPageViewOptions} options\n   */\n  constructor(options) {\n    const container = options.container;\n    const defaultViewport = options.defaultViewport;\n\n    this.id = options.id;\n    this.renderingId = \"page\" + this.id;\n\n    this.pdfPage = null;\n    this.pageLabel = null;\n    this.rotation = 0;\n    this.scale = options.scale || DEFAULT_SCALE;\n    this.viewport = defaultViewport;\n    this.pdfPageRotate = defaultViewport.rotation;\n    this._optionalContentConfigPromise =\n      options.optionalContentConfigPromise || null;\n    this.hasRestrictedScaling = false;\n    this.textLayerMode = Number.isInteger(options.textLayerMode)\n      ? options.textLayerMode\n      : TextLayerMode.ENABLE;\n    this.imageResourcesPath = options.imageResourcesPath || \"\";\n    this.renderInteractiveForms = options.renderInteractiveForms !== false;\n    this.useOnlyCssZoom = options.useOnlyCssZoom || false;\n    this.maxCanvasPixels = options.maxCanvasPixels || MAX_CANVAS_PIXELS;\n\n    this.eventBus = options.eventBus;\n    this.renderingQueue = options.renderingQueue;\n    this.textLayerFactory = options.textLayerFactory;\n    this.annotationLayerFactory = options.annotationLayerFactory;\n    this.xfaLayerFactory = options.xfaLayerFactory;\n    this.structTreeLayerFactory = options.structTreeLayerFactory;\n    this.renderer = options.renderer || RendererType.CANVAS;\n    this.l10n = options.l10n || NullL10n;\n\n    this.paintTask = null;\n    this.paintedViewportMap = new WeakMap();\n    this.renderingState = RenderingStates.INITIAL;\n    this.resume = null;\n    this._renderError = null;\n    this._isStandalone = !this.renderingQueue?.hasViewer();\n\n    this.annotationLayer = null;\n    this.textLayer = null;\n    this.zoomLayer = null;\n    this.xfaLayer = null;\n    this.structTreeLayer = null;\n\n    const div = document.createElement(\"div\");\n    div.className = \"page\";\n    div.style.width = Math.floor(this.viewport.width) + \"px\";\n    div.style.height = Math.floor(this.viewport.height) + \"px\";\n    div.setAttribute(\"data-page-number\", this.id);\n    div.setAttribute(\"role\", \"region\");\n    this.l10n.get(\"page_landmark\", { page: this.id }).then(msg => {\n      div.setAttribute(\"aria-label\", msg);\n    });\n    this.div = div;\n\n    container.appendChild(div);\n  }\n\n  setPdfPage(pdfPage) {\n    this.pdfPage = pdfPage;\n    this.pdfPageRotate = pdfPage.rotate;\n\n    const totalRotation = (this.rotation + this.pdfPageRotate) % 360;\n    this.viewport = pdfPage.getViewport({\n      scale: this.scale * CSS_UNITS,\n      rotation: totalRotation,\n    });\n    this.reset();\n  }\n\n  destroy() {\n    this.reset();\n    if (this.pdfPage) {\n      this.pdfPage.cleanup();\n    }\n  }\n\n  /**\n   * @private\n   */\n  async _renderAnnotationLayer() {\n    let error = null;\n    try {\n      await this.annotationLayer.render(this.viewport, \"display\");\n    } catch (ex) {\n      error = ex;\n    } finally {\n      this.eventBus.dispatch(\"annotationlayerrendered\", {\n        source: this,\n        pageNumber: this.id,\n        error,\n      });\n    }\n  }\n\n  /**\n   * @private\n   */\n  async _renderXfaLayer() {\n    let error = null;\n    try {\n      await this.xfaLayer.render(this.viewport, \"display\");\n    } catch (ex) {\n      error = ex;\n    } finally {\n      this.eventBus.dispatch(\"xfalayerrendered\", {\n        source: this,\n        pageNumber: this.id,\n        error,\n      });\n    }\n  }\n\n  /**\n   * @private\n   */\n  _resetZoomLayer(removeFromDOM = false) {\n    if (!this.zoomLayer) {\n      return;\n    }\n    const zoomLayerCanvas = this.zoomLayer.firstChild;\n    this.paintedViewportMap.delete(zoomLayerCanvas);\n    // Zeroing the width and height causes Firefox to release graphics\n    // resources immediately, which can greatly reduce memory consumption.\n    zoomLayerCanvas.width = 0;\n    zoomLayerCanvas.height = 0;\n\n    if (removeFromDOM) {\n      // Note: `ChildNode.remove` doesn't throw if the parent node is undefined.\n      this.zoomLayer.remove();\n    }\n    this.zoomLayer = null;\n  }\n\n  reset({\n    keepZoomLayer = false,\n    keepAnnotationLayer = false,\n    keepXfaLayer = false,\n  } = {}) {\n    this.cancelRendering({ keepAnnotationLayer, keepXfaLayer });\n    this.renderingState = RenderingStates.INITIAL;\n\n    const div = this.div;\n    div.style.width = Math.floor(this.viewport.width) + \"px\";\n    div.style.height = Math.floor(this.viewport.height) + \"px\";\n\n    const childNodes = div.childNodes,\n      zoomLayerNode = (keepZoomLayer && this.zoomLayer) || null,\n      annotationLayerNode =\n        (keepAnnotationLayer && this.annotationLayer?.div) || null,\n      xfaLayerNode = (keepXfaLayer && this.xfaLayer?.div) || null;\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      const node = childNodes[i];\n      switch (node) {\n        case zoomLayerNode:\n        case annotationLayerNode:\n        case xfaLayerNode:\n          continue;\n      }\n      div.removeChild(node);\n    }\n    div.removeAttribute(\"data-loaded\");\n\n    if (annotationLayerNode) {\n      // Hide the annotation layer until all elements are resized\n      // so they are not displayed on the already resized page.\n      this.annotationLayer.hide();\n    }\n    if (xfaLayerNode) {\n      // Hide the XFA layer until all elements are resized\n      // so they are not displayed on the already resized page.\n      this.xfaLayer.hide();\n    }\n\n    if (!zoomLayerNode) {\n      if (this.canvas) {\n        this.paintedViewportMap.delete(this.canvas);\n        // Zeroing the width and height causes Firefox to release graphics\n        // resources immediately, which can greatly reduce memory consumption.\n        this.canvas.width = 0;\n        this.canvas.height = 0;\n        delete this.canvas;\n      }\n      this._resetZoomLayer();\n    }\n    if (this.svg) {\n      this.paintedViewportMap.delete(this.svg);\n      delete this.svg;\n    }\n\n    this.loadingIconDiv = document.createElement(\"div\");\n    this.loadingIconDiv.className = \"loadingIcon\";\n    this.loadingIconDiv.setAttribute(\"role\", \"img\");\n    this.l10n.get(\"loading\").then(msg => {\n      this.loadingIconDiv?.setAttribute(\"aria-label\", msg);\n    });\n    div.appendChild(this.loadingIconDiv);\n  }\n\n  update(scale, rotation, optionalContentConfigPromise = null) {\n    this.scale = scale || this.scale;\n    // The rotation may be zero.\n    if (typeof rotation !== \"undefined\") {\n      this.rotation = rotation;\n    }\n    if (optionalContentConfigPromise instanceof Promise) {\n      this._optionalContentConfigPromise = optionalContentConfigPromise;\n    }\n    if (this._isStandalone) {\n      const doc = document.documentElement;\n      doc.style.setProperty(\"--zoom-factor\", this.scale);\n    }\n\n    const totalRotation = (this.rotation + this.pdfPageRotate) % 360;\n    this.viewport = this.viewport.clone({\n      scale: this.scale * CSS_UNITS,\n      rotation: totalRotation,\n    });\n\n    if (this.svg) {\n      this.cssTransform({\n        target: this.svg,\n        redrawAnnotationLayer: true,\n        redrawXfaLayer: true,\n      });\n\n      this.eventBus.dispatch(\"pagerendered\", {\n        source: this,\n        pageNumber: this.id,\n        cssTransform: true,\n        timestamp: performance.now(),\n        error: this._renderError,\n      });\n      return;\n    }\n\n    let isScalingRestricted = false;\n    if (this.canvas && this.maxCanvasPixels > 0) {\n      const outputScale = this.outputScale;\n      if (\n        ((Math.floor(this.viewport.width) * outputScale.sx) | 0) *\n          ((Math.floor(this.viewport.height) * outputScale.sy) | 0) >\n        this.maxCanvasPixels\n      ) {\n        isScalingRestricted = true;\n      }\n    }\n\n    if (this.canvas) {\n      if (\n        this.useOnlyCssZoom ||\n        (this.hasRestrictedScaling && isScalingRestricted)\n      ) {\n        this.cssTransform({\n          target: this.canvas,\n          redrawAnnotationLayer: true,\n          redrawXfaLayer: true,\n        });\n\n        this.eventBus.dispatch(\"pagerendered\", {\n          source: this,\n          pageNumber: this.id,\n          cssTransform: true,\n          timestamp: performance.now(),\n          error: this._renderError,\n        });\n        return;\n      }\n      if (!this.zoomLayer && !this.canvas.hidden) {\n        this.zoomLayer = this.canvas.parentNode;\n        this.zoomLayer.style.position = \"absolute\";\n      }\n    }\n    if (this.zoomLayer) {\n      this.cssTransform({ target: this.zoomLayer.firstChild });\n    }\n    this.reset({\n      keepZoomLayer: true,\n      keepAnnotationLayer: true,\n      keepXfaLayer: true,\n    });\n  }\n\n  /**\n   * PLEASE NOTE: Most likely you want to use the `this.reset()` method,\n   *              rather than calling this one directly.\n   */\n  cancelRendering({ keepAnnotationLayer = false, keepXfaLayer = false } = {}) {\n    if (this.paintTask) {\n      this.paintTask.cancel();\n      this.paintTask = null;\n    }\n    this.resume = null;\n\n    if (this.textLayer) {\n      this.textLayer.cancel();\n      this.textLayer = null;\n    }\n    if (\n      this.annotationLayer &&\n      (!keepAnnotationLayer || !this.annotationLayer.div)\n    ) {\n      this.annotationLayer.cancel();\n      this.annotationLayer = null;\n    }\n    if (this.xfaLayer && (!keepXfaLayer || !this.xfaLayer.div)) {\n      this.xfaLayer.cancel();\n      this.xfaLayer = null;\n    }\n    if (this._onTextLayerRendered) {\n      this.eventBus._off(\"textlayerrendered\", this._onTextLayerRendered);\n      this._onTextLayerRendered = null;\n    }\n  }\n\n  cssTransform({\n    target,\n    redrawAnnotationLayer = false,\n    redrawXfaLayer = false,\n  }) {\n    // Scale target (canvas or svg), its wrapper and page container.\n    const width = this.viewport.width;\n    const height = this.viewport.height;\n    const div = this.div;\n    target.style.width =\n      target.parentNode.style.width =\n      div.style.width =\n        Math.floor(width) + \"px\";\n    target.style.height =\n      target.parentNode.style.height =\n      div.style.height =\n        Math.floor(height) + \"px\";\n    // The canvas may have been originally rotated; rotate relative to that.\n    const relativeRotation =\n      this.viewport.rotation - this.paintedViewportMap.get(target).rotation;\n    const absRotation = Math.abs(relativeRotation);\n    let scaleX = 1,\n      scaleY = 1;\n    if (absRotation === 90 || absRotation === 270) {\n      // Scale x and y because of the rotation.\n      scaleX = height / width;\n      scaleY = width / height;\n    }\n    target.style.transform = `rotate(${relativeRotation}deg) scale(${scaleX}, ${scaleY})`;\n\n    if (this.textLayer) {\n      // Rotating the text layer is more complicated since the divs inside the\n      // the text layer are rotated.\n      // TODO: This could probably be simplified by drawing the text layer in\n      // one orientation and then rotating overall.\n      const textLayerViewport = this.textLayer.viewport;\n      const textRelativeRotation =\n        this.viewport.rotation - textLayerViewport.rotation;\n      const textAbsRotation = Math.abs(textRelativeRotation);\n      let scale = width / textLayerViewport.width;\n      if (textAbsRotation === 90 || textAbsRotation === 270) {\n        scale = width / textLayerViewport.height;\n      }\n      const textLayerDiv = this.textLayer.textLayerDiv;\n      let transX, transY;\n      switch (textAbsRotation) {\n        case 0:\n          transX = transY = 0;\n          break;\n        case 90:\n          transX = 0;\n          transY = \"-\" + textLayerDiv.style.height;\n          break;\n        case 180:\n          transX = \"-\" + textLayerDiv.style.width;\n          transY = \"-\" + textLayerDiv.style.height;\n          break;\n        case 270:\n          transX = \"-\" + textLayerDiv.style.width;\n          transY = 0;\n          break;\n        default:\n          console.error(\"Bad rotation value.\");\n          break;\n      }\n\n      textLayerDiv.style.transform =\n        `rotate(${textAbsRotation}deg) ` +\n        `scale(${scale}) ` +\n        `translate(${transX}, ${transY})`;\n      textLayerDiv.style.transformOrigin = \"0% 0%\";\n    }\n\n    if (redrawAnnotationLayer && this.annotationLayer) {\n      this._renderAnnotationLayer();\n    }\n    if (redrawXfaLayer && this.xfaLayer) {\n      this._renderXfaLayer();\n    }\n  }\n\n  get width() {\n    return this.viewport.width;\n  }\n\n  get height() {\n    return this.viewport.height;\n  }\n\n  getPagePoint(x, y) {\n    return this.viewport.convertToPdfPoint(x, y);\n  }\n\n  draw() {\n    if (this.renderingState !== RenderingStates.INITIAL) {\n      console.error(\"Must be in new state before drawing\");\n      this.reset(); // Ensure that we reset all state to prevent issues.\n    }\n    const { div, pdfPage } = this;\n\n    if (!pdfPage) {\n      this.renderingState = RenderingStates.FINISHED;\n\n      if (this.loadingIconDiv) {\n        div.removeChild(this.loadingIconDiv);\n        delete this.loadingIconDiv;\n      }\n      return Promise.reject(new Error(\"pdfPage is not loaded\"));\n    }\n\n    this.renderingState = RenderingStates.RUNNING;\n\n    // Wrap the canvas so that if it has a CSS transform for high DPI the\n    // overflow will be hidden in Firefox.\n    const canvasWrapper = document.createElement(\"div\");\n    canvasWrapper.style.width = div.style.width;\n    canvasWrapper.style.height = div.style.height;\n    canvasWrapper.classList.add(\"canvasWrapper\");\n\n    if (this.annotationLayer?.div) {\n      // The annotation layer needs to stay on top.\n      div.insertBefore(canvasWrapper, this.annotationLayer.div);\n    } else {\n      div.appendChild(canvasWrapper);\n    }\n\n    let textLayer = null;\n    if (this.textLayerMode !== TextLayerMode.DISABLE && this.textLayerFactory) {\n      const textLayerDiv = document.createElement(\"div\");\n      textLayerDiv.className = \"textLayer\";\n      textLayerDiv.style.width = canvasWrapper.style.width;\n      textLayerDiv.style.height = canvasWrapper.style.height;\n      if (this.annotationLayer?.div) {\n        // The annotation layer needs to stay on top.\n        div.insertBefore(textLayerDiv, this.annotationLayer.div);\n      } else {\n        div.appendChild(textLayerDiv);\n      }\n\n      textLayer = this.textLayerFactory.createTextLayerBuilder(\n        textLayerDiv,\n        this.id - 1,\n        this.viewport,\n        this.textLayerMode === TextLayerMode.ENABLE_ENHANCE,\n        this.eventBus\n      );\n    }\n    this.textLayer = textLayer;\n\n    if (this.xfaLayer?.div) {\n      // The xfa layer needs to stay on top.\n      div.appendChild(this.xfaLayer.div);\n    }\n\n    let renderContinueCallback = null;\n    if (this.renderingQueue) {\n      renderContinueCallback = cont => {\n        if (!this.renderingQueue.isHighestPriority(this)) {\n          this.renderingState = RenderingStates.PAUSED;\n          this.resume = () => {\n            this.renderingState = RenderingStates.RUNNING;\n            cont();\n          };\n          return;\n        }\n        cont();\n      };\n    }\n\n    const finishPaintTask = async (error = null) => {\n      // The paintTask may have been replaced by a new one, so only remove\n      // the reference to the paintTask if it matches the one that is\n      // triggering this callback.\n      if (paintTask === this.paintTask) {\n        this.paintTask = null;\n      }\n\n      if (error instanceof RenderingCancelledException) {\n        this._renderError = null;\n        return;\n      }\n      this._renderError = error;\n\n      this.renderingState = RenderingStates.FINISHED;\n\n      if (this.loadingIconDiv) {\n        div.removeChild(this.loadingIconDiv);\n        delete this.loadingIconDiv;\n      }\n      this._resetZoomLayer(/* removeFromDOM = */ true);\n\n      this.eventBus.dispatch(\"pagerendered\", {\n        source: this,\n        pageNumber: this.id,\n        cssTransform: false,\n        timestamp: performance.now(),\n        error: this._renderError,\n      });\n\n      if (error) {\n        throw error;\n      }\n    };\n\n    const paintTask =\n      this.renderer === RendererType.SVG\n        ? this.paintOnSvg(canvasWrapper)\n        : this.paintOnCanvas(canvasWrapper);\n    paintTask.onRenderContinue = renderContinueCallback;\n    this.paintTask = paintTask;\n\n    const resultPromise = paintTask.promise.then(\n      () => {\n        return finishPaintTask(null).then(() => {\n          if (textLayer) {\n            const readableStream = pdfPage.streamTextContent({\n              normalizeWhitespace: true,\n              includeMarkedContent: true,\n            });\n            textLayer.setTextContentStream(readableStream);\n            textLayer.render();\n          }\n        });\n      },\n      function (reason) {\n        return finishPaintTask(reason);\n      }\n    );\n\n    if (this.annotationLayerFactory) {\n      if (!this.annotationLayer) {\n        this.annotationLayer =\n          this.annotationLayerFactory.createAnnotationLayerBuilder(\n            div,\n            pdfPage,\n            /* annotationStorage = */ null,\n            this.imageResourcesPath,\n            this.renderInteractiveForms,\n            this.l10n,\n            /* enableScripting */ null,\n            /* hasJSActionsPromise = */ null,\n            /* mouseState = */ null\n          );\n      }\n      this._renderAnnotationLayer();\n    }\n\n    if (this.xfaLayerFactory) {\n      if (!this.xfaLayer) {\n        this.xfaLayer = this.xfaLayerFactory.createXfaLayerBuilder(\n          div,\n          pdfPage,\n          /* annotationStorage = */ null\n        );\n      }\n      this._renderXfaLayer();\n    }\n\n    // The structure tree is currently only supported when the text layer is\n    // enabled and a canvas is used for rendering.\n    if (this.structTreeLayerFactory && this.textLayer && this.canvas) {\n      // The structure tree must be generated after the text layer for the\n      // aria-owns to work.\n      this._onTextLayerRendered = event => {\n        if (event.pageNumber !== this.id) {\n          return;\n        }\n        this.eventBus._off(\"textlayerrendered\", this._onTextLayerRendered);\n        this._onTextLayerRendered = null;\n\n        if (!this.canvas) {\n          return; // The canvas was removed, prevent errors below.\n        }\n        this.pdfPage.getStructTree().then(tree => {\n          if (!tree) {\n            return;\n          }\n          if (!this.canvas) {\n            return; // The canvas was removed, prevent errors below.\n          }\n          const treeDom = this.structTreeLayer.render(tree);\n          treeDom.classList.add(\"structTree\");\n          this.canvas.appendChild(treeDom);\n        });\n      };\n      this.eventBus._on(\"textlayerrendered\", this._onTextLayerRendered);\n      this.structTreeLayer =\n        this.structTreeLayerFactory.createStructTreeLayerBuilder(pdfPage);\n    }\n\n    div.setAttribute(\"data-loaded\", true);\n\n    this.eventBus.dispatch(\"pagerender\", {\n      source: this,\n      pageNumber: this.id,\n    });\n    return resultPromise;\n  }\n\n  paintOnCanvas(canvasWrapper) {\n    const renderCapability = createPromiseCapability();\n    const result = {\n      promise: renderCapability.promise,\n      onRenderContinue(cont) {\n        cont();\n      },\n      cancel() {\n        renderTask.cancel();\n      },\n    };\n\n    const viewport = this.viewport;\n    const canvas = document.createElement(\"canvas\");\n\n    // Keep the canvas hidden until the first draw callback, or until drawing\n    // is complete when `!this.renderingQueue`, to prevent black flickering.\n    canvas.hidden = true;\n    let isCanvasHidden = true;\n    const showCanvas = function () {\n      if (isCanvasHidden) {\n        canvas.hidden = false;\n        isCanvasHidden = false;\n      }\n    };\n\n    canvasWrapper.appendChild(canvas);\n    this.canvas = canvas;\n\n    if (\n      typeof PDFJSDev === \"undefined\" ||\n      PDFJSDev.test(\"MOZCENTRAL || GENERIC\")\n    ) {\n      canvas.mozOpaque = true;\n    }\n\n    const ctx = canvas.getContext(\"2d\", { alpha: false });\n    const outputScale = getOutputScale(ctx);\n    this.outputScale = outputScale;\n\n    if (this.useOnlyCssZoom) {\n      const actualSizeViewport = viewport.clone({ scale: CSS_UNITS });\n      // Use a scale that makes the canvas have the originally intended size\n      // of the page.\n      outputScale.sx *= actualSizeViewport.width / viewport.width;\n      outputScale.sy *= actualSizeViewport.height / viewport.height;\n      outputScale.scaled = true;\n    }\n\n    if (this.maxCanvasPixels > 0) {\n      const pixelsInViewport = viewport.width * viewport.height;\n      const maxScale = Math.sqrt(this.maxCanvasPixels / pixelsInViewport);\n      if (outputScale.sx > maxScale || outputScale.sy > maxScale) {\n        outputScale.sx = maxScale;\n        outputScale.sy = maxScale;\n        outputScale.scaled = true;\n        this.hasRestrictedScaling = true;\n      } else {\n        this.hasRestrictedScaling = false;\n      }\n    }\n\n    const sfx = approximateFraction(outputScale.sx);\n    const sfy = approximateFraction(outputScale.sy);\n    canvas.width = roundToDivide(viewport.width * outputScale.sx, sfx[0]);\n    canvas.height = roundToDivide(viewport.height * outputScale.sy, sfy[0]);\n    canvas.style.width = roundToDivide(viewport.width, sfx[1]) + \"px\";\n    canvas.style.height = roundToDivide(viewport.height, sfy[1]) + \"px\";\n    // Add the viewport so it's known what it was originally drawn with.\n    this.paintedViewportMap.set(canvas, viewport);\n\n    // Rendering area\n    const transform = !outputScale.scaled\n      ? null\n      : [outputScale.sx, 0, 0, outputScale.sy, 0, 0];\n    const renderContext = {\n      canvasContext: ctx,\n      transform,\n      viewport: this.viewport,\n      renderInteractiveForms: this.renderInteractiveForms,\n      optionalContentConfigPromise: this._optionalContentConfigPromise,\n    };\n    const renderTask = this.pdfPage.render(renderContext);\n    renderTask.onContinue = function (cont) {\n      showCanvas();\n      if (result.onRenderContinue) {\n        result.onRenderContinue(cont);\n      } else {\n        cont();\n      }\n    };\n\n    renderTask.promise.then(\n      function () {\n        showCanvas();\n        renderCapability.resolve(undefined);\n      },\n      function (error) {\n        showCanvas();\n        renderCapability.reject(error);\n      }\n    );\n    return result;\n  }\n\n  paintOnSvg(wrapper) {\n    if (\n      typeof PDFJSDev !== \"undefined\" &&\n      PDFJSDev.test(\"MOZCENTRAL || CHROME\")\n    ) {\n      // Return a mock object, to prevent errors such as e.g.\n      // \"TypeError: paintTask.promise is undefined\".\n      return {\n        promise: Promise.reject(new Error(\"SVG rendering is not supported.\")),\n        onRenderContinue(cont) {},\n        cancel() {},\n      };\n    }\n\n    let cancelled = false;\n    const ensureNotCancelled = () => {\n      if (cancelled) {\n        throw new RenderingCancelledException(\n          `Rendering cancelled, page ${this.id}`,\n          \"svg\"\n        );\n      }\n    };\n\n    const pdfPage = this.pdfPage;\n    const actualSizeViewport = this.viewport.clone({ scale: CSS_UNITS });\n    const promise = pdfPage.getOperatorList().then(opList => {\n      ensureNotCancelled();\n      const svgGfx = new SVGGraphics(\n        pdfPage.commonObjs,\n        pdfPage.objs,\n        /* forceDataSchema = */ compatibilityParams.disableCreateObjectURL\n      );\n      return svgGfx.getSVG(opList, actualSizeViewport).then(svg => {\n        ensureNotCancelled();\n        this.svg = svg;\n        this.paintedViewportMap.set(svg, actualSizeViewport);\n\n        svg.style.width = wrapper.style.width;\n        svg.style.height = wrapper.style.height;\n        this.renderingState = RenderingStates.FINISHED;\n        wrapper.appendChild(svg);\n      });\n    });\n\n    return {\n      promise,\n      onRenderContinue(cont) {\n        cont();\n      },\n      cancel() {\n        cancelled = true;\n      },\n    };\n  }\n\n  /**\n   * @param {string|null} label\n   */\n  setPageLabel(label) {\n    this.pageLabel = typeof label === \"string\" ? label : null;\n\n    if (this.pageLabel !== null) {\n      this.div.setAttribute(\"data-page-label\", this.pageLabel);\n    } else {\n      this.div.removeAttribute(\"data-page-label\");\n    }\n  }\n}\n\nexport { PDFPageView };\n", "/* Copyright 2012 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RenderingCancelledException } from \"pdfjs-lib\";\n\nconst CLEANUP_TIMEOUT = 30000;\n\nconst RenderingStates = {\n  INITIAL: 0,\n  RUNNING: 1,\n  PAUSED: 2,\n  FINISHED: 3,\n};\n\n/**\n * Controls rendering of the views for pages and thumbnails.\n */\nclass PDFRenderingQueue {\n  constructor() {\n    this.pdfViewer = null;\n    this.pdfThumbnailViewer = null;\n    this.onIdle = null;\n    this.highestPriorityPage = null;\n    this.idleTimeout = null;\n    this.printing = false;\n    this.isThumbnailViewEnabled = false;\n  }\n\n  /**\n   * @param {PDFViewer} pdfViewer\n   */\n  setViewer(pdfViewer) {\n    this.pdfViewer = pdfViewer;\n  }\n\n  /**\n   * @param {PDFThumbnailViewer} pdfThumbnailViewer\n   */\n  setThumbnailViewer(pdfThumbnailViewer) {\n    this.pdfThumbnailViewer = pdfThumbnailViewer;\n  }\n\n  /**\n   * @param {IRenderableView} view\n   * @returns {boolean}\n   */\n  isHighestPriority(view) {\n    return this.highestPriorityPage === view.renderingId;\n  }\n\n  /**\n   * @returns {boolean}\n   */\n  hasViewer() {\n    return !!this.pdfViewer;\n  }\n\n  /**\n   * @param {Object} currentlyVisiblePages\n   */\n  renderHighestPriority(currentlyVisiblePages) {\n    if (this.idleTimeout) {\n      clearTimeout(this.idleTimeout);\n      this.idleTimeout = null;\n    }\n\n    // Pages have a higher priority than thumbnails, so check them first.\n    if (this.pdfViewer.forceRendering(currentlyVisiblePages)) {\n      return;\n    }\n    // No pages needed rendering, so check thumbnails.\n    if (this.pdfThumbnailViewer && this.isThumbnailViewEnabled) {\n      if (this.pdfThumbnailViewer.forceRendering()) {\n        return;\n      }\n    }\n\n    if (this.printing) {\n      // If printing is currently ongoing do not reschedule cleanup.\n      return;\n    }\n\n    if (this.onIdle) {\n      this.idleTimeout = setTimeout(this.onIdle.bind(this), CLEANUP_TIMEOUT);\n    }\n  }\n\n  /**\n   * @param {Object} visible\n   * @param {Array} views\n   * @param {boolean} scrolledDown\n   */\n  getHighestPriority(visible, views, scrolledDown) {\n    /**\n     * The state has changed. Figure out which page has the highest priority to\n     * render next (if any).\n     *\n     * Priority:\n     * 1. visible pages\n     * 2. if last scrolled down, the page after the visible pages, or\n     *    if last scrolled up, the page before the visible pages\n     */\n    const visibleViews = visible.views;\n\n    const numVisible = visibleViews.length;\n    if (numVisible === 0) {\n      return null;\n    }\n    for (let i = 0; i < numVisible; ++i) {\n      const view = visibleViews[i].view;\n      if (!this.isViewFinished(view)) {\n        return view;\n      }\n    }\n\n    // All the visible views have rendered; try to render next/previous pages.\n    if (scrolledDown) {\n      const nextPageIndex = visible.last.id;\n      // IDs start at 1, so no need to add 1.\n      if (views[nextPageIndex] && !this.isViewFinished(views[nextPageIndex])) {\n        return views[nextPageIndex];\n      }\n    } else {\n      const previousPageIndex = visible.first.id - 2;\n      if (\n        views[previousPageIndex] &&\n        !this.isViewFinished(views[previousPageIndex])\n      ) {\n        return views[previousPageIndex];\n      }\n    }\n    // Everything that needs to be rendered has been.\n    return null;\n  }\n\n  /**\n   * @param {IRenderableView} view\n   * @returns {boolean}\n   */\n  isViewFinished(view) {\n    return view.renderingState === RenderingStates.FINISHED;\n  }\n\n  /**\n   * Render a page or thumbnail view. This calls the appropriate function\n   * based on the views state. If the view is already rendered it will return\n   * `false`.\n   *\n   * @param {IRenderableView} view\n   */\n  renderView(view) {\n    switch (view.renderingState) {\n      case RenderingStates.FINISHED:\n        return false;\n      case RenderingStates.PAUSED:\n        this.highestPriorityPage = view.renderingId;\n        view.resume();\n        break;\n      case RenderingStates.RUNNING:\n        this.highestPriorityPage = view.renderingId;\n        break;\n      case RenderingStates.INITIAL:\n        this.highestPriorityPage = view.renderingId;\n        view\n          .draw()\n          .finally(() => {\n            this.renderHighestPriority();\n          })\n          .catch(reason => {\n            if (reason instanceof RenderingCancelledException) {\n              return;\n            }\n            console.error(`renderView: \"${reason}\"`);\n          });\n        break;\n    }\n    return true;\n  }\n}\n\nexport { PDFRenderingQueue, RenderingStates };\n", "/* Copyright 2021 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createPromiseCapability, shadow } from \"pdfjs-lib\";\nimport { apiPageLayoutToSpreadMode } from \"./ui_utils.js\";\nimport { RenderingStates } from \"./pdf_rendering_queue.js\";\n\n/**\n * @typedef {Object} PDFScriptingManagerOptions\n * @property {EventBus} eventBus - The application event bus.\n * @property {string} sandboxBundleSrc - The path and filename of the scripting\n *   bundle.\n * @property {Object} [scriptingFactory] - The factory that is used when\n *   initializing scripting; must contain a `createScripting` method.\n *   PLEASE NOTE: Primarily intended for the default viewer use-case.\n * @property {function} [docPropertiesLookup] - The function that is used to\n *   lookup the necessary document properties.\n */\n\nclass PDFScriptingManager {\n  /**\n   * @param {PDFScriptingManagerOptions} options\n   */\n  constructor({\n    eventBus,\n    sandboxBundleSrc = null,\n    scriptingFactory = null,\n    docPropertiesLookup = null,\n  }) {\n    this._pdfDocument = null;\n    this._pdfViewer = null;\n    this._closeCapability = null;\n    this._destroyCapability = null;\n\n    this._scripting = null;\n    this._mouseState = Object.create(null);\n    this._ready = false;\n\n    this._eventBus = eventBus;\n    this._sandboxBundleSrc = sandboxBundleSrc;\n    this._scriptingFactory = scriptingFactory;\n    this._docPropertiesLookup = docPropertiesLookup;\n\n    // The default viewer already handles adding/removing of DOM events,\n    // hence limit this to only the viewer components.\n    if (\n      typeof PDFJSDev !== \"undefined\" &&\n      PDFJSDev.test(\"COMPONENTS\") &&\n      !this._scriptingFactory\n    ) {\n      window.addEventListener(\"updatefromsandbox\", event => {\n        this._eventBus.dispatch(\"updatefromsandbox\", {\n          source: window,\n          detail: event.detail,\n        });\n      });\n    }\n  }\n\n  setViewer(pdfViewer) {\n    this._pdfViewer = pdfViewer;\n  }\n\n  async setDocument(pdfDocument) {\n    if (this._pdfDocument) {\n      await this._destroyScripting();\n    }\n    this._pdfDocument = pdfDocument;\n\n    if (!pdfDocument) {\n      return;\n    }\n    const [objects, calculationOrder, docActions] = await Promise.all([\n      pdfDocument.getFieldObjects(),\n      pdfDocument.getCalculationOrderIds(),\n      pdfDocument.getJSActions(),\n    ]);\n\n    if (!objects && !docActions) {\n      // No FieldObjects or JavaScript actions were found in the document.\n      await this._destroyScripting();\n      return;\n    }\n    if (pdfDocument !== this._pdfDocument) {\n      return; // The document was closed while the data resolved.\n    }\n    try {\n      this._scripting = this._createScripting();\n    } catch (error) {\n      console.error(`PDFScriptingManager.setDocument: \"${error?.message}\".`);\n\n      await this._destroyScripting();\n      return;\n    }\n\n    this._internalEvents.set(\"updatefromsandbox\", event => {\n      if (event?.source !== window) {\n        return;\n      }\n      this._updateFromSandbox(event.detail);\n    });\n    this._internalEvents.set(\"dispatcheventinsandbox\", event => {\n      this._scripting?.dispatchEventInSandbox(event.detail);\n    });\n\n    this._internalEvents.set(\"pagechanging\", ({ pageNumber, previous }) => {\n      if (pageNumber === previous) {\n        return; // The current page didn't change.\n      }\n      this._dispatchPageClose(previous);\n      this._dispatchPageOpen(pageNumber);\n    });\n    this._internalEvents.set(\"pagerendered\", ({ pageNumber }) => {\n      if (!this._pageOpenPending.has(pageNumber)) {\n        return; // No pending \"PageOpen\" event for the newly rendered page.\n      }\n      if (pageNumber !== this._pdfViewer.currentPageNumber) {\n        return; // The newly rendered page is no longer the current one.\n      }\n      this._dispatchPageOpen(pageNumber);\n    });\n    this._internalEvents.set(\"pagesdestroy\", async event => {\n      await this._dispatchPageClose(this._pdfViewer.currentPageNumber);\n\n      await this._scripting?.dispatchEventInSandbox({\n        id: \"doc\",\n        name: \"WillClose\",\n      });\n\n      this._closeCapability?.resolve();\n    });\n\n    this._domEvents.set(\"mousedown\", event => {\n      this._mouseState.isDown = true;\n    });\n    this._domEvents.set(\"mouseup\", event => {\n      this._mouseState.isDown = false;\n    });\n\n    for (const [name, listener] of this._internalEvents) {\n      this._eventBus._on(name, listener);\n    }\n    for (const [name, listener] of this._domEvents) {\n      window.addEventListener(name, listener);\n    }\n\n    try {\n      const docProperties = await this._getDocProperties();\n      if (pdfDocument !== this._pdfDocument) {\n        return; // The document was closed while the properties resolved.\n      }\n\n      await this._scripting.createSandbox({\n        objects,\n        calculationOrder,\n        appInfo: {\n          platform: navigator.platform,\n          language: navigator.language,\n        },\n        docInfo: {\n          ...docProperties,\n          actions: docActions,\n        },\n      });\n\n      this._eventBus.dispatch(\"sandboxcreated\", { source: this });\n    } catch (error) {\n      console.error(`PDFScriptingManager.setDocument: \"${error?.message}\".`);\n\n      await this._destroyScripting();\n      return;\n    }\n\n    await this._scripting?.dispatchEventInSandbox({\n      id: \"doc\",\n      name: \"Open\",\n    });\n    await this._dispatchPageOpen(\n      this._pdfViewer.currentPageNumber,\n      /* initialize = */ true\n    );\n\n    // Defer this slightly, to ensure that scripting is *fully* initialized.\n    Promise.resolve().then(() => {\n      if (pdfDocument === this._pdfDocument) {\n        this._ready = true;\n      }\n    });\n  }\n\n  async dispatchWillSave(detail) {\n    return this._scripting?.dispatchEventInSandbox({\n      id: \"doc\",\n      name: \"WillSave\",\n    });\n  }\n\n  async dispatchDidSave(detail) {\n    return this._scripting?.dispatchEventInSandbox({\n      id: \"doc\",\n      name: \"DidSave\",\n    });\n  }\n\n  async dispatchWillPrint(detail) {\n    return this._scripting?.dispatchEventInSandbox({\n      id: \"doc\",\n      name: \"WillPrint\",\n    });\n  }\n\n  async dispatchDidPrint(detail) {\n    return this._scripting?.dispatchEventInSandbox({\n      id: \"doc\",\n      name: \"DidPrint\",\n    });\n  }\n\n  get mouseState() {\n    return this._mouseState;\n  }\n\n  get destroyPromise() {\n    return this._destroyCapability?.promise || null;\n  }\n\n  get ready() {\n    return this._ready;\n  }\n\n  /**\n   * @private\n   */\n  get _internalEvents() {\n    return shadow(this, \"_internalEvents\", new Map());\n  }\n\n  /**\n   * @private\n   */\n  get _domEvents() {\n    return shadow(this, \"_domEvents\", new Map());\n  }\n\n  /**\n   * @private\n   */\n  get _pageOpenPending() {\n    return shadow(this, \"_pageOpenPending\", new Set());\n  }\n\n  /**\n   * @private\n   */\n  get _visitedPages() {\n    return shadow(this, \"_visitedPages\", new Map());\n  }\n\n  /**\n   * @private\n   */\n  async _updateFromSandbox(detail) {\n    // Ignore some events, see below, that don't make sense in PresentationMode.\n    const isInPresentationMode =\n      this._pdfViewer.isInPresentationMode ||\n      this._pdfViewer.isChangingPresentationMode;\n\n    const { id, siblings, command, value } = detail;\n    if (!id) {\n      switch (command) {\n        case \"clear\":\n          console.clear();\n          break;\n        case \"error\":\n          console.error(value);\n          break;\n        case \"layout\":\n          this._pdfViewer.spreadMode = apiPageLayoutToSpreadMode(value);\n          break;\n        case \"page-num\":\n          this._pdfViewer.currentPageNumber = value + 1;\n          break;\n        case \"print\":\n          await this._pdfViewer.pagesPromise;\n          this._eventBus.dispatch(\"print\", { source: this });\n          break;\n        case \"println\":\n          console.log(value);\n          break;\n        case \"zoom\":\n          if (isInPresentationMode) {\n            return;\n          }\n          this._pdfViewer.currentScaleValue = value;\n          break;\n      }\n      return;\n    }\n\n    if (isInPresentationMode) {\n      if (detail.focus) {\n        return;\n      }\n    }\n    delete detail.id;\n    delete detail.siblings;\n\n    const ids = siblings ? [id, ...siblings] : [id];\n    for (const elementId of ids) {\n      const element = document.getElementById(elementId);\n      if (element) {\n        element.dispatchEvent(new CustomEvent(\"updatefromsandbox\", { detail }));\n      } else {\n        // The element hasn't been rendered yet, use the AnnotationStorage.\n        this._pdfDocument?.annotationStorage.setValue(elementId, detail);\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  async _dispatchPageOpen(pageNumber, initialize = false) {\n    const pdfDocument = this._pdfDocument,\n      visitedPages = this._visitedPages;\n\n    if (initialize) {\n      this._closeCapability = createPromiseCapability();\n    }\n    if (!this._closeCapability) {\n      return; // Scripting isn't fully initialized yet.\n    }\n    const pageView = this._pdfViewer.getPageView(/* index = */ pageNumber - 1);\n\n    if (pageView?.renderingState !== RenderingStates.FINISHED) {\n      this._pageOpenPending.add(pageNumber);\n      return; // Wait for the page to finish rendering.\n    }\n    this._pageOpenPending.delete(pageNumber);\n\n    const actionsPromise = (async () => {\n      // Avoid sending, and thus serializing, the `actions` data more than once.\n      const actions = await (!visitedPages.has(pageNumber)\n        ? pageView.pdfPage?.getJSActions()\n        : null);\n      if (pdfDocument !== this._pdfDocument) {\n        return; // The document was closed while the actions resolved.\n      }\n\n      await this._scripting?.dispatchEventInSandbox({\n        id: \"page\",\n        name: \"PageOpen\",\n        pageNumber,\n        actions,\n      });\n    })();\n    visitedPages.set(pageNumber, actionsPromise);\n  }\n\n  /**\n   * @private\n   */\n  async _dispatchPageClose(pageNumber) {\n    const pdfDocument = this._pdfDocument,\n      visitedPages = this._visitedPages;\n\n    if (!this._closeCapability) {\n      return; // Scripting isn't fully initialized yet.\n    }\n    if (this._pageOpenPending.has(pageNumber)) {\n      return; // The page is still rendering; no \"PageOpen\" event dispatched.\n    }\n    const actionsPromise = visitedPages.get(pageNumber);\n    if (!actionsPromise) {\n      return; // The \"PageClose\" event must be preceded by a \"PageOpen\" event.\n    }\n    visitedPages.set(pageNumber, null);\n\n    // Ensure that the \"PageOpen\" event is dispatched first.\n    await actionsPromise;\n    if (pdfDocument !== this._pdfDocument) {\n      return; // The document was closed while the actions resolved.\n    }\n\n    await this._scripting?.dispatchEventInSandbox({\n      id: \"page\",\n      name: \"PageClose\",\n      pageNumber,\n    });\n  }\n\n  /**\n   * @returns {Promise<Object>} A promise that is resolved with an {Object}\n   *   containing the necessary document properties; please find the expected\n   *   format in `PDFViewerApplication._scriptingDocProperties`.\n   * @private\n   */\n  async _getDocProperties() {\n    if (this._docPropertiesLookup) {\n      return this._docPropertiesLookup(this._pdfDocument);\n    }\n    if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"COMPONENTS\")) {\n      const { docPropertiesLookup } = require(\"./generic_scripting.js\");\n\n      return docPropertiesLookup(this._pdfDocument);\n    }\n    throw new Error(\"_getDocProperties: Unable to lookup properties.\");\n  }\n\n  /**\n   * @private\n   */\n  _createScripting() {\n    this._destroyCapability = createPromiseCapability();\n\n    if (this._scripting) {\n      throw new Error(\"_createScripting: Scripting already exists.\");\n    }\n    if (this._scriptingFactory) {\n      return this._scriptingFactory.createScripting({\n        sandboxBundleSrc: this._sandboxBundleSrc,\n      });\n    }\n    if (typeof PDFJSDev !== \"undefined\" && PDFJSDev.test(\"COMPONENTS\")) {\n      const { GenericScripting } = require(\"./generic_scripting.js\");\n\n      return new GenericScripting(this._sandboxBundleSrc);\n    }\n    throw new Error(\"_createScripting: Cannot create scripting.\");\n  }\n\n  /**\n   * @private\n   */\n  async _destroyScripting() {\n    if (!this._scripting) {\n      this._pdfDocument = null;\n\n      this._destroyCapability?.resolve();\n      return;\n    }\n    if (this._closeCapability) {\n      await Promise.race([\n        this._closeCapability.promise,\n        new Promise(resolve => {\n          // Avoid the scripting/sandbox-destruction hanging indefinitely.\n          setTimeout(resolve, 1000);\n        }),\n      ]).catch(reason => {\n        // Ignore any errors, to ensure that the sandbox is always destroyed.\n      });\n      this._closeCapability = null;\n    }\n    this._pdfDocument = null;\n\n    try {\n      await this._scripting.destroySandbox();\n    } catch (ex) {}\n\n    for (const [name, listener] of this._internalEvents) {\n      this._eventBus._off(name, listener);\n    }\n    this._internalEvents.clear();\n\n    for (const [name, listener] of this._domEvents) {\n      window.removeEventListener(name, listener);\n    }\n    this._domEvents.clear();\n\n    this._pageOpenPending.clear();\n    this._visitedPages.clear();\n\n    this._scripting = null;\n    delete this._mouseState.isDown;\n    this._ready = false;\n\n    this._destroyCapability?.resolve();\n  }\n}\n\nexport { PDFScriptingManager };\n", "/* Copyright 2020 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getPdfFilenameFromUrl, loadScript } from \"pdfjs-lib\";\n\nasync function docPropertiesLookup(pdfDocument) {\n  const url = \"\",\n    baseUrl = url.split(\"#\")[0];\n  // eslint-disable-next-line prefer-const\n  let { info, metadata, contentDispositionFilename, contentLength } =\n    await pdfDocument.getMetadata();\n\n  if (!contentLength) {\n    const { length } = await pdfDocument.getDownloadInfo();\n    contentLength = length;\n  }\n\n  return {\n    ...info,\n    baseURL: baseUrl,\n    filesize: contentLength,\n    filename: contentDispositionFilename || getPdfFilenameFromUrl(url),\n    metadata: metadata?.getRaw(),\n    authors: metadata?.get(\"dc:creator\"),\n    numPages: pdfDocument.numPages,\n    URL: url,\n  };\n}\n\nclass GenericScripting {\n  constructor(sandboxBundleSrc) {\n    this._ready = loadScript(\n      sandboxBundleSrc,\n      /* removeScriptElement = */ true\n    ).then(() => {\n      return window.pdfjsSandbox.QuickJSSandbox();\n    });\n  }\n\n  async createSandbox(data) {\n    const sandbox = await this._ready;\n    sandbox.create(data);\n  }\n\n  async dispatchEventInSandbox(event) {\n    const sandbox = await this._ready;\n    sandbox.dispatchEvent(event);\n  }\n\n  async destroySandbox() {\n    const sandbox = await this._ready;\n    sandbox.nukeSandbox();\n  }\n}\n\nexport { docPropertiesLookup, GenericScripting };\n", "/* Copyright 2017 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { BaseViewer } from \"./base_viewer.js\";\nimport { shadow } from \"pdfjs-lib\";\n\nclass PDFSinglePageViewer extends BaseViewer {\n  constructor(options) {\n    super(options);\n\n    this.eventBus._on(\"pagesinit\", evt => {\n      // Since the pages are placed in a `DocumentFragment`, make sure that\n      // the current page becomes visible upon loading of the document.\n      this._ensurePageViewVisible();\n    });\n  }\n\n  get _viewerElement() {\n    // Since we only want to display *one* page at a time when using the\n    // `PDFSinglePageViewer`, we cannot append them to the `viewer` DOM element.\n    // Instead, they are placed in a `DocumentFragment`, and only the current\n    // page is displayed in the viewer (refer to `this._ensurePageViewVisible`).\n    return shadow(this, \"_viewerElement\", this._shadowViewer);\n  }\n\n  get _pageWidthScaleFactor() {\n    return 1;\n  }\n\n  _resetView() {\n    super._resetView();\n    this._previousPageNumber = 1;\n    this._shadowViewer = document.createDocumentFragment();\n    this._updateScrollDown = null;\n  }\n\n  _ensurePageViewVisible() {\n    const pageView = this._pages[this._currentPageNumber - 1];\n    const previousPageView = this._pages[this._previousPageNumber - 1];\n\n    const viewerNodes = this.viewer.childNodes;\n    switch (viewerNodes.length) {\n      case 0: // Should *only* occur on initial loading.\n        this.viewer.appendChild(pageView.div);\n        break;\n      case 1: // The normal page-switching case.\n        if (viewerNodes[0] !== previousPageView.div) {\n          throw new Error(\n            \"_ensurePageViewVisible: Unexpected previously visible page.\"\n          );\n        }\n        if (pageView === previousPageView) {\n          break; // The correct page is already visible.\n        }\n        // Switch visible pages, and reset the viewerContainer scroll position.\n        this._shadowViewer.appendChild(previousPageView.div);\n        this.viewer.appendChild(pageView.div);\n\n        this.container.scrollTop = 0;\n        break;\n      default:\n        throw new Error(\n          \"_ensurePageViewVisible: Only one page should be visible at a time.\"\n        );\n    }\n    this._previousPageNumber = this._currentPageNumber;\n  }\n\n  _scrollUpdate() {\n    if (this._updateScrollDown) {\n      this._updateScrollDown();\n    }\n    super._scrollUpdate();\n  }\n\n  _scrollIntoView({ pageDiv, pageSpot = null, pageNumber = null }) {\n    if (pageNumber) {\n      // Ensure that `this._currentPageNumber` is correct.\n      this._setCurrentPageNumber(pageNumber);\n    }\n    const scrolledDown = this._currentPageNumber >= this._previousPageNumber;\n\n    this._ensurePageViewVisible();\n    // Ensure that rendering always occurs, to avoid showing a blank page,\n    // even if the current position doesn't change when the page is scrolled.\n    this.update();\n\n    super._scrollIntoView({ pageDiv, pageSpot, pageNumber });\n\n    // Since scrolling is tracked using `requestAnimationFrame`, update the\n    // scroll direction during the next `this._scrollUpdate` invocation.\n    this._updateScrollDown = () => {\n      this.scroll.down = scrolledDown;\n      this._updateScrollDown = null;\n    };\n  }\n\n  _getVisiblePages() {\n    return this._getCurrentVisiblePage();\n  }\n\n  _updateHelper(visiblePages) {}\n\n  get _isScrollModeHorizontal() {\n    // The Scroll/Spread modes are never used in `PDFSinglePageViewer`.\n    return shadow(this, \"_isScrollModeHorizontal\", false);\n  }\n\n  _updateScrollMode() {}\n\n  _updateSpreadMode() {}\n\n  _getPageAdvance() {\n    return 1;\n  }\n}\n\nexport { PDFSinglePageViewer };\n", "/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createPromiseCapability, version } from \"pdfjs-lib\";\nimport {\n  CSS_UNITS,\n  DEFAULT_SCALE,\n  DEFAULT_SCALE_VALUE,\n  getVisibleElements,\n  isPortraitOrientation,\n  isValidRotation,\n  isValidScrollMode,\n  isValidSpreadMode,\n  MAX_AUTO_SCALE,\n  moveToEndOfArray,\n  PresentationModeState,\n  RendererType,\n  SCROLLBAR_PADDING,\n  scrollIntoView,\n  ScrollMode,\n  SpreadMode,\n  TextLayerMode,\n  UNKNOWN_SCALE,\n  VERTICAL_PADDING,\n  watchScroll,\n} from \"./ui_utils.js\";\nimport { PDFRenderingQueue, RenderingStates } from \"./pdf_rendering_queue.js\";\nimport { AnnotationLayerBuilder } from \"./annotation_layer_builder.js\";\nimport { NullL10n } from \"./l10n_utils.js\";\nimport { PDFPageView } from \"./pdf_page_view.js\";\nimport { SimpleLinkService } from \"./pdf_link_service.js\";\nimport { StructTreeLayerBuilder } from \"./struct_tree_layer_builder.js\";\nimport { TextLayerBuilder } from \"./text_layer_builder.js\";\nimport { XfaLayerBuilder } from \"./xfa_layer_builder.js\";\n\nconst DEFAULT_CACHE_SIZE = 10;\n\n/**\n * @typedef {Object} PDFViewerOptions\n * @property {HTMLDivElement} container - The container for the viewer element.\n * @property {HTMLDivElement} [viewer] - The viewer element.\n * @property {EventBus} eventBus - The application event bus.\n * @property {IPDFLinkService} linkService - The navigation/linking service.\n * @property {DownloadManager} [downloadManager] - The download manager\n *   component.\n * @property {PDFFindController} [findController] - The find controller\n *   component.\n * @property {PDFScriptingManager} [scriptingManager] - The scripting manager\n *   component.\n * @property {PDFRenderingQueue} [renderingQueue] - The rendering queue object.\n * @property {boolean} [removePageBorders] - Removes the border shadow around\n *   the pages. The default value is `false`.\n * @property {number} [textLayerMode] - Controls if the text layer used for\n *   selection and searching is created, and if the improved text selection\n *   behaviour is enabled. The constants from {TextLayerMode} should be used.\n *   The default value is `TextLayerMode.ENABLE`.\n * @property {string} [imageResourcesPath] - Path for image resources, mainly\n *   mainly for annotation icons. Include trailing slash.\n * @property {boolean} [renderInteractiveForms] - Enables rendering of\n *   interactive form elements. The default value is `true`.\n * @property {boolean} [enablePrintAutoRotate] - Enables automatic rotation of\n *   landscape pages upon printing. The default is `false`.\n * @property {string} renderer - 'canvas' or 'svg'. The default is 'canvas'.\n * @property {boolean} [useOnlyCssZoom] - Enables CSS only zooming. The default\n *   value is `false`.\n * @property {number} [maxCanvasPixels] - The maximum supported canvas size in\n *   total pixels, i.e. width * height. Use -1 for no limit. The default value\n *   is 4096 * 4096 (16 mega-pixels).\n * @property {IL10n} l10n - Localization service.\n */\n\nfunction PDFPageViewBuffer(size) {\n  const data = [];\n  this.push = function (view) {\n    const i = data.indexOf(view);\n    if (i >= 0) {\n      data.splice(i, 1);\n    }\n    data.push(view);\n    if (data.length > size) {\n      data.shift().destroy();\n    }\n  };\n  /**\n   * After calling resize, the size of the buffer will be newSize. The optional\n   * parameter pagesToKeep is, if present, an array of pages to push to the back\n   * of the buffer, delaying their destruction. The size of pagesToKeep has no\n   * impact on the final size of the buffer; if pagesToKeep has length larger\n   * than newSize, some of those pages will be destroyed anyway.\n   */\n  this.resize = function (newSize, pagesToKeep) {\n    size = newSize;\n    if (pagesToKeep) {\n      const pageIdsToKeep = new Set();\n      for (let i = 0, iMax = pagesToKeep.length; i < iMax; ++i) {\n        pageIdsToKeep.add(pagesToKeep[i].id);\n      }\n      moveToEndOfArray(data, function (page) {\n        return pageIdsToKeep.has(page.id);\n      });\n    }\n    while (data.length > size) {\n      data.shift().destroy();\n    }\n  };\n\n  this.has = function (view) {\n    return data.includes(view);\n  };\n}\n\nfunction isSameScale(oldScale, newScale) {\n  if (newScale === oldScale) {\n    return true;\n  }\n  if (Math.abs(newScale - oldScale) < 1e-15) {\n    // Prevent unnecessary re-rendering of all pages when the scale\n    // changes only because of limited numerical precision.\n    return true;\n  }\n  return false;\n}\n\n/**\n * Simple viewer control to display PDF content/pages.\n * @implements {IRenderableView}\n */\nclass BaseViewer {\n  /**\n   * @param {PDFViewerOptions} options\n   */\n  constructor(options) {\n    if (this.constructor === BaseViewer) {\n      throw new Error(\"Cannot initialize BaseViewer.\");\n    }\n    const viewerVersion =\n      typeof PDFJSDev !== \"undefined\" ? PDFJSDev.eval(\"BUNDLE_VERSION\") : null;\n    if (version !== viewerVersion) {\n      throw new Error(\n        `The API version \"${version}\" does not match the Viewer version \"${viewerVersion}\".`\n      );\n    }\n    this.container = options.container;\n    this.viewer = options.viewer || options.container.firstElementChild;\n\n    if (\n      typeof PDFJSDev === \"undefined\" ||\n      PDFJSDev.test(\"!PRODUCTION || GENERIC\")\n    ) {\n      if (\n        !(\n          this.container?.tagName.toUpperCase() === \"DIV\" &&\n          this.viewer?.tagName.toUpperCase() === \"DIV\"\n        )\n      ) {\n        throw new Error(\"Invalid `container` and/or `viewer` option.\");\n      }\n\n      if (\n        this.container.offsetParent &&\n        getComputedStyle(this.container).position !== \"absolute\"\n      ) {\n        throw new Error(\"The `container` must be absolutely positioned.\");\n      }\n    }\n    this.eventBus = options.eventBus;\n    this.linkService = options.linkService || new SimpleLinkService();\n    this.downloadManager = options.downloadManager || null;\n    this.findController = options.findController || null;\n    this._scriptingManager = options.scriptingManager || null;\n    this.removePageBorders = options.removePageBorders || false;\n    this.textLayerMode = Number.isInteger(options.textLayerMode)\n      ? options.textLayerMode\n      : TextLayerMode.ENABLE;\n    this.imageResourcesPath = options.imageResourcesPath || \"\";\n    this.renderInteractiveForms = options.renderInteractiveForms !== false;\n    this.enablePrintAutoRotate = options.enablePrintAutoRotate || false;\n    this.renderer = options.renderer || RendererType.CANVAS;\n    this.useOnlyCssZoom = options.useOnlyCssZoom || false;\n    this.maxCanvasPixels = options.maxCanvasPixels;\n    this.l10n = options.l10n || NullL10n;\n\n    this.defaultRenderingQueue = !options.renderingQueue;\n    if (this.defaultRenderingQueue) {\n      // Custom rendering queue is not specified, using default one\n      this.renderingQueue = new PDFRenderingQueue();\n      this.renderingQueue.setViewer(this);\n    } else {\n      this.renderingQueue = options.renderingQueue;\n    }\n    this._doc = document.documentElement;\n\n    this.scroll = watchScroll(this.container, this._scrollUpdate.bind(this));\n    this.presentationModeState = PresentationModeState.UNKNOWN;\n    this._onBeforeDraw = this._onAfterDraw = null;\n    this._resetView();\n\n    if (this.removePageBorders) {\n      this.viewer.classList.add(\"removePageBorders\");\n    }\n    // Defer the dispatching of this event, to give other viewer components\n    // time to initialize *and* register 'baseviewerinit' event listeners.\n    Promise.resolve().then(() => {\n      this.eventBus.dispatch(\"baseviewerinit\", { source: this });\n    });\n  }\n\n  get pagesCount() {\n    return this._pages.length;\n  }\n\n  getPageView(index) {\n    return this._pages[index];\n  }\n\n  /**\n   * @type {boolean} - True if all {PDFPageView} objects are initialized.\n   */\n  get pageViewsReady() {\n    if (!this._pagesCapability.settled) {\n      return false;\n    }\n    // Prevent printing errors when 'disableAutoFetch' is set, by ensuring\n    // that *all* pages have in fact been completely loaded.\n    return this._pages.every(function (pageView) {\n      return pageView?.pdfPage;\n    });\n  }\n\n  /**\n   * @type {boolean}\n   */\n  get enableScripting() {\n    return !!this._scriptingManager;\n  }\n\n  /**\n   * @type {number}\n   */\n  get currentPageNumber() {\n    return this._currentPageNumber;\n  }\n\n  /**\n   * @param {number} val - The page number.\n   */\n  set currentPageNumber(val) {\n    if (!Number.isInteger(val)) {\n      throw new Error(\"Invalid page number.\");\n    }\n    if (!this.pdfDocument) {\n      return;\n    }\n    // The intent can be to just reset a scroll position and/or scale.\n    if (!this._setCurrentPageNumber(val, /* resetCurrentPageView = */ true)) {\n      console.error(`currentPageNumber: \"${val}\" is not a valid page.`);\n    }\n  }\n\n  /**\n   * @returns {boolean} Whether the pageNumber is valid (within bounds).\n   * @private\n   */\n  _setCurrentPageNumber(val, resetCurrentPageView = false) {\n    if (this._currentPageNumber === val) {\n      if (resetCurrentPageView) {\n        this._resetCurrentPageView();\n      }\n      return true;\n    }\n\n    if (!(0 < val && val <= this.pagesCount)) {\n      return false;\n    }\n    const previous = this._currentPageNumber;\n    this._currentPageNumber = val;\n\n    this.eventBus.dispatch(\"pagechanging\", {\n      source: this,\n      pageNumber: val,\n      pageLabel: this._pageLabels?.[val - 1] ?? null,\n      previous,\n    });\n\n    if (resetCurrentPageView) {\n      this._resetCurrentPageView();\n    }\n    return true;\n  }\n\n  /**\n   * @type {string|null} Returns the current page label, or `null` if no page\n   *   labels exist.\n   */\n  get currentPageLabel() {\n    return this._pageLabels?.[this._currentPageNumber - 1] ?? null;\n  }\n\n  /**\n   * @param {string} val - The page label.\n   */\n  set currentPageLabel(val) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    let page = val | 0; // Fallback page number.\n    if (this._pageLabels) {\n      const i = this._pageLabels.indexOf(val);\n      if (i >= 0) {\n        page = i + 1;\n      }\n    }\n    // The intent can be to just reset a scroll position and/or scale.\n    if (!this._setCurrentPageNumber(page, /* resetCurrentPageView = */ true)) {\n      console.error(`currentPageLabel: \"${val}\" is not a valid page.`);\n    }\n  }\n\n  /**\n   * @type {number}\n   */\n  get currentScale() {\n    return this._currentScale !== UNKNOWN_SCALE\n      ? this._currentScale\n      : DEFAULT_SCALE;\n  }\n\n  /**\n   * @param {number} val - Scale of the pages in percents.\n   */\n  set currentScale(val) {\n    if (isNaN(val)) {\n      throw new Error(\"Invalid numeric scale.\");\n    }\n    if (!this.pdfDocument) {\n      return;\n    }\n    this._setScale(val, false);\n  }\n\n  /**\n   * @type {string}\n   */\n  get currentScaleValue() {\n    return this._currentScaleValue;\n  }\n\n  /**\n   * @param val - The scale of the pages (in percent or predefined value).\n   */\n  set currentScaleValue(val) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    this._setScale(val, false);\n  }\n\n  /**\n   * @type {number}\n   */\n  get pagesRotation() {\n    return this._pagesRotation;\n  }\n\n  /**\n   * @param {number} rotation - The rotation of the pages (0, 90, 180, 270).\n   */\n  set pagesRotation(rotation) {\n    if (!isValidRotation(rotation)) {\n      throw new Error(\"Invalid pages rotation angle.\");\n    }\n    if (!this.pdfDocument) {\n      return;\n    }\n    // Normalize the rotation, by clamping it to the [0, 360) range.\n    rotation %= 360;\n    if (rotation < 0) {\n      rotation += 360;\n    }\n    if (this._pagesRotation === rotation) {\n      return; // The rotation didn't change.\n    }\n    this._pagesRotation = rotation;\n\n    const pageNumber = this._currentPageNumber;\n\n    for (let i = 0, ii = this._pages.length; i < ii; i++) {\n      const pageView = this._pages[i];\n      pageView.update(pageView.scale, rotation);\n    }\n    // Prevent errors in case the rotation changes *before* the scale has been\n    // set to a non-default value.\n    if (this._currentScaleValue) {\n      this._setScale(this._currentScaleValue, true);\n    }\n\n    this.eventBus.dispatch(\"rotationchanging\", {\n      source: this,\n      pagesRotation: rotation,\n      pageNumber,\n    });\n\n    if (this.defaultRenderingQueue) {\n      this.update();\n    }\n  }\n\n  get firstPagePromise() {\n    return this.pdfDocument ? this._firstPageCapability.promise : null;\n  }\n\n  get onePageRendered() {\n    return this.pdfDocument ? this._onePageRenderedCapability.promise : null;\n  }\n\n  get pagesPromise() {\n    return this.pdfDocument ? this._pagesCapability.promise : null;\n  }\n\n  /**\n   * @private\n   */\n  get _viewerElement() {\n    // In most viewers, e.g. `PDFViewer`, this should return `this.viewer`.\n    throw new Error(\"Not implemented: _viewerElement\");\n  }\n\n  /**\n   * @private\n   */\n  _onePageRenderedOrForceFetch() {\n    // Unless the viewer *and* its pages are visible, rendering won't start and\n    // `this._onePageRenderedCapability` thus won't be resolved.\n    // To ensure that automatic printing, on document load, still works even in\n    // those cases we force-allow fetching of all pages when:\n    //  - The viewer is hidden in the DOM, e.g. in a `display: none` <iframe>\n    //    element; fixes bug 1618621.\n    //  - The viewer is visible, but none of the pages are (e.g. if the\n    //    viewer is very small); fixes bug 1618955.\n    if (\n      !this.container.offsetParent ||\n      this._getVisiblePages().views.length === 0\n    ) {\n      return Promise.resolve();\n    }\n    return this._onePageRenderedCapability.promise;\n  }\n\n  /**\n   * @param pdfDocument {PDFDocument}\n   */\n  setDocument(pdfDocument) {\n    if (this.pdfDocument) {\n      this.eventBus.dispatch(\"pagesdestroy\", { source: this });\n\n      this._cancelRendering();\n      this._resetView();\n\n      if (this.findController) {\n        this.findController.setDocument(null);\n      }\n      if (this._scriptingManager) {\n        this._scriptingManager.setDocument(null);\n      }\n    }\n\n    this.pdfDocument = pdfDocument;\n    if (!pdfDocument) {\n      return;\n    }\n    const isPureXfa = pdfDocument.isPureXfa;\n    const pagesCount = pdfDocument.numPages;\n    const firstPagePromise = pdfDocument.getPage(1);\n    // Rendering (potentially) depends on this, hence fetching it immediately.\n    const optionalContentConfigPromise = pdfDocument.getOptionalContentConfig();\n\n    this._pagesCapability.promise.then(() => {\n      this.eventBus.dispatch(\"pagesloaded\", {\n        source: this,\n        pagesCount,\n      });\n    });\n\n    this._onBeforeDraw = evt => {\n      const pageView = this._pages[evt.pageNumber - 1];\n      if (!pageView) {\n        return;\n      }\n      // Add the page to the buffer at the start of drawing. That way it can be\n      // evicted from the buffer and destroyed even if we pause its rendering.\n      this._buffer.push(pageView);\n    };\n    this.eventBus._on(\"pagerender\", this._onBeforeDraw);\n\n    this._onAfterDraw = evt => {\n      if (evt.cssTransform || this._onePageRenderedCapability.settled) {\n        return;\n      }\n      this._onePageRenderedCapability.resolve();\n\n      this.eventBus._off(\"pagerendered\", this._onAfterDraw);\n      this._onAfterDraw = null;\n    };\n    this.eventBus._on(\"pagerendered\", this._onAfterDraw);\n\n    // Fetch a single page so we can get a viewport that will be the default\n    // viewport for all pages\n    firstPagePromise\n      .then(firstPdfPage => {\n        this._firstPageCapability.resolve(firstPdfPage);\n        this._optionalContentConfigPromise = optionalContentConfigPromise;\n\n        const scale = this.currentScale;\n        const viewport = firstPdfPage.getViewport({ scale: scale * CSS_UNITS });\n        const textLayerFactory =\n          this.textLayerMode !== TextLayerMode.DISABLE ? this : null;\n        const xfaLayerFactory = isPureXfa ? this : null;\n\n        for (let pageNum = 1; pageNum <= pagesCount; ++pageNum) {\n          const pageView = new PDFPageView({\n            container: this._viewerElement,\n            eventBus: this.eventBus,\n            id: pageNum,\n            scale,\n            defaultViewport: viewport.clone(),\n            optionalContentConfigPromise,\n            renderingQueue: this.renderingQueue,\n            textLayerFactory,\n            textLayerMode: this.textLayerMode,\n            annotationLayerFactory: this,\n            xfaLayerFactory,\n            structTreeLayerFactory: this,\n            imageResourcesPath: this.imageResourcesPath,\n            renderInteractiveForms: this.renderInteractiveForms,\n            renderer: this.renderer,\n            useOnlyCssZoom: this.useOnlyCssZoom,\n            maxCanvasPixels: this.maxCanvasPixels,\n            l10n: this.l10n,\n          });\n          this._pages.push(pageView);\n        }\n        // Set the first `pdfPage` immediately, since it's already loaded,\n        // rather than having to repeat the `PDFDocumentProxy.getPage` call in\n        // the `this._ensurePdfPageLoaded` method before rendering can start.\n        const firstPageView = this._pages[0];\n        if (firstPageView) {\n          firstPageView.setPdfPage(firstPdfPage);\n          this.linkService.cachePageRef(1, firstPdfPage.ref);\n        }\n        if (this._spreadMode !== SpreadMode.NONE) {\n          this._updateSpreadMode();\n        }\n\n        // Fetch all the pages since the viewport is needed before printing\n        // starts to create the correct size canvas. Wait until one page is\n        // rendered so we don't tie up too many resources early on.\n        this._onePageRenderedOrForceFetch().then(() => {\n          if (this.findController) {\n            this.findController.setDocument(pdfDocument); // Enable searching.\n          }\n          if (this._scriptingManager) {\n            this._scriptingManager.setDocument(pdfDocument); // Enable scripting.\n          }\n\n          // In addition to 'disableAutoFetch' being set, also attempt to reduce\n          // resource usage when loading *very* long/large documents.\n          if (pdfDocument.loadingParams.disableAutoFetch || pagesCount > 7500) {\n            // XXX: Printing is semi-broken with auto fetch disabled.\n            this._pagesCapability.resolve();\n            return;\n          }\n          let getPagesLeft = pagesCount - 1; // The first page was already loaded.\n\n          if (getPagesLeft <= 0) {\n            this._pagesCapability.resolve();\n            return;\n          }\n          for (let pageNum = 2; pageNum <= pagesCount; ++pageNum) {\n            pdfDocument.getPage(pageNum).then(\n              pdfPage => {\n                const pageView = this._pages[pageNum - 1];\n                if (!pageView.pdfPage) {\n                  pageView.setPdfPage(pdfPage);\n                }\n                this.linkService.cachePageRef(pageNum, pdfPage.ref);\n                if (--getPagesLeft === 0) {\n                  this._pagesCapability.resolve();\n                }\n              },\n              reason => {\n                console.error(\n                  `Unable to get page ${pageNum} to initialize viewer`,\n                  reason\n                );\n                if (--getPagesLeft === 0) {\n                  this._pagesCapability.resolve();\n                }\n              }\n            );\n          }\n        });\n\n        this.eventBus.dispatch(\"pagesinit\", { source: this });\n\n        if (this.defaultRenderingQueue) {\n          this.update();\n        }\n      })\n      .catch(reason => {\n        console.error(\"Unable to initialize viewer\", reason);\n      });\n  }\n\n  /**\n   * @param {Array|null} labels\n   */\n  setPageLabels(labels) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    if (!labels) {\n      this._pageLabels = null;\n    } else if (\n      !(Array.isArray(labels) && this.pdfDocument.numPages === labels.length)\n    ) {\n      this._pageLabels = null;\n      console.error(`setPageLabels: Invalid page labels.`);\n    } else {\n      this._pageLabels = labels;\n    }\n    // Update all the `PDFPageView` instances.\n    for (let i = 0, ii = this._pages.length; i < ii; i++) {\n      this._pages[i].setPageLabel(this._pageLabels?.[i] ?? null);\n    }\n  }\n\n  _resetView() {\n    this._pages = [];\n    this._currentPageNumber = 1;\n    this._currentScale = UNKNOWN_SCALE;\n    this._currentScaleValue = null;\n    this._pageLabels = null;\n    this._buffer = new PDFPageViewBuffer(DEFAULT_CACHE_SIZE);\n    this._location = null;\n    this._pagesRotation = 0;\n    this._optionalContentConfigPromise = null;\n    this._pagesRequests = new WeakMap();\n    this._firstPageCapability = createPromiseCapability();\n    this._onePageRenderedCapability = createPromiseCapability();\n    this._pagesCapability = createPromiseCapability();\n    this._scrollMode = ScrollMode.VERTICAL;\n    this._spreadMode = SpreadMode.NONE;\n\n    if (this._onBeforeDraw) {\n      this.eventBus._off(\"pagerender\", this._onBeforeDraw);\n      this._onBeforeDraw = null;\n    }\n    if (this._onAfterDraw) {\n      this.eventBus._off(\"pagerendered\", this._onAfterDraw);\n      this._onAfterDraw = null;\n    }\n    // Remove the pages from the DOM...\n    this.viewer.textContent = \"\";\n    // ... and reset the Scroll mode CSS class(es) afterwards.\n    this._updateScrollMode();\n  }\n\n  _scrollUpdate() {\n    if (this.pagesCount === 0) {\n      return;\n    }\n    this.update();\n  }\n\n  _scrollIntoView({ pageDiv, pageSpot = null, pageNumber = null }) {\n    scrollIntoView(pageDiv, pageSpot);\n  }\n\n  _setScaleUpdatePages(newScale, newValue, noScroll = false, preset = false) {\n    this._currentScaleValue = newValue.toString();\n\n    if (isSameScale(this._currentScale, newScale)) {\n      if (preset) {\n        this.eventBus.dispatch(\"scalechanging\", {\n          source: this,\n          scale: newScale,\n          presetValue: newValue,\n        });\n      }\n      return;\n    }\n    this._doc.style.setProperty(\"--zoom-factor\", newScale);\n\n    for (let i = 0, ii = this._pages.length; i < ii; i++) {\n      this._pages[i].update(newScale);\n    }\n    this._currentScale = newScale;\n\n    if (!noScroll) {\n      let page = this._currentPageNumber,\n        dest;\n      if (\n        this._location &&\n        !(this.isInPresentationMode || this.isChangingPresentationMode)\n      ) {\n        page = this._location.pageNumber;\n        dest = [\n          null,\n          { name: \"XYZ\" },\n          this._location.left,\n          this._location.top,\n          null,\n        ];\n      }\n      this.scrollPageIntoView({\n        pageNumber: page,\n        destArray: dest,\n        allowNegativeOffset: true,\n      });\n    }\n\n    this.eventBus.dispatch(\"scalechanging\", {\n      source: this,\n      scale: newScale,\n      presetValue: preset ? newValue : undefined,\n    });\n\n    if (this.defaultRenderingQueue) {\n      this.update();\n    }\n  }\n\n  /**\n   * @private\n   */\n  get _pageWidthScaleFactor() {\n    if (\n      this._spreadMode !== SpreadMode.NONE &&\n      this._scrollMode !== ScrollMode.HORIZONTAL &&\n      !this.isInPresentationMode\n    ) {\n      return 2;\n    }\n    return 1;\n  }\n\n  _setScale(value, noScroll = false) {\n    let scale = parseFloat(value);\n\n    if (scale > 0) {\n      this._setScaleUpdatePages(scale, value, noScroll, /* preset = */ false);\n    } else {\n      const currentPage = this._pages[this._currentPageNumber - 1];\n      if (!currentPage) {\n        return;\n      }\n      const noPadding = this.isInPresentationMode || this.removePageBorders;\n      let hPadding = noPadding ? 0 : SCROLLBAR_PADDING;\n      let vPadding = noPadding ? 0 : VERTICAL_PADDING;\n\n      if (!noPadding && this._isScrollModeHorizontal) {\n        [hPadding, vPadding] = [vPadding, hPadding]; // Swap the padding values.\n      }\n      const pageWidthScale =\n        (((this.container.clientWidth - hPadding) / currentPage.width) *\n          currentPage.scale) /\n        this._pageWidthScaleFactor;\n      const pageHeightScale =\n        ((this.container.clientHeight - vPadding) / currentPage.height) *\n        currentPage.scale;\n      switch (value) {\n        case \"page-actual\":\n          scale = 1;\n          break;\n        case \"page-width\":\n          scale = pageWidthScale;\n          break;\n        case \"page-height\":\n          scale = pageHeightScale;\n          break;\n        case \"page-fit\":\n          scale = Math.min(pageWidthScale, pageHeightScale);\n          break;\n        case \"auto\":\n          // For pages in landscape mode, fit the page height to the viewer\n          // *unless* the page would thus become too wide to fit horizontally.\n          const horizontalScale = isPortraitOrientation(currentPage)\n            ? pageWidthScale\n            : Math.min(pageHeightScale, pageWidthScale);\n          scale = Math.min(MAX_AUTO_SCALE, horizontalScale);\n          break;\n        default:\n          console.error(`_setScale: \"${value}\" is an unknown zoom value.`);\n          return;\n      }\n      this._setScaleUpdatePages(scale, value, noScroll, /* preset = */ true);\n    }\n  }\n\n  /**\n   * Refreshes page view: scrolls to the current page and updates the scale.\n   * @private\n   */\n  _resetCurrentPageView() {\n    if (this.isInPresentationMode) {\n      // Fixes the case when PDF has different page sizes.\n      this._setScale(this._currentScaleValue, true);\n    }\n\n    const pageView = this._pages[this._currentPageNumber - 1];\n    this._scrollIntoView({ pageDiv: pageView.div });\n  }\n\n  /**\n   * @param {string} label - The page label.\n   * @returns {number|null} The page number corresponding to the page label,\n   *   or `null` when no page labels exist and/or the input is invalid.\n   */\n  pageLabelToPageNumber(label) {\n    if (!this._pageLabels) {\n      return null;\n    }\n    const i = this._pageLabels.indexOf(label);\n    if (i < 0) {\n      return null;\n    }\n    return i + 1;\n  }\n\n  /**\n   * @typedef ScrollPageIntoViewParameters\n   * @property {number} pageNumber - The page number.\n   * @property {Array} [destArray] - The original PDF destination array, in the\n   *   format: <page-ref> </XYZ|/FitXXX> <args..>\n   * @property {boolean} [allowNegativeOffset] - Allow negative page offsets.\n   *   The default value is `false`.\n   * @property {boolean} [ignoreDestinationZoom] - Ignore the zoom argument in\n   *   the destination array. The default value is `false`.\n   */\n\n  /**\n   * Scrolls page into view.\n   * @param {ScrollPageIntoViewParameters} params\n   */\n  scrollPageIntoView({\n    pageNumber,\n    destArray = null,\n    allowNegativeOffset = false,\n    ignoreDestinationZoom = false,\n  }) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    const pageView =\n      Number.isInteger(pageNumber) && this._pages[pageNumber - 1];\n    if (!pageView) {\n      console.error(\n        `scrollPageIntoView: \"${pageNumber}\" is not a valid pageNumber parameter.`\n      );\n      return;\n    }\n\n    if (this.isInPresentationMode || !destArray) {\n      this._setCurrentPageNumber(pageNumber, /* resetCurrentPageView = */ true);\n      return;\n    }\n    let x = 0,\n      y = 0;\n    let width = 0,\n      height = 0,\n      widthScale,\n      heightScale;\n    const changeOrientation = pageView.rotation % 180 !== 0;\n    const pageWidth =\n      (changeOrientation ? pageView.height : pageView.width) /\n      pageView.scale /\n      CSS_UNITS;\n    const pageHeight =\n      (changeOrientation ? pageView.width : pageView.height) /\n      pageView.scale /\n      CSS_UNITS;\n    let scale = 0;\n    switch (destArray[1].name) {\n      case \"XYZ\":\n        x = destArray[2];\n        y = destArray[3];\n        scale = destArray[4];\n        // If x and/or y coordinates are not supplied, default to\n        // _top_ left of the page (not the obvious bottom left,\n        // since aligning the bottom of the intended page with the\n        // top of the window is rarely helpful).\n        x = x !== null ? x : 0;\n        y = y !== null ? y : pageHeight;\n        break;\n      case \"Fit\":\n      case \"FitB\":\n        scale = \"page-fit\";\n        break;\n      case \"FitH\":\n      case \"FitBH\":\n        y = destArray[2];\n        scale = \"page-width\";\n        // According to the PDF spec, section 12.3.2.2, a `null` value in the\n        // parameter should maintain the position relative to the new page.\n        if (y === null && this._location) {\n          x = this._location.left;\n          y = this._location.top;\n        } else if (typeof y !== \"number\") {\n          // The \"top\" value isn't optional, according to the spec, however some\n          // bad PDF generators will pretend that it is (fixes bug 1663390).\n          y = pageHeight;\n        }\n        break;\n      case \"FitV\":\n      case \"FitBV\":\n        x = destArray[2];\n        width = pageWidth;\n        height = pageHeight;\n        scale = \"page-height\";\n        break;\n      case \"FitR\":\n        x = destArray[2];\n        y = destArray[3];\n        width = destArray[4] - x;\n        height = destArray[5] - y;\n        const hPadding = this.removePageBorders ? 0 : SCROLLBAR_PADDING;\n        const vPadding = this.removePageBorders ? 0 : VERTICAL_PADDING;\n\n        widthScale =\n          (this.container.clientWidth - hPadding) / width / CSS_UNITS;\n        heightScale =\n          (this.container.clientHeight - vPadding) / height / CSS_UNITS;\n        scale = Math.min(Math.abs(widthScale), Math.abs(heightScale));\n        break;\n      default:\n        console.error(\n          `scrollPageIntoView: \"${destArray[1].name}\" is not a valid destination type.`\n        );\n        return;\n    }\n\n    if (!ignoreDestinationZoom) {\n      if (scale && scale !== this._currentScale) {\n        this.currentScaleValue = scale;\n      } else if (this._currentScale === UNKNOWN_SCALE) {\n        this.currentScaleValue = DEFAULT_SCALE_VALUE;\n      }\n    }\n\n    if (scale === \"page-fit\" && !destArray[4]) {\n      this._scrollIntoView({\n        pageDiv: pageView.div,\n        pageNumber,\n      });\n      return;\n    }\n\n    const boundingRect = [\n      pageView.viewport.convertToViewportPoint(x, y),\n      pageView.viewport.convertToViewportPoint(x + width, y + height),\n    ];\n    let left = Math.min(boundingRect[0][0], boundingRect[1][0]);\n    let top = Math.min(boundingRect[0][1], boundingRect[1][1]);\n\n    if (!allowNegativeOffset) {\n      // Some bad PDF generators will create destinations with e.g. top values\n      // that exceeds the page height. Ensure that offsets are not negative,\n      // to prevent a previous page from becoming visible (fixes bug 874482).\n      left = Math.max(left, 0);\n      top = Math.max(top, 0);\n    }\n    this._scrollIntoView({\n      pageDiv: pageView.div,\n      pageSpot: { left, top },\n      pageNumber,\n    });\n  }\n\n  _updateLocation(firstPage) {\n    const currentScale = this._currentScale;\n    const currentScaleValue = this._currentScaleValue;\n    const normalizedScaleValue =\n      parseFloat(currentScaleValue) === currentScale\n        ? Math.round(currentScale * 10000) / 100\n        : currentScaleValue;\n\n    const pageNumber = firstPage.id;\n    let pdfOpenParams = \"#page=\" + pageNumber;\n    pdfOpenParams += \"&zoom=\" + normalizedScaleValue;\n    const currentPageView = this._pages[pageNumber - 1];\n    const container = this.container;\n    const topLeft = currentPageView.getPagePoint(\n      container.scrollLeft - firstPage.x,\n      container.scrollTop - firstPage.y\n    );\n    const intLeft = Math.round(topLeft[0]);\n    const intTop = Math.round(topLeft[1]);\n    pdfOpenParams += \",\" + intLeft + \",\" + intTop;\n\n    this._location = {\n      pageNumber,\n      scale: normalizedScaleValue,\n      top: intTop,\n      left: intLeft,\n      rotation: this._pagesRotation,\n      pdfOpenParams,\n    };\n  }\n\n  _updateHelper(visiblePages) {\n    throw new Error(\"Not implemented: _updateHelper\");\n  }\n\n  update() {\n    const visible = this._getVisiblePages();\n    const visiblePages = visible.views,\n      numVisiblePages = visiblePages.length;\n\n    if (numVisiblePages === 0) {\n      return;\n    }\n    const newCacheSize = Math.max(DEFAULT_CACHE_SIZE, 2 * numVisiblePages + 1);\n    this._buffer.resize(newCacheSize, visiblePages);\n\n    this.renderingQueue.renderHighestPriority(visible);\n\n    this._updateHelper(visiblePages); // Run any class-specific update code.\n\n    this._updateLocation(visible.first);\n    this.eventBus.dispatch(\"updateviewarea\", {\n      source: this,\n      location: this._location,\n    });\n  }\n\n  containsElement(element) {\n    return this.container.contains(element);\n  }\n\n  focus() {\n    this.container.focus();\n  }\n\n  get _isScrollModeHorizontal() {\n    // Used to ensure that pre-rendering of the next/previous page works\n    // correctly, since Scroll/Spread modes are ignored in Presentation Mode.\n    return this.isInPresentationMode\n      ? false\n      : this._scrollMode === ScrollMode.HORIZONTAL;\n  }\n\n  get _isContainerRtl() {\n    return getComputedStyle(this.container).direction === \"rtl\";\n  }\n\n  get isInPresentationMode() {\n    return this.presentationModeState === PresentationModeState.FULLSCREEN;\n  }\n\n  get isChangingPresentationMode() {\n    return this.presentationModeState === PresentationModeState.CHANGING;\n  }\n\n  get isHorizontalScrollbarEnabled() {\n    return this.isInPresentationMode\n      ? false\n      : this.container.scrollWidth > this.container.clientWidth;\n  }\n\n  get isVerticalScrollbarEnabled() {\n    return this.isInPresentationMode\n      ? false\n      : this.container.scrollHeight > this.container.clientHeight;\n  }\n\n  /**\n   * Helper method for `this._getVisiblePages`. Should only ever be used when\n   * the viewer can only display a single page at a time, for example in:\n   *  - `PDFSinglePageViewer`.\n   *  - `PDFViewer` with Presentation Mode active.\n   */\n  _getCurrentVisiblePage() {\n    if (!this.pagesCount) {\n      return { views: [] };\n    }\n    const pageView = this._pages[this._currentPageNumber - 1];\n    // NOTE: Compute the `x` and `y` properties of the current view,\n    // since `this._updateLocation` depends of them being available.\n    const element = pageView.div;\n\n    const view = {\n      id: pageView.id,\n      x: element.offsetLeft + element.clientLeft,\n      y: element.offsetTop + element.clientTop,\n      view: pageView,\n    };\n    return { first: view, last: view, views: [view] };\n  }\n\n  _getVisiblePages() {\n    return getVisibleElements({\n      scrollEl: this.container,\n      views: this._pages,\n      sortByVisibility: true,\n      horizontal: this._isScrollModeHorizontal,\n      rtl: this._isScrollModeHorizontal && this._isContainerRtl,\n    });\n  }\n\n  /**\n   * @param {number} pageNumber\n   */\n  isPageVisible(pageNumber) {\n    if (!this.pdfDocument) {\n      return false;\n    }\n    if (\n      !(\n        Number.isInteger(pageNumber) &&\n        pageNumber > 0 &&\n        pageNumber <= this.pagesCount\n      )\n    ) {\n      console.error(`isPageVisible: \"${pageNumber}\" is not a valid page.`);\n      return false;\n    }\n    return this._getVisiblePages().views.some(function (view) {\n      return view.id === pageNumber;\n    });\n  }\n\n  /**\n   * @param {number} pageNumber\n   */\n  isPageCached(pageNumber) {\n    if (!this.pdfDocument || !this._buffer) {\n      return false;\n    }\n    if (\n      !(\n        Number.isInteger(pageNumber) &&\n        pageNumber > 0 &&\n        pageNumber <= this.pagesCount\n      )\n    ) {\n      console.error(`isPageCached: \"${pageNumber}\" is not a valid page.`);\n      return false;\n    }\n    const pageView = this._pages[pageNumber - 1];\n    if (!pageView) {\n      return false;\n    }\n    return this._buffer.has(pageView);\n  }\n\n  cleanup() {\n    for (let i = 0, ii = this._pages.length; i < ii; i++) {\n      if (\n        this._pages[i] &&\n        this._pages[i].renderingState !== RenderingStates.FINISHED\n      ) {\n        this._pages[i].reset();\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  _cancelRendering() {\n    for (let i = 0, ii = this._pages.length; i < ii; i++) {\n      if (this._pages[i]) {\n        this._pages[i].cancelRendering();\n      }\n    }\n  }\n\n  /**\n   * @param {PDFPageView} pageView\n   * @returns {Promise} Returns a promise containing a {PDFPageProxy} object.\n   * @private\n   */\n  _ensurePdfPageLoaded(pageView) {\n    if (pageView.pdfPage) {\n      return Promise.resolve(pageView.pdfPage);\n    }\n    if (this._pagesRequests.has(pageView)) {\n      return this._pagesRequests.get(pageView);\n    }\n    const promise = this.pdfDocument\n      .getPage(pageView.id)\n      .then(pdfPage => {\n        if (!pageView.pdfPage) {\n          pageView.setPdfPage(pdfPage);\n        }\n        this._pagesRequests.delete(pageView);\n        return pdfPage;\n      })\n      .catch(reason => {\n        console.error(\"Unable to get page for page view\", reason);\n        // Page error -- there is nothing that can be done.\n        this._pagesRequests.delete(pageView);\n      });\n    this._pagesRequests.set(pageView, promise);\n    return promise;\n  }\n\n  forceRendering(currentlyVisiblePages) {\n    const visiblePages = currentlyVisiblePages || this._getVisiblePages();\n    const scrollAhead = this._isScrollModeHorizontal\n      ? this.scroll.right\n      : this.scroll.down;\n    const pageView = this.renderingQueue.getHighestPriority(\n      visiblePages,\n      this._pages,\n      scrollAhead\n    );\n    if (pageView) {\n      this._ensurePdfPageLoaded(pageView).then(() => {\n        this.renderingQueue.renderView(pageView);\n      });\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * @param {HTMLDivElement} textLayerDiv\n   * @param {number} pageIndex\n   * @param {PageViewport} viewport\n   * @param {boolean} enhanceTextSelection\n   * @param {EventBus} eventBus\n   * @returns {TextLayerBuilder}\n   */\n  createTextLayerBuilder(\n    textLayerDiv,\n    pageIndex,\n    viewport,\n    enhanceTextSelection = false,\n    eventBus\n  ) {\n    return new TextLayerBuilder({\n      textLayerDiv,\n      eventBus,\n      pageIndex,\n      viewport,\n      findController: this.isInPresentationMode ? null : this.findController,\n      enhanceTextSelection: this.isInPresentationMode\n        ? false\n        : enhanceTextSelection,\n    });\n  }\n\n  /**\n   * @param {HTMLDivElement} pageDiv\n   * @param {PDFPage} pdfPage\n   * @param {AnnotationStorage} [annotationStorage] - Storage for annotation\n   *   data in forms.\n   * @param {string} [imageResourcesPath] - Path for image resources, mainly\n   *   for annotation icons. Include trailing slash.\n   * @param {boolean} renderInteractiveForms\n   * @param {IL10n} l10n\n   * @param {boolean} [enableScripting]\n   * @param {Promise<boolean>} [hasJSActionsPromise]\n   * @param {Object} [mouseState]\n   * @returns {AnnotationLayerBuilder}\n   */\n  createAnnotationLayerBuilder(\n    pageDiv,\n    pdfPage,\n    annotationStorage = null,\n    imageResourcesPath = \"\",\n    renderInteractiveForms = false,\n    l10n = NullL10n,\n    enableScripting = null,\n    hasJSActionsPromise = null,\n    mouseState = null\n  ) {\n    return new AnnotationLayerBuilder({\n      pageDiv,\n      pdfPage,\n      annotationStorage:\n        annotationStorage || this.pdfDocument?.annotationStorage,\n      imageResourcesPath,\n      renderInteractiveForms,\n      linkService: this.linkService,\n      downloadManager: this.downloadManager,\n      l10n,\n      enableScripting: enableScripting ?? this.enableScripting,\n      hasJSActionsPromise:\n        hasJSActionsPromise || this.pdfDocument?.hasJSActions(),\n      mouseState: mouseState || this._scriptingManager?.mouseState,\n    });\n  }\n\n  /**\n   * @param {HTMLDivElement} pageDiv\n   * @param {PDFPage} pdfPage\n   * @param {AnnotationStorage} [annotationStorage] - Storage for annotation\n   *   data in forms.\n   * @returns {XfaLayerBuilder}\n   */\n  createXfaLayerBuilder(pageDiv, pdfPage, annotationStorage = null) {\n    return new XfaLayerBuilder({\n      pageDiv,\n      pdfPage,\n      annotationStorage:\n        annotationStorage || this.pdfDocument?.annotationStorage,\n    });\n  }\n\n  /**\n   * @param {PDFPage} pdfPage\n   * @returns {StructTreeLayerBuilder}\n   */\n  createStructTreeLayerBuilder(pdfPage) {\n    return new StructTreeLayerBuilder({\n      pdfPage,\n    });\n  }\n\n  /**\n   * @type {boolean} Whether all pages of the PDF document have identical\n   *   widths and heights.\n   */\n  get hasEqualPageSizes() {\n    const firstPageView = this._pages[0];\n    for (let i = 1, ii = this._pages.length; i < ii; ++i) {\n      const pageView = this._pages[i];\n      if (\n        pageView.width !== firstPageView.width ||\n        pageView.height !== firstPageView.height\n      ) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Returns sizes of the pages.\n   * @returns {Array} Array of objects with width/height/rotation fields.\n   */\n  getPagesOverview() {\n    return this._pages.map(pageView => {\n      const viewport = pageView.pdfPage.getViewport({ scale: 1 });\n\n      if (!this.enablePrintAutoRotate || isPortraitOrientation(viewport)) {\n        return {\n          width: viewport.width,\n          height: viewport.height,\n          rotation: viewport.rotation,\n        };\n      }\n      // Landscape orientation.\n      return {\n        width: viewport.height,\n        height: viewport.width,\n        rotation: (viewport.rotation - 90) % 360,\n      };\n    });\n  }\n\n  /**\n   * @type {Promise<OptionalContentConfig | null>}\n   */\n  get optionalContentConfigPromise() {\n    if (!this.pdfDocument) {\n      return Promise.resolve(null);\n    }\n    if (!this._optionalContentConfigPromise) {\n      // Prevent issues if the getter is accessed *before* the `onePageRendered`\n      // promise has resolved; won't (normally) happen in the default viewer.\n      return this.pdfDocument.getOptionalContentConfig();\n    }\n    return this._optionalContentConfigPromise;\n  }\n\n  /**\n   * @param {Promise<OptionalContentConfig>} promise - A promise that is\n   *   resolved with an {@link OptionalContentConfig} instance.\n   */\n  set optionalContentConfigPromise(promise) {\n    if (!(promise instanceof Promise)) {\n      throw new Error(`Invalid optionalContentConfigPromise: ${promise}`);\n    }\n    if (!this.pdfDocument) {\n      return;\n    }\n    if (!this._optionalContentConfigPromise) {\n      // Ignore the setter *before* the `onePageRendered` promise has resolved,\n      // since it'll be overwritten anyway; won't happen in the default viewer.\n      return;\n    }\n    this._optionalContentConfigPromise = promise;\n\n    for (const pageView of this._pages) {\n      pageView.update(pageView.scale, pageView.rotation, promise);\n    }\n    this.update();\n\n    this.eventBus.dispatch(\"optionalcontentconfigchanged\", {\n      source: this,\n      promise,\n    });\n  }\n\n  /**\n   * @type {number} One of the values in {ScrollMode}.\n   */\n  get scrollMode() {\n    return this._scrollMode;\n  }\n\n  /**\n   * @param {number} mode - The direction in which the document pages should be\n   *   laid out within the scrolling container.\n   *   The constants from {ScrollMode} should be used.\n   */\n  set scrollMode(mode) {\n    if (this._scrollMode === mode) {\n      return; // The Scroll mode didn't change.\n    }\n    if (!isValidScrollMode(mode)) {\n      throw new Error(`Invalid scroll mode: ${mode}`);\n    }\n    this._scrollMode = mode;\n    this.eventBus.dispatch(\"scrollmodechanged\", { source: this, mode });\n\n    this._updateScrollMode(/* pageNumber = */ this._currentPageNumber);\n  }\n\n  _updateScrollMode(pageNumber = null) {\n    const scrollMode = this._scrollMode,\n      viewer = this.viewer;\n\n    viewer.classList.toggle(\n      \"scrollHorizontal\",\n      scrollMode === ScrollMode.HORIZONTAL\n    );\n    viewer.classList.toggle(\"scrollWrapped\", scrollMode === ScrollMode.WRAPPED);\n\n    if (!this.pdfDocument || !pageNumber) {\n      return;\n    }\n    // Non-numeric scale values can be sensitive to the scroll orientation.\n    // Call this before re-scrolling to the current page, to ensure that any\n    // changes in scale don't move the current page.\n    if (this._currentScaleValue && isNaN(this._currentScaleValue)) {\n      this._setScale(this._currentScaleValue, true);\n    }\n    this._setCurrentPageNumber(pageNumber, /* resetCurrentPageView = */ true);\n    this.update();\n  }\n\n  /**\n   * @type {number} One of the values in {SpreadMode}.\n   */\n  get spreadMode() {\n    return this._spreadMode;\n  }\n\n  /**\n   * @param {number} mode - Group the pages in spreads, starting with odd- or\n   *   even-number pages (unless `SpreadMode.NONE` is used).\n   *   The constants from {SpreadMode} should be used.\n   */\n  set spreadMode(mode) {\n    if (this._spreadMode === mode) {\n      return; // The Spread mode didn't change.\n    }\n    if (!isValidSpreadMode(mode)) {\n      throw new Error(`Invalid spread mode: ${mode}`);\n    }\n    this._spreadMode = mode;\n    this.eventBus.dispatch(\"spreadmodechanged\", { source: this, mode });\n\n    this._updateSpreadMode(/* pageNumber = */ this._currentPageNumber);\n  }\n\n  _updateSpreadMode(pageNumber = null) {\n    if (!this.pdfDocument) {\n      return;\n    }\n    const viewer = this.viewer,\n      pages = this._pages;\n    // Temporarily remove all the pages from the DOM.\n    viewer.textContent = \"\";\n\n    if (this._spreadMode === SpreadMode.NONE) {\n      for (let i = 0, iMax = pages.length; i < iMax; ++i) {\n        viewer.appendChild(pages[i].div);\n      }\n    } else {\n      const parity = this._spreadMode - 1;\n      let spread = null;\n      for (let i = 0, iMax = pages.length; i < iMax; ++i) {\n        if (spread === null) {\n          spread = document.createElement(\"div\");\n          spread.className = \"spread\";\n          viewer.appendChild(spread);\n        } else if (i % 2 === parity) {\n          spread = spread.cloneNode(false);\n          viewer.appendChild(spread);\n        }\n        spread.appendChild(pages[i].div);\n      }\n    }\n\n    if (!pageNumber) {\n      return;\n    }\n    if (this._currentScaleValue && isNaN(this._currentScaleValue)) {\n      this._setScale(this._currentScaleValue, true);\n    }\n    this._setCurrentPageNumber(pageNumber, /* resetCurrentPageView = */ true);\n    this.update();\n  }\n\n  /**\n   * @private\n   */\n  _getPageAdvance(currentPageNumber, previous = false) {\n    if (this.isInPresentationMode) {\n      return 1;\n    }\n    switch (this._scrollMode) {\n      case ScrollMode.WRAPPED: {\n        const { views } = this._getVisiblePages(),\n          pageLayout = new Map();\n\n        // Determine the current (visible) page layout.\n        for (const { id, y, percent, widthPercent } of views) {\n          if (percent === 0 || widthPercent < 100) {\n            continue;\n          }\n          let yArray = pageLayout.get(y);\n          if (!yArray) {\n            pageLayout.set(y, (yArray ||= []));\n          }\n          yArray.push(id);\n        }\n        // Find the row of the current page.\n        for (const yArray of pageLayout.values()) {\n          const currentIndex = yArray.indexOf(currentPageNumber);\n          if (currentIndex === -1) {\n            continue;\n          }\n          const numPages = yArray.length;\n          if (numPages === 1) {\n            break;\n          }\n          // Handle documents with varying page sizes.\n          if (previous) {\n            for (let i = currentIndex - 1, ii = 0; i >= ii; i--) {\n              const currentId = yArray[i],\n                expectedId = yArray[i + 1] - 1;\n              if (currentId < expectedId) {\n                return currentPageNumber - expectedId;\n              }\n            }\n          } else {\n            for (let i = currentIndex + 1, ii = numPages; i < ii; i++) {\n              const currentId = yArray[i],\n                expectedId = yArray[i - 1] + 1;\n              if (currentId > expectedId) {\n                return expectedId - currentPageNumber;\n              }\n            }\n          }\n          // The current row is \"complete\", advance to the previous/next one.\n          if (previous) {\n            const firstId = yArray[0];\n            if (firstId < currentPageNumber) {\n              return currentPageNumber - firstId + 1;\n            }\n          } else {\n            const lastId = yArray[numPages - 1];\n            if (lastId > currentPageNumber) {\n              return lastId - currentPageNumber + 1;\n            }\n          }\n          break;\n        }\n        break;\n      }\n      case ScrollMode.HORIZONTAL: {\n        break;\n      }\n      case ScrollMode.VERTICAL: {\n        if (this._spreadMode === SpreadMode.NONE) {\n          break; // Normal vertical scrolling.\n        }\n        const parity = this._spreadMode - 1;\n\n        if (previous && currentPageNumber % 2 !== parity) {\n          break; // Left-hand side page.\n        } else if (!previous && currentPageNumber % 2 === parity) {\n          break; // Right-hand side page.\n        }\n        const { views } = this._getVisiblePages(),\n          expectedId = previous ? currentPageNumber - 1 : currentPageNumber + 1;\n\n        for (const { id, percent, widthPercent } of views) {\n          if (id !== expectedId) {\n            continue;\n          }\n          if (percent > 0 && widthPercent === 100) {\n            return 2;\n          }\n          break;\n        }\n        break;\n      }\n    }\n    return 1;\n  }\n\n  /**\n   * Go to the next page, taking scroll/spread-modes into account.\n   * @returns {boolean} Whether navigation occured.\n   */\n  nextPage() {\n    const currentPageNumber = this._currentPageNumber,\n      pagesCount = this.pagesCount;\n\n    if (currentPageNumber >= pagesCount) {\n      return false;\n    }\n    const advance =\n      this._getPageAdvance(currentPageNumber, /* previous = */ false) || 1;\n\n    this.currentPageNumber = Math.min(currentPageNumber + advance, pagesCount);\n    return true;\n  }\n\n  /**\n   * Go to the previous page, taking scroll/spread-modes into account.\n   * @returns {boolean} Whether navigation occured.\n   */\n  previousPage() {\n    const currentPageNumber = this._currentPageNumber;\n\n    if (currentPageNumber <= 1) {\n      return false;\n    }\n    const advance =\n      this._getPageAdvance(currentPageNumber, /* previous = */ true) || 1;\n\n    this.currentPageNumber = Math.max(currentPageNumber - advance, 1);\n    return true;\n  }\n}\n\nexport { BaseViewer };\n", "/* Copyright 2021 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst PDF_ROLE_TO_HTML_ROLE = {\n  // Document level structure types\n  Document: null, // There's a \"document\" role, but it doesn't make sense here.\n  DocumentFragment: null,\n  // Grouping level structure types\n  Part: \"group\",\n  Sect: \"group\", // XXX: There's a \"section\" role, but it's abstract.\n  Div: \"group\",\n  Aside: \"note\",\n  NonStruct: \"none\",\n  // Block level structure types\n  P: null,\n  // H<n>,\n  H: \"heading\",\n  Title: null,\n  FENote: \"note\",\n  // Sub-block level structure type\n  Sub: \"group\",\n  // General inline level structure types\n  Lbl: null,\n  Span: null,\n  Em: null,\n  Strong: null,\n  Link: \"link\",\n  Annot: \"note\",\n  Form: \"form\",\n  // Ruby and Warichu structure types\n  Ruby: null,\n  RB: null,\n  RT: null,\n  RP: null,\n  Warichu: null,\n  WT: null,\n  WP: null,\n  // List standard structure types\n  L: \"list\",\n  LI: \"listitem\",\n  LBody: null,\n  // Table standard structure types\n  Table: \"table\",\n  TR: \"row\",\n  TH: \"columnheader\",\n  TD: \"cell\",\n  THead: \"columnheader\",\n  TBody: null,\n  TFoot: null,\n  // Standard structure type Caption\n  Caption: null,\n  // Standard structure type Figure\n  Figure: \"figure\",\n  // Standard structure type Formula\n  Formula: null,\n  // standard structure type Artifact\n  Artifact: null,\n};\n\nconst HEADING_PATTERN = /^H(\\d+)$/;\n\n/**\n * @typedef {Object} StructTreeLayerBuilderOptions\n * @property {PDFPage} pdfPage\n */\n\nclass StructTreeLayerBuilder {\n  /**\n   * @param {StructTreeLayerBuilderOptions} options\n   */\n  constructor({ pdfPage }) {\n    this.pdfPage = pdfPage;\n  }\n\n  render(structTree) {\n    return this._walk(structTree);\n  }\n\n  _setAttributes(structElement, htmlElement) {\n    if (structElement.alt !== undefined) {\n      htmlElement.setAttribute(\"aria-label\", structElement.alt);\n    }\n    if (structElement.id !== undefined) {\n      htmlElement.setAttribute(\"aria-owns\", structElement.id);\n    }\n  }\n\n  _walk(node) {\n    if (!node) {\n      return null;\n    }\n\n    const element = document.createElement(\"span\");\n    if (\"role\" in node) {\n      const { role } = node;\n      const match = role.match(HEADING_PATTERN);\n      if (match) {\n        element.setAttribute(\"role\", \"heading\");\n        element.setAttribute(\"aria-level\", match[1]);\n      } else if (PDF_ROLE_TO_HTML_ROLE[role]) {\n        element.setAttribute(\"role\", PDF_ROLE_TO_HTML_ROLE[role]);\n      }\n    }\n\n    this._setAttributes(node, element);\n\n    if (node.children) {\n      if (node.children.length === 1 && \"id\" in node.children[0]) {\n        // Often there is only one content node so just set the values on the\n        // parent node to avoid creating an extra span.\n        this._setAttributes(node.children[0], element);\n      } else {\n        for (const kid of node.children) {\n          element.appendChild(this._walk(kid));\n        }\n      }\n    }\n    return element;\n  }\n}\n\n/**\n * @implements IPDFStructTreeLayerFactory\n */\nclass DefaultStructTreeLayerFactory {\n  /**\n   * @param {PDFPage} pdfPage\n   * @returns {StructTreeLayerBuilder}\n   */\n  createStructTreeLayerBuilder(pdfPage) {\n    return new StructTreeLayerBuilder({\n      pdfPage,\n    });\n  }\n}\n\nexport { DefaultStructTreeLayerFactory, StructTreeLayerBuilder };\n", "/* Copyright 2021 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { XfaLayer } from \"pdfjs-lib\";\n\n/**\n * @typedef {Object} XfaLayerBuilderOptions\n * @property {HTMLDivElement} pageDiv\n * @property {PDFPage} pdfPage\n * @property {AnnotationStorage} [annotationStorage]\n */\n\nclass XfaLayerBuilder {\n  /**\n   * @param {XfaLayerBuilderOptions} options\n   */\n  constructor({ pageDiv, pdfPage, xfaHtml, annotationStorage }) {\n    this.pageDiv = pageDiv;\n    this.pdfPage = pdfPage;\n    this.xfaHtml = xfaHtml;\n    this.annotationStorage = annotationStorage;\n\n    this.div = null;\n    this._cancelled = false;\n  }\n\n  /**\n   * @param {PageViewport} viewport\n   * @param {string} intent (default value is 'display')\n   * @returns {Promise<void>} A promise that is resolved when rendering of the\n   *   annotations is complete.\n   */\n  render(viewport, intent = \"display\") {\n    if (intent === \"print\") {\n      const parameters = {\n        viewport: viewport.clone({ dontFlip: true }),\n        div: this.div,\n        xfa: this.xfaHtml,\n        page: null,\n        annotationStorage: this.annotationStorage,\n        intent,\n      };\n\n      // Create an xfa layer div and render the form\n      const div = document.createElement(\"div\");\n      this.pageDiv.appendChild(div);\n      parameters.div = div;\n\n      XfaLayer.render(parameters);\n      return Promise.resolve();\n    }\n\n    // intent === \"display\"\n    return this.pdfPage\n      .getXfa()\n      .then(xfa => {\n        if (this._cancelled) {\n          return;\n        }\n        const parameters = {\n          viewport: viewport.clone({ dontFlip: true }),\n          div: this.div,\n          xfa,\n          page: this.pdfPage,\n          annotationStorage: this.annotationStorage,\n          intent,\n        };\n\n        if (this.div) {\n          XfaLayer.update(parameters);\n        } else {\n          // Create an xfa layer div and render the form\n          this.div = document.createElement(\"div\");\n          this.pageDiv.appendChild(this.div);\n          parameters.div = this.div;\n\n          XfaLayer.render(parameters);\n        }\n      })\n      .catch(error => {\n        console.error(error);\n      });\n  }\n\n  cancel() {\n    this._cancelled = true;\n  }\n\n  hide() {\n    if (!this.div) {\n      return;\n    }\n    this.div.hidden = true;\n  }\n}\n\n/**\n * @implements IPDFXfaLayerFactory\n */\nclass DefaultXfaLayerFactory {\n  /**\n   * @param {HTMLDivElement} pageDiv\n   * @param {PDFPage} pdfPage\n   * @param {AnnotationStorage} [annotationStorage]\n   * @param {Object} [xfaHtml]\n   */\n  createXfaLayerBuilder(\n    pageDiv,\n    pdfPage,\n    annotationStorage = null,\n    xfaHtml = null\n  ) {\n    return new XfaLayerBuilder({\n      pageDiv,\n      pdfPage,\n      annotationStorage,\n      xfaHtml,\n    });\n  }\n}\n\nexport { DefaultXfaLayerFactory, XfaLayerBuilder };\n", "/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ScrollMode, SpreadMode } from \"./ui_utils.js\";\nimport { BaseViewer } from \"./base_viewer.js\";\nimport { shadow } from \"pdfjs-lib\";\n\nclass PDFViewer extends BaseViewer {\n  get _viewerElement() {\n    return shadow(this, \"_viewerElement\", this.viewer);\n  }\n\n  _scrollIntoView({ pageDiv, pageSpot = null, pageNumber = null }) {\n    if (!pageSpot && !this.isInPresentationMode) {\n      const left = pageDiv.offsetLeft + pageDiv.clientLeft;\n      const right = left + pageDiv.clientWidth;\n      const { scrollLeft, clientWidth } = this.container;\n      if (\n        this._isScrollModeHorizontal ||\n        left < scrollLeft ||\n        right > scrollLeft + clientWidth\n      ) {\n        pageSpot = { left: 0, top: 0 };\n      }\n    }\n    super._scrollIntoView({ pageDiv, pageSpot, pageNumber });\n  }\n\n  _getVisiblePages() {\n    if (this.isInPresentationMode) {\n      // The algorithm in `getVisibleElements` doesn't work in all browsers and\n      // configurations (e.g. Chrome) when Presentation Mode is active.\n      return this._getCurrentVisiblePage();\n    }\n    return super._getVisiblePages();\n  }\n\n  _updateHelper(visiblePages) {\n    if (this.isInPresentationMode) {\n      return;\n    }\n    let currentId = this._currentPageNumber;\n    let stillFullyVisible = false;\n\n    for (const page of visiblePages) {\n      if (page.percent < 100) {\n        break;\n      }\n      if (\n        page.id === currentId &&\n        this._scrollMode === ScrollMode.VERTICAL &&\n        this._spreadMode === SpreadMode.NONE\n      ) {\n        stillFullyVisible = true;\n        break;\n      }\n    }\n    if (!stillFullyVisible) {\n      currentId = visiblePages[0].id;\n    }\n    this._setCurrentPageNumber(currentId);\n  }\n}\n\nexport { PDFViewer };\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __w_pdfjs_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __w_pdfjs_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnnotationLayerBuilder,\n  DefaultAnnotationLayerFactory,\n} from \"./annotation_layer_builder.js\";\nimport {\n  DefaultTextLayerFactory,\n  TextLayerBuilder,\n} from \"./text_layer_builder.js\";\nimport { EventBus, ProgressBar } from \"./ui_utils.js\";\nimport { PDFLinkService, SimpleLinkService } from \"./pdf_link_service.js\";\nimport { DownloadManager } from \"./download_manager.js\";\nimport { GenericL10n } from \"./genericl10n.js\";\nimport { NullL10n } from \"./l10n_utils.js\";\nimport { PDFFindController } from \"./pdf_find_controller.js\";\nimport { PDFHistory } from \"./pdf_history.js\";\nimport { PDFPageView } from \"./pdf_page_view.js\";\nimport { PDFScriptingManager } from \"./pdf_scripting_manager.js\";\nimport { PDFSinglePageViewer } from \"./pdf_single_page_viewer.js\";\nimport { PDFViewer } from \"./pdf_viewer.js\";\n\n// eslint-disable-next-line no-unused-vars\nconst pdfjsVersion = PDFJSDev.eval(\"BUNDLE_VERSION\");\n// eslint-disable-next-line no-unused-vars\nconst pdfjsBuild = PDFJSDev.eval(\"BUNDLE_BUILD\");\n\nexport {\n  AnnotationLayerBuilder,\n  DefaultAnnotationLayerFactory,\n  DefaultTextLayerFactory,\n  DownloadManager,\n  EventBus,\n  GenericL10n,\n  NullL10n,\n  PDFFindController,\n  PDFHistory,\n  PDFLinkService,\n  PDFPageView,\n  PDFScriptingManager,\n  PDFSinglePageViewer,\n  PDFViewer,\n  ProgressBar,\n  SimpleLinkService,\n  TextLayerBuilder,\n};\n"], "names": ["constructor", "annotationStorage", "imageResourcesPath", "renderInteractiveForms", "l10n", "enableScripting", "hasJSActionsPromise", "mouseState", "render", "intent", "hasJSActions", "annotations", "parameters", "viewport", "dont<PERSON><PERSON>", "div", "page", "linkService", "downloadManager", "Annotation<PERSON><PERSON>er", "document", "cancel", "hide", "createAnnotationLayerBuilder", "window", "pdfjsLib", "__non_webpack_require__", "module", "DEFAULT_L10N_STRINGS", "of_pages", "page_of_pages", "document_properties_kb", "document_properties_mb", "document_properties_date_string", "document_properties_page_size_unit_inches", "document_properties_page_size_unit_millimeters", "document_properties_page_size_orientation_portrait", "document_properties_page_size_orientation_landscape", "document_properties_page_size_name_a3", "document_properties_page_size_name_a4", "document_properties_page_size_name_letter", "document_properties_page_size_name_legal", "document_properties_page_size_dimension_string", "document_properties_page_size_dimension_name_string", "document_properties_linearized_yes", "document_properties_linearized_no", "print_progress_percent", "additional_layers", "page_landmark", "thumb_page_title", "thumb_page_canvas", "find_reached_top", "find_reached_bottom", "find_not_found", "error_version_info", "error_message", "error_stack", "error_file", "error_line", "rendering_error", "page_scale_width", "page_scale_fit", "page_scale_auto", "page_scale_actual", "page_scale_percent", "loading", "loading_error", "invalid_file_error", "missing_file_error", "unexpected_response_error", "printing_not_supported", "printing_not_ready", "web_fonts_disabled", "key", "args", "PARTIAL_LANG_CODES", "en", "es", "fy", "ga", "gu", "hi", "hy", "nb", "ne", "nn", "pa", "pt", "sv", "zh", "langCode", "name", "NullL10n", "fallback", "getL10nFallback", "formatL10nValue", "externalLinkTarget", "externalLinkRel", "ignoreDestinationZoom", "setDocument", "baseUrl", "Object", "<PERSON><PERSON><PERSON><PERSON>", "setHistory", "pagesCount", "rotation", "_goToDestinationHelper", "namedDest", "destRef", "explicitDest", "pageNumber", "pageIndex", "console", "Number", "destArray", "goToDestination", "Array", "goToPage", "val", "getDestinationHash", "dest", "escape", "str", "JSON", "getAnchorUrl", "setHash", "hash", "params", "parseQueryString", "source", "query", "phraseSearch", "zoomArgs", "zoomArg", "zoomArgNumber", "parseFloat", "allowNegativeOffset", "mode", "unescape", "isValidExplicitDestination", "executeNamedAction", "cachePageRef", "refStr", "pageRef", "_cachedPageNumber", "isPageVisible", "isPageCached", "dest<PERSON><PERSON><PERSON>", "zoom", "allowNull", "i", "param", "CSS_UNITS", "DEFAULT_SCALE_VALUE", "DEFAULT_SCALE", "MIN_SCALE", "MAX_SCALE", "UNKNOWN_SCALE", "MAX_AUTO_SCALE", "SCROLLBAR_PADDING", "VERTICAL_PADDING", "LOADINGBAR_END_OFFSET_VAR", "PresentationModeState", "UNKNOWN", "NORMAL", "CHANGING", "FULLSCREEN", "SidebarView", "NONE", "THUMBS", "OUTLINE", "ATTACHMENTS", "LAYERS", "RendererType", "CANVAS", "SVG", "TextLayerMode", "DISABLE", "ENABLE", "ENABLE_ENHANCE", "ScrollMode", "VERTICAL", "HORIZONTAL", "WRAPPED", "SpreadMode", "ODD", "EVEN", "AutoPrintRegExp", "devicePixelRatio", "backingStoreRatio", "ctx", "pixelRatio", "sx", "sy", "scaled", "scrollMatches", "parent", "element", "offsetY", "offsetX", "getComputedStyle", "spot", "debounceScroll", "rAF", "currentX", "viewAreaElement", "lastX", "state", "currentY", "lastY", "callback", "right", "down", "_event<PERSON><PERSON><PERSON>", "part", "value", "decodeURIComponent", "minIndex", "maxIndex", "items", "condition", "currentIndex", "currentItem", "Math", "xinv", "limit", "x_", "x", "a", "b", "c", "d", "p", "q", "result", "r", "changeOrientation", "rotate", "width", "height", "index", "elt", "views", "pageTop", "sortByVisibility", "horizontal", "rtl", "top", "scrollEl", "bottom", "left", "view", "elementBottom", "elementLeft", "elementRight", "visible", "numViews", "firstVisibleElementInd", "binarySearchFirstItem", "backtrackBeforeAllVisibleElements", "lastEdge", "currentWidth", "currentHeight", "viewWidth", "viewHeight", "viewRight", "viewBottom", "hiddenHeight", "hiddenWidth", "fractionHeight", "fractionWidth", "percent", "id", "y", "widthPercent", "first", "last", "pc", "evt", "delta", "angle", "normalizeWheelEventDirection", "MOUSE_DOM_DELTA_PIXEL_MODE", "MOUSE_DOM_DELTA_LINE_MODE", "MOUSE_PIXELS_PER_LINE", "MOUSE_LINES_PER_PAGE", "size", "WaitOnType", "EVENT", "TIMEOUT", "delay", "target", "clearTimeout", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "handler", "timeout<PERSON><PERSON><PERSON>", "timeout", "setTimeout", "animationStarted", "on", "options", "external", "once", "off", "dispatch", "eventListeners", "listener", "externalListeners", "_on", "_off", "ii", "units", "_updateBar", "progressSize", "isNaN", "clamp", "<PERSON><PERSON><PERSON><PERSON>", "container", "viewer", "scrollbarWidth", "doc", "show", "moved", "len", "arr", "write", "read", "curRoot", "curActiveOrFocused", "EXPAND_DIVS_TIMEOUT", "findController", "enhanceTextSelection", "_finishRendering", "endOfContent", "numTextDivs", "textLayerFrag", "textContent", "textContentStream", "textDivs", "textContentItemsStr", "setTextContentStream", "setTextContent", "_convertMatches", "iIndex", "end", "m", "mm", "matches", "matchIdx", "match", "begin", "divIdx", "offset", "matchesLength", "_renderMatches", "isSelectedPage", "pageIdx", "selectedMatchIdx", "highlightAll", "prevEnd", "infinity", "appendTextToDiv", "content", "node", "span", "className", "i0", "i1", "isSelected", "highlightSuffix", "selected<PERSON><PERSON>t", "beginText", "n0", "n1", "matchIndex", "_updateMatches", "clearedUntilDivIdx", "n", "pageMatches", "pageMatchesLength", "_bindMouse", "expandDivsTimer", "adjustTop", "divBounds", "createTextLayerBuilder", "downloadUrl", "createValidAbsoluteUrl", "download", "url", "downloadData", "blobUrl", "createObjectURL", "compatibilityParams", "openOrDownloadData", "isPdfData", "isPdfFile", "contentType", "URL", "type", "viewerUrl", "encodeURIComponent", "sourceEventType", "userAgent", "navigator", "platform", "maxTouchPoints", "isAndroid", "isIOS", "isIOSChrome", "OptionKind", "VIEWER", "API", "WORKER", "PREFERENCE", "defaultOptions", "cursorToolOnLoad", "kind", "defaultUrl", "defaultZoomValue", "disableHistory", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "enablePermissions", "enablePrintAutoRotate", "historyUpdateUrl", "maxCanvasPixels", "compatibility", "pdfBugEnabled", "printResolution", "renderer", "sidebarViewOnLoad", "scrollModeOnLoad", "spreadModeOnLoad", "textLayerMode", "useOnlyCssZoom", "viewerCssTheme", "viewOnLoad", "cMapPacked", "cMapUrl", "disableAutoFetch", "disableFontFace", "disable<PERSON><PERSON><PERSON>", "disableStream", "docBaseUrl", "enableXfa", "fontExtraProperties", "isEvalSupported", "maxImageSize", "pdfBug", "standardFontDataUrl", "verbosity", "workerPort", "workerSrc", "userOptions", "get", "userOption", "defaultOption", "getAll", "valueType", "set", "setAll", "remove", "webL10n", "fixupLangCode", "getLanguage", "getDirection", "translate", "gL10nData", "gTextData", "gTextProp", "gLanguage", "gMacros", "gReadyState", "gAsyncResourceLoading", "script", "l10nId", "l10nArgs", "onSuccess", "onFailure", "xhr", "baseURL", "href", "text", "dictionary", "reBlank", "reComment", "reSection", "reImport", "reSplit", "entries", "rawText", "currentLang", "genericLang", "lang", "skip<PERSON><PERSON>", "parsedRawLinesCallback", "line", "loadImport", "tmp", "evalString", "nextEntry", "xhrLoadText", "parseRawLines", "parsedPropertiesCallback", "parseProperties", "prop", "data", "success<PERSON>allback", "clear", "langLinks", "getL10nResourceLinks", "langCount", "dict", "getL10nDictionary", "defaultLocale", "anyCaseLang", "onResourceLoaded", "gResourceCount", "link", "parseResource", "resource", "locales2rules", "list", "start", "pluralRules", "isBetween", "getPluralRules", "rv", "substIndexes", "substArguments", "reIndex", "reMatch", "macroName", "paramName", "macro", "reArgs", "arg", "getL10nAttributes", "getL10nData", "getChildElementCount", "children", "found", "l", "textNode", "count", "getTranslatableChildren", "elementCount", "translateElement", "getData", "getText", "setLanguage", "loadLocale", "rtlList", "shortCode", "getReadyState", "ready", "FindState", "FOUND", "NOT_FOUND", "PENDING", "FIND_TIMEOUT", "MATCH_SCROLL_OFFSET_TOP", "MATCH_SCROLL_OFFSET_LEFT", "CHARACTERS_TO_NORMALIZE", "normalizationRegex", "replace", "diffs", "normalizedText", "normalizedCh", "diff", "ch", "totalDiff", "eventBus", "highlightMatches", "selected", "executeCommand", "pdfDocument", "cmd", "findbarClosed", "pendingTimeout", "scrollMatchIntoView", "scrollIntoView", "_reset", "wrapped", "_query", "normalize", "_shouldDirtyMatch", "_prepareMatches", "currentElem", "matchesWithLength", "nextElem", "prevElem", "isSubTerm", "_isEntireWord", "startIdx", "getCharacterType", "endIdx", "_calculatePhraseMatch", "queryLen", "pageContent", "entireWord", "originalMatchIdx", "getOriginalIndex", "matchEnd", "originalQueryLen", "_calculateWordMatch", "query<PERSON>rray", "subquery", "subqueryLen", "matchLength", "skipped", "_calculateMatch", "pageDiffs", "pageMatchesCount", "_extractText", "promise", "Promise", "extractTextCapability", "pdfPage", "normalizeWhitespace", "textItems", "str<PERSON><PERSON>", "j", "jj", "reason", "_updatePage", "_updateAllPages", "_nextMatch", "previous", "currentPageIndex", "numPages", "numPageMatches", "_matchesReady", "numMatches", "_nextPageMatch", "_advanceOffsetPage", "_updateMatch", "previousPage", "_onFindBarClose", "_requestMatchesCount", "current", "total", "_updateUIResultsCount", "matchesCount", "_updateUIState", "<PERSON><PERSON><PERSON><PERSON>", "CharacterType", "SPACE", "ALPHA_LETTER", "PUNCT", "HAN_LETTER", "KATAKANA_LETTER", "HIRAGANA_LETTER", "HALFWIDTH_KATAKANA_LETTER", "THAI_LETTER", "charCode", "isAlphabeticalScript", "isAscii", "isAsciiSpace", "isAsciiAlpha", "isAsciiDigit", "isThai", "is<PERSON>an", "isKatakana", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isHalfwidthKatakana", "HASH_CHANGE_TIMEOUT", "POSITION_UPDATED_THRESHOLD", "UPDATE_VIEWAREA_TIMEOUT", "initialize", "resetHistory", "updateUrl", "reInitialized", "getCurrentHash", "destination", "reset", "push", "forceReplace", "isDestArraysEqual", "pushPage", "pushCurrentPosition", "back", "forward", "popStateInProgress", "initialBookmark", "initialRotation", "_pushOrReplaceState", "shouldReplace", "newState", "fingerprint", "uid", "newUrl", "_tryPushCurrentPosition", "temporary", "position", "_isValidPage", "_isValidState", "checkReload", "performance", "perfEntry", "_updateInternalState", "removeTemporary", "_parseCurrentHash", "checkNameddest", "nameddest", "_updateViewarea", "location", "_popState", "newHash", "hash<PERSON><PERSON>ed", "waitOnEventOrTimeout", "isValidRotation", "_pageHide", "_bindEvents", "updateViewarea", "popState", "pageHide", "_unbindEvents", "destHash", "second", "isEntryEqual", "firstDest", "secondDest", "MAX_CANVAS_PIXELS", "defaultViewport", "RenderingStates", "msg", "setPdfPage", "totalRotation", "scale", "destroy", "_renderAnnotationLayer", "error", "_renderXfaLayer", "_reset<PERSON><PERSON><PERSON><PERSON>er", "removeFromDOM", "zoomLayerCanvas", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keepAnnotationLayer", "keepXfa<PERSON><PERSON>er", "childNodes", "zoomLayerNode", "annotationLayerNode", "xfaLayerNode", "update", "optionalContentConfigPromise", "redrawAnnotationLayer", "redrawXfaLayer", "cssTransform", "timestamp", "isScalingRestricted", "outputScale", "cancelRendering", "relativeRotation", "absRotation", "scaleX", "scaleY", "textLayerViewport", "textRelativeRotation", "textAbsRotation", "textLayerDiv", "transX", "transY", "getPagePoint", "draw", "canvasWrapper", "textLayer", "renderContinueCallback", "cont", "finishPaintTask", "paintTask", "resultPromise", "readableStream", "includeMarkedContent", "event", "tree", "treeDom", "paintOnCanvas", "renderCapability", "onRenderContinue", "renderTask", "canvas", "isCanvasHidden", "showCanvas", "alpha", "getOutputScale", "actualSizeViewport", "pixelsInViewport", "maxScale", "sfx", "approximateFraction", "sfy", "roundToDivide", "transform", "renderContext", "canvasContext", "paintOnSvg", "cancelled", "ensureNotCancelled", "opList", "svgGfx", "svg", "wrapper", "setPageLabel", "CLEANUP_TIMEOUT", "INITIAL", "RUNNING", "PAUSED", "FINISHED", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isHighestPriority", "<PERSON><PERSON><PERSON><PERSON>", "renderHighestPriority", "getHighestPriority", "visibleViews", "numVisible", "nextPageIndex", "previousPageIndex", "isViewFinished", "render<PERSON>iew", "sandboxBundleSrc", "scriptingFactory", "docPropertiesLookup", "detail", "docProperties", "appInfo", "language", "docInfo", "actions", "dispatchWillSave", "dispatchDidSave", "dispatchWillPrint", "dispatchDidPrint", "destroyPromise", "_internalEvents", "shadow", "_domEvents", "_pageOpenPending", "_visitedPages", "_updateFromSandbox", "isInPresentationMode", "apiPageLayoutToSpreadMode", "ids", "_dispatchPageOpen", "visitedPages", "pageView", "actionsPromise", "_dispatchPageClose", "_getDocProperties", "require", "_createScripting", "_destroyScripting", "contentLength", "filesize", "filename", "contentDispositionFilename", "getPdfFilenameFromUrl", "metadata", "authors", "createSandbox", "sandbox", "dispatchEventInSandbox", "destroySandbox", "_viewerElement", "_pageWidthScaleFactor", "_resetView", "_ensurePageViewVisible", "previousPageView", "viewerNodes", "_scrollUpdate", "_scrollIntoView", "pageSpot", "scrolledDown", "_getVisiblePages", "_update<PERSON><PERSON>per", "_isScrollModeHorizontal", "_updateScrollMode", "_updateSpreadMode", "_getPageAdvance", "DEFAULT_CACHE_SIZE", "pageIdsToKeep", "iMax", "pagesToKeep", "moveToEndOfArray", "newScale", "viewerVersion", "version", "watchScroll", "getPageView", "pageViewsReady", "currentPageNumber", "_setCurrentPageNumber", "resetCurrentPageView", "pageLabel", "currentPageLabel", "currentScale", "currentScaleValue", "pagesRotation", "firstPagePromise", "one<PERSON>ageRendered", "pagesPromise", "_onePageRenderedOrForceFetch", "isPureXfa", "firstPdfPage", "textLayerFactory", "xfaLayerFactory", "pageNum", "renderingQueue", "annotationLayerFactory", "structTreeLayerFactory", "firstPageView", "getPagesLeft", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "labels", "_setScaleUpdatePages", "noScroll", "preset", "newValue", "isSameScale", "presetValue", "_setScale", "currentPage", "noPadding", "hPadding", "vPadding", "pageWidthScale", "pageHeightScale", "horizontalScale", "isPortraitOrientation", "_resetCurrentPageView", "pageDiv", "pageLabelToPageNumber", "scrollPageIntoView", "pageWidth", "pageHeight", "widthScale", "heightScale", "boundingRect", "_updateLocation", "normalizedScaleValue", "firstPage", "pdfOpenParams", "currentPageView", "topLeft", "intLeft", "intTop", "visiblePages", "numVisiblePages", "newCacheSize", "containsElement", "focus", "_isContainerRtl", "isChangingPresentationMode", "isHorizontalScrollbarEnabled", "isVerticalScrollbarEnabled", "_getCurrentVisiblePage", "cleanup", "_cancelRendering", "_ensurePdfPageLoaded", "forceRendering", "currentlyVisiblePages", "scrollAhead", "createXfaLayerBuilder", "createStructTreeLayerBuilder", "hasEqualPageSizes", "getPagesOverview", "scrollMode", "isValidScrollMode", "spreadMode", "isValidSpreadMode", "pages", "parity", "spread", "pageLayout", "yArray", "currentId", "expectedId", "firstId", "lastId", "nextPage", "advance", "PDF_ROLE_TO_HTML_ROLE", "Document", "DocumentFragment", "Part", "Sect", "Div", "Aside", "NonStruct", "P", "H", "Title", "FENote", "Sub", "Lbl", "Span", "Em", "Strong", "Link", "<PERSON><PERSON>", "Form", "<PERSON>", "RB", "RT", "RP", "<PERSON><PERSON><PERSON>", "WT", "WP", "L", "LI", "LBody", "Table", "TR", "TH", "TD", "THead", "TBody", "TFoot", "Caption", "Figure", "Formula", "Artifact", "HEADING_PATTERN", "_setAttributes", "structElement", "htmlElement", "_walk", "role", "xfa", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xfaHtml", "scrollLeft", "stillFullyVisible", "pdfjsVersion", "pdfjsBuild"], "sourceRoot": ""}
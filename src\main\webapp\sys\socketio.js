﻿/*! Socket.IO.js build:0.9.16, development. Copyright(c) 2011 LearnBoost <<EMAIL>> MIT Licensed */
var io = ("undefined" === typeof module ? {} : module.exports); (function () {
    (function (exports, global) { var io = exports; io.version = "0.9.16"; io.protocol = 1; io.transports = []; io.j = []; io.sockets = {}; io.connect = function (host, details) { var uri = io.util.parseUri(host), uuri, socket; if (global && global.location) { uri.protocol = uri.protocol || global.location.protocol.slice(0, -1); uri.host = uri.host || (global.document ? global.document.domain : global.location.hostname); uri.port = uri.port || global.location.port } uuri = io.util.uniqueUri(uri); var options = { host: uri.host, secure: "https" == uri.protocol, port: uri.port || ("https" == uri.protocol ? 443 : 80), query: uri.query || "" }; io.util.merge(options, details); if (options["force new connection"] || !io.sockets[uuri]) { socket = new io.Socket(options) } if (!options["force new connection"] && socket) { io.sockets[uuri] = socket } socket = socket || io.sockets[uuri]; return socket.of(uri.path.length > 1 ? uri.path : "") } })("object" === typeof module ? module.exports : (this.io = {}), this); (function (exports, global) { var util = exports.util = {}; var re = /^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/; var parts = ["source", "protocol", "authority", "userInfo", "user", "password", "host", "port", "relative", "path", "directory", "file", "query", "anchor"]; util.parseUri = function (str) { var m = re.exec(str || ""), uri = {}, i = 14; while (i--) { uri[parts[i]] = m[i] || "" } return uri }; util.uniqueUri = function (uri) { var protocol = uri.protocol, host = uri.host, port = uri.port; if ("document" in global) { host = host || document.domain; port = port || (protocol == "https" && document.location.protocol !== "https:" ? 443 : document.location.port) } else { host = host || "localhost"; if (!port && protocol == "https") { port = 443 } } return (protocol || "http") + "://" + host + ":" + (port || 80) }; util.query = function (base, addition) { var query = util.chunkQuery(base || ""), components = []; util.merge(query, util.chunkQuery(addition || "")); for (var part in query) { if (query.hasOwnProperty(part)) { components.push(part + "=" + query[part]) } } return components.length ? "?" + components.join("&") : "" }; util.chunkQuery = function (qs) { var query = {}, params = qs.split("&"), i = 0, l = params.length, kv; for (; i < l; ++i) { kv = params[i].split("="); if (kv[0]) { query[kv[0]] = kv[1] } } return query }; var pageLoaded = false; util.load = function (fn) { if ("document" in global && document.readyState === "complete" || pageLoaded) { return fn() } util.on(global, "load", fn, false) }; util.on = function (element, event, fn, capture) { if (element.attachEvent) { element.attachEvent("on" + event, fn) } else { if (element.addEventListener) { element.addEventListener(event, fn, capture) } } }; util.request = function (xdomain) { if (xdomain && "undefined" != typeof XDomainRequest && !util.ua.hasCORS) { return new XDomainRequest() } if ("undefined" != typeof XMLHttpRequest && (!xdomain || util.ua.hasCORS)) { return new XMLHttpRequest() } if (!xdomain) { try { return new window[(["Active"].concat("Object").join("X"))]("Microsoft.XMLHTTP") } catch (e) { } } return null }; if ("undefined" != typeof window) { util.load(function () { pageLoaded = true }) } util.defer = function (fn) { if (!util.ua.webkit || "undefined" != typeof importScripts) { return fn() } util.load(function () { setTimeout(fn, 100) }) }; util.merge = function merge(target, additional, deep, lastseen) { var seen = lastseen || [], depth = typeof deep == "undefined" ? 2 : deep, prop; for (prop in additional) { if (additional.hasOwnProperty(prop) && util.indexOf(seen, prop) < 0) { if (typeof target[prop] !== "object" || !depth) { target[prop] = additional[prop]; seen.push(additional[prop]) } else { util.merge(target[prop], additional[prop], depth - 1, seen) } } } return target }; util.mixin = function (ctor, ctor2) { util.merge(ctor.prototype, ctor2.prototype) }; util.inherit = function (ctor, ctor2) { function f() { } f.prototype = ctor2.prototype; ctor.prototype = new f }; util.isArray = Array.isArray || function (obj) { return Object.prototype.toString.call(obj) === "[object Array]" }; util.intersect = function (arr, arr2) { var ret = [], longest = arr.length > arr2.length ? arr : arr2, shortest = arr.length > arr2.length ? arr2 : arr; for (var i = 0, l = shortest.length; i < l; i++) { if (~util.indexOf(longest, shortest[i])) { ret.push(shortest[i]) } } return ret }; util.indexOf = function (arr, o, i) { for (var j = arr.length, i = i < 0 ? i + j < 0 ? 0 : i + j : i || 0; i < j && arr[i] !== o; i++) { } return j <= i ? -1 : i }; util.toArray = function (enu) { var arr = []; for (var i = 0, l = enu.length; i < l; i++) { arr.push(enu[i]) } return arr }; util.ua = {}; util.ua.hasCORS = "undefined" != typeof XMLHttpRequest && (function () { try { var a = new XMLHttpRequest() } catch (e) { return false } return a.withCredentials != undefined })(); util.ua.webkit = "undefined" != typeof navigator && /webkit/i.test(navigator.userAgent); util.ua.iDevice = "undefined" != typeof navigator && /iPad|iPhone|iPod/i.test(navigator.userAgent) })("undefined" != typeof io ? io : module.exports, this); (function (exports, io) {
        exports.EventEmitter = EventEmitter; function EventEmitter() { } EventEmitter.prototype.on = function (name, fn) {
            if (!this.$events) { this.$events = {} } if (!this.$events[name]) { this.$events[name] = fn } else {
                if (io.util.isArray(this.$events[name])) {
                    this.$events[name].push(fn)
                } else { this.$events[name] = [this.$events[name], fn] } 
            } return this
        }; EventEmitter.prototype.addListener = EventEmitter.prototype.on; EventEmitter.prototype.once = function (name, fn) { var self = this; function on() { self.removeListener(name, on); fn.apply(this, arguments) } on.listener = fn; this.on(name, on); return this }; EventEmitter.prototype.removeListener = function (name, fn) { if (this.$events && this.$events[name]) { var list = this.$events[name]; if (io.util.isArray(list)) { var pos = -1; for (var i = 0, l = list.length; i < l; i++) { if (list[i] === fn || (list[i].listener && list[i].listener === fn)) { pos = i; break } } if (pos < 0) { return this } list.splice(pos, 1); if (!list.length) { delete this.$events[name] } } else { if (list === fn || (list.listener && list.listener === fn)) { delete this.$events[name] } } } return this }; EventEmitter.prototype.removeAllListeners = function (name) { if (name === undefined) { this.$events = {}; return this } if (this.$events && this.$events[name]) { this.$events[name] = null } return this }; EventEmitter.prototype.listeners = function (name) { if (!this.$events) { this.$events = {} } if (!this.$events[name]) { this.$events[name] = [] } if (!io.util.isArray(this.$events[name])) { this.$events[name] = [this.$events[name]] } return this.$events[name] }; EventEmitter.prototype.emit = function (name) { if (!this.$events) { return false } var handler = this.$events[name]; if (!handler) { return false } var args = Array.prototype.slice.call(arguments, 1); if ("function" == typeof handler) { handler.apply(this, args) } else { if (io.util.isArray(handler)) { var listeners = handler.slice(); for (var i = 0, l = listeners.length; i < l; i++) { listeners[i].apply(this, args) } } else { return false } } return true } 
    })("undefined" != typeof io ? io : module.exports, "undefined" != typeof io ? io : module.parent.exports); (function (exports, nativeJSON) { if (nativeJSON && nativeJSON.parse) { return exports.JSON = { parse: nativeJSON.parse, stringify: nativeJSON.stringify} } var JSON = exports.JSON = {}; function f(n) { return n < 10 ? "0" + n : n } function date(d, key) { return isFinite(d.valueOf()) ? d.getUTCFullYear() + "-" + f(d.getUTCMonth() + 1) + "-" + f(d.getUTCDate()) + "T" + f(d.getUTCHours()) + ":" + f(d.getUTCMinutes()) + ":" + f(d.getUTCSeconds()) + "Z" : null } var cx = /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g, escapable = /[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g, gap, indent, meta = { "\b": "\\b", "\t": "\\t", "\n": "\\n", "\f": "\\f", "\r": "\\r", '"': '\\"', "\\": "\\\\" }, rep; function quote(string) { escapable.lastIndex = 0; return escapable.test(string) ? '"' + string.replace(escapable, function (a) { var c = meta[a]; return typeof c === "string" ? c : "\\u" + ("0000" + a.charCodeAt(0).toString(16)).slice(-4) }) + '"' : '"' + string + '"' } function str(key, holder) { var i, k, v, length, mind = gap, partial, value = holder[key]; if (value instanceof Date) { value = date(key) } if (typeof rep === "function") { value = rep.call(holder, key, value) } switch (typeof value) { case "string": return quote(value); case "number": return isFinite(value) ? String(value) : "null"; case "boolean": case "null": return String(value); case "object": if (!value) { return "null" } gap += indent; partial = []; if (Object.prototype.toString.apply(value) === "[object Array]") { length = value.length; for (i = 0; i < length; i += 1) { partial[i] = str(i, value) || "null" } v = partial.length === 0 ? "[]" : gap ? "[\n" + gap + partial.join(",\n" + gap) + "\n" + mind + "]" : "[" + partial.join(",") + "]"; gap = mind; return v } if (rep && typeof rep === "object") { length = rep.length; for (i = 0; i < length; i += 1) { if (typeof rep[i] === "string") { k = rep[i]; v = str(k, value); if (v) { partial.push(quote(k) + (gap ? ": " : ":") + v) } } } } else { for (k in value) { if (Object.prototype.hasOwnProperty.call(value, k)) { v = str(k, value); if (v) { partial.push(quote(k) + (gap ? ": " : ":") + v) } } } } v = partial.length === 0 ? "{}" : gap ? "{\n" + gap + partial.join(",\n" + gap) + "\n" + mind + "}" : "{" + partial.join(",") + "}"; gap = mind; return v } } JSON.stringify = function (value, replacer, space) { var i; gap = ""; indent = ""; if (typeof space === "number") { for (i = 0; i < space; i += 1) { indent += " " } } else { if (typeof space === "string") { indent = space } } rep = replacer; if (replacer && typeof replacer !== "function" && (typeof replacer !== "object" || typeof replacer.length !== "number")) { throw new Error("JSON.stringify") } return str("", { "": value }) }; JSON.parse = function (text, reviver) { var j; function walk(holder, key) { var k, v, value = holder[key]; if (value && typeof value === "object") { for (k in value) { if (Object.prototype.hasOwnProperty.call(value, k)) { v = walk(value, k); if (v !== undefined) { value[k] = v } else { delete value[k] } } } } return reviver.call(holder, key, value) } text = String(text); cx.lastIndex = 0; if (cx.test(text)) { text = text.replace(cx, function (a) { return "\\u" + ("0000" + a.charCodeAt(0).toString(16)).slice(-4) }) } if (/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g, "@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, "]").replace(/(?:^|:|,)(?:\s*\[)+/g, ""))) { j = eval("(" + text + ")"); return typeof reviver === "function" ? walk({ "": j }, "") : j } throw new SyntaxError("JSON.parse") } })("undefined" != typeof io ? io : module.exports, typeof JSON !== "undefined" ? JSON : undefined); (function (exports, io) {
        var parser = exports.parser = {}; var packets = parser.packets = ["disconnect", "connect", "heartbeat", "message", "json", "event", "ack", "error", "noop"];
        var reasons = parser.reasons = ["transport not supported", "client not handshaken", "unauthorized"]; var advice = parser.advice = ["reconnect"]; var JSON = io.JSON, indexOf = io.util.indexOf; parser.encodePacket = function (packet) { var type = indexOf(packets, packet.type), id = packet.id || "", endpoint = packet.endpoint || "", ack = packet.ack, data = null; switch (packet.type) { case "error": var reason = packet.reason ? indexOf(reasons, packet.reason) : "", adv = packet.advice ? indexOf(advice, packet.advice) : ""; if (reason !== "" || adv !== "") { data = reason + (adv !== "" ? ("+" + adv) : "") } break; case "message": if (packet.data !== "") { data = packet.data } break; case "event": var ev = { name: packet.name }; if (packet.args && packet.args.length) { ev.args = packet.args } data = JSON.stringify(ev); break; case "json": data = JSON.stringify(packet.data); break; case "connect": if (packet.qs) { data = packet.qs } break; case "ack": data = packet.ackId + (packet.args && packet.args.length ? "+" + JSON.stringify(packet.args) : ""); break } var encoded = [type, id + (ack == "data" ? "+" : ""), endpoint]; if (data !== null && data !== undefined) { encoded.push(data) } return encoded.join(":") }; parser.encodePayload = function (packets) { var decoded = ""; if (packets.length == 1) { return packets[0] } for (var i = 0, l = packets.length; i < l; i++) { var packet = packets[i]; decoded += "\ufffd" + packet.length + "\ufffd" + packets[i] } return decoded }; var regexp = /([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/; parser.decodePacket = function (data) { var pieces = data.match(regexp); if (!pieces) { return {} } var id = pieces[2] || "", data = pieces[5] || "", packet = { type: packets[pieces[1]], endpoint: pieces[4] || "" }; if (id) { packet.id = id; if (pieces[3]) { packet.ack = "data" } else { packet.ack = true } } switch (packet.type) { case "error": var pieces = data.split("+"); packet.reason = reasons[pieces[0]] || ""; packet.advice = advice[pieces[1]] || ""; break; case "message": packet.data = data || ""; break; case "event": try { var opts = JSON.parse(data); packet.name = opts.name; packet.args = opts.args } catch (e) { } packet.args = packet.args || []; break; case "json": try { packet.data = JSON.parse(data) } catch (e) { } break; case "connect": packet.qs = data || ""; break; case "ack": var pieces = data.match(/^([0-9]+)(\+)?(.*)/); if (pieces) { packet.ackId = pieces[1]; packet.args = []; if (pieces[3]) { try { packet.args = pieces[3] ? JSON.parse(pieces[3]) : [] } catch (e) { } } } break; case "disconnect": case "heartbeat": break } return packet }; parser.decodePayload = function (data) { if (data.charAt(0) == "\ufffd") { var ret = []; for (var i = 1, length = ""; i < data.length; i++) { if (data.charAt(i) == "\ufffd") { ret.push(parser.decodePacket(data.substr(i + 1).substr(0, length))); i += Number(length) + 1; length = "" } else { length += data.charAt(i) } } return ret } else { return [parser.decodePacket(data)] } } 
    })("undefined" != typeof io ? io : module.exports, "undefined" != typeof io ? io : module.parent.exports); (function (exports, io) { exports.Transport = Transport; function Transport(socket, sessid) { this.socket = socket; this.sessid = sessid } io.util.mixin(Transport, io.EventEmitter); Transport.prototype.heartbeats = function () { return true }; Transport.prototype.onData = function (data) { this.clearCloseTimeout(); if (this.socket.connected || this.socket.connecting || this.socket.reconnecting) { this.setCloseTimeout() } if (data !== "") { var msgs = io.parser.decodePayload(data); if (msgs && msgs.length) { for (var i = 0, l = msgs.length; i < l; i++) { this.onPacket(msgs[i]) } } } return this }; Transport.prototype.onPacket = function (packet) { this.socket.setHeartbeatTimeout(); if (packet.type == "heartbeat") { return this.onHeartbeat() } if (packet.type == "connect" && packet.endpoint == "") { this.onConnect() } if (packet.type == "error" && packet.advice == "reconnect") { this.isOpen = false } this.socket.onPacket(packet); return this }; Transport.prototype.setCloseTimeout = function () { if (!this.closeTimeout) { var self = this; this.closeTimeout = setTimeout(function () { self.onDisconnect() }, this.socket.closeTimeout) } }; Transport.prototype.onDisconnect = function () { if (this.isOpen) { this.close() } this.clearTimeouts(); this.socket.onDisconnect(); return this }; Transport.prototype.onConnect = function () { this.socket.onConnect(); return this }; Transport.prototype.clearCloseTimeout = function () { if (this.closeTimeout) { clearTimeout(this.closeTimeout); this.closeTimeout = null } }; Transport.prototype.clearTimeouts = function () { this.clearCloseTimeout(); if (this.reopenTimeout) { clearTimeout(this.reopenTimeout) } }; Transport.prototype.packet = function (packet) { this.send(io.parser.encodePacket(packet)) }; Transport.prototype.onHeartbeat = function (heartbeat) { this.packet({ type: "heartbeat" }) }; Transport.prototype.onOpen = function () { this.isOpen = true; this.clearCloseTimeout(); this.socket.onOpen() }; Transport.prototype.onClose = function () { var self = this; this.isOpen = false; this.socket.onClose(); this.onDisconnect() }; Transport.prototype.prepareUrl = function () { var options = this.socket.options; return this.scheme() + "://" + options.host + ":" + options.port + "/" + options.resource + "/" + io.protocol + "/" + this.name + "/" + this.sessid }; Transport.prototype.ready = function (socket, fn) { fn.call(this) } })("undefined" != typeof io ? io : module.exports, "undefined" != typeof io ? io : module.parent.exports); (function (exports, io, global) {
        exports.Socket = Socket; function Socket(options) {
            this.options = { port: 80, secure: false, document: "document" in global ? document : false, resource: "socket.io", transports: io.transports, "connect timeout": 10000, "try multiple transports": true, "reconnect": true, "reconnection delay": 500, "reconnection limit": Infinity, "reopen delay": 3000, "max reconnection attempts": 10, "sync disconnect on unload": false, "auto connect": true, "flash policy port": 10843, "manualFlush": false };
            io.util.merge(this.options, options); this.connected = false; this.open = false; this.connecting = false; this.reconnecting = false; this.namespaces = {}; this.buffer = []; this.doBuffer = false; if (this.options["sync disconnect on unload"] && (!this.isXDomain() || io.util.ua.hasCORS)) { var self = this; io.util.on(global, "beforeunload", function () { self.disconnectSync() }, false) } if (this.options["auto connect"]) { this.connect() } 
        } io.util.mixin(Socket, io.EventEmitter); Socket.prototype.of = function (name) { if (!this.namespaces[name]) { this.namespaces[name] = new io.SocketNamespace(this, name); if (name !== "") { this.namespaces[name].packet({ type: "connect" }) } } return this.namespaces[name] }; Socket.prototype.publish = function () { this.emit.apply(this, arguments); var nsp; for (var i in this.namespaces) { if (this.namespaces.hasOwnProperty(i)) { nsp = this.of(i); nsp.$emit.apply(nsp, arguments) } } }; function empty() { } Socket.prototype.handshake = function (fn) { var self = this, options = this.options; function complete(data) { if (data instanceof Error) { self.connecting = false; self.onError(data.message) } else { fn.apply(null, data.split(":")) } } var url = ["http" + (options.secure ? "s" : "") + ":/", options.host + ":" + options.port, options.resource, io.protocol, io.util.query(this.options.query, "t=" + +new Date)].join("/"); if (this.isXDomain() && !io.util.ua.hasCORS) { var insertAt = document.getElementsByTagName("script")[0], script = document.createElement("script"); script.src = url + "&jsonp=" + io.j.length; insertAt.parentNode.appendChild(script, insertAt); io.j.push(function (data) { complete(data); script.parentNode.removeChild(script) }) } else { var xhr = io.util.request(); xhr.open("GET", url, true); if (this.isXDomain()) { xhr.withCredentials = true } xhr.onreadystatechange = function () { if (xhr.readyState == 4) { xhr.onreadystatechange = empty; if (xhr.status == 200) { complete(xhr.responseText) } else { if (xhr.status == 403) { self.onError(xhr.responseText) } else { self.connecting = false; !self.reconnecting && self.onError(xhr.responseText) } } } }; xhr.send(null) } }; Socket.prototype.getTransport = function (override) { var transports = override || this.transports, match; for (var i = 0, transport; transport = transports[i]; i++) { if (io.Transport[transport] && io.Transport[transport].check(this) && (!this.isXDomain() || io.Transport[transport].xdomainCheck(this))) { return new io.Transport[transport](this, this.sessionid) } } return null }; Socket.prototype.connect = function (fn) { if (this.connecting) { return this } var self = this; self.connecting = true; this.handshake(function (sid, heartbeat, close, transports) { self.sessionid = sid; self.closeTimeout = close * 1000; self.heartbeatTimeout = heartbeat * 1000; if (!self.transports) { self.transports = self.origTransports = (transports ? io.util.intersect(transports.split(","), self.options.transports) : self.options.transports) } self.setHeartbeatTimeout(); function connect(transports) { if (self.transport) { self.transport.clearTimeouts() } self.transport = self.getTransport(transports); if (!self.transport) { return self.publish("connect_failed") } self.transport.ready(self, function () { self.connecting = true; self.publish("connecting", self.transport.name); self.transport.open(); if (self.options["connect timeout"]) { self.connectTimeoutTimer = setTimeout(function () { if (!self.connected) { self.connecting = false; if (self.options["try multiple transports"]) { var remaining = self.transports; while (remaining.length > 0 && remaining.splice(0, 1)[0] != self.transport.name) { } if (remaining.length) { connect(remaining) } else { self.publish("connect_failed") } } } }, self.options["connect timeout"]) } }) } connect(self.transports); self.once("connect", function () { clearTimeout(self.connectTimeoutTimer); fn && typeof fn == "function" && fn() }) }); return this }; Socket.prototype.setHeartbeatTimeout = function () { clearTimeout(this.heartbeatTimeoutTimer); if (this.transport && !this.transport.heartbeats()) { return } var self = this; this.heartbeatTimeoutTimer = setTimeout(function () { self.transport.onClose() }, this.heartbeatTimeout) }; Socket.prototype.packet = function (data) { if (this.connected && !this.doBuffer) { this.transport.packet(data) } else { this.buffer.push(data) } return this }; Socket.prototype.setBuffer = function (v) { this.doBuffer = v; if (!v && this.connected && this.buffer.length) { if (!this.options["manualFlush"]) { this.flushBuffer() } } }; Socket.prototype.flushBuffer = function () { this.transport.payload(this.buffer); this.buffer = [] }; Socket.prototype.disconnect = function () { if (this.connected || this.connecting) { if (this.open) { this.of("").packet({ type: "disconnect" }) } this.onDisconnect("booted") } return this }; Socket.prototype.disconnectSync = function () { var xhr = io.util.request(); var uri = ["http" + (this.options.secure ? "s" : "") + ":/", this.options.host + ":" + this.options.port, this.options.resource, io.protocol, "", this.sessionid].join("/") + "/?disconnect=1"; xhr.open("GET", uri, false); xhr.send(null); this.onDisconnect("booted") }; Socket.prototype.isXDomain = function () { var port = global.location.port || ("https:" == global.location.protocol ? 443 : 80); return this.options.host !== global.location.hostname || this.options.port != port }; Socket.prototype.onConnect = function () {
            if (!this.connected) {
                this.connected = true; this.connecting = false; if (!this.doBuffer) {
                    this.setBuffer(false)
                } this.emit("connect")
            } 
        }; Socket.prototype.onOpen = function () { this.open = true }; Socket.prototype.onClose = function () { this.open = false; clearTimeout(this.heartbeatTimeoutTimer) }; Socket.prototype.onPacket = function (packet) { this.of(packet.endpoint).onPacket(packet) }; Socket.prototype.onError = function (err) { if (err && err.advice) { if (err.advice === "reconnect" && (this.connected || this.connecting)) { this.disconnect(); if (this.options.reconnect) { this.reconnect() } } } this.publish("error", err && err.reason ? err.reason : err) }; Socket.prototype.onDisconnect = function (reason) { var wasConnected = this.connected, wasConnecting = this.connecting; this.connected = false; this.connecting = false; this.open = false; if (wasConnected || wasConnecting) { this.transport.close(); this.transport.clearTimeouts(); if (wasConnected) { this.publish("disconnect", reason); if ("booted" != reason && this.options.reconnect && !this.reconnecting) { this.reconnect() } } } }; Socket.prototype.reconnect = function () { this.reconnecting = true; this.reconnectionAttempts = 0; this.reconnectionDelay = this.options["reconnection delay"]; var self = this, maxAttempts = this.options["max reconnection attempts"], tryMultiple = this.options["try multiple transports"], limit = this.options["reconnection limit"]; function reset() { if (self.connected) { for (var i in self.namespaces) { if (self.namespaces.hasOwnProperty(i) && "" !== i) { self.namespaces[i].packet({ type: "connect" }) } } self.publish("reconnect", self.transport.name, self.reconnectionAttempts) } clearTimeout(self.reconnectionTimer); self.removeListener("connect_failed", maybeReconnect); self.removeListener("connect", maybeReconnect); self.reconnecting = false; delete self.reconnectionAttempts; delete self.reconnectionDelay; delete self.reconnectionTimer; delete self.redoTransports; self.options["try multiple transports"] = tryMultiple } function maybeReconnect() { if (!self.reconnecting) { return } if (self.connected) { return reset() } if (self.connecting && self.reconnecting) { return self.reconnectionTimer = setTimeout(maybeReconnect, 1000) } if (self.reconnectionAttempts++ >= maxAttempts) { if (!self.redoTransports) { self.on("connect_failed", maybeReconnect); self.options["try multiple transports"] = true; self.transports = self.origTransports; self.transport = self.getTransport(); self.redoTransports = true; self.connect() } else { self.publish("reconnect_failed"); reset() } } else { if (self.reconnectionDelay < limit) { self.reconnectionDelay *= 2 } self.connect(); self.publish("reconnecting", self.reconnectionDelay, self.reconnectionAttempts); self.reconnectionTimer = setTimeout(maybeReconnect, self.reconnectionDelay) } } this.options["try multiple transports"] = false; this.reconnectionTimer = setTimeout(maybeReconnect, this.reconnectionDelay); this.on("connect", maybeReconnect) } 
    })("undefined" != typeof io ? io : module.exports, "undefined" != typeof io ? io : module.parent.exports, this); (function (exports, io) {
        exports.SocketNamespace = SocketNamespace; function SocketNamespace(socket, name) { this.socket = socket; this.name = name || ""; this.flags = {}; this.json = new Flag(this, "json"); this.ackPackets = 0; this.acks = {} } io.util.mixin(SocketNamespace, io.EventEmitter); SocketNamespace.prototype.$emit = io.EventEmitter.prototype.emit; SocketNamespace.prototype.of = function () { return this.socket.of.apply(this.socket, arguments) }; SocketNamespace.prototype.packet = function (packet) { packet.endpoint = this.name; this.socket.packet(packet); this.flags = {}; return this }; SocketNamespace.prototype.send = function (data, fn) { var packet = { type: this.flags.json ? "json" : "message", data: data }; if ("function" == typeof fn) { packet.id = ++this.ackPackets; packet.ack = true; this.acks[packet.id] = fn } return this.packet(packet) }; SocketNamespace.prototype.emit = function (name) { var args = Array.prototype.slice.call(arguments, 1), lastArg = args[args.length - 1], packet = { type: "event", name: name }; if ("function" == typeof lastArg) { packet.id = ++this.ackPackets; packet.ack = "data"; this.acks[packet.id] = lastArg; args = args.slice(0, args.length - 1) } packet.args = args; return this.packet(packet) }; SocketNamespace.prototype.disconnect = function () { if (this.name === "") { this.socket.disconnect() } else { this.packet({ type: "disconnect" }); this.$emit("disconnect") } return this }; SocketNamespace.prototype.onPacket = function (packet) {
            var self = this; function ack() { self.packet({ type: "ack", args: io.util.toArray(arguments), ackId: packet.id }) } switch (packet.type) {
                case "connect": this.$emit("connect"); break; case "disconnect": if (this.name === "") { this.socket.onDisconnect(packet.reason || "booted") } else { this.$emit("disconnect", packet.reason) } break; case "message": case "json": var params = ["message", packet.data]; if (packet.ack == "data") { params.push(ack) } else { if (packet.ack) { this.packet({ type: "ack", ackId: packet.id }) } } this.$emit.apply(this, params); break; case "event": var params = [packet.name].concat(packet.args); if (packet.ack == "data") { params.push(ack) } this.$emit.apply(this, params); break; case "ack": if (this.acks[packet.ackId]) { this.acks[packet.ackId].apply(this, packet.args); delete this.acks[packet.ackId] } break; case "error": if (packet.advice) { this.socket.onError(packet) } else {
                        if (packet.reason == "unauthorized") { this.$emit("connect_failed", packet.reason) } else {
                            this.$emit("error", packet.reason)
                        } 
                    } break
            } 
        }; function Flag(nsp, name) { this.namespace = nsp; this.name = name } Flag.prototype.send = function () { this.namespace.flags[this.name] = true; this.namespace.send.apply(this.namespace, arguments) }; Flag.prototype.emit = function () { this.namespace.flags[this.name] = true; this.namespace.emit.apply(this.namespace, arguments) } 
    })("undefined" != typeof io ? io : module.exports, "undefined" != typeof io ? io : module.parent.exports); (function (exports, io, global) { exports.websocket = WS; function WS(socket) { io.Transport.apply(this, arguments) } io.util.inherit(WS, io.Transport); WS.prototype.name = "websocket"; WS.prototype.open = function () { var query = io.util.query(this.socket.options.query), self = this, Socket; if (!Socket) { Socket = global.MozWebSocket || global.WebSocket } this.websocket = new Socket(this.prepareUrl() + query); this.websocket.onopen = function () { self.onOpen(); self.socket.setBuffer(false) }; this.websocket.onmessage = function (ev) { self.onData(ev.data) }; this.websocket.onclose = function () { self.onClose(); self.socket.setBuffer(true) }; this.websocket.onerror = function (e) { self.onError(e) }; return this }; if (io.util.ua.iDevice) { WS.prototype.send = function (data) { var self = this; setTimeout(function () { self.websocket.send(data) }, 0); return this } } else { WS.prototype.send = function (data) { this.websocket.send(data); return this } } WS.prototype.payload = function (arr) { for (var i = 0, l = arr.length; i < l; i++) { this.packet(arr[i]) } return this }; WS.prototype.close = function () { this.websocket.close(); return this }; WS.prototype.onError = function (e) { this.socket.onError(e) }; WS.prototype.scheme = function () { return this.socket.options.secure ? "wss" : "ws" }; WS.check = function () { return ("WebSocket" in global && !("__addTask" in WebSocket)) || "MozWebSocket" in global }; WS.xdomainCheck = function () { return true }; io.transports.push("websocket") })("undefined" != typeof io ? io.Transport : module.exports, "undefined" != typeof io ? io : module.parent.exports, this); (function (exports, io, global) { exports.XHR = XHR; function XHR(socket) { if (!socket) { return } io.Transport.apply(this, arguments); this.sendBuffer = [] } io.util.inherit(XHR, io.Transport); XHR.prototype.open = function () { this.socket.setBuffer(false); this.onOpen(); this.get(); this.setCloseTimeout(); return this }; XHR.prototype.payload = function (payload) { var msgs = []; for (var i = 0, l = payload.length; i < l; i++) { msgs.push(io.parser.encodePacket(payload[i])) } this.send(io.parser.encodePayload(msgs)) }; XHR.prototype.send = function (data) { this.post(data); return this }; function empty() { } XHR.prototype.post = function (data) { var self = this; this.socket.setBuffer(true); function stateChange() { if (this.readyState == 4) { this.onreadystatechange = empty; self.posting = false; if (this.status == 200) { self.socket.setBuffer(false) } else { self.onClose() } } } function onload() { this.onload = empty; self.socket.setBuffer(false) } this.sendXHR = this.request("POST"); if (global.XDomainRequest && this.sendXHR instanceof XDomainRequest) { this.sendXHR.onload = this.sendXHR.onerror = onload } else { this.sendXHR.onreadystatechange = stateChange } this.sendXHR.send(data) }; XHR.prototype.close = function () { this.onClose(); return this }; XHR.prototype.request = function (method) { var req = io.util.request(this.socket.isXDomain()), query = io.util.query(this.socket.options.query, "t=" + +new Date); req.open(method || "GET", this.prepareUrl() + query, true); if (method == "POST") { try { if (req.setRequestHeader) { req.setRequestHeader("Content-type", "text/plain;charset=UTF-8") } else { req.contentType = "text/plain" } } catch (e) { } } return req }; XHR.prototype.scheme = function () { return this.socket.options.secure ? "https" : "http" }; XHR.check = function (socket, xdomain) { try { var request = io.util.request(xdomain), usesXDomReq = (global.XDomainRequest && request instanceof XDomainRequest), socketProtocol = (socket && socket.options && socket.options.secure ? "https:" : "http:"), isXProtocol = (global.location && socketProtocol != global.location.protocol); if (request && !(usesXDomReq && isXProtocol)) { return true } } catch (e) { } return false }; XHR.xdomainCheck = function (socket) { return XHR.check(socket, true) } })("undefined" != typeof io ? io.Transport : module.exports, "undefined" != typeof io ? io : module.parent.exports, this); (function (exports, io) {
        exports.htmlfile = HTMLFile; function HTMLFile(socket) { io.Transport.XHR.apply(this, arguments) } io.util.inherit(HTMLFile, io.Transport.XHR); HTMLFile.prototype.name = "htmlfile"; HTMLFile.prototype.get = function () { this.doc = new window[(["Active"].concat("Object").join("X"))]("htmlfile"); this.doc.open(); this.doc.write("<html></html>"); this.doc.close(); this.doc.parentWindow.s = this; var iframeC = this.doc.createElement("div"); iframeC.className = "socketio"; this.doc.body.appendChild(iframeC); this.iframe = this.doc.createElement("iframe"); iframeC.appendChild(this.iframe); var self = this, query = io.util.query(this.socket.options.query, "t=" + +new Date); this.iframe.src = this.prepareUrl() + query; io.util.on(window, "unload", function () { self.destroy() }) }; HTMLFile.prototype._ = function (data, doc) { data = data.replace(/\\\//g, "/"); this.onData(data); try { var script = doc.getElementsByTagName("script")[0]; script.parentNode.removeChild(script) } catch (e) { } }; HTMLFile.prototype.destroy = function () {
            if (this.iframe) {
                try {
                    this.iframe.src = "about:blank"
                } catch (e) { } this.doc = null; this.iframe.parentNode.removeChild(this.iframe); this.iframe = null; CollectGarbage()
            } 
        }; HTMLFile.prototype.close = function () { this.destroy(); return io.Transport.XHR.prototype.close.call(this) }; HTMLFile.check = function (socket) { if (typeof window != "undefined" && (["Active"].concat("Object").join("X")) in window) { try { var a = new window[(["Active"].concat("Object").join("X"))]("htmlfile"); return a && io.Transport.XHR.check(socket) } catch (e) { } } return false }; HTMLFile.xdomainCheck = function () { return false }; io.transports.push("htmlfile")
    })("undefined" != typeof io ? io.Transport : module.exports, "undefined" != typeof io ? io : module.parent.exports); (function (exports, io, global) { exports["xhr-polling"] = XHRPolling; function XHRPolling() { io.Transport.XHR.apply(this, arguments) } io.util.inherit(XHRPolling, io.Transport.XHR); io.util.merge(XHRPolling, io.Transport.XHR); XHRPolling.prototype.name = "xhr-polling"; XHRPolling.prototype.heartbeats = function () { return false }; XHRPolling.prototype.open = function () { var self = this; io.Transport.XHR.prototype.open.call(self); return false }; function empty() { } XHRPolling.prototype.get = function () { if (!this.isOpen) { return } var self = this; function stateChange() { if (this.readyState == 4) { this.onreadystatechange = empty; if (this.status == 200) { self.onData(this.responseText); self.get() } else { self.onClose() } } } function onload() { this.onload = empty; this.onerror = empty; self.retryCounter = 1; self.onData(this.responseText); self.get() } function onerror() { self.retryCounter++; if (!self.retryCounter || self.retryCounter > 3) { self.onClose() } else { self.get() } } this.xhr = this.request(); if (global.XDomainRequest && this.xhr instanceof XDomainRequest) { this.xhr.onload = onload; this.xhr.onerror = onerror } else { this.xhr.onreadystatechange = stateChange } this.xhr.send(null) }; XHRPolling.prototype.onClose = function () { io.Transport.XHR.prototype.onClose.call(this); if (this.xhr) { this.xhr.onreadystatechange = this.xhr.onload = this.xhr.onerror = empty; try { this.xhr.abort() } catch (e) { } this.xhr = null } }; XHRPolling.prototype.ready = function (socket, fn) { var self = this; io.util.defer(function () { fn.call(self) }) }; io.transports.push("xhr-polling") })("undefined" != typeof io ? io.Transport : module.exports, "undefined" != typeof io ? io : module.parent.exports, this); (function (exports, io, global) { var indicator = global.document && "MozAppearance" in global.document.documentElement.style; exports["jsonp-polling"] = JSONPPolling; function JSONPPolling(socket) { io.Transport["xhr-polling"].apply(this, arguments); this.index = io.j.length; var self = this; io.j.push(function (msg) { self._(msg) }) } io.util.inherit(JSONPPolling, io.Transport["xhr-polling"]); JSONPPolling.prototype.name = "jsonp-polling"; JSONPPolling.prototype.post = function (data) { var self = this, query = io.util.query(this.socket.options.query, "t=" + (+new Date) + "&i=" + this.index); if (!this.form) { var form = document.createElement("form"), area = document.createElement("textarea"), id = this.iframeId = "socketio_iframe_" + this.index, iframe; form.className = "socketio"; form.style.position = "absolute"; form.style.top = "0px"; form.style.left = "0px"; form.style.display = "none"; form.target = id; form.method = "POST"; form.setAttribute("accept-charset", "utf-8"); area.name = "d"; form.appendChild(area); document.body.appendChild(form); this.form = form; this.area = area } this.form.action = this.prepareUrl() + query; function complete() { initIframe(); self.socket.setBuffer(false) } function initIframe() { if (self.iframe) { self.form.removeChild(self.iframe) } try { iframe = document.createElement('<iframe name="' + self.iframeId + '">') } catch (e) { iframe = document.createElement("iframe"); iframe.name = self.iframeId } iframe.id = self.iframeId; self.form.appendChild(iframe); self.iframe = iframe } initIframe(); this.area.value = io.JSON.stringify(data); try { this.form.submit() } catch (e) { } if (this.iframe.attachEvent) { iframe.onreadystatechange = function () { if (self.iframe.readyState == "complete") { complete() } } } else { this.iframe.onload = complete } this.socket.setBuffer(true) }; JSONPPolling.prototype.get = function () { var self = this, script = document.createElement("script"), query = io.util.query(this.socket.options.query, "t=" + (+new Date) + "&i=" + this.index); if (this.script) { this.script.parentNode.removeChild(this.script); this.script = null } script.async = true; script.src = this.prepareUrl() + query; script.onerror = function () { self.onClose() }; var insertAt = document.getElementsByTagName("script")[0]; insertAt.parentNode.insertBefore(script, insertAt); this.script = script; if (indicator) { setTimeout(function () { var iframe = document.createElement("iframe"); document.body.appendChild(iframe); document.body.removeChild(iframe) }, 100) } }; JSONPPolling.prototype._ = function (msg) { this.onData(msg); if (this.isOpen) { this.get() } return this }; JSONPPolling.prototype.ready = function (socket, fn) { var self = this; if (!indicator) { return fn.call(this) } io.util.load(function () { fn.call(self) }) }; JSONPPolling.check = function () { return "document" in global }; JSONPPolling.xdomainCheck = function () { return true }; io.transports.push("jsonp-polling") })("undefined" != typeof io ? io.Transport : module.exports, "undefined" != typeof io ? io : module.parent.exports, this); if (typeof define === "function" && define.amd) {
        define([], function () {
            return io
        })
    } 
})();
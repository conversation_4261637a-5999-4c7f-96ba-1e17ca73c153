<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>播放视频</title>
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/plugin/fastclick/fastclick.js"></script>
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <script src="../sys/system.js" type="text/javascript"></script>
    <style>
        body {
            background: #f3f3f7;
            text-align: center;
        }
        .out-container {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
            overflow: scroll;
            -webkit-overflow-scrolling: touch;
        }
    </style>
</head>
<body style="margin: 0">
    <section class="out-container">
        <div style="text-align: center" id="divMedia">

        </div>
        <canvas id="canvas" style="position: absolute;width: 100%;"></canvas>
    </section>
    <script>
        $(function () {
            var objMedia = parent.objdata.curMedia;
            $("#divMedia").html('<video width="100%" style="max-height: 50%" src="' + ossPrefix + objMedia.videourl + '" controls crossorigin="anonymous"><source type="video/mp4">您的浏览器不支持Video标签。</video>');
            setTimeout(function () {
                $("video")[0].play();
                /*$("video")[0].addEventListener('play', function () {
                    var video = this;
                    var canvas = document.getElementById("canvas");
                    var ctx = canvas.getContext('2d');//类型是2d
                    var winw = $(window).width();
                    canvas.width = winw;//画布的宽度等于视频屏幕的宽度
                    canvas.height = video.videoHeight*canvas.width/video.videoWidth;
                    console.log(canvas.width,canvas.height)
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);//在画布上画画
                    var images = canvas.toDataURL('image/png');
                    $("#img").prop('src', images)
                })*/
            }, 1000);
        })

    </script>
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/wxcommon/weui/jquery-weui.min.js"></script>
    <!--<script data-main="playVideo" src="http://ptlib.tb-n.com/lib/sys/require.min.js"></script>-->
</body>
</html>



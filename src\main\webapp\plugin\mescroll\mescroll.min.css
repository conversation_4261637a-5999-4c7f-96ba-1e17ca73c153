/*! mescroll -- 精致的下拉刷新和上拉加载js框架  ( a great JS framework for pull-refresh and pull-up-loading )
 * version 1.3.5
 * 2018-08-20
 * author: wenju < <EMAIL> > 文举
 * *
 * 官网:  http://www.mescroll.com
 * 动态:  https://github.com/mescroll/mescroll/releases
 * 问答:  http://www.mescroll.com/qa.html
 * issues:  https://github.com/mescroll/mescroll/issues
 * QQ交流群: 633126761
 *
 * 温馨提示: mescroll唯一的全局样式: html,body{height:100%},用于固定body的高度满屏; 如果影响到您原本的项目样式,可自行删除
 *
 *
 * ----- mescroll的html结构解析 ----
 *
 <body>
   <div id="mescroll" class="mescroll">
    <div>

      //下拉刷新区域 ( mescroll初始化之后自动创建 )
      <div class="mescroll-downwarp">
        <div class="downwarp-content">
          <p class="downwarp-progress"></p> <p class="downwarp-tip">下拉刷新 </p>
        </div>
      </div>

      //界面的具体内容
      //<div>界面内容</div>
      //数据列表..
      //<ul id="dataList" class="data-list">
      //	<li>数据列表</li>

        //空布局 ( 列表无任何数据时, 且配置了warpId时, 会自动创建显示 )
        <div class="mescroll-empty">
          <img class="empty-icon" src="../img/mescroll-empty.png"/>
          <p class="empty-tip">暂无相关数据~</p>
           <p class="empty-btn">去逛逛 ></p>
        </div>

      //</ul>

      //上拉加载区域 ( mescroll初始化之后自动创建 )
      <div class="mescroll-upwarp">
        //加载中..
        <p class="upwarp-progress mescroll-rotate"></p><p class="upwarp-tip">加载中..</p>
        //无数据
        <p class="upwarp-nodata">-- 没有更多数据了 --</p>
      </div>

    </div>
	</div>

	//回到顶部按钮 ( 列表滚动到配置的距离时, 且配置了warpId时, 会自动创建显示, 注意是添加在body中的 )
	<img class="mescroll-totop" src="../img/mescroll-totop.png"/>
</body>
 *
 */

html,body{height:100%}body{-webkit-overflow-scrolling:touch}.mescroll{width:100%;height:100%;overflow-y:auto}.mescroll-hardware{-webkit-transform:translateZ(0);-webkit-transform-style:preserve-3d;-webkit-backface-visibility:hidden;-webkit-perspective:1000}.mescroll-downwarp{position:relative;width:100%;height:0;overflow:hidden;text-align:center}.mescroll-downwarp-reset{-webkit-transition:height 300ms;transition:height 300ms}.mescroll-downwarp .downwarp-content{position:absolute;left:0;bottom:0;width:100%;min-height:30px;padding:10px 0}.mescroll-upwarp{min-height:30px;padding:15px 0;text-align:center;visibility:hidden}.mescroll-downwarp .downwarp-tip,.mescroll-upwarp .upwarp-tip,.mescroll-upwarp .upwarp-nodata{display:inline-block;font-size:12px;color:gray;vertical-align:middle}.mescroll-downwarp .downwarp-tip,.mescroll-upwarp .upwarp-tip{margin-left:8px}.mescroll-downwarp .downwarp-progress,.mescroll-upwarp .upwarp-progress{display:inline-block;width:16px;height:16px;border-radius:50%;border:1px solid gray;border-bottom-color:transparent;vertical-align:middle}.mescroll-rotate{-webkit-animation:mescrollRotate .6s linear infinite;animation:mescrollRotate .6s linear infinite}@-webkit-keyframes mescrollRotate{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes mescrollRotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.mescroll-empty{width:100%;padding-top:20px;text-align:center}.mescroll-empty .empty-icon{width:45%}.mescroll-empty .empty-tip{margin-top:6px;font-size:14px;color:gray}.mescroll-empty .empty-btn{max-width:50%;margin:20px auto;padding:10px;border:1px solid #65aadd;border-radius:6px;background-color:white;color:#65aadd}.mescroll-empty .empty-btn:active{opacity:.75}.mescroll-totop{z-index:9990;position:fixed;right:10px;bottom:30px;width:36px;height:36px;border-radius:50%;opacity:0}.mescroll-fade-in{-webkit-animation:mescrollFadeIn .5s linear forwards;animation:mescrollFadeIn .5s linear forwards}@-webkit-keyframes mescrollFadeIn{0%{opacity:0}100%{opacity:1}}@keyframes mescrollFadeIn{0%{opacity:0}100%{opacity:1}}.mescroll-fade-out{pointer-events:none;-webkit-animation:mescrollFadeOut .5s linear forwards;animation:mescrollFadeOut .5s linear forwards}@-webkit-keyframes mescrollFadeOut{0%{opacity:1}100%{opacity:0}}@keyframes mescrollFadeOut{0%{opacity:1}100%{opacity:0}}.mescroll-bar::-webkit-scrollbar-track{background-color:transparent}.mescroll-bar::-webkit-scrollbar{width:6px}.mescroll-bar::-webkit-scrollbar-thumb{border-radius:6px;background-color:#ccc}.mescroll-bar::-webkit-scrollbar-thumb:hover{background-color:#aaa}
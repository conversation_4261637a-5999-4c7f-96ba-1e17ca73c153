/*
 新增日期: 2017.12.16
 內容摘要: 添加宝宝
 */
var objdata = {
    isios: navigator.userAgent.indexOf("iPhone") > -1
};
require.config({
    paths: {
        system: '../../sys/system_zepto',
        commonwx: '../commonwx'//通用方法
    },
    waitSeconds: 0
});
require(['system', 'commonwx'], function (system, wxtool) {
    window.wxtool = wxtool;
    wxtool.init(wx, 'wxhome');
    var _uploaderFiles = $("#uploaderFiles");
    //选择图片
    $("#uploaderInput").click(function () {
        wxtool.chooseImage({
            count: 8,
            sizeType: ['original', 'compressed'],
            success: function (arrloacalid, objImgData) {//objImgData 苹果手机src问题;
                var strhtml = '';
                for (var i = 0; i < arrloacalid.length; i++) {
                    var localId = arrloacalid[i];
                    var src = localId;
                    if (objImgData && objImgData[localId]) {
                        src = objImgData[localId];
                    }
                    strhtml += '<li class="weui-uploader__file" localid="' + localId + '" style="background-image:url(' + src + ')"></li>';
                }
                _uploaderFiles.append(strhtml);
            }, fail: function (error, objerror) {
                alert(error);
            }
        });
    })
    // 找到DOM里file-content，若无，则插入一个。
    function findFileCtn(type, id) {
        var $file = _uploaderFiles.find('[' + type + '="' + id + '"]');
        var $fileCtn = $file.find('.weui-uploader__file-content');
        if (!$fileCtn.length) {
            $fileCtn = $('<div class="weui-uploader__file-content"></div>');
            $file.append($fileCtn);
        }
        $file.addClass('weui-uploader__file_status');
        return $fileCtn;
    }

    function clearFileStatus(type, id) {
        var $file = _uploaderFiles.find('[' + type + '="' + id + '"]').removeClass('weui-uploader__file_status');
        $file.find('.weui-uploader__file-content').remove();
    }

    //上传
    $("#btnupload").click(function () {
        var arrlocalid = [];
        $("#uploaderFiles").children().each(function () {
            var _this = $(this);
            var localId = _this.attr('localid');
            findFileCtn("localid", localId).html('<i class=weui-loading style=width:30px;height:30px></i>');
            if (localId) {
                arrlocalid.push(localId)
            }
        })
        if (arrlocalid.length > 0) {
            wxtool.uploadLocalImage(arrlocalid, {
                startOne: function (index) {

                },
                completeOne: function (issucc, index, localid, serverId) {
                    $("#uploaderFiles").find("[localid='" + localid + "']").attr("serverid", serverId);
                },
                complete: function (arrServerId) {

                    $.sm(function (re, err, obj) {
                        if (err) {
                            alert("图片上传阿里云失败");
                        } else {
                            var threadid = obj.id;
                            objdata.inter = setInterval(function () {
                                $.sm(function (re, err, obj) {
                                    if (err) {
                                        clearInterval(objdata.inter);
                                    } else {
                                        for (var serverid in obj.objprogress) {
                                            if (obj.objprogress[serverid] != 100) {
                                                findFileCtn("serverid", serverid).html(obj.objprogress[serverid] + '%');
                                            } else {
                                                clearFileStatus("serverid", serverid);
                                            }
                                        }
                                        if (obj.total == obj.success) {
                                            $.hideLoading();
                                            clearInterval(objdata.inter);
                                        }
                                    }
                                }, ["weixin.uploadimg.WxToOssGetProsess", threadid]);
                            }, 1000);
                        }
                    }, ["weixin.uploadimg.WxToOss", "wxhome", JSON.stringify(arrServerId), 'test222', 'noneed']);
                }
            });
        }
    })
});
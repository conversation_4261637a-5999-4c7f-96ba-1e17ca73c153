* {
    box-sizing: inherit;
    -moz-box-sizing: inherit;
    -webkit-box-sizing: inherit;
}
/*开始核验*/
.obj-title{font-size: 16px;color: #008ff3;background: #dbeefb;border-left: 3px solid #00aaf3;padding: 11px 10px;font-weight: bold;}	
.verify-cell{font-size: 14px;color: #333333;border-bottom: 1px solid #dddddd;padding-bottom: 12px;}	
.verify-cell h6{font-size: 16px;color: #333333;margin: 12px 15px 5px 15px;}		
.verify-cell:last-of-type{border-bottom: none;}
.verify-txt{background: #fbfbe6;font-size: 12px;color: #666666;margin: 5px 15px 7px 15px;padding: 5px 8px;text-align: justify;}
.verify-form{margin: 0 15px;}
.verify-form textarea{background: #f7f7f7;height: 60px;border-radius: 5px;border: none;padding: 5px 10px;}
.set-radio-style3 .red-radio input[type="radio"]:checked{border: 1px solid red;}
.set-radio-style3 .red-radio input[type="radio"]:checked:before{background: red;}
.tishi-label{position: absolute;background: #ffdfd9;color: #df382b;font-size: 12px;margin: 0 25px 0 40px;bottom: 66px;padding: 5px 10px;}
.tishi-label .triangle-out-bottom{bottom: -11px;left: auto;right: 12px;border-right-color: #ffdfd9;}
/*核检结果*/
.progressbar_1{ 
	background-color:#eeeeee; 
    height: 5px; 
    width:85%;    
    color:#222222; 
	border-radius:10px;
	margin-top: 3px;
} 
.progressbar_1 .bar { 
    background-color:#ed4f4c; 
    height:5px; 
	border-radius:10px;
	position: relative;
} 
.gradual-red{
	
}
.verify-state{height: 199px;background: url(images/verify/verify_bg2.png)no-repeat;background-size: 100% 200px;}
.btn-state{color: #ffffff;font-size: 12px;padding: 3px 14px 1px 14px;border-radius: 20px 20px 20px 0;line-height: 19px;
	background: -webkit-linear-gradient(left,#fb805f,#ee6464); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left,#fb805f,#ee6464); /* Opera 11.1-12.0 */ 
	background: -moz-linear-gradient(left,#fb805f,#ee6464); /* Firefox 3.6-15 */
	background: linear-gradient(left,#fb805f,#ee6464); /* 标准语法 */
}
.verify-con{font-size: 12px;color: #666666;border-top: 1px solid #dddddd;padding: 10px 0;line-height: 24px;}
.state-txt{color: #ed4f4c;}	
.success-state{color: #ed4f4c;}			
.success-state .state-txt{color: #0fd69c;}
.success-state .progressbar_1 .bar{background: #0fd69c;}
.success-state .btn-state{
	background: -webkit-linear-gradient(left,#32eab4,#01cd91); /* Safari 5.1-6.0 */
	background: -o-linear-gradient(left,#32eab4,#01cd91); /* Opera 11.1-12.0 */ 
	background: -moz-linear-gradient(left,#32eab4,#01cd91); /* Firefox 3.6-15 */
	background: linear-gradient(left,#32eab4,#01cd91); /* 标准语法 */
}
/*详情*/
.verify-sub{color: #ffffff;padding: 15px 18px;font-size: 13px;line-height: 24px;height: 140px;}
.verify-sub h5{font-size: 17px;border-bottom: 3px solid #fff016;display: inline-block;line-height: 20px;margin: 10px 0;}	
.verify-sub p{position: relative;padding-left: 10px;}
.verify-sub p:before{content: "";width: 3px;height: 3px;border-radius: 50%;background: #fff016;position: absolute;left: 0;top: 10px;}
.result-title{align-items: center;font-size: 16px;color: #474747;font-weight: bold;margin-bottom: 15px;}
/*达标状态*/
.tab-common6{height: 45px;padding: 0;border-radius: 14px 14px 0 0;margin-top: -14px;font-size: 14px;border-bottom: 1px solid #dddddd;}
.tab-common6 li{font-size: 13px;}
.tab-common6 li.current{color: #00cd91;font-weight: bold;}
.tab-common6 li:before{content: "";width: 1px;height: 40%;top: 36%;background: #eeeeee;position: absolute;right: 0;}
.tab-common6 li:last-child:before{content: none;}
.tab-common6 li.current:after{margin-left: -30px;width: 60px;}
.verify-wrapouter{height: 257px;background: url(images/verify/verify_bg3.png) no-repeat;background-size: 100% 257px;position: relative;}
.stat-item-num{position: relative;color: #ffffff;width: 134px;height: 134px;border-radius: 50%;background: #4dadf9;margin-top: -66px; }
.stat-mess{font-size: 10px;text-align: center}	
.verify-list{align-items: center;justify-content: space-around;text-align: center;margin: 0 15px;background: #ffffff;font-size: 12px;color: #666666;padding: 10px 10px 4px 10px;flex-direction: column;height: 126px;border-radius: 2px;box-shadow: 0 7px 12px -3px #e8e8e8;}				
.state-list{padding: 2px 15px;border-bottom: 1px solid #dddddd;}
.state-list h5{font-size: 16px;color: #474747;}
.verify-label{font-size: 11px;color: #ffffff;padding: 0 3px;margin-right: 5px;}
.verify-redcon{background: #fbeeee;margin-right: 3px;}
.verify-greencon{background: #e9f8f4;margin-left: 3px;}
.verify-lists{flex-direction: column;height: 58px;line-height: 24px;position: relative;font-size: 11px;}
.verify-lists span{font-size: 22px;font-weight: bold;}
.verify-lists:after{content: "";height: 16px;position: absolute;right: 0;bottom: 9px;}
.verify-redcon .verify-lists:after{border-right: 1px solid #f2dbdb;}
.verify-greencon .verify-lists:after{border-right: 1px solid #d7efe8;}
.verify-lists:last-child:after{border-right: none;}
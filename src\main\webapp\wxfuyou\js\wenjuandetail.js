var objnotices = {};
var objdata = {};
require.config({
    paths:{
        system:'../../sys/system_zepto',
        moment:'../../plugin/js/moment.min',
        common:'../../wxcommon/common',
        commonwx:'../../wxcommon/commonwx',
        commonhome:'../../wxcommon/commonhome',
        mescroll:'../../plugin/mescroll/mescroll'
    },
    waitSeconds:0
});
require(['system','moment','commonwx','mescroll','common','commonhome'],function (s,moment,wxtool,MeScroll) {
    getMyInfo(function (objinfo) {
        if (!objinfo || !objinfo.objyey) {
            return
        }
        window.objinfo = objinfo;
        var stuno = objinfo.objbaby.stuno;
        var id = Arg("id");
        $.sm(function (objwenjuan,err) {
            if (err) {
                return;
            }
            if (!objwenjuan.length) {
                $(".out-container").html('<div style="text-align: center;margin: 150px auto;">此次调查问卷已删除</div>').show();
                return;
            }
            window.objwenjuan = objwenjuan;
            $("#wtitle").text(objwenjuan.wtitle)
            $("#introduce").html(objwenjuan.introduce.replace(/\s/g,'&nbsp;'))
            if(objwenjuan.isniming){
                $("#wenjuandatajiamifanghu").click(function () {
                    parent.openwin('wenjuandatajiamifanghu','wenjuandatajiamifanghu.html?v=' + Arg("v"),"数据加密保护")
                })
            }
            //没有落款时隐藏
            objwenjuan.luokuan?$("#luokuan").text(objwenjuan.luokuan).parent().show():$("#luokuan").parent().hide();
            parent.document.title = objwenjuan.wtitle;
            if (!objwenjuan.isread) {
                //修改为已读
                $.sm(function (re,err) {
                    if (err) parent.layer.msg('修改已读失败');
                },["wxhome.wenjuan.read",id,objinfo.curbid,objinfo.objbaby.ptype,objinfo.openid,'',objinfo.objbaby.stuname])
            }
            if (objwenjuan.iscommit) {//提交了 显示详情按钮
                var strusetime = "用时：" + getStrChaTime(objwenjuan.readtime,objwenjuan.committime);
                $("#strusetime").text(strusetime);
                $("#strcommittime").text(objwenjuan.committime.substring(0,16));
                if (objwenjuan.writeroletype == 1) {
                    $("#strfromname").text(objwenjuan.writename + commonptype[objwenjuan.writeptype || 2]);
                } else {
                    $("#strfromname").text(objwenjuan.writename);
                }

                $("#divcommit").show();

                $("#btncommit").hide();
                $("#btnclose").show();
            } else {//没提交 显示提交按钮
                $("#divcommit").hide();

                $("#btncommit").show();
                $("#btnclose").hide();
            }
            var strwjtime = '';
            var istart = true;
            if (objwenjuan.starttime && objwenjuan.endtime) {
                var strflag = "";
                if (getTimeByStr(objwenjuan.starttime) > new Date()) {
                    strflag = '<span style="color: red">(未开始)</span>';
                    istart = false;
                } else if (getTimeByStr(objwenjuan.endtime) < new Date()) {
                    strflag = '<span style="color: red">(已结束)</span>';
                }
                strwjtime =
                    '<p class="ptime">\
                        <span class="weui-flex_center wjicon"><img src="images/icon_time.png" style="width: 14px;"></span>\
                        ' + objwenjuan.starttime.substring(0,16) + '到' + objwenjuan.endtime.substring(0,16) + strflag + '\
                    </p>';
            } else if (objwenjuan.starttime) {
                var strflag = "";
                if (getTimeByStr(objwenjuan.starttime) > new Date()) {
                    strflag = '<span style="color: red">(未开始)</span>';
                    istart = false;
                }
                strwjtime =
                    '<p class="ptime">\
                        <span class="weui-flex_center wjicon"><img src="images/icon_time.png" style="width: 14px;"></span>\
                        ' + objwenjuan.starttime.substring(0,16) + '开始' + strflag + '\
                    </p>';
            } else if (objwenjuan.endtime) {
                var strflag = "";
                if (getTimeByStr(objwenjuan.endtime) < new Date()) {
                    strflag = '<span style="color: red">(已结束)</span>';
                }
                strwjtime =
                    '<p class="ptime">\
                        <span class="weui-flex_center wjicon"><img src="images/icon_time.png" style="width: 14px;"></span>\
                        ' + objwenjuan.endtime.substring(0,16) + '结束' + strflag + '\
                    </p>';
            }
            $("#strtime").replaceWith(strwjtime)
            //加载问题
            initWenti(id,stuno);
            if(!istart){
                $("#btncommit").hide();
            }
        },['weixin.wj.getData',JSON.stringify({
            roletype:"home",
            sendOrReceive:'onedetail',
            id:id,
            stuno:stuno
        })]);
    });
    //点击星星
    $("#divcontent").on('click','.icon_star',function () {
        var index = $(this).index();
        var _parent = $(this).parent();
        _parent.data('starnum',index + 1);
        _parent.children().each(function (i) {
            if (index >= i) {
                $(this).addClass('current');
            } else {
                $(this).removeClass('current');
            }
        })
    })
    //提交
    $("#btncommit").click(function () {
        var arr = checkWenTi(true);
        if (!arr) return;
        console.log(arr)
        //修改为已提交 并且保存问卷结果
        var objuser = {
            stuno:objinfo.curbid,
            stuname:objinfo.objbaby.stuname,
            ptype:objinfo.objbaby.ptype,
            openid:objinfo.openid
        };
        $.showLoading("提交中，请稍候");
        $.sm(function (re,err) {
            $.hideLoading();
            if(err){
                return parent.layer.msg('提交异常:' + err);
            }
            if (re) {
                parent.layer.msg("提交成功");
                parent.getprevwin() && parent.getprevwin().location.reload();
                history.back();
            }
        },['wxhome.wj.commit',objwenjuan.id,JSON.stringify(objuser),JSON.stringify(arr)])//fromid为null
    })
    //关闭详情
    $("#btnclose").click(function () {
        history.back();
    })
});
function checkWenTi(iserrorstop) {
    var arrdaan = [];
    var childs = $("#divcontent").children();
    for (var i = 0; i < childs.length; i++) {//循环大题
        var sort = i + 1;
        var sort1 = 0;
        var _oneouter = childs.eq(i);
        var hastype = _oneouter.data("hastype");
        var childlength = 0;
        var wentifenlei = "";
        if (!hastype) {//适配有外层分类和无外出分类
            childlength = 1;
        } else {
            wentifenlei = _oneouter.children('h5').find('input').val();
            childlength = _oneouter.children('.divsonwenti').children().length;
        }
        for (var j = 0; j < childlength; j++) {//循环题目
            sort1 = j + 1;
            //适配有外层分类和无外出分类
            var _onequestion = null;
            if (!hastype) {
                _onequestion = _oneouter;
            } else {
                _onequestion = _oneouter.children('.divsonwenti').children().eq(j);
            }
            //开始获取问题
            var curqtype = _onequestion.data("type");
            var wtid = _onequestion.data("id");
            var objset = _onequestion.data("objset") || {};
            var wtxuhao = sort + (hastype?"." + sort1:"");
            var teacherlength = _onequestion.children('.preview-cell').length;
            var objonewenti = {
                id:wtid,
                diaochatype:objset.diaochatype,
                qtype:curqtype,
                arrteacher:[]
            };
            for (var k = 0; k < teacherlength; k++) {//循环老师
                var _oneteacherquestion = _onequestion.children('.preview-cell').eq(k);
                var objoneteacher = {
                    tid:_oneteacherquestion.data('tid'),
                    teachername:_oneteacherquestion.data('teachername')
                };
                //获取问题设置
                if (curqtype == 'select') {//
                    _oneteacherquestion.find('ul').children().each(function () {
                        var _radio = $(this).find("input[type='radio']");
                        if (_radio.prop('checked')) {
                            objoneteacher.re = {
                                wentiitem:$(this).find('.optionname').text()
                            };
                            if (_radio.data('cantiankong')) {
                                objoneteacher.re.wentiresult = _radio.siblings().children('input').val();
                            }
                        }
                    })
                    if (iserrorstop && objset.ismust && !objoneteacher.re) {
                        parent.layer.msg(wtxuhao + "题需要作答哦");
                        return false
                    }
                } else if (curqtype == 'multi') {//zuishaoxuanxiang zuiduoxuanxiang
                    var arrchecked = [];
                    _oneteacherquestion.find('ul').children().each(function () {
                        var _checkbox = $(this).find("input[type='checkbox']");
                        if (_checkbox.prop('checked')) {
                            var obj = {
                                wentiitem:$(this).find('.optionname').text()
                            };
                            if (_checkbox.data('cantiankong')) {
                                obj.wentiresult = _checkbox.siblings().children('input').val();
                            }
                            arrchecked.push(obj);
                        }
                    })
                    if (iserrorstop && objset.ismust && arrchecked.length < objset.zuishaoxuanxiang) {
                        parent.layer.msg(wtxuhao + "题最少选择" + objset.zuishaoxuanxiang + "哦");
                        return false;
                    }
                    if (iserrorstop && objset.ismust && objset.zuiduoxuanxiang != -1 && arrchecked.length > objset.zuiduoxuanxiang) {
                        parent.layer.msg(wtxuhao + "题最多选择" + objset.zuiduoxuanxiang + "哦");
                        return false;
                    }
                    if (arrchecked.length) {
                        objoneteacher.re = arrchecked;
                    }
                } else if (curqtype == 'question') {
                    //objonewenti.inputtype = objset.inputtype || '多行文本';
                    var val = _oneteacherquestion.find('textarea').val();
                    if (iserrorstop && objset.ismust) {
                        if (!val) {
                            parent.layer.msg(wtxuhao + "题需要作答哦");
                            return false
                        } else {//验证格式 var arr = ['单行文字','多行文本','整数','小数','手机号','邮箱','省份城市','省市区','身份证号'];
                            if (objset.inputtype == '整数') {

                            } else if (objset.inputtype == '小数') {

                            } else if (objset.inputtype == '手机号') {

                            } else if (objset.inputtype == '邮箱') {

                            } else if (objset.inputtype == '身份证号') {

                            }
                        }
                    }
                    objoneteacher.re = {
                        wentiresult:val
                    };
                } else if (curqtype == 'star') {
                    var starnum = 0;
                    _oneteacherquestion.find('.star-rank').children().each(function () {
                        if ($(this).hasClass('current')) {
                            starnum += 1;
                        }
                    })
                    if (iserrorstop && objset.ismust) {
                        if (!starnum) {
                            parent.layer.msg(wtxuhao + "题需要作答哦");
                            return false
                        }
                    }
                    objoneteacher.re = {
                        wentiitem:starnum
                    };
                }
                objonewenti.arrteacher.push(objoneteacher);
            }
            arrdaan.push(objonewenti);
        }
    }
    return arrdaan;
}
function initWenti(id,stuno) {
    var _content = $("#divcontent").html('');
    _content.on('change','input',function () {
        if ($(this).prop('checked') && $(this).data('cantiankong')) {
            $(this).siblings().children('input').show();
        } else {
            $(this).siblings().children('input').hide();
        }
    })
    $.sm(function (res) {
        var arrwenti = res[0];
        var arrdaan = res[1];
        objdata.arrdaan = arrdaan;
        objdata.arrteacher = res[2];
        var arrouter = [];
        for (var i = 0; i < arrwenti.length; i++) {
            var onewenti = arrwenti[i];
            if (onewenti.wentifenlei && onewenti.sort1) {//子问题
                if (!arrouter[onewenti.sort]) {//分类下第一个问题
                    arrouter[onewenti.sort] = {
                        typename:onewenti.wentifenlei,
                        arrwenti:[onewenti]
                    };
                } else {//分类下非第一个问题
                    arrouter[onewenti.sort].arrwenti.push(onewenti);
                }
            } else {
                arrouter[onewenti.sort] = onewenti;
            }
        }
        for (var i = 1; i < arrouter.length; i++) {
            if (!arrouter[i].typename) {//单个问题
                addonewenti(_content,arrouter[i].wentitype,arrouter[i]);
            } else {//类型问题
                var _typeelem = addtypewenti(_content,i,arrouter[i].typename);
                var _soncontent = _typeelem.find('.divsonwenti');
                for (var j = 0; j < arrouter[i].arrwenti.length; j++) {
                    addonewenti(_soncontent,arrouter[i].arrwenti[j].wentitype,arrouter[i].arrwenti[j],true);
                }
            }
        }
    },[['wxyey.wj.getonewenjuanwenti',id],['wxyey.wj.getonestudaan',id,stuno],["wxhome.getstuteacher",stuno]])
}
function addtypewenti(_content,sort,typename) {
    var strhtml =
        '<div data-sort="' + sort + '" class="question-outer" data-hastype="true">\
            <h5 style="color: #ec5f6d;"><span><label>' + sort + '.</label>' + typename + '</span></h5>\
            <div class="divsonwenti"></div>\
        </div>';
    var _div = $(strhtml)
    _content.append(_div);
    return _div;
}
function addonewenti(_content,type,objwt,isinner) {
    var mainclass = isinner?'question-inner':'question-outer';
    var xuhao = 0;
    if (isinner) {
        xuhao = objwt.sort + "." + objwt.sort1;
    } else {
        xuhao = objwt.sort;
    }
    var arrteacher = [];
    if (objwenjuan.diaochatype == 'teacher' && objwt.diaochatype == 'teacher') {
        arrteacher = objdata.arrteacher;
    } else {
        arrteacher = [{
            id:0
        }];
    }
    var strteacherhtml = '';
    for (var i = 0; i < arrteacher.length; i++) {
        strteacherhtml +=
            '<div class="preview-cell" data-tid="' + arrteacher[i].id + '" data-teachername="' + arrteacher[i].name + '">';
        if (arrteacher[i].name) {
            strteacherhtml +=
                '<div class="weui-flex" style="margin: 10px 20px 0 20px;">\
                    <span class="name-label weui-flex_center"><img src="images/icon_teacher.png" style="width: 14px;height: 14px;margin-right: 5px;"><span class="tname">' + arrteacher[i].name + '</span></span>\
                </div>';
        }
        //获取答案
        if (objwt.id && objdata.arrdaan) {// && arrteacher[i].id
            objwt.arrdanan = [];
            for (var j = 0; j < objdata.arrdaan.length; j++) {
                if (objdata.arrdaan[j].toid == arrteacher[i].id && objdata.arrdaan[j].wentiid == objwt.id) {
                    objwt.arrdanan.push(objdata.arrdaan[j]);
                }
            }
        }
        if (type == 'select') {
            var arr = objwt.arritem;
            if (!objwt.arritem && objwt.wentiitem) {
                arr = JSON.parse(objwt.wentiitem);
            }
            var stroption = '';
            for (var j = 0; j < arr.length; j++) {
                if (objwt.arrdanan) {
                    for (var k = 0; k < objwt.arrdanan.length; k++) {
                        if (objwt.arrdanan[k].wentiitem == arr[j].name) {
                            arr[j].ischecked = true;
                            arr[j].wentiresult = objwt.arrdanan[k].wentiresult;
                            break;
                        }
                    }
                }
                stroption += getoneselectstr(arr[j],objwt.id,i);
            }
            strteacherhtml +=
                '<ul class="set-radio-green">\
                    ' + stroption + '\
                </ul>';
        } else if (type == 'multi') {
            var arr = objwt.arritem;
            var stroption = '';
            if (!objwt.arritem && objwt.wentiitem) {
                arr = JSON.parse(objwt.wentiitem);
            }
            for (var j = 0; j < arr.length; j++) {
                if (objwt.arrdanan) {
                    for (var k = 0; k < objwt.arrdanan.length; k++) {
                        if (objwt.arrdanan[k].wentiitem == arr[j].name) {
                            arr[j].ischecked = true;
                            arr[j].wentiresult = objwt.arrdanan[k].wentiresult;
                            break;
                        }
                    }
                }
                stroption += getonemulstr(arr[j],objwt.id);
            }
            strteacherhtml +=
                '<ul class="set-checkbox-greencheck">\
                    ' + stroption + '\
                </ul>';
        } else if (type == 'question') {
            var strvalue = "";
            if (objwt.arrdanan && objwt.arrdanan.length) {
                strvalue = objwt.arrdanan[0].wentiresult
            }
            var strinput = '';
            if (objwt.inputtype == '多行文本') {
                strinput = '<textarea placeholder="请输入内容">' + strvalue + '</textarea>';
            } else if (objwt.inputtype == '单行文本') {
                strinput = '<input placeholder="请输入内容" value="' + strvalue + '"></input>';
            } else {
                strinput = '<input placeholder="请输入' + objwt.inputtype + '" value="' + strvalue + '"></input>';
            }
            strteacherhtml += '<div class="questicon-textarea">' + strinput + '</div>';
        } else if (type == 'star') {
            var intval = 0;
            if (objwt.arrdanan && objwt.arrdanan.length) {
                intval = (objwt.arrdanan[0].wentiitem || 0) * 1;
            }
            var strstarhtml = '';
            for (var j = 0; j < (objwt.starnum||5); j++) {
                strstarhtml += '<i class="iconfont icon_star' + (j < intval?' current':'') + '"></i>';
            }
            strteacherhtml +=
                '<div class="star-rank weui-flex">\
                    ' + strstarhtml + '\
                </div>';
        }
        strteacherhtml += '</div>';
    }
    var strnote = '';
    var objset = {
        ismust:objwt.ismust,
        diaochatype:objwt.diaochatype
    };
    if (type == 'select') {
        strnote = '';
    } else if (type == 'multi') {
        objset.zuishaoxuanxiang = objwt.zuishaoxuanxiang || 1;
        objset.zuiduoxuanxiang = objwt.zuiduoxuanxiang || -1;
        if (objwt.zuiduoxuanxiang == 1) {
            strnote = '多选题';
        } else {
            strnote = '至少选' + objwt.zuishaoxuanxiang + "项";
        }
    } else if (type == 'question') {
        objset.inputtype = objwt.inputtype;
        strnote = objwt.inputtype == '多行文本'?'问答题':objwt.inputtype;
    } else if (type == 'star') {
        strnote = '评星题';
    }
    var str =
        '<div class="' + mainclass + ' onequestion" data-id="' + objwt.id + '" data-type="' + type + '">\
            <h5>\
                <span>' + (objwt.ismust?'<span style="color: red">*</span>':'') + '<label>' + xuhao + (isinner?'':'.') + '</label>' + objwt.wentititle + '</span>\
                 '+ (strnote?'<span style="font-size: 15px;color: #999999;">（' + strnote + '）</span>':'') +'\
            </h5>\
            ' + strteacherhtml + '\
        </div>';
    var _dom = $(str);
    _dom.data('objset',objset);
    _content.append(_dom);
}
function getoneselectstr(obj,wtid,index) {
    var oneselectstr =
        '<li>\
            <span class="weui-flex_center">\
                <label>\
                    <input type="radio"' + (obj.ischecked?' checked="checked"':'') + ' name="radio' + wtid + "_" + index + '" data-cantiankong="' + (obj.cantiankong || "") + '">\
                    <span class="optionname">' + obj.name + (obj.cantiankong?'<input type="text" style="border-bottom: 1px solid #cccccc;margin: 0 5px;'+ (obj.wentiresult?'':'display: none') +'" value="'+ (obj.wentiresult||'') +'">':'') + '</span>\
                </label>\
                ' + (obj.url?'<img src="' + (ossPrefix + obj.url) + '" style="width: 200px;margin: 5px 0 5px 20px;">':'') + '\
            </span>\
        </li>';
    return oneselectstr;
}
function getonemulstr(obj,wtid) {
    var oneselectstr =
        '<li>\
            <span class="weui-flex_center">\
                <label>\
                    <input type="checkbox"' + (obj.ischecked?' checked="checked"':'') + ' name="checkbox' + wtid + '" data-cantiankong="' + (obj.cantiankong || "") + '">\
                    <span class="optionname">' + obj.name + (obj.cantiankong?'<input type="text" style="border-bottom: 1px solid #cccccc;margin: 0 5px;'+ (obj.wentiresult?'':'display: none') +'" value="'+ (obj.wentiresult||'') +'">':'') + '</span>\
                </label>\
                ' + (obj.url?'<img src="' + (ossPrefix + obj.url) + '" style="width: 200px;margin: 5px 0 5px 20px;">':'') + '\
            </span>\
        </li>';
    return oneselectstr;
}
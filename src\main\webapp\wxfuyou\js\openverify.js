require.config({
    baseUrl: '../',//指定js文件的基路径
    paths: {
        system: 'sys/system_zepto',
        moment:'plugin/js/moment',
        common: 'wxcommon/common',
        chdccommon: 'wxchdc/js/chdccommon',
        fun: 'sys/function',
        open: 'wxchdc/js/openverifyjson'
    },
    waitSeconds: 0
});
var objdata = {};
require(['system', 'common', 'chdccommon','moment','open','fun'], function (s, c, p, moment) {
    objdata.curmoment = moment();
    //获取用户信息
    selectEmployeeInfo(function (obj) {
        if (!obj) {
            console.log("没有obj");
            $("#divunsub").show().siblings().hide();
            return;
        }
        window.objinfo = obj;
        console.log(JSON.stringify(obj));
        objdata.openid = obj.openid;
        if (!obj.mobile) {
            parent.layer.open({
                content: "没有绑定手机号，请绑定后使用",
                btn: '确定',
                yes: function () {
                    var args = Arg.all();
                    parent.location.href = "bind.html?v=" + Arg('v') + "&frompage=openverify" + "&flag=" + args['flag'] + (Arg("id") ? '&id=' + Arg("id") : "");
                }
            });
            return;
        }
        console.log(obj.mobile);
        console.log("objinfo.areacode:" + objinfo.areacode);
        if (objinfo.areacode) {
        } else {
            parent.layer.msg("请先添加地区用户！");
            return;
        }
        $('body').on('click', '#back', function () {
            history.back();
        });
        //处理当前页面逻辑
        objopenverify.initEvent();
        objopenverify.initData();
    });
});
var objopenverify = {
    /**
     * 注册事件
     */
    initEvent: function () {
        $("#a_openverify,#a_reverify").click(function () {
            parent.openwin("openverifyedit", 'openverifyedit.html?v=' + Arg("v") + '&frompage=' + window.name + (Arg('yid') ? "&yid=" + Arg('yid') : "") + (Arg("areaname") ? "&areaname=" + Arg("areaname") : "") + (Arg("id") ? '&id=' + Arg("id") : ""), "开学核验");
        });
        $("#a_checkresult").click(function () {
            parent.openwin("openverifydetail", 'openverifydetail.html?v=' + Arg("v") + '&frompage=' + window.name + (Arg('yid') ? "&yid=" + Arg('yid') : "") + (Arg("id") ? '&id=' + Arg("id") : ""), "开学核验");
        });
    },
    //初始化开学条件核验数据
    initData: function () {
        var nums = 0;
        if(!$.isEmptyObject(objverify)){//获取全部个数，没有登记时
            for (var n in objverify) {
                var objitem = objverify[n];
                for (var o in objitem) {
                    if(o != 'name'){
                        nums++;
                    }
                }
            }
        }
        $.sm(function (re, err) {
            if (err) {
                parent.layer.msg(err);
            } else if(re){
                var yesnum = 0, flag = 0;//nums全部项，yesnum选择是的项, flag标记是否登记过
                var item = re[0];
                if(item){
                    nums = 0;
                    flag = 1;
                    //yeyid,gid,areacode,verifiedUnit,verified,verifieddate,registtype
                    $("#lbverifyunit").text(item[3]);//最近核检单位
                    $("#lbverifydate").text(item[5]);//最近核检时间
                    for (var i = 8; i < item.length; i++) {//
                        nums++;
                        if(item[i] == 1){
                            yesnum++;
                        }
                    }
                }
                if(flag){//登记过
                    if(nums == yesnum){//全部达标
                        $("#btn_state").html("已达标");
                        if($("#div_state").hasClass('success-state')){
                        }else{
                            $("#div_state").addClass('success-state');
                        }
                    }else{//
                        $("#btn_state").html("未达标");
                        $("#div_state").removeClass('success-state');
                    }
                    $(".bar").css("width", (yesnum ? formatFloatValue(yesnum/nums * 100) : 0) + '%');
                    $("#btmopr-btn").hide();
                    $("#btmmore-btn").show();
                    $("#verify_unit").show();
                }else{
                    $("#btn_state").html("未核验");
                    $("#div_state").removeClass('success-state');
                    $("#btmopr-btn").show();
                    $("#btmmore-btn").hide();
                }
                $("#spstandards").html(yesnum);//达标的人数
                $("#spall").html(nums);//全部人数
            }
        }, ["openverify.data", Arg('yid')]);
    }
};

//微信sdkcommon
define(function (wx, zhtype) {
    var baiduak = "aZO941OOxNhp7NE9ifCwa1yk";
    var tengxunak = "YD4BZ-ND3CJ-JF2FO-FIDUR-B3YYV-RBBKD";//YD4BZ-ND3CJ-JF2FO-FIDUR-B3YYV-RBBKD
    var arrtengxunak = [ "EEDBZ-2WLWW-6TBRY-O4JJM-RDA7T-KMB3Q","YD4BZ-ND3CJ-JF2FO-FIDUR-B3YYV-RBBKD", "T6QBZ-SUAL6-D7VST-EONY4-ENWO7-SZF64"];
    var ua = window.navigator.userAgent.toLowerCase();
    var objbro = {};
    if (ua.match(/DingTalk/i) == "dingtalk") {//用钉钉打开
        objbro.isDingTalk = true
    } else if (ua.match(/MicroMessenger/i) == "micromessenger") {//用微信打开
        objbro.isWeiXin = true
    }
    var wxtool = {
        osshost: "http://tbfile.oss-cn-beijing.aliyuncs.com",
        isready: false,
        isios: navigator.userAgent.indexOf("iPhone") > -1,//navigator.userAgent.indexOf("iPhone") > -1,
        wx: null,
        zhtype: null,
        jsApiList: ['checkJsApi', 'getNetworkType', 'previewImage', 'getLocation', 'chooseImage', 'getLocalImgData', 'uploadImage', 'startRecord', 'stopRecord', 'onVoiceRecordEnd', 'translateVoice', 'uploadVoice', 'playVoice', 'pauseVoice', 'stopVoice', 'onVoicePlayEnd', 'onMenuShareTimeline', 'onMenuShareAppMessage', 'scanQRCode', 'updateAppMessageShareData', 'updateTimelineShareData'],
        init: function (wx, zhtype, jsApiList) {
            this.wx = wx;
            this.zhtype = zhtype;
            if (jsApiList) {
                this.jsApiList = jsApiList;
            }
            return wx;
        },
        ready: function (cb) {
            var _this = this;
            if (_this.isready) {
                cb(true, this.wx);
            } else {
                if (!this.wx) {
                    require.config({
                        paths: {
                            wxsdk: '../sys/jweixin-1.4.0'
                        }
                    });
                    require(['wxsdk'], function (wx) {
                        _this.wx = wx;
                        _this.initConfig(function (issucc, err) {
                            if (!issucc) {
                                cb(false, err);
                            } else {
                                cb(true, _this.wx);
                            }
                        });
                    })
                } else {
                    _this.initConfig(function (issucc, err, objerr) {
                        if (!issucc) {
                            cb(false, err, objerr);
                        } else {
                            cb(true, _this.wx);
                        }
                    });
                }
            }
        },
        initConfig: function (cb) {//初始化配置
            var _this = this;
            if (!this.wx || !this.zhtype) {
                cb && cb(false, '未初始化wxtool', {
                    errcode: "not init",
                    errmsg: '未初始化wxtool'
                });
                return;
            }
            if (!objbro.isWeiXin) {
                cb && cb(false, '不是微信浏览器', {
                    errcode: "not wx",
                    errmsg: '不是微信浏览器'
                });
                return;
            }
            var wx = _this.wx;
            var zhtype = _this.zhtype;
            $.sm(function (re, err) {
                if (re) {
                    var wxinfo = JSON.parse(re);
                    wx.config({
                        debug: false,
                        appId: wxinfo['appId'],
                        timestamp: wxinfo['timestamp'],
                        nonceStr: wxinfo['nonceStr'],
                        signature: wxinfo['signature'],
                        jsApiList: _this.jsApiList
                    });
                    wx.ready(function (res) {
                        _this.isready = true;
                        cb && cb(true);
                    });
                    wx.error(function (res) {
                        cb && cb(false, "wxerror:" + res.errMsg, {
                            errcode: "wxerror",
                            errmsg: res.errMsg
                        });
                        console.log("wx.error" + JSON.stringify(res));
                    });
                } else {
                    cb && cb(false, err);
                }
            }, ["wx_jssdk_config", zhtype, parent.location.href.split('#')[0]]);
        }, getPosition: function (config) {//获取位置
            this.ready(function (issucc, err, objerr) {
                if (!issucc) {
                    config.complete(false, err);
                } else {
                    config.postype = config.postype || 'wx';//wx 或 baidu
                    config.needdetail = config.needdetail == undefined ? true : config.needdetail;
                    getPosition(config);
                }
            });
        }, getLocalImaDataOneByOne: function (localIds, objLocalData, cb) {//一个个获取imgdata base64格式
            var _this = this;
            var localId = localIds.pop();
            _this.wx.getLocalImgData({
                localId: localId,
                success: function (res) {
                    var localData = res.localData;
                    objLocalData[localId] = localData;
                    if (!localIds.length) {
                        cb(objLocalData);
                    } else {
                        _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                    }
                }, fail: function (a, b) {
                    alert(a);
                    if (!localIds.length) {
                        cb(objLocalData);
                    } else {
                        _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                    }
                }
            });
        }
        , getAllLocalImgData: function (localIds, cb) {//获取所有的imgdata base64格式
            var _this = this;
            _this.ready(function (issucc, wx, objerror) {
                if (issucc) {
                    var objLocalData = {};
                    _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                }
            });
        }
        , uploadImageOneByOne: function (wx, arrLocalId, arrServerId, config, index) {//一个个上传到微信服务器 返回serverid
            var _this = this;
            var localId = arrLocalId[index];
            index++;
            config.startOne && config.startOne(index);
            wx.uploadImage({
                localId: localId,
                isShowProgressTips: 0, // 默认为1，显示进度提示
                success: function (res) {
                    arrServerId.push(res.serverId);
                    config.completeOne && config.completeOne(true, index, localId, res.serverId);
                    if (index >= arrLocalId.length) {
                        config.complete && config.complete(arrServerId);
                    } else {
                        _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, index);
                    }
                }, fail: function (a, b) {
                    arrServerId.push('');
                    config.completeOne && config.completeOne(false, index, localId);
                    if (index >= arrLocalId.length) {
                        config.complete && config.complete(arrServerId);
                    } else {
                        _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, index);
                    }
                }
            });
        }
        , uploadLocalImage: function (arrLocalId, config) {//上传多个图片
            var _this = this;
            _this.ready(function (issucc, wx) {
                if (issucc) {
                    var arrServerId = [];
                    if (!config) {
                        config = {};
                    } else {
                        if (typeof config == "function") {
                            config = {
                                complete: config
                            }
                        }
                    }
                    _this.uploadImageOneByOne(wx, arrLocalId, arrServerId, config, 0);
                }
            });
        }
        , uploadOneLocalImage: function (localId, cb) {//上传单个图片
            var _this = this;
            _this.ready(function (issucc, wx) {
                if (issucc) {
                    wx.uploadImage({
                        localId: localId,
                        isShowProgressTips: 0, // 默认为1，显示进度提示
                        success: function (res) {
                            cb && cb(res.serverId);
                        }, fail: function (a, b) {
                            cb && cb();
                        }
                    });
                } else {
                    cb && cb();
                }
            });
        }, chooseImage: function (option) {//选择图片
            var _this = this;
            _this.ready(function (issucc, wx, objerror) {
                if (!issucc) {
                    option.fail && option.fail(wx, objerror);
                    return;
                }
                wx.chooseImage({
                    count: option.count ? option.count > 9 ? 9 : option.count : 9, // 默认9
                    sizeType: option.sizeType || ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: option.sourceType || ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                    success: function (res) {
                        //日志
                        if (res.errMsg == "chooseImage:ok") {
                            var localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                            if (_this.isios) {
                                //日志
                                var arrlocalid = JSON.parse(JSON.stringify(localIds));
                                _this.getAllLocalImgData(arrlocalid, function (objLocalImgData) {
                                    option.success(localIds, objLocalImgData);
                                });
                            } else {
                                option.success(localIds);
                            }
                            //option.success(localIds);
                        } else {
                            option.fail && option.fail(issucc, wx);
                        }
                    }
                });
            });
        }, upToOss: function (obj) {
            var _this = this;
            var dir = _this.zhtype + "/" + (obj.funtype || "test");
            if (obj.arrtoup.length <= 0)return;
            if (!_this.osssignature) {
                $.sm(function (re, err) {
                    if (re) {
                        _this.upexpire = re.expire;
                        _this.osssignature = {
                            'key': re.dir,
                            'policy': re.policy,
                            'OSSAccessKeyId': re.accessid,
                            'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                            'signature': re.signature
                        };
                    }
                }, ["oss.getsignature", dir], "", "", {
                    async: false
                });
            }
            _this.upToOssStart(obj);
        }, upToOssStart: function (obj) {
            this.upToOssOneByOne(obj.arrtoup, 0, obj);
        }, upToOssOneByOne: function (arrtoup, i, objconfig) {
            var _this = this;
            if (i == arrtoup.length) {
                objconfig.complete && objconfig.complete();
                return;
            }
            var oneobj = arrtoup[i];
            i++;
            objconfig.onestart && objconfig.onestart(oneobj, i);//第i张图片开始上传 回调
            var localId = oneobj.localId;
            if (oneobj.imgData) {
                _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                    objconfig.onecomplete && objconfig.onecomplete(oneobj, i);
                    _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                });
            } else {
                this.getAllLocalImgData([localId], function (objImgData) {
                    oneobj.imgData = objImgData[localId];
                    _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                        objconfig.onecomplete && objconfig.onecomplete(oneobj, i);
                        _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                    });
                })
            }
        }, upToOssOne: function (oneobj, objconfig, cb) {
            var _this = this;
            var base64 = '';
            var fileType = '';
            var fileafter = '';
            if (!oneobj.imgData) {
                cb && cb(oneobj);
                return;
            }
            if (this.isios) {
                oneobj.imgData = oneobj.imgData.replace('image/jgp', 'image/jpeg');
                base64 = oneobj.imgData.split(',')[1];
                fileType = oneobj.imgData.split(';')[0].split(':')[1];
                fileafter = '.jpeg';
            } else {
                base64 = oneobj.imgData;
                fileType = 'image/jpg';
                fileafter = '.jpg';
            }
            var blob = toBlob(base64, fileType);
            if (!(objconfig.needsize === false))
                oneobj.size = blob.size;
            var filename = makeFilename() + fileafter;
            var request = new FormData();
            request.append("OSSAccessKeyId", _this.osssignature.OSSAccessKeyId);//Bucket 拥有者的Access Key Id。
            request.append("policy", _this.osssignature.policy);//policy规定了请求的表单域的合法性
            request.append("Signature", _this.osssignature.signature);//根据Access Key Secret和policy计算的签名信息，OSS验证该签名信息从而验证该Post请求的合法性
            //---以上都是阿里的认证策略
            request.append("key", _this.osssignature.key + "/" + filename);//文件名字，可设置路径
            request.append("success_action_status", '200');// 让服务端返回200,不然，默认会返回204
            request.append('file', blob);//需要上传的文件 file
            //request.append("callback", "");//回调，非必选，可以在policy中自定义
            $.ajax({
                url: _this.osshost,  //上传阿里地址
                data: request,
                processData: false,//默认true，设置为 false，不需要进行序列化处理
                cache: false,//设置为false将不会从浏览器缓存中加载请求信息
                //async: false,//发送同步请求
                contentType: false,//避免服务器不能正常解析文件---------具体的可以查下这些参数的含义
                //dataType: 'JSONP',//不涉及跨域  写json即可
                type: 'post',
                success: function (callbackHost, request) { //callbackHost：success,request中就是 回调的一些信息，包括状态码什么的
                    /*$("body").append("<div style='width: 80%'><img src=" + _this.osshost + "/" + _this.osssignature.key + filename + "></div>");//动态向页面添加上传图片*/
                    oneobj.url = _this.osssignature.key + "/" + filename;
                    if (objconfig.needwh) {
                        _this.getImageWidth(_this.osshost + "/" + _this.osssignature.key + "/" + filename, function (w, h) {
                            oneobj.width = w;
                            oneobj.height = h;
                            cb && cb(oneobj, _this.osshost);
                        })
                    } else {
                        cb && cb(oneobj, _this.osshost);
                    }
                },
                error: function (returndata) {
                    cb && cb(oneobj);
                    alert("上传图片出错");
                    alert(JSON.stringify(returndata));
                }
            });
        }, getImageWidth: function (url, callback) {
            if (!url) {
                return callback(null, null, "图片地址为空");
            }
            var img = new Image();
            img.src = url;
            // 如果图片被缓存，则直接返回缓存数据
            if (img.complete) {
                callback(img.width, img.height);
            } else {
                // 完全加载完毕的事件
                img.onload = function () {
                    callback(img.width, img.height);
                }
                img.onerror = function () {
                    callback(null, null, "获取图片失败");
                }
            }
        }, uploadOneblob: function (objconfig) {
            var oneobj = objconfig.obj || {};
            var blob = objconfig.blob;
            var _this = this;
            var dir = _this.zhtype + "/" + (objconfig.funtype || "test");
            if (!_this.osssignature) {
                $.sm(function (re, err) {
                    if (re) {
                        _this.upexpire = re.expire;
                        _this.osssignature = {
                            'key': re.dir,
                            'policy': re.policy,
                            'OSSAccessKeyId': re.accessid,
                            'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                            'signature': re.signature
                        };
                    }
                }, ["oss.getsignature", dir], "", "", {
                    async: false
                });
            }
            var filetype = blob.type;
            var fileafter = filetype.split('/')[1];
            var filename = makeFilename() + "." + fileafter;
            var request = new FormData();
            request.append("OSSAccessKeyId", _this.osssignature.OSSAccessKeyId);//Bucket 拥有者的Access Key Id。
            request.append("policy", _this.osssignature.policy);//policy规定了请求的表单域的合法性
            request.append("Signature", _this.osssignature.signature);//根据Access Key Secret和policy计算的签名信息，OSS验证该签名信息从而验证该Post请求的合法性
            //---以上都是阿里的认证策略
            request.append("key", _this.osssignature.key + "/" + filename);//文件名字，可设置路径
            request.append("success_action_status", '200');// 让服务端返回200,不然，默认会返回204
            request.append('file', blob);//需要上传的文件 file
            //request.append("callback", "");//回调，非必选，可以在policy中自定义
            $.ajax({
                url: _this.osshost,  //上传阿里地址
                data: request,
                processData: false,//默认true，设置为 false，不需要进行序列化处理
                cache: false,//设置为false将不会从浏览器缓存中加载请求信息
                //async: false,//发送同步请求
                contentType: false,//避免服务器不能正常解析文件---------具体的可以查下这些参数的含义
                //dataType: 'JSONP',//不涉及跨域  写json即可
                type: 'post',
                success: function (callbackHost, request) { //callbackHost：success,request中就是 回调的一些信息，包括状态码什么的
                    /*$("body").append("<div style='width: 80%'><img src=" + _this.osshost + "/" + _this.osssignature.key + filename + "></div>");//动态向页面添加上传图片*/
                    var key = _this.osssignature.key + "/" + filename;
                    oneobj.key = key;
                    if (objconfig.needwh) {
                        _this.getImageWidth(_this.osshost + "/" + key, function (w, h) {
                            oneobj.width = w;
                            oneobj.height = h;
                            objconfig.success && objconfig.success(oneobj, _this.osshost);
                        })
                    } else {
                        objconfig.success && objconfig.success(oneobj, _this.osshost);
                    }
                },
                error: function (returndata) {
                    objconfig.success && objconfig.success(oneobj);
                    alert("上传图片出错");
                    alert(JSON.stringify(returndata));
                }
            });
        }
    };

    //
    function toBlob(urlData, fileType) {
        var bytes = window.atob(urlData),
            n = bytes.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bytes.charCodeAt(n);
        }
        return new Blob([u8arr], {type: fileType});
    }

    //生成文件名字的key
    function makeFilename() {
        var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
        var maxPos = chars.length;
        var random_string = '';
        for (i = 0; i < 10; i++) {
            random_string += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return random_string + new Date().getTime();
    }

    /**
     *
     * @param config
     *      postype wx  baidu
     *      needdetail 是否需要详情
     */
    function getPosition(config) {
        wxtool.wx.getLocation({
            type: 'gcj02', // 默认为 wgs84 的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
            success: function (res) {
                console.log(JSON.stringify(res));
                //转换为百度坐标
                if (config.postype == 'wx') {
                    if (config.needdetail) {
                        getTengxunPosition(res, config);
                    } else {
                        config.complete(true, {
                            lat_wx: res.latitude,
                            lng_wx: res.longitude
                        });
                    }
                } else {
                    getBaiduPosition(res, config);
                }
            }, fail: function (res) {
                console.log(res);
                config.complete(false, {
                    error: "fail",
                    errmsg: "定位失败，请确保开启了gps"
                });
            }
            , cancel: function (res) {
                console.log(res);
                config.complete(false, {
                    error: "cancel",
                    errmsg: "您取消了定位"
                });
            }
        });
    }

    //arrtengxunak
    function getTengxunPosition(objwxpostion, config) {
        var akindex = parent.tengxunakindex || 0;
        if (akindex > arrtengxunak.length - 1) {
            config.complete(false, objwxpostion);
            return;
        }
        requestTengxunPosition(objwxpostion, arrtengxunak[akindex], function (obj) {
            if (obj) {
                obj.lat_wx = objwxpostion.latitude;
                obj.lng_wx = objwxpostion.longitude;
                config.complete(true, obj);
            } else {
                parent.tengxunakindex = akindex + 1;
                getTengxunPosition(objwxpostion, config);
            }
        });
    }

    function requestTengxunPosition(objwxpostion, tengxunak, cb) {
        var url = "https://apis.map.qq.com/ws/geocoder/v1/?location=" + objwxpostion.latitude + "," + objwxpostion.longitude + "&output=jsonp&key=" + tengxunak;
        $.ajax({
            url: url,
            type: 'GET',
            contentType: 'application/json; charset=utf-8',
            dataType: 'jsonp',
            success: function (data) {
                var obj = data.result;
                cb && cb(obj);
            }, error: function (data) {
                cb && cb(null);
            }
        });
    }

    function getBaiduPosition(objwxpostion, config) {
        var url = "http://api.map.baidu.com/geoconv/v1/?coords=" + objwxpostion.longitude + "," + objwxpostion.latitude + "&from=1&to=5&ak=" + baiduak;
        $.ajax({
            url: url,
            type: 'GET',
            contentType: "application/json",
            dataType: 'jsonp',//这里要用jsonp的方式不然会报错
            success: function (data) {
                var lng = data.result[0].x;//经度
                var lat = data.result[0].y;//纬度
                var obj = {
                    lat_wx: objwxpostion.latitude,
                    lng_wx: objwxpostion.longitude,
                    lat_bd: lat,
                    lng_bd: lng
                };
                if (config.needdetail) {
                    getBaiduAdderss(obj, config);
                } else {
                    config.complete(true, obj);
                }
            }
        });
    }

    function getBaiduAdderss(obj, config) {
        var url = "http://api.map.baidu.com/geocoder/v2/?callback=renderReverse&location=" + obj.lat_bd + "," + obj.lng_bd + "&output=json&pois=1&ak=" + baiduak;
        $.ajax({
            url: url,
            type: 'GET',
            contentType: "application/json",
            dataType: 'jsonp',//这里要用jsonp的方式不然会报错
            success: function (data) {
                objdata.address = data.result.formatted_address;
                objdata.country = data.result.addressComponent.country;
                var province = objdata.province = data.result.addressComponent.province;
                var city = objdata.city = data.result.addressComponent.city;
                var district = objdata.district = data.result.addressComponent.district;
                var street = data.result.addressComponent.street;
                if (province === city) {
                    province = '';
                }
                String(obj.lng_bd).length > 15 ? lng = String(obj.lng_bd).substring(0, 15) : null;
                String(obj.lat_bd).length > 15 ? lat = String(obj.lat_bd).substring(0, 15) : null;
                var objre = data.result;
                objre.lat_wx = obj.lat_wx;
                objre.lng_wx = obj.lng_wx;
                objre.lat_bd = obj.lat_bd;
                objre.lng_bd = obj.lng_bd;
                config.complete && config.complete(true, objre);
            }
        });
    }

    var dingtool = {
        osshost: "http://tbfile.oss-cn-beijing.aliyuncs.com",
        isready: false,
        isios: navigator.userAgent.indexOf("iPhone") > -1,//navigator.userAgent.indexOf("iPhone") > -1,
        dd: null,
        zhtype: null,
        init: function (wx, zhtype, jsApiList, cb) {
            this.zhtype = zhtype;
            if (jsApiList) {
                this.jsApiList = jsApiList;
            }
            if (this.dd) {
                return cb && cb(this.dd);
            }
            wx = this;
            if (parent.dd)
                this.dd = parent.dd;
            return cb && cb(this.dd);
        },
        ready: function (cb) {
            var _this = this;
            if (_this.isready) {
                cb(true, this.dd);
            } else {
                _this.init(_this, _this.zhtype, null, function (dd) {
                    _this.initConfig(function (issucc, err) {
                        if (!issucc) {
                            cb(false, err);
                        } else {
                            cb(true, _this.dd);
                        }
                    });
                });
            }
        },
        initConfig: function (cb) {//初始化配置
            var _this = this;
            if (!this.dd || !this.zhtype) {
                cb && cb(false, '未初始化ddtool', {
                    errcode: "not init",
                    errmsg: '未初始化ddtool'
                });
                return;
            }
            /*if (!objbro.isDingTalk) {
                cb && cb(false,'不是钉钉浏览器',{
                    errcode:"not dd",
                    errmsg:'不是钉钉浏览器'
                });
                return;
            }*/
            var yguid = parent.objinfo ? parent.objinfo.curyguid : "";
            if (!yguid) {
                cb && cb(false, '没有幼儿园', {
                    errcode: "not has yey",
                    errmsg: '没有幼儿园'
                });
                return;
            }
            var dd = _this.dd;
            var zhtype = _this.zhtype;
            $.sm(function (re, err) {
                if (re) {
                    var ddinfo = JSON.parse(re);
                    /*dd.config({
                        debug:false,
                        appId:ddinfo['appId'],
                        timestamp:ddinfo['timestamp'],
                        nonceStr:ddinfo['nonceStr'],
                        signature:ddinfo['signature'],
                        jsApiList:_this.jsApiList
                    });*/
                    dd.config({
                        agentId: ddinfo['agentId'], // 必填，微应用ID
                        corpId: ddinfo['corpId'],//必填，企业ID
                        timeStamp: ddinfo['timeStamp'], // 必填，生成签名的时间戳
                        nonceStr: ddinfo['nonceStr'], // 必填，生成签名的随机串
                        signature: ddinfo['signature'], // 必填，签名
                        type: 0,   //选填。0表示微应用的jsapi,1表示服务窗的jsapi；不填默认为0。该参数从dingtalk.js的0.8.3版本开始支持
                        jsApiList: [
                            'biz.util.previewImage',
                            'device.geolocation.get'
                        ]
                    });
                    dd.error(function (error) {
                        /*cb && cb(false,"dderror:" + res.errMsg,{
                            errcode:"dderror",
                            errmsg:res.errMsg
                        });*/
                        alert('dd error: ' + JSON.stringify(error));
                    });
                    dd.ready(function () {
                        _this.isready = true;
                        cb && cb(true);
                    });
                } else {
                    cb && cb(false, err);
                }
            }, ["dd_jssdk_config", zhtype, yguid, parent.location.href.split('#')[0]]);
        }, getPosition: function (config) {//获取位置
            this.ready(function (issucc, err, objerr) {
                if (!issucc) {
                    config.complete(false, err);
                } else {
                    config.postype = config.postype || 'dd';//dd 或 baidu
                    config.needdetail = config.needdetail == undefined ? true : config.needdetail;
                    //getPosition(config);
                    dingtool.dd.device.geolocation.get({
                        targetAccuracy: 50,
                        coordinate: 1,
                        withReGeocode: config.needdetail,
                        useCache: config.useCache ? true : false, //默认是true，如果需要频繁获取地理位置，请设置false
                        onSuccess: function (res) {
                            if (config.postype == 'dd') {
                                var obj = {
                                    lat_wx: res.latitude,
                                    lng_wx: res.longitude
                                };
                                if (config.needdetail) {
                                    obj.address = res.address;
                                }
                                config.complete(true, obj);
                            } else {
                                getBaiduPosition(res, config);
                            }
                        },
                        onFail: function (err) {

                        }
                    });
                    /*wxtool.dd.getLocation({
                        type:'gcj02', // 默认为 wgs84 的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
                        success:function (res) {
                            console.log(JSON.stringify(res));
                            //转换为百度坐标
                            if (config.postype == 'wx') {
                                if (config.needdetail) {
                                    getTengxunPosition(res,config);
                                } else {
                                    config.complete(true,{
                                        lat_wx:res.latitude,
                                        lng_wx:res.longitude
                                    });
                                }
                            } else {
                                getBaiduPosition(res,config);
                            }
                        },fail:function (res) {
                            console.log(res);
                            config.complete(false,{
                                error:"fail",
                                errmsg:"定位失败，请确保开启了gps"
                            });
                        }
                        ,cancel:function (res) {
                            console.log(res);
                            config.complete(false,{
                                error:"cancel",
                                errmsg:"您取消了定位"
                            });
                        }
                    });*/
                }
            });
        }, getLocalImaDataOneByOne: function (localIds, objLocalData, cb) {//一个个获取imgdata base64格式
            var _this = this;
            var localId = localIds.pop();
            _this.dd.getLocalImgData({
                localId: localId,
                success: function (res) {
                    var localData = res.localData;
                    objLocalData[localId] = localData;
                    if (!localIds.length) {
                        cb(objLocalData);
                    } else {
                        _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                    }
                }, fail: function (a, b) {
                    alert(a);
                    if (!localIds.length) {
                        cb(objLocalData);
                    } else {
                        _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                    }
                }
            });
        }
        , getAllLocalImgData: function (localIds, cb) {//获取所有的imgdata base64格式
            var _this = this;
            _this.ready(function (issucc, dd, objerror) {
                if (issucc) {
                    var objLocalData = {};
                    _this.getLocalImaDataOneByOne(localIds, objLocalData, cb);
                }
            });
        }
        , uploadImageOneByOne: function (dd, arrLocalId, arrServerId, config, index) {//一个个上传到微信服务器 返回serverid
            var _this = this;
            var localId = arrLocalId[index];
            index++;
            config.startOne && config.startOne(index);
            dd.uploadImage({
                localId: localId,
                isShowProgressTips: 0, // 默认为1，显示进度提示
                success: function (res) {
                    arrServerId.push(res.serverId);
                    config.completeOne && config.completeOne(true, index, localId, res.serverId);
                    if (index >= arrLocalId.length) {
                        config.complete && config.complete(arrServerId);
                    } else {
                        _this.uploadImageOneByOne(dd, arrLocalId, arrServerId, config, index);
                    }
                }, fail: function (a, b) {
                    arrServerId.push('');
                    config.completeOne && config.completeOne(false, index, localId);
                    if (index >= arrLocalId.length) {
                        config.complete && config.complete(arrServerId);
                    } else {
                        _this.uploadImageOneByOne(dd, arrLocalId, arrServerId, config, index);
                    }
                }
            });
        }
        , uploadLocalImage: function (arrLocalId, config) {//上传多个图片
            var _this = this;
            _this.ready(function (issucc, dd) {
                if (issucc) {
                    var arrServerId = [];
                    if (!config) {
                        config = {};
                    } else {
                        if (typeof config == "function") {
                            config = {
                                complete: config
                            }
                        }
                    }
                    _this.uploadImageOneByOne(dd, arrLocalId, arrServerId, config, 0);
                }
            });
        }
        , uploadOneLocalImage: function (localId, cb) {//上传单个图片
            var _this = this;
            _this.ready(function (issucc, dd) {
                if (issucc) {
                    dd.uploadImage({
                        localId: localId,
                        isShowProgressTips: 0, // 默认为1，显示进度提示
                        success: function (res) {
                            cb && cb(res.serverId);
                        }, fail: function (a, b) {
                            cb && cb();
                        }
                    });
                } else {
                    cb && cb();
                }
            });
        }, chooseImage: function (option) {//选择图片
            var _this = this;
            _this.ready(function (issucc, dd, objerror) {
                if (!issucc) {
                    option.fail && option.fail(dd, objerror);
                    return;
                }
                dd.chooseImage({
                    count: option.count ? option.count > 9 ? 9 : option.count : 9, // 默认9
                    sizeType: option.sizeType || ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: option.sourceType || ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                    success: function (res) {
                        //日志
                        if (res.errMsg == "chooseImage:ok") {
                            var localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                            if (_this.isios) {
                                //日志
                                var arrlocalid = JSON.parse(JSON.stringify(localIds));
                                _this.getAllLocalImgData(arrlocalid, function (objLocalImgData) {
                                    option.success(localIds, objLocalImgData);
                                });
                            } else {
                                option.success(localIds);
                            }
                            //option.success(localIds);
                        } else {
                            option.fail && option.fail(issucc, dd);
                        }
                    }
                });
            });
        }, upToOss: function (obj) {
            var _this = this;
            var dir = _this.zhtype + "/" + (obj.funtype || "test");
            if (obj.arrtoup.length <= 0)return;
            if (!_this.osssignature) {
                $.sm(function (re, err) {
                    if (re) {
                        _this.upexpire = re.expire;
                        _this.osssignature = {
                            'key': re.dir,
                            'policy': re.policy,
                            'OSSAccessKeyId': re.accessid,
                            'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                            'signature': re.signature
                        };
                    }
                }, ["oss.getsignature", dir], "", "", {
                    async: false
                });
            }
            _this.upToOssStart(obj);
        }, upToOssStart: function (obj) {
            this.upToOssOneByOne(obj.arrtoup, 0, obj);
        }, upToOssOneByOne: function (arrtoup, i, objconfig) {
            var _this = this;
            if (i == arrtoup.length) {
                objconfig.complete && objconfig.complete();
                return;
            }
            var oneobj = arrtoup[i];
            i++;
            objconfig.onestart && objconfig.onestart(oneobj, i);//第i张图片开始上传 回调
            var localId = oneobj.localId;
            if (oneobj.imgData) {
                _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                    objconfig.onecomplete && objconfig.onecomplete(oneobj, i);
                    _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                });
            } else {
                this.getAllLocalImgData([localId], function (objImgData) {
                    oneobj.imgData = objImgData[localId];
                    _this.upToOssOne(oneobj, objconfig, function (oneobj) {
                        objconfig.onecomplete && objconfig.onecomplete(oneobj, i);
                        _this.upToOssOneByOne(objconfig.arrtoup, i, objconfig);
                    });
                })
            }
        }, upToOssOne: function (oneobj, objconfig, cb) {
            var _this = this;
            var base64 = '';
            var fileType = '';
            var fileafter = '';
            if (!oneobj.imgData) {
                cb && cb(oneobj);
                return;
            }
            if (this.isios) {
                oneobj.imgData = oneobj.imgData.replace('image/jgp', 'image/jpeg');
                base64 = oneobj.imgData.split(',')[1];
                fileType = oneobj.imgData.split(';')[0].split(':')[1];
                fileafter = '.jpeg';
            } else {
                base64 = oneobj.imgData;
                fileType = 'image/jpg';
                fileafter = '.jpg';
            }
            var blob = toBlob(base64, fileType);
            if (!(objconfig.needsize === false))
                oneobj.size = blob.size;
            var filename = makeFilename() + fileafter;
            var request = new FormData();
            request.append("OSSAccessKeyId", _this.osssignature.OSSAccessKeyId);//Bucket 拥有者的Access Key Id。
            request.append("policy", _this.osssignature.policy);//policy规定了请求的表单域的合法性
            request.append("Signature", _this.osssignature.signature);//根据Access Key Secret和policy计算的签名信息，OSS验证该签名信息从而验证该Post请求的合法性
            //---以上都是阿里的认证策略
            request.append("key", _this.osssignature.key + "/" + filename);//文件名字，可设置路径
            request.append("success_action_status", '200');// 让服务端返回200,不然，默认会返回204
            request.append('file', blob);//需要上传的文件 file
            //request.append("callback", "");//回调，非必选，可以在policy中自定义
            $.ajax({
                url: _this.osshost,  //上传阿里地址
                data: request,
                processData: false,//默认true，设置为 false，不需要进行序列化处理
                cache: false,//设置为false将不会从浏览器缓存中加载请求信息
                //async: false,//发送同步请求
                contentType: false,//避免服务器不能正常解析文件---------具体的可以查下这些参数的含义
                //dataType: 'JSONP',//不涉及跨域  写json即可
                type: 'post',
                success: function (callbackHost, request) { //callbackHost：success,request中就是 回调的一些信息，包括状态码什么的
                    /*$("body").append("<div style='width: 80%'><img src=" + _this.osshost + "/" + _this.osssignature.key + filename + "></div>");//动态向页面添加上传图片*/
                    oneobj.url = _this.osssignature.key + "/" + filename;
                    if (objconfig.needwh) {
                        _this.getImageWidth(_this.osshost + "/" + _this.osssignature.key + "/" + filename, function (w, h) {
                            oneobj.width = w;
                            oneobj.height = h;
                            cb && cb(oneobj, _this.osshost);
                        })
                    } else {
                        cb && cb(oneobj, _this.osshost);
                    }
                },
                error: function (returndata) {
                    cb && cb(oneobj);
                    alert("上传图片出错");
                    alert(JSON.stringify(returndata));
                }
            });
        }, getImageWidth: function (url, callback) {
            if (!url) {
                return callback(null, null, "图片地址为空");
            }
            var img = new Image();
            img.src = url;
            // 如果图片被缓存，则直接返回缓存数据
            if (img.complete) {
                callback(img.width, img.height);
            } else {
                // 完全加载完毕的事件
                img.onload = function () {
                    callback(img.width, img.height);
                }
                img.onerror = function () {
                    callback(null, null, "获取图片失败");
                }
            }
        }
    };
    if (objbro.isWeiXin) {
        return wxtool;
    } else if (objbro.isDingTalk) {
        return dingtool;
    }
    return wxtool;
});
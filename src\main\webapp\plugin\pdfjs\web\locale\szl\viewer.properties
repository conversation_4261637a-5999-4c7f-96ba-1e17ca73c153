# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Piyrwyjszo strōna
previous_label=Piyrwyjszo
next.title=Nastympno strōna
next_label=Dalij

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Strōna
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ze {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} ze {{pagesCount}})

zoom_out.title=Zmyńsz
zoom_out_label=Zmyńsz
zoom_in.title=Zwiynksz
zoom_in_label=Zwiynksz
zoom.title=Srogość
presentation_mode.title=Przełōncz na tryb prezyntacyje
presentation_mode_label=Tryb prezyntacyje
open_file.title=Ôdewrzij zbiōr
open_file_label=Ôdewrzij
print.title=Durkuj
print_label=Durkuj
download.title=Pobier
download_label=Pobier
bookmark.title=Aktualny widok (kopiuj abo ôdewrzij w nowym ôknie)
bookmark_label=Aktualny widok

# Secondary toolbar and context menu
tools.title=Noczynia
tools_label=Noczynia
first_page.title=Idź ku piyrszyj strōnie
first_page.label=Idź ku piyrszyj strōnie
first_page_label=Idź ku piyrszyj strōnie
last_page.title=Idź ku ôstatnij strōnie
last_page.label=Idź ku ôstatnij strōnie
last_page_label=Idź ku ôstatnij strōnie
page_rotate_cw.title=Zwyrtnij w prawo
page_rotate_cw.label=Zwyrtnij w prawo
page_rotate_cw_label=Zwyrtnij w prawo
page_rotate_ccw.title=Zwyrtnij w lewo
page_rotate_ccw.label=Zwyrtnij w lewo
page_rotate_ccw_label=Zwyrtnij w lewo

cursor_text_select_tool.title=Załōncz noczynie ôbiyranio tekstu
cursor_text_select_tool_label=Noczynie ôbiyranio tekstu
cursor_hand_tool.title=Załōncz noczynie rōnczka
cursor_hand_tool_label=Noczynie rōnczka

scroll_vertical.title=Używej piōnowego przewijanio
scroll_vertical_label=Piōnowe przewijanie
scroll_horizontal.title=Używej poziōmego przewijanio
scroll_horizontal_label=Poziōme przewijanie
scroll_wrapped.title=Używej szichtowego przewijanio
scroll_wrapped_label=Szichtowe przewijanie

spread_none.title=Niy dowej strōn w widoku po dwie
spread_none_label=Po jednyj strōnie
spread_odd.title=Dej strōny po dwie: niyparzysto i parzysto
spread_odd_label=Niyparzysto i parzysto
spread_even.title=Dej strōny po dwie: parzysto i niyparzysto
spread_even_label=Parzysto i niyparzysto

# Document properties dialog box
document_properties.title=Włosności dokumyntu…
document_properties_label=Włosności dokumyntu…
document_properties_file_name=Miano zbioru:
document_properties_file_size=Srogość zbioru:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} B)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} B)
document_properties_title=Tytuł:
document_properties_author=Autōr:
document_properties_subject=Tymat:
document_properties_keywords=Kluczowe słowa:
document_properties_creation_date=Data zrychtowanio:
document_properties_modification_date=Data zmiany:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Zrychtowane ôd:
document_properties_producer=PDF ôd:
document_properties_version=Wersyjo PDF:
document_properties_page_count=Wielość strōn:
document_properties_page_size=Srogość strōny:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=piōnowo
document_properties_page_size_orientation_landscape=poziōmo
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Gibki necowy podglōnd:
document_properties_linearized_yes=Ja
document_properties_linearized_no=Niy
document_properties_close=Zawrzij

print_progress_message=Rychtowanie dokumyntu do durku…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Pociep

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Przełōncz posek na rancie
toggle_sidebar_notification.title=Przełōncz posek na rancie (dokumynt mo struktura/przidowki)
toggle_sidebar_notification2.title=Przełōncz posek na rancie (dokumynt mo struktura/przidowki/warstwy)
toggle_sidebar_label=Przełōncz posek na rancie
document_outline.title=Pokoż struktura dokumyntu (tuplowane klikniyncie rozszyrzo/swijo wszyskie elymynta)
document_outline_label=Struktura dokumyntu
attachments.title=Pokoż przidowki
attachments_label=Przidowki
layers.title=Pokoż warstwy (tuplowane klikniyncie resetuje wszyskie warstwy do bazowego stanu)
layers_label=Warstwy
thumbs.title=Pokoż miniatury
thumbs_label=Miniatury
findbar.title=Znojdź w dokumyncie
findbar_label=Znojdź

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Strōna {{page}}

additional_layers=Nadbytnie warstwy
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Strōna {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatura strōny {{page}}

# Find panel button title and messages
find_input.title=Znojdź
find_input.placeholder=Znojdź w dokumyncie…
find_previous.title=Znojdź piyrwyjsze pokozanie sie tyj frazy
find_previous_label=Piyrwyjszo
find_next.title=Znojdź nastympne pokozanie sie tyj frazy
find_next_label=Dalij
find_highlight=Zaznacz wszysko
find_match_case_label=Poznowej srogość liter
find_entire_word_label=Cołke słowa
find_reached_top=Doszło do samego wiyrchu strōny, dalij ôd spodku
find_reached_bottom=Doszło do samego spodku strōny, dalij ôd wiyrchu
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} ze {{total}}, co pasujōm
find_match_count[two]={{current}} ze {{total}}, co pasujōm
find_match_count[few]={{current}} ze {{total}}, co pasujōm
find_match_count[many]={{current}} ze {{total}}, co pasujōm
find_match_count[other]={{current}} ze {{total}}, co pasujōm
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(total) ]}
find_match_count_limit[zero]=Wiyncyj jak {{limit}}, co pasujōm
find_match_count_limit[one]=Wiyncyj jak {{limit}}, co pasuje
find_match_count_limit[two]=Wiyncyj jak {{limit}}, co pasujōm
find_match_count_limit[few]=Wiyncyj jak {{limit}}, co pasujōm
find_match_count_limit[many]=Wiyncyj jak {{limit}}, co pasujōm
find_match_count_limit[other]=Wiyncyj jak {{limit}}, co pasujōm
find_not_found=Fraza niy ma znodniynto

# Error panel labels
error_more_info=Wiyncyj informacyji
error_less_info=Mynij informacyji
error_close=Zawrzij
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Wiadōmość: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Sztapel: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Zbiōr: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Linijo: {{line}}
rendering_error=Przi renderowaniu strōny pokozoł sie feler.

# Predefined zoom values
page_scale_width=Szyrzka strōny
page_scale_fit=Napasowanie strōny
page_scale_auto=Autōmatyczno srogość
page_scale_actual=Aktualno srogość
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Feler

# Loading indicator messages
loading_error=Przi ladowaniu PDFa pokozoł sie feler.
invalid_file_error=Zły abo felerny zbiōr PDF.
missing_file_error=Chybio zbioru PDF.
unexpected_response_error=Niyôczekowano ôdpowiydź serwera.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Anotacyjo typu {{type}}]
password_label=Wkludź hasło, coby ôdewrzić tyn zbiōr PDF.
password_invalid=Hasło je złe. Sprōbuj jeszcze roz.
password_ok=OK
password_cancel=Pociep

printing_not_supported=Pozōr: Ta przeglōndarka niy cołkiym ôbsuguje durk.
printing_not_ready=Pozōr: Tyn PDF niy ma za tela zaladowany do durku.
web_fonts_disabled=Necowe fōnty sōm zastawiōne: niy idzie użyć wkludzōnych fōntōw PDF.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.

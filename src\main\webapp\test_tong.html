﻿<html>
<head>
    <title>消息测试页面</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <script type="text/javascript" src="./layui-btkj/layui.js"></script>
    <script type="text/javascript" src="./plugin/js/moment.js"></script>
    <script type="text/javascript" src="./sys/jquery.js"></script>
    <script type="text/javascript" src="./sys/system.js"></script>
</head>
<body>
<h2>消息测试页面</h2>
<div style="">
    <input type="button" value="刷新消息字典" id="btnrefresh"/>
    <input type="button" value="刷新配置文件" id="btnrefreshprop"/>
</div>
<script type="text/javascript">
    $(function () {
        $('input').click(function (e) {
            var id = $(e.target).prop('id');
            switch (id) {
                case 'btnrefresh'://刷新消息字典
                    $.sm(function(re,err) {
                        alert(re,err);
                    }, [ "common.refreshmsg" ]);
                    break;
                case 'btnrefreshprop'://刷新配置文件
                    $.sm(function (re, err) {
                        alert(re);
                    }, ["common.refreshProperties"]);
                    break;
                default:
                    break;
            }
        });
    });
</script>
</body>
</html>

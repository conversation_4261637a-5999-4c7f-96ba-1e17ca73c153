﻿/*
新增日期: 2014.07.29
作 者: 郭玉峰
功能：转换时间与当前时间关系
 */
var dateFormat = function(time, format) {
	var o = {
		'M+' : time.getMonth() + 1, // 月份
		'd+' : time.getDate(), // 日期
		'h+' : time.getHours(), // 小时
		'm+' : time.getMinutes(), // 分钟
		's+' : time.getSeconds(), // 秒
		'q+' : Math.floor((time.getMonth() + 3) / 3), // 季度
		'S' : time.getMilliseconds(), // 毫秒
		'e+' : time.getHours() > 12 ? '下午' : '上午', // 上下午
		'r+' : time.getHours() > 12 ? time.getHours() - 12 : time.getHours()
	// 12制小时
	};
	if (/(y+)/.test(format)) {
		format = format.replace(RegExp.$1, (time.getFullYear() + '').substr(4 - RegExp.$1.length));
	}
	for ( var k in o) {
		if (new RegExp('(' + k + ')').test(format)) {
			format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
		}
	}
	return format;
};
/**
 * 格式化字符串
 * strtime 格式 2015-10-01 01:01:01
 * isdo 是否处理几分钟前和几秒前
 */
var getTimeStr = function(strtime, isdo) {
	var arr = strtime.split(' ');
	var arr0 = arr[0].split('-');
	var arr1 = arr[1].split(':');

	time = new Date(arr0[0], arr0[1] * 1 - 1, arr0[2], arr1[0], arr1[1], arr1[2]);
	var NUM = 1000 * 3600 * 24;
	var et = time.getTime();
	var today = new Date();
	var nowt = today.getTime();
	var ft = '';

	var d1 = new Date(arr0[0], arr0[1] * 1 - 1, arr0[2]);
	var d2 = new Date(today.getFullYear(), today.getMonth(), today.getDate());

	switch (parseInt((d2.getTime() - d1.getTime()) / NUM)) {
	case 0:
		ft = dateFormat(time, 'ee rr:mm');
		if (isdo) {
			var minuteleft = Math.floor((nowt - et) / (60 * 1000)); // 计算相差的分钟数
			if (minuteleft < 30) {
				ft = minuteleft == 0 ? Math.floor((nowt - et) / 1000) + "秒前" : minuteleft + "分钟前";
			}
		}
		break;
	case 1:
		ft = dateFormat(time, '昨天 hh:mm');
		break;
	case 2:
		ft = dateFormat(time, '前天 hh:mm');
		break;
	default:
		ft = dateFormat(time, 'MM月dd日');
	}
	return ft;
};
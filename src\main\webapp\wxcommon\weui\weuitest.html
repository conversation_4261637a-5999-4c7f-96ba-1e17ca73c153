<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <title>测试</title>
    <link rel="stylesheet" type="text/css" href="weui.min.css">
    <link rel="stylesheet" type="text/css" href="jquery-weui.css">
    <script type="text/javascript" src="../../sys/jquery.js"></script>
    <style>
        body {
            background: #f3f4f6;
        }
        #selSex {
            color: #dddddd;
        }
        #selSex .current {
            color: #000000;
        }
        .weui-picker-calendar {
            height: auto;
        }
    </style>
</head>
<body>
    <section class="addbodydiv">
        <div id="btnaction">打开actions</div>
        <br><br><br>
        <div style="height:100px;" id="div1" class="mypickerouter">
            <input id="txt1"> <input id="txt2" value="2018-03-29">
        </div>
        <div>
            <input type="button" value="点击弹出" id="btn1">
        </div>
        <div>
            <input type="button" value="选择" id="btn2">
        </div>
        <div id="divcalender" style="height: 13rem"></div>
        <script>
            $(function () {
                $("#btnaction").calendar();
                $.showLoading("上传中");
                setTimeout(function () {
                    $.hideLoading()
                },3000)
                $.modal({
                    title:"添加常用语模板",
                    autoClose:false,
                    text:'<textarea style="width: 230px;height: 80px;line-height: 20px;padding: 6px 10px;border: 1px solid #e6e6e6;color: #333;" id="modalinput"></textarea>',
                    buttons:[{
                        text:"确定",onClick:function () {
                            if(!$("#modalinput").val()){

                            } else {
                                $.closeModal();
                            }
                        }
                    },{
                        text:"取消",className:"default",onClick:function () {
                            $.closeModal();
                        }
                    }]
                });
                $("#btnaction").click(function () {
                    $.actions({
                        actions:[{
                            text:"编辑",
                            onClick:function () {
                                //do something
                            }
                        },{
                            text:"删除",
                            onClick:function () {
                                //do something
                            }
                        }]
                    });
                })
                return
                $.modal({
                    title:"Hello",
                    text:'<p class="weui-prompt-text">请输入密码</p><input type="password" class="weui-input weui-prompt-input" id="weui-prompt-password" value="" placeholder="输入密码" />',
                    buttons:[
                        {
                            text:"验证",onClick:function () {

                        }
                        },
                        {
                            text:"取消",className:"default",onClick:function () {
                            console.log(3)
                        }
                        }
                    ]
                });
                var _calender = $("#divcalender");
                _calender.calendar({
                    titlehide:true,
                    toolbar:false,
                    weekHeader:false,
                    container:"#divcalender",
                    onMonthYearChangeEnd:function (p,currentYear,currentMonth) {
                        console.log(currentYear + " " + currentMonth);
                        return;
                        setMonthShow(p);
                    },
                    onOpen:function (p) {
                        console.log(p.currentYear + " " + p.currentMonth);
                        // _div1.calendar("setValue", [currentYear + "-" + (currentMonth+1)  + "-12"]);  //设置日期
                        //p.months.filter(".picker-calendar-month-current").find().css('color','red');
                        /* p.months.filter(".picker-calendar-month-current").find(".picker-calendar-day[data-day=11]").addClass("can-serve");
                         p.months.filter(".picker-calendar-month-current").find(".picker-calendar-day[data-day=22]").append('<span class="reserve-mark full">满</span>');
                         p.months.filter(".picker-calendar-month-current").find(".picker-calendar-day[data-day=23]").append('<span class="reserve-mark xiuxi">休</span>');
                         p.months.filter(".picker-calendar-month-current").find(".picker-calendar-day[data-day=24]").addClass("have-serve").append('<i class="iconfont icon_check"></i>');*/
                        //setMonthShow(p);
                        return;
                        if (dataobj.orderdate) {
                            var arr = getDateArray(dataobj.orderdate);
                            if (arr[0] == p.currentYear && arr[1] == p.currentMonth) {
                                setMonthShow(p);
                            } else {
                                setTimeout(function () {
                                    p.setValue([dataobj.orderdate]);
                                },2000)
                            }
                        } else {
                            setMonthShow(p);
                        }
                    },
                    onBeforeDayClick:function (p) {
                        return;
                        //1 移除旧的
                        if (arrprevselectday)
                            p.months.filter(".picker-calendar-month-current").find("[data-year=" + arrprevselectday[0] + "][data-month=" + arrprevselectday[1] + "][data-day=" + arrprevselectday[2] + "]").removeClass("have-serve").find('.icon_check').remove();
                    },
                    onDayClick:function (p,dayContainer,year,month,day) {
                        return;
                        if ($(dayContainer).hasClass("can-serve")) {
                            //2 添加新的
                            $(dayContainer).addClass("have-serve").append('<i class="iconfont icon_check"></i>');
                            arrprevselectday = [year,month,day];
                        } else {
                            layer.msg("不可预约");
                        }
                    }
                });
            })
        </script>
    </section>
    <script src="jquery-weui.js?v=44"></script>
    <script>
        $(function () {
            $("#btn1").click(function () {
                $.prompt({
                    title:'标题',
                    text:'内容文案',
                    input:'输入框默认值',
                    empty:false, // 是否允许为空
                    onOK:function (input) {
                        alert(input)
                    },
                    onCancel:function () {
                        //点击取消
                    }
                });
            });
            /*$("#btn1").picker({
             title: "请选择您的手机",
             cols: [
             {
             textAlign: 'center',
             values: ['iPhone 4', 'iPhone 4S', 'iPhone 5', 'iPhone 5S', 'iPhone 6', 'iPhone 6 Plus', 'iPad 2', 'iPad Retina', 'iPad Air', 'iPad mini', 'iPad mini 2', 'iPad mini 3']
             }
             ]
             });*/
            $("#btn2").select({
                title:"您的爱好",
                multi:true,
                items:[
                    {
                        title:"画画",
                        value:1
                    },
                    {
                        title:"打球",
                        value:2
                    },
                    {
                        title:"唱歌",
                        value:3
                    },
                    {
                        title:"游泳",
                        value:4
                    },
                ]
            });

            var _div1 = $("#div1");
            // var _txt1 = $("#txt1");
            var _txt2 = $("#txt2");
            // _txt1.calendar();
            _div1.datetimePicker({
                inputChange:false,//滑动时,input值不改变
                times:function () {
                    return [{
                        values:['上午','下午']
                    }];
                },
                onPick:function (picker) {
                    var values = picker.value;
                    console.log(values)
                }
            });
        })
    </script>
</body>
</html>

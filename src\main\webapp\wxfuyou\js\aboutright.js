/**
 * Created by sarah on 2017/11/24.
 * 检查用户权限 1.是否绑定
 */
require.config({
    baseUrl:'../../',//指定js文件的基路径
    paths : {

    },
    waitSeconds: 0
});
define("aboutright",function () {
    return new Promise(function (resolve, reject) {
        $.sm(function (re, err) {
            resolve(re);
        },["wx.aboutright.userIsBind"])
    }).then(function (data) {
        if(data && data[0] && !data[0][0]){
            return false;
        }else{
            return true;
        }
    });
});

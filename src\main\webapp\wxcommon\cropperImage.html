<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>播放视频</title>
    <!--<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>-->
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <script src="../sys/system.js" type="text/javascript"></script>


    <style>
        body {
            background: #f3f3f7;
            text-align: center;
        }
        .out-container {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
            overflow: scroll;
            -webkit-overflow-scrolling: touch;
        }
        .img-container {
            width: 80%;
            min-height: 200px;
        }
        #image {
            max-width: 100%;
        }
        .img-preview,img-preview2 {
            width: 200px; height: 200px; margin: 10px;
            overflow: hidden;
        }
    </style>
    <script src="../plugin/corpper/js/cropper.js"></script>
    <link rel="stylesheet" href="../plugin/corpper/css/cropper.css">
</head>
<body style="margin: 0">
    <section class="out-container">
        <div class="img-container">
            <img id="image" src="https://osstbfile.tb-n.com/wxyey/yeyenv/_1PzYPwClykPNO-JqVeckpB1tnK1U3eDjBzk20ODp_ZdEq16xdG6BgNrnUVFOW1f1563193736309.jpg" alt="Picture">
        </div>
        <div class="img-preview"></div>
        <div>
            <button id="rotateleft45">左旋45</button>
            <button id="rotaterigth45">右旋45</button>
            <button id="btnok">确定</button>
        </div>
        <div class="img-preview2" style="overflow: hidden">
            <img id="imageoss" style="max-width: 100%" src="">
        </div>
    </section>
    <script>
        $(function () {
            var $image = $("#image");
            var options = {
                aspectRatio:1 / 1,
                preview:'.img-preview',
                crop:function (e) {
                    console.log(e.detail);
                    /*$dataX.val(Math.round(e.detail.x));
                    $dataY.val(Math.round(e.detail.y));
                    $dataHeight.val(Math.round(e.detail.height));
                    $dataWidth.val(Math.round(e.detail.width));
                    $dataRotate.val(e.detail.rotate);
                    $dataScaleX.val(e.detail.scaleX);
                    $dataScaleY.val(e.detail.scaleY);*/
                }
            };
            // Cropper
            $image.on({
                ready:function (e) {
                    console.log(e.type);
                },
                cropstart:function (e) {
                    console.log(e.type,e.originalEvent.detail.action);
                },
                cropmove:function (e) {
                    //console.log(e.type, e.originalEvent.detail.action);
                },
                cropend:function (e) {
                    console.log(e.type,e.originalEvent.detail);
                },
                crop:function (e) {
                    //console.log(e.type);
                },
                zoom:function (e) {
                    console.log(e.type,e.originalEvent.detail.ratio);
                }
            }).cropper(options);
            $("#rotateleft45").click(function () {
                $image.cropper('rotate',-45)
            })
            $("#rotaterigth45").click(function () {
                $image.cropper('rotate',45)
            })
            $("#btnok").click(function () {
                var obj = $image.cropper('getData');
                //x: 261.05417814508723, y: 122.475665748393, width: 507.7685950413223, height: 507.7685950413223,rotate
                //https://osstbfile.tb-n.com/wxyey/yeyenv/_1PzYPwClykPNO-JqVeckpB1tnK1U3eDjBzk20ODp_ZdEq16xdG6BgNrnUVFOW1f1563193736309.jpg?x-oss-process=image/crop,x_-21,y_-202,w_1041,h_1041/rotate,90
                var src = $image.prop('src');
                var rotate = obj.rotate<0?obj.rotate+360:obj.rotate;
                var newsrc = src +'?x-oss-process=image/crop,x_'+ Math.round(obj.x) +',y_'+ Math.round(obj.y) +',w_'+ Math.round(obj.width) +',h_'+ Math.round(obj.height) +'/rotate,'+rotate;
                $("#imageoss").prop('src',newsrc)
            })
        })
    </script>
</body>
</html>



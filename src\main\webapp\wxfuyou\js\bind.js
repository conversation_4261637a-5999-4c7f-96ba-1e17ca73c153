﻿require.config({
    baseUrl:'../',//指定js文件的基路径
    paths : {
        layer : 'plugin/layer_mobile/layer_mobile.min'
    },
    waitSeconds: 0
});
var objdata ={
    timer:null,//当前定时器
    objsendtime : 300,
    objphone:'',//最近发送验证码的手机号
    curupdata:{}
};
require(["layer"],function (layer) {
    $(function () {
        //查询验证码
        $.sm(function (re1, err1) {
            if (re1 && re1[0]) {
                if (re1[0][2]) {
                    $('body').html('');
                    $.alert("已经绑定,进入个人信息",function () {
                        location.href='mine.html'
                    });
                } else {
                    $("#nickname").html(re1[0][3]);
                    $("#photo").prop('src', re1[0][4]);
                    var dbtime = re1[0] && parseInt(re1[0], 10);
                    objdata.objsendtime = dbtime > 300 ? objdata.objsendtime : dbtime;
                    //如果发送时间>0
                    if (objdata.objsendtime > 0) {
                        objdata.objphone = re1[0][1];
                        if (objdata.objphone) {
                            $('#btnGetVerCode').prop('disabled', true).val(objdata.objsendtime + ' 秒');
                            objdata.timer = setInterval(function () {
                                objdata.objsendtime--;
                                $('#btnGetVerCode').html(objdata.objsendtime + ' 秒');
                                if (objdata.objsendtime == 0) {
                                    $('#btnGetVerCode').prop('disabled', false).html('发送');
                                    clearInterval(objdata.timer);
                                    objdata.objsendtime = 300;
                                }
                            }, 1000);
                        }
                    } else {
                        objdata.objsendtime = 300;
                    }
                }
            } else {
                $('body').html('');
                $.alert("没有用户信息，请确保您是在微信公众号中打开");
            }
        }, ['wx.selverifycode', 'wx_subscribe']);
        //获取验证码
        $('#btnGetVerCode').click(function () {
            var txtmobile = $('#txtmobile').val()
            var istrue = validatemobile($('#txtmobile'));
            if (!istrue) {
                return layer.msg('请输入正确的手机号！');
            }
            if ($('#btnGetVerCode').prop('disabled')) {
                return false;
            }
            $('#btnGetVerCode').prop('disabled', true);
            if (objdata.objphone != txtmobile) {
                $.sm(function (re, err) {
                    if (re) {
                        objdata.objphone = txtmobile;
                        layer.open({
                            content: '发送成功'
                            , time: 2
                        });
                        if (objdata.timer) {
                            clearInterval(objdata.timer);
                        }
                        objdata.timer = setInterval(function () {
                            objdata.objsendtime--;
                            $('#btnGetVerCode').html(objdata.objsendtime + ' 秒');
                            if (objdata.objsendtime == 0) {
                                $('#btnGetVerCode').prop('disabled', false).html('发送');
                                clearInterval(objdata.timer);
                                objdata.objsendtime = 300;
                            }
                        }, 1000);
                    } else if (err) {
                        $('#btnGetVerCode').prop('disabled', false);
                        console.log(err)
                    } else {
                        $('#btnGetVerCode').prop('disabled', false);
                        console.log('系统错误')
                    }
                }, ['wx.smssend', txtmobile, 'wx_subscribe']);
            } else {
                layer.open({
                    content: '请使用上次发送的短信验证码'
                    , time: 2
                });
                if (objdata.timer) {
                    clearInterval(objdata.timer);
                }
                objdata.timer = setInterval(function () {
                    objdata.objsendtime--;
                    $('#btnGetVerCode').html(objdata.objsendtime + ' 秒');
                    if (objdata.objsendtime == 0) {
                        $('#btnGetVerCode').prop('disabled', false).html('发送');
                        clearInterval(objdata.timer);
                        objdata.objsendtime = 300;
                    }
                }, 1000);
            }
        });
        // 点击绑定
        $("#btnbind").click(function() {
            var istrue = validatemobile($('#txtmobile'));
            if (!istrue) {
                return layer.msg('请输入正确的手机号！');
            }
            var vcode = $('#txtvcode').val();
            if(!vcode || (vcode.length != 6)){
                return layer.msg('请输入正确的验证码！');
            }
            if($("#btnbind").prop('disabled')){
                return false;
            }
            if(!$("#agree-service")[0].checked){
                return layer.msg('请选择同意服务条款！');
            }
            $("#btnbind").prop('disabled',true);
            $.sm(function(re1,err1){
                if(re1&&re1[0]){
                    // 绑定规则
                    $.sm(function(re, err) {
                        $("#btnbind").prop('disabled',false);
                        if (re) {
                            // 绑定成功 重新读取
                            parent.location.href = "mine.html?v="+Arg('v')
                        } else {
                            layer.msg(err);
                        }
                    }, [ 'bind.bind', $("#txtmobile").val() ]);
                }else{
                    $("#btnbind").prop('disabled',false);
                    layer.open({
                        content:'验证码错误'
                        ,time: 2
                    });
                    console.log(err1);
                }
            },['wx.smsverifycode',vcode,'wx_subscribe']);
        });
    })
    function validatemobile(obj) {
        var mobile = obj.val();
        if(mobile.length == 0)
        {
            layer.msg('请输入手机号码！');
            obj.focus();
            return false;
        }
        if(mobile.length != 11)
        {
            layer.msg('请输入有效的手机号码！');
            obj.focus();
            return false;
        }
        if(!mobileCheck(mobile))
        {
            layer.msg('请输入有效的手机号码！');
            obj.focus();
            return false;
        }
        return true;
    }
});
function mobileCheck(mobile) {
    var first = mobile.charAt(0);
    if (first==1 && mobile.length==11){
        return true;
    }else {
        return false;
    }
}

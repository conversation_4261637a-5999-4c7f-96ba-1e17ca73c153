﻿define(['../../plugin/iscroll/js/iscroll', '../wxcommon/scrollclass.js'], function () {

    function AlbumClass() {
        var elem;
        var objThis = {};
        var _this = this;

        /**
         * 查询数据
         * @param ismore
         * @param callback
         * @param isfirst
         */
        function querydata(ismore, callback) {
            var swhere = '';
            if (objThis.dbParam.isImage && objThis.dbParam.isVideo) {
                swhere = " and mtype in('image','video')";
            } else if (objThis.dbParam.isImage) {
                swhere = " and mtype = 'image'";
            } else if (objThis.dbParam.isVideo) {
                swhere = " and mtype = 'video'";
            }
            var key = "mtime";
            if (objThis.dbParam.roletype == 'home' && objThis.dbParam.datatype == 'baby') {
                key = "s.creatime";
            }
            if (!objThis.isFirst) {
                if (ismore && objThis.starttime) {
                    swhere += " and " + key + " < '" + objThis.starttime + "'";
                    if(!objThis.starttime){
                        debugger
                    }
                } else {
                    swhere += " and " + key + " > '" + objThis.endtime + "'";
                    if(!objThis.endtime){
                        debugger
                    }
                }
            }
            objThis.dbParam.swhere = swhere;
            $.sm(function (re, err) {
                if (re) {
                    callback(re);
                    if (ismore) {
                        objThis.arrdata = objThis.arrdata.concat(re);
                    } else {
                        objThis.arrdata = re.concat(objThis.arrdata);
                    }
                } else {
                    callback([]);
                }
            }, ['weixin.material.getData', JSON.stringify(objThis.dbParam)]);
        }

        /**
         * @param arr[Arrary] 消息列表数据
         * @param noticeLen[Number|null] 页面已有消息条数
         * @param direction["up"|"down"|default down] 消息插入方向
         * */
        function makeHtml(arr, direction) {
            var html = '';
            if (arr.length) {
                objThis.isFirst = false;
                var lastHtml = '';
                var arrDay = [];
                var oneDay = {};
                if (direction == 'before') {
                    objThis.lastDate_old = objThis.lastDate = elem.find('.photo-pic').children(".time-star").eq(0).text();
                }
                for (var i = 0; i < arr.length; i++) {  //
                    var obj = arr[i];
                    var strDate = obj.mtime.split(" ")[0];//得到日期
                    if (objThis.lastDate && (strDate == objThis.lastDate)) {//如果和上次最后的日期一样
                        objThis.lastDate_old = objThis.lastDate;
                        lastHtml += makeonehtml(obj);
                    } else {
                        if (strDate != objThis.curdate) {//如果是新的日期 生成日期
                            objThis.curdate = strDate;
                            oneDay = {
                                strDay: strDate,
                                html: ''
                            }
                            arrDay.push(oneDay);
                        } else {
                            oneDay = arrDay[arrDay.length - 1];
                        }
                        oneDay.html += makeonehtml(obj);
                    }
                }
                objThis.lastDate = objThis.curdate;
                //如果有上次的  先把上次的放上
                if (lastHtml) {
                    if (direction == 'before')
                        elem.find(".photo-list[data-date='" + objThis.lastDate_old + "']").prepend(lastHtml);
                    else
                        elem.find(".photo-list[data-date='" + objThis.lastDate_old + "']").append(lastHtml);
                }
                //循环天数
                for (var j = 0; j < arrDay.length; j++) {
                    html += '<div class="photo-pic">' +
                        '<p class="time-star">' + arrDay[j].strDay + '</p>' +
                        '<i class="iconfont"></i>' +
                        '<div class="photo-stat">' +
                        '<div class="photo-list" data-date="' + arrDay[j].strDay + '">' +
                        arrDay[j].html +
                        '</div>' +
                        '</div>' +
                        '</div>';
                }
            }
            return html;
        }

        function makeonehtml(obj) {
            //objRe.data[obj.id] = obj;
            return '<div class="pho-medi" data-id="' + obj.id + '">' +
                '<div style="position:relative;">' +
                '<img src="' + (obj.murl?ossPrefix + obj.murl + '?x-oss-process=image/resize,m_fill,w_300,limit_0,h_300':'../wxcommon/images/albumDefault.png') + '" class="medi-pho">' +

                (obj.mtype == 'video' ? '<span class="pho-play"><img src="images/play-ico.png" alt="" style="width:30px;height:30px;"/></span>' : '') +
                '</div>' +
                '</div>';
        }

        this.load = function (objCur) {
            objCur.curdate = "";//当前处理日期
            objCur.lastDate = "";//最后一个日期
            objCur.lastDate_old = "";//上次的最后一个日期
            objCur.data = {};
            objCur.arrdata = [];
            elem = $("#" + objCur.wrapperId);
            //查询班级数据，并显示
            querydata(true, function (re) {
                if (re.length > 0) {
                    elem.find(".thelist").html(makeHtml(re));
                    objCur.endtime = re[0].mtime;
                    objCur.starttime = re[re.length - 1].mtime;
                } else {
                    elem.find(".thelist").html('').siblings('.pullUp').children('.pullUpLabel').html('暂无内容');
                }
                setTimeout(function () {
                    objCur.myscroll = new scrollClass({
                        wrapper: objCur.wrapperId,
                        // onScrollMove:function (_this) {
                        //     var top = -(_this.y+objCur.myscroll.pullDownOffset);
                        //     if(top>174){
                        //         $("#head").show();
                        //     } else {
                        //         $("#head").hide();
                        //     }
                        // },
                        // onScrollEnd:function (_this) {
                        //     var top = -(_this.y+objCur.myscroll.pullDownOffset);
                        //     if(top>174){
                        //         $("#head").show();
                        //     } else {
                        //         $("#head").hide();
                        //     }
                        // },
                        downAction: function () {
                            _this.refresh();
                            /*querydata(false, function (re) {
                             if (re.length > 0) {
                             elem.find(".thelist").prepend(makeHtml(re, "before"));
                             objCur.endtime = re[0].mtime;
                             }
                             objCur.myscroll.scroll.refresh();
                             });*/
                        },
                        upAction: function () {
                            querydata(true, function (re) {
                                if (re.length > 0) {
                                    //上拉之前消息条数
                                    elem.find(".thelist").append(makeHtml(re, "after"));
                                    objCur.myscroll._wrapper.find('.pullUpLabel').html('下拉加载更多');
                                    objCur.starttime = re[re.length - 1].mtime;
                                    setTimeout(function () {
                                        objCur.myscroll.scroll.refresh();
                                    }, 1000);
                                    setTimeout(function () {
                                        objCur.myscroll.scroll.refresh();
                                    }, 3000);
                                } else {
                                    objCur.myscroll._wrapper.find('.pullUpLabel').html('没有啦');
                                    objCur.myscroll._wrapper.find('.pullUpIcon').hide();
                                }
                            });
                        }
                    });
                    objCur.myscroll.init();
                    setTimeout(function () {
                        objCur.myscroll.scroll.refresh();
                    }, 1000);
                    setTimeout(function () {
                        objCur.myscroll.scroll.refresh();
                        setInterval(function () {//临时修复不能下拉的问题
                            objCur.myscroll.scroll.refresh();
                        }, 5000);
                    }, 3000);
                    setTimeout(function () {
                        elem.css('left', 0);
                        //document.getElementById('wrapper').style.left = '0';
                    }, 10);
                }, 10);
            });
        }
        this.reLoad = function () {
            objThis.myscroll.scroll.destroy();
            objThis.isFirst = true;
            this.load(objThis);
        }
        this.init = function (objConfig) {
            var objCur = {
                isFirst: true
            };
            objCur = $.extend(objCur, objConfig);
            this.objCur = objCur;
            objThis = objCur;
            this.load(objCur)
            this.initEvent();
            return objCur;
        };
        this.initEvent = function () {
            elem.find(".thelist").off('click', '.pho-medi').on('click', '.pho-medi', function () {
                $(".pswp").show();
                var curid = $(this).data("id");
                var arr = [];
                var curindex = 0;
                var curi = 0;
                var winwidth = $(window).width();

                for (var i = 0; i < objThis.arrdata.length; i++) {
                    var obj = objThis.arrdata[i];
                    var id = obj.id;
                    if (id == curid) {
                        curindex = i;
                    }
                    var objmedia = JSON.parse(obj.mdetail);
                    var objitem = {
                        id: id,
                        mtype: obj.mtype,
                        src: obj.murl ? ossPrefix + obj.murl + getImgOSSParam(objmedia, winwidth) : '../wxcommon/images/albumDefault.png',///format,jpg/interlace,1
                        objmedia: objmedia,
                        title: " "
                        //msrc: ossPrefix + obj.murl + '?x-oss-process=image/resize,m_fill,w_300,limit_0,h_300',
                    }
                    if (objmedia.w && objmedia.h) {
                        if (objmedia.w > winwidth) {
                            var w = winwidth * 2;
                            var h = w / objmedia.w * objmedia.h;
                        } else {
                            var w = objmedia.w;
                            var h = objmedia.h;
                        }
                        objitem.w = w;
                        objitem.h = h;
                    } else {
                        objitem.w = 132;
                        objitem.h = 102;
                    }
                    if (id == curid) {
                        curindex = i;
                    }
                    arr.push(objitem);
                    /* if (obj.mtype == 'image') {
                     } else {
                     if(objmedia.w && objmedia.h) {
                     arr.push({
                     id: id,
                     src: ossPrefix + obj.murl,
                     msrc:'images/play-ico.png',
                     w: objmedia.w,
                     h: objmedia.h,
                     title: '<div>'+ obj.mdescription +'</div>'
                     });
                     /!* arr.push({
                     id: id,
                     type:"video",
                     html:'<span style="cursor: pointer;display: inline-block;left: 50%;position: absolute;top: 50%;transform: translate(-50%, -50%);padding: 30px;" class="playVideo">'+
                     '<img src="images/play-ico.png" alt="">' +
                     '</span>',
                     /!*html: '<div style="text-align: center">' +
                     '<video width="80%" height="80%" controls  autoplay="autoplay">' +
                     '<source src="' + ossPrefix + objmedia.videourl + '" type="video/mp4">' +
                     '您的浏览器不支持Video标签。' +
                     '</video>'
                     + '</div>',*!/
                     title: '<div>' + obj.mdescription + '</div>'
                     })*!/
                     }
                     }*/
                }
                require(['../wxcommon/photoswipe.js'], function (objswipe) {
                    var removePhoto = function (_swipe, item, index) {
                        $.confirm({
                            title: '删除确认',
                            text: '确认删除吗？',
                            onOK: function () {
                                $.sm(function (re, err) {
                                    if (re) {
                                        console.log(objswipe);
                                        console.log(objThis);
                                        console.log(_this);
                                        var obj = objThis.arrdata[index];
                                        objThis.onchange && objThis.onchange("cut", [obj]);
                                        //在数组中移除
                                        objThis.arrdata.splice(index, 1);
                                        var curpic = elem.find('[data-id=' + item.id + ']');
                                        if (curpic.siblings().length == 0) {
                                            curpic.parent().parent().remove();
                                        } else {
                                            curpic.remove();
                                        }
                                        if (_swipe.items && item && index > -1) {//修改显示
                                            if (_swipe.items.length == 1) {
                                                _swipe.close();
                                            } else if (_swipe.items.length > 1) {
                                                if (index == _swipe.items.length - 1) {
                                                    _swipe.prev();
                                                }
                                                _swipe.items.splice(index, 1);
                                                _swipe.invalidateCurrItems();
                                                _swipe.updateSize(true);
                                                _swipe.ui.update();
                                            }
                                        }
                                    } else {
                                        $.toast("删除失败", "cancel");
                                    }
                                }, ["weixin.material.delete", JSON.stringify(objThis.dbParam), item.id]);
                            }
                        });
                    };
                    var editPhoto = function (_swipe, item, index) {
                        objdata.cureditobj = objThis.arrdata[index];
                        parent.openwin("photoedit", 'photoedit.html?v=' + Arg("v") + '&frompage=' + window.name, "编辑照片")
                    };
                    var downloadPhoto = function (_swipe, item, index) {
                        objdata.cureditobj = objThis.arrdata[index];
                        if(objdata.cureditobj.mtype=='video'){
                            alert("抱歉，视频暂时不支持下载");
                        } else {
                            //parent.openwin("photodownload", 'photodownload.html?v=' + Arg("v") + '&frompage=' + window.name, "长按图片下载")
                            if(wxtool.isready){
                                wx.previewImage({
                                    current: ossPrefix + objdata.cureditobj.murl, // 当前显示图片的http链接
                                    urls: [ossPrefix + objdata.cureditobj.murl] // 需要预览的图片http链接列表
                                })
                            } else {
                                wxtool.ready(function () {
                                    parent.layer.msg("打开原图后长按可下载",function () {
                                        wx.previewImage({
                                            current: ossPrefix + objdata.cureditobj.murl, // 当前显示图片的http链接
                                            urls: [ossPrefix + objdata.cureditobj.murl] // 需要预览的图片http链接列表
                                        })
                                    });
                                })
                            }

                        }
                    };
                    var viewPhoto = function (_swipe, item, index) {
                        var obj = objThis.arrdata[index];
                        $("#upname").html(obj.upname);
                        var laotime = '';
                        if (objThis.dbParam.datatype == 'class') {//如果班级的
                            $("#uptime").html(obj.mtime.split(".")[0]);
                            laotime = obj.creatime;
                        } else {
                            $("#uptime").html(obj.uptime.split(".")[0]);
                            laotime = obj.mtime;
                        }
                        if (obj.sourcetype == 'lao') {
                            if (obj.fromname) {
                                $("#laoname").html(obj.fromname).parent().show();
                            }
                            if (laotime) {
                                $("#laotime").html(laotime.split(".")[0]).parent().show();
                            }
                        } else {
                            $("#laoname").html('').parent().hide();
                            $("#laotime").html('').parent().hide();
                        }
                        $("#discription").html(obj.mdescription);

                        $("#secViewClose").show();//显示关闭
                        $("#secViewLao").hide();//不显示捞
                        $("#secView").css('visibility', 'visible');
                        $("#secViewClose,#btnClosView").unbind('click').click(function () {
                            $("#secView").css('visibility', 'hidden');
                        })
                    };
                    var laoPhoto = function (_swipe, item, index) {
                        var obj = objThis.arrdata[index];
                        $("#upname").html(obj.upname);
                        var laotime = '';
                        if (objThis.dbParam.datatype == 'class') {//如果班级的
                            $("#uptime").html(obj.mtime.split(".")[0]);
                            laotime = obj.creatime;
                        } else {
                            $("#uptime").html(obj.uptime.split(".")[0]);
                            laotime = obj.mtime;
                        }
                        if (obj.fromname) {
                            $("#laoname").html(obj.fromname).parent().show();
                        }
                        if (laotime) {
                            $("#laotime").html(laotime.split(".")[0]).parent().show();
                            $("#secViewClose").show();//不显示关闭
                            $("#secViewLao").hide();
                        } else {
                            $("#secViewClose").hide();//不显示关闭
                            $("#secViewLao").show().unbind('click').click(function () {
                                $.sm(function (re, err) {
                                    if (re) {
                                        // (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
                                        // (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
                                        Date.prototype.Format = function (fmt) { //author: meizz
                                            var o = {
                                                "M+": this.getMonth() + 1, //月份
                                                "d+": this.getDate(), //日
                                                "h+": this.getHours(), //小时
                                                "m+": this.getMinutes(), //分
                                                "s+": this.getSeconds(), //秒
                                                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                                                "S": this.getMilliseconds() //毫秒
                                            };
                                            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
                                            for (var k in o)
                                                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                                            return fmt;
                                        }
                                        obj.fromname = objdata.fromname;
                                        obj.fromopenid = objdata.openid;
                                        obj.fromtype = objdata.fromtype;
                                        obj.sontime = new Date().Format("yyyy-MM-dd hh:mm:ss");
                                        obj.sourcetype = 'lao';
                                        obj.sid = re;
                                        parent.layer.msg("已经捞到宝宝相册");
                                        $("#secView").css('visibility', 'hidden');
                                        $(".pswp__button--lao").css('color', 'gray').html('照片已捞');
                                        if (objBaby) {
                                            objBaby.refresh();
                                        }
                                    }
                                }, ["weixin.material.lao", obj.id, objdata.curbid, objdata.fromtype, objdata.fromname]);
                            });//显示捞
                        }
                        $("#discription").html(obj.mdescription);
                        $("#secView").css('visibility', 'visible');
                        $("#secViewClose,#btnClosView").unbind('click').click(function () {
                            $("#secView").css('visibility', 'hidden');
                        })
                    };
                    //------------------------------------------------------------------------
                    var objConfig = {
                        playVideo: function (item) {//点击播放视频
                            parent.objdata.curMedia = item.objmedia;
                            parent.openwin("playVideo", "../wxcommon/playVideo.html?v=" + Arg("v"), "播放视频");
                        }, afterChange: function (gallery) {
                            var index = gallery.getCurrentIndex();
                            var item = gallery.currItem;
                            if (item.mtype == 'video') {//增加视频的播放图标
                                $(".pswp__button--download").hide();
                                var str = '<span style="cursor: pointer;display: inline-block;left: 50%;position: absolute;top: 50%;transform: translate(-50%, -50%);padding: 30px;" class="playVideo">' +
                                    '<img class = "pswp__single-tap" src="images/play-ico.png" alt="">' +
                                    '</span>'
                                $(item.container).parent().append(str);
                            } else {
                                $(".pswp__button--download").show();
                            }
                            var obj = objThis.arrdata[index];
                            var des = '<div>' + obj.mdescription + '</div>';
                            des += '<div>';
                            //'<div>'+ obj.mdescription +'</div><div>'+ obj.fromname + ' ' + obj.mtime.substring(0,16) + ' 上传</div>'
                            if (objThis.dbParam.roletype == 'yey') {//老师打开我的相册
                                if (obj.fromopenid == objdata.openid) {
                                    des += "我";
                                } else {
                                    des += obj.fromname;
                                }
                                des += " " + obj.mtime.substring(0, 16) + " 上传";
                            } else if (objThis.dbParam.roletype == 'home') {//家长
                                if (objThis.dbParam.datatype == 'class') {//没有删除 有捞
                                    obj.updatatime = obj.mtime;
                                    obj.sontime = obj.sontime;
                                } else if (objThis.dbParam.datatype == 'baby') {
                                    obj.updatatime = obj.uptime;
                                    obj.sontime = obj.mtime;
                                }
                                if (obj.upfromtype < 20) {
                                    des += obj.upname + "老师 " + obj.updatatime.substring(0, 16) + " 上传";
                                } else {
                                    des += (parent.objdata.pname[obj.upfromtype] || '家长') + " " + obj.updatatime.substring(0, 16) + " 上传";
                                }
                                //des += obj.upname + "老师 " + obj.uptime.substring(0,16) + " 上传";
                                if (obj.fromopenid && obj.sontime) {//如果已经捞
                                    if (obj.fromtype > 20) {
                                        $(".pswp__button--lao").css('color', 'gray').html('照片已捞');
                                        des += ",宝宝" + (parent.objdata.pname[obj.fromtype] || '家长') + obj.sontime.substring(0, 16) + "捞到宝宝相册";
                                    } else {
                                        des += "到宝宝相册";
                                    }
                                } else {
                                    if (objThis.dbParam.datatype == 'class') {//没有删除 有捞
                                        $(".pswp__button--lao").css('color', '#12B7F5').html('<img src="images/pic_find.png" style="width: 20px;">捞照片');
                                    }
                                }
                            }
                            des += '</div>';
                            $(".pswp__caption__center").html(des);
                            /*// gallery.setContent(1111);
                             objThis.arrdata[index];
                             //判断是否已经捞 控制捞是否显示
                             if (objThis.dbParam.roletype == 'home') {//家长
                             if (objThis.dbParam.datatype == 'class') {

                             }
                             }*/
                        }
                    }
                    if (objThis.dbParam.roletype == 'yey') {//老师打开我的相册
                        objConfig.removePhoto = removePhoto;//老师可以删除
                        objConfig.editPhoto = editPhoto;//老师可以编辑
                        objConfig.download = downloadPhoto;//
                        if (objThis.dbParam.datatype == 'my') {
                            //判断打开的是班级相册 还是宝宝相册
                            if (objdata.curLocation) {
                                function move(type, item, _swipe) {
                                    var obj = {
                                        movetype: type,
                                        id: item.id,
                                        fromtype: objdata.ttype,
                                        fromname: objdata.fromname
                                    };
                                    for (var o in objdata.curLocation) {
                                        obj[o] = objdata.curLocation[o];
                                    }
                                    $.sm(function (re, err) {
                                        if (re) {
                                            objClass && objClass.refresh && objClass.refresh();
                                            if (type == 'move') {
                                                var index = _swipe.getCurrentIndex();
                                                var obj = objThis.arrdata[index];
                                                objThis.onchange && objThis.onchange("cut", [obj]);
                                                //在数组中移除
                                                objThis.arrdata.splice(index, 1);
                                                var curpic = elem.find('[data-id=' + item.id + ']');
                                                if (curpic.siblings().length == 0) {
                                                    curpic.parent().parent().remove();
                                                } else {
                                                    curpic.remove();
                                                }
                                                if (_swipe.items && item && index > -1) {//修改显示
                                                    if (_swipe.items.length == 1) {
                                                        _swipe.close();
                                                    } else if (_swipe.items.length > 1) {
                                                        if (index == _swipe.items.length - 1) {
                                                            _swipe.prev();
                                                        }
                                                        _swipe.items.splice(index, 1);
                                                        _swipe.invalidateCurrItems();
                                                        _swipe.updateSize(true);
                                                        _swipe.ui.update();
                                                    }
                                                }
                                            }
                                            $.toast('操作成功');
                                        } else {
                                            $.toast('操作失败', "cancel");
                                        }
                                    }, ["weixin.material.move", JSON.stringify(objThis.dbParam), JSON.stringify(obj)]);
                                }

                                if (objdata.curLocation.type == 'class') {
                                    objConfig.shareButtons = [
                                        {
                                            id: 'copyto',
                                            label: '复制到' + objdata.curLocation.classname + '相册',
                                            click: function (elem, _swipe) {
                                                move("copy", elem, _swipe);
                                            }
                                        }, {
                                            id: 'moveto',
                                            label: '移动到' + objdata.curLocation.classname + '相册',
                                            click: function (elem, _swipe) {
                                                move("move", elem, _swipe);
                                            }
                                        }
                                    ]
                                } else if (objdata.curLocation.type == 'student') {
                                    objConfig.shareButtons = [
                                        {
                                            id: 'copyto',
                                            label: '复制到' + objdata.curLocation.stuname + '宝宝的相册',
                                            click: function (elem, _swipe) {
                                                move("copy", elem, _swipe);
                                            }
                                        }, {
                                            id: 'moveto',
                                            label: '移动到' + objdata.curLocation.stuname + '宝宝的相册',
                                            click: function (elem, _swipe) {
                                                move("move", elem, _swipe);
                                            }
                                        }
                                    ]
                                } else {
                                    objConfig.shareButtons = [
                                        {
                                            id: 'copyto',
                                            label: '复制到班级相册',
                                            click: function (elem) {
                                                $.alert("在班级相册打开某个班级，就可以复制啦");
                                            }
                                        }, {
                                            id: 'moveto',
                                            label: '移动到班级相册',
                                            click: function (elem) {
                                                $.alert("在班级相册到打某个班级，就可以移动啦");
                                            }
                                        }
                                    ]
                                }
                            }
                        }

                    } else if (objThis.dbParam.roletype == 'home') {//家长
                        objConfig.viewPhoto = viewPhoto;//
                        objConfig.download = downloadPhoto;//
                        if (objThis.dbParam.datatype == 'class') {//没有删除 有捞
                            $(".pswp__button--lao").show();
                            $(".pswp__button--remove").hide();
                            objConfig.laoPhoto = laoPhoto;//
                        } else if (objThis.dbParam.datatype == 'baby') {
                            $(".pswp__button--remove").show();
                            $(".pswp__button--lao").hide();
                            objConfig.removePhoto = removePhoto;//可以删除
                        }
                    }
                    if (objThis.dbParam.roletype == 'home') {
                        parent.photoswipepage = 'photoAlbum';
                    }
                    objswipe.init(arr, curindex, objConfig, function (gallery) {
                        _this.gallery = gallery;
                    });
                })
            });
        };
        this.refresh = function () {
            querydata(false, function (re) {
                objThis.onchange && objThis.onchange("add", re);
                if (re.length > 0) {
                    elem.find(".thelist").prepend(makeHtml(re, 'before'));
                    objThis.endtime = re[0].mtime;
                }
                objThis.myscroll.scroll.refresh();
            });
        }
    }

    return function () {
        //每次都返回一个新对象
        return new AlbumClass();
    };
});
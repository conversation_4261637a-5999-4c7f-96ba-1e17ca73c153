/* 通用样式 */
.out-container {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    overflow: scroll;
    -webkit-overflow-scrolling: touch;
}
.weui-flex_center{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;}
/*公用切换*/
.commontab{align-items: center;justify-content: space-around;font-size: 13px;color: #333333;background: #ffffff;margin: 0 10px;border-radius: 4px;height: 90px;}
.commontab li{position: relative;text-align: center;}
.commontab li img{width: 44px;height: 44px;}
.commontab li.current{color: #4791ff;}
.tab-common6{display: -webkit-box;display: -webkit-flex;display: flex;justify-content: space-around;align-items:center;background: #ffffff;border-bottom: 1px solid #eeeeee;color: #666666;font-size: 13px;height: 35px;}
.tab-common6 li{display: -webkit-box;display: -webkit-flex;display: flex;align-items: center;justify-content: center;width: 100%;height: 100%;position: relative;}
.tab-common6 li.current{color: #02c6ac;}
.tab-common6 li.current:after{content:"";height: 2px;width: 34px;position: absolute;bottom: 0;left: 50%;margin-left: -17px;background: #02c6ac;border-radius: 3px;}
/*按钮*/
.fullbtn-line{font-size: 13px;color: #ffffff;padding: 3px 10px;border-radius: 4px;border: 1px solid #ffffff;}
.cirlebtn-left{color: #ffffff;font-size: 15px;background: #28daab;padding: 0 6px;border-radius: 0 20px 20px 0;position: absolute;left: 0;top: 7px;}
.cirlebtn-right{color: #ffffff;font-size: 15px;background: #28daab;padding: 0 6px;border-radius: 20px 0 0 20px;position: absolute;right: 0;top: 7px;}
/*底部按钮*/
.btmopr-btn{padding: 15px 0;position: fixed;bottom: 0;width: 100%;z-index: 9; background: #fff;box-shadow: 0px 0px 5px 1px #ececec;}
.btmopr-btn a{margin: 0 25px;background: #00cd91;height: 44px;line-height: 44px;font-size: 16px;border-radius: 30px;}
.btmmore-btn{justify-content: center;padding: 15px 0;position: fixed;bottom: 0;width: 100%;z-index: 9; background: #fff;box-shadow: 0px 0px 5px 1px #ececec;}
.btmmore-btn a:first-child{margin: 0 2px;height: 44px;line-height: 44px;font-size: 16px;border-radius: 30px 0 0 30px;background: #dddddd;color: #666666;}
.btmmore-btn a:last-child{margin: 0 2px;height: 44px;line-height: 44px;font-size: 16px;border-radius: 0 30px 30px 0;background: #00cd91;}   	
/*更改 遮罩层/弹框*/
.popup-con{position: fixed;top: 0;bottom: 0;left: 0;right: 0;z-index: 9;display:-webkit-box;display:-webkit-flex;display:flex;align-items: center;justify-content: center;}
.popup-shadow{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,0.5);}
.popup-box{position: relative;background: #ffffff;margin: 0 15px;border-radius: 6px;font-size: 13px;color: #333333;padding:20px 0;width: 100%;}
.popup-box h5{margin: 0 20px;font-weight: bold;font-size: 17px;color: #333333;border-bottom: 1px dashed #dddddd;height: 50px;}
/*无数据*/
.no-infodiv{font-size: 12px;color: #999999;position: absolute;top: 215px;bottom: 0;width: 100%;background: #ffffff;text-align: center;padding-top: 30%;}
/*取消before,after*/
.nobefore:before{content: none!important;}
.noafter:after{content: none!important;}
/*单选框*/
.set-radio-style3 input[type="radio"]{-webkit-appearance: none;position: relative;margin-right: 5px;width: 12px;height: 12px;background: #ffffff;border-radius: 50%;display: inline-block;border: 1px solid #d2d2d2;box-sizing: content-box;outline: none;}
.set-radio-style3 input[type="radio"]:checked{-webkit-appearance: none;border-radius: 50%;display: inline-block;border: 1px solid #03c7ad;box-sizing: content-box;}        
.set-radio-style3 input[type="radio"]:checked:before{content:"";display: inline-block;width: 6px;height: 6px;border-radius: 50%;background: #03c7ad;position: absolute;left: 3px;top: 3px;}
/*对话框箭头*/
.triangle-out-bottom{
	position: absolute;
  	border: 6px solid transparent;
  	border-right-color: #00bb87;
  	bottom: -13px;
  	left: 50%;
  	margin-left: -6px;
  	border-left:6px solid transparent;
  	transform:rotate(-90deg);
	-ms-transform:rotate(-90deg); 	/* IE 9 */
	-moz-transform:rotate(-90deg); 	/* Firefox */
	-webkit-transform:rotate(-90deg); /* Safari å’Œ Chrome */
	-o-transform:rotate(-90deg); 	/* Opera */
}
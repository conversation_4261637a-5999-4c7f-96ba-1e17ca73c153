﻿body { background: #f7f7f7; }
.frames {
    position: absolute;
    top: 0;
    bottom: 45px;
    width: 100%;
    -webkit-overflow-scrolling: touch;
    overflow-y: scroll;
}
.oneFrame {
    display: none;
    width: 100%;
    height: 100%;
    border: none;
    position: absolute;
    left: 0;
    top: 0;
}
.oneopenpage {
    display: none;
}
.oneopenpage, .oneopenpage_overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
}
.oneopenpage_overlay {
    background-color: rgba(0, 0, 0, .6);
    opacity: 0;
    -webkit-transition: opacity .3s;
    transition: opacity .3s
}
.oneopenpage_frame {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 100;
    top: 0;
    bottom: 0;
    background-color: #ffffff;
    border: none;
    border-radius: 0;
    opacity: .6;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
}
.oneopenpage--visible {
    display: block;
}
.oneopenpage--visible .oneopenpage_overlay {
    opacity: 1;
}
.oneopenpage--visible .oneopenpage_frame {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
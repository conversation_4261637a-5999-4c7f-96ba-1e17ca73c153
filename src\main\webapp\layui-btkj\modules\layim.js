/**

 @Name：layim v3.9.1 Pro 商用版
 @Author：贤心
 @Site：http://layim.layui.com
 @License：LGPL

 */

layui.define(['layer', 'laytpl', 'upload'], function (exports) {

    var v = '3.9.1';
    var $ = layui.$;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var device = layui.device();

    var SHOW = 'layui-show', THIS = 'layim-this', MAX_ITEM = 20;

    //回调
    var call = {};

    //对外API
    var LAYIM = function () {
        this.v = v;
        $('body').on('click', '*[layim-event]', function (e) {
            var othis = $(this), methid = othis.attr('layim-event');
            events[methid] ? events[methid].call(this, othis, e) : '';
        });
    };

    //基础配置
    LAYIM.prototype.config = function (options) {
        var skin = [];
        layui.each(Array(5), function (index) {
            skin.push(layui.cache.dir + 'css/modules/layim/skin/' + (index + 1) + '.jpg')
        });
        options = options || {};
        options.skin = options.skin || [];
        layui.each(options.skin, function (index, item) {
            skin.unshift(item);
        });
        options.skin = skin;
        options = $.extend({
            isfriend: !0
            , isgroup: !0
            , voice: 'default.mp3'
        }, options);
        if (!window.JSON || !window.JSON.parse) return;
        init(options);
        return this;
    };

    //监听事件
    LAYIM.prototype.on = function (events, callback) {
        if (typeof callback === 'function') {
            call[events] ? call[events].push(callback) : call[events] = [callback];
        }
        return this;
    };

    //获取所有缓存数据
    LAYIM.prototype.cache = function () {
        return cache;
    };

    //打开一个自定义的会话界面
    LAYIM.prototype.chat = function (data) {
        if (!window.JSON || !window.JSON.parse) return;
        return popchat(data), this;
    };
    LAYIM.prototype.hideLayimMain = function (data) {
        popmin();
    };
    LAYIM.prototype.showLayimMain = function (data) {
        cache.mine = cache.mine || {};
        layimMain.show();
        var local = layui.data('layim')[cache.mine.id] || {};
        delete local.close;
        layui.data('layim', {
            key: cache.mine.id
            , value: local
        });
    };
    //设置聊天界面最小化
    LAYIM.prototype.setChatMin = function () {
        return setChatMin(), this;
    };
    LAYIM.prototype.setChatShow = function () {
        return layimChat.show(), this;
    };

    //设置当前会话状态
    LAYIM.prototype.setChatStatus = function (str) {
        var thatChat = thisChat();
        if (!thatChat) return;
        var status = thatChat.elem.find('.layim-chat-status');
        return status.html(str), this;
    };

    //接受消息
    LAYIM.prototype.getMessage = function (data) {
        return getMessage(data), this;
    };

    //桌面消息通知
    LAYIM.prototype.notice = function (data) {
        return notice(data), this;
    };

    //打开添加好友/群组面板
    LAYIM.prototype.add = function (data) {
        return popAdd(data), this;
    };

    //好友分组面板
    LAYIM.prototype.setFriendGroup = function (data) {
        return popAdd(data, 'setGroup'), this;
    };

    //消息盒子的提醒
    LAYIM.prototype.msgbox = function (nums) {
        return msgbox(nums), this;
    };

    //设置好友在线/离线状态
    LAYIM.prototype.setFriendStatus = function (id, type) {
        var list = $('.layim-friend' + id);
        list[type === 'online' ? 'removeClass' : 'addClass']('layim-list-gray');
    };

    //解析聊天内容
    LAYIM.prototype.content = function (content) {
        return layui.data.content(content);
    };
    //解析聊天内容
    LAYIM.prototype.getContentHtml = function (content) {
        return layui.data.getContentHtml(content);
    };

    var makeOneHistory = function (data) {
        return '<li layim-event="chat" data-type="history" data-id="' + data.id + '"><img src="' + data.avatar + '"><span>' + data.name + '</span><span class="layui-badge unreadnum" style="float:right;' + (data.unreadnum > 0 ? "" : "display:none;") + '">' + (data.unreadnum || 0) + '</span><p>' + layui.data.getmsgsummary(data) + '</p></li>'
    }
    var makeListHistory = function () {
        var strhtml = '';
        for (var i = 0; i < cache.history.length; i++) {
            strhtml += makeOneHistory(cache.history[i]);
        }
        return strhtml;
        /*var arr = ['{{# var length = 0; layui.each(d.history, function(i, data){ length++; }}'
            , '<li layim-event="chat" data-type="history" data-id="{{ data.id }}"><img src="{{ data.avatar }}"><span>{{ data.name||"佚名" }}</span><span class="unreadnum" style="color: red;float:right;{{data.unreadnum>0?"":"display:none;"}}">{{ data.unreadnum }}</span><p>{{ layui.data.getmsgsummary(data) }}</p></li>'
            , '{{# }); if(length === 0){ }}'
            , '<li class="layim-null">' + ( "暂无家长沟通记录") + '</li>'
            , '{{# } }}'].join('');*/
        //return arr;
    }
    //主模板
    var listTpl = function (options) {
        var nodata = {
            friend: "暂无班级宝宝信息"
            , group: "暂无联系人信息"
            , history: "暂无历史会话"
        };

        options = options || {};
        options.item = options.item || ('d.' + options.type);

        return ['{{# var length = 0; layui.each(' + options.item + ', function(i, data){ length++; }}'
            , '<li layim-event="chat" data-type="' + options.type + '" data-index="{{ ' + (options.index || 'i') + ' }}" class="layim-' + (options.type === 'history' ? '{{i}}' : options.type + '{{data.id}}') + ' {{ data.status === "offline" ? "layim-list-gray" : "" }}"><img src="{{ data.avatar }}"><span>{{ data.username||data.groupname||data.name||"佚名" }}</span><p>{{ data.remark||data.sign||"" }}</p><span class="layim-msg-status">new</span></li>'
            , '{{# }); if(length === 0){ }}'
            , '<li class="layim-null">' + (nodata[options.type] || "暂无数据") + '</li>'
            , '{{# } }}'].join('');
    };

    var elemTpl = ['<div class="layui-layim-main">'
        , '<div class="layui-layim-info">'
        , '<div class="layui-layim-user">{{ d.mine.username }}</div>'
        , '<div class="layui-layim-status">'
        , '{{# if(d.mine.status === "online"){ }}'
        , '<span class="layui-icon layim-status-online" layim-event="status" lay-type="show">&#xe617;</span>'
        , '{{# } else if(d.mine.status === "hide") { }}'
        , '<span class="layui-icon layim-status-hide" layim-event="status" lay-type="show">&#xe60f;</span>'
        , '{{# } }}'
        , '<ul class="layui-anim layim-menu-box">'
        , '<li {{d.mine.status === "online" ? "class=layim-this" : ""}} layim-event="status" lay-type="online"><i class="layui-icon">&#xe605;</i><cite class="layui-icon layim-status-online">&#xe617;</cite>在线</li>'
        , '<li {{d.mine.status === "hide" ? "class=layim-this" : ""}} layim-event="status" lay-type="hide"><i class="layui-icon">&#xe605;</i><cite class="layui-icon layim-status-hide">&#xe60f;</cite>隐身</li>'
        , '</ul>'
        , '</div>'
        , '<input class="layui-layim-remark" placeholder="编辑签名" value="{{ d.mine.remark||d.mine.sign||"" }}">'
        , '</div>'
        , '<ul class="layui-unselect layui-layim-tab">'
        , '<li class="layim-this" title="消息" layim-event="tab" lay-type="history">消息</li>'
        , '<li title="宝宝" layim-event="tab" lay-type="friend">宝宝</li>'
        , '<li title="老师" layim-event="tab" lay-type="group">老师</li>'
        , '</ul>'
        , '<ul class="layui-unselect layim-tab-content  layui-show">'
        , '<li>'
        , '<ul class="layui-layim-list layui-show layim-list-history">'
        , '</ul>'
        , '</li>'
        , '</ul>'

        , '<ul class="layui-unselect layim-tab-content layim-list-friend">'
        , '{{# layui.each(d.friend, function(index, item){  }}'
        , '<li>'
        , '<h5 layim-event="spread" lay-type="false" classno="{{item.classno}}"><i class="layui-icon">&#xe602;</i><span>{{ item.claname }}</span><em>(<cite class="layim-count"> {{ item.stucount }}</cite>)</em></h5>'
        , '<ul class="layui-layim-list">'
        , '</ul>'
        , '</li>'
        , '{{# }); }}'
        , '</ul>'

        , '<ul class="layui-unselect layim-tab-content">'
        , '<li>'
        , '<ul class="layui-layim-list layui-show layim-list-group">'
        , listTpl({
            type: 'group'
        })
        , '</ul>'
        , '</li>'
        , '</ul>'
        , '<ul class="layui-unselect layim-tab-content">'
        , '<li>'
        , '<ul class="layui-layim-list layui-show" id="layui-layim-search"></ul>'
        , '</li>'
        , '</ul>'
        , '<ul class="layui-unselect layui-layim-tool">'
        //, '<li class="layui-icon layim-tool-search" layim-event="search" title="搜索">&#xe615;</li>'
        //, '{{# if(d.base.msgbox){ }}'
        //, '<li class="layui-icon layim-tool-msgbox" layim-event="msgbox" title="查找聊天记录">&#xe645;<span class="layui-anim"></span></li>'
        //, '{{# } }}'
        , '<li class="layui-icon layim-tool-skin" layim-event="skin" title="更换背景"><img src="images/desktop/icon_skin.png"></li>'
        , '<li class="layui-icon" title="搜索" layim-event="search"><img src="images/desktop/icon_search.png"></li>'
        , '<li class="layui-icon" title="聊天记录" layim-event="searchrecord" style="display: none"><i class="layui-icon" style="font-size: 20px;">&#xe60e;</i></li>'
        , '</ul>'
        , '<div class="layui-layim-search"><input><label class="layui-icon" layim-event="closeSearch">&#x1007;</label></div>'
        , '</div>'

        , '<div class="layui-layim-search search-content" style="display: none;">'
        , '<div class="layui-layim-searchtop" style="background: #ffffff;height: 38px;">'
        , '<i class="iconfont icon_search" style="margin: 5px 0 0 10px;">'
        , '</i>'
        , '<input type="text" placeholder="输入学生姓名" style="width: auto;vertical-align: top;margin-top: 4px;background: none;"/>'
        , '<i class="iconfont icon_close_white" layim-event="closeSearch" style="margin: -2px 10px 0 0;float: right;">'
        , '</i>'
        , '</div>'
        , '<div class="layui-layim-divsearchresult">'
        , '<div class="searchresult searchresultbaby" style="display: none">'
        , '<h5>宝宝</h5>'
        , '<ul class="layui-layim-list layui-show layim-list-student">'
        , '<li layim-event="chat" data-type="history" data-index="138" class="layim-138 ">'
        , '<img src="http://tbfile.oss-cn-beijing.aliyuncs.com/wxhome/test/tbeqcL08rY5LPlTsi2V5JP_IvM0B_q9xEEUXniIpMX2m8p3s0u0Gcr-KFUUjm6m41554024292183.jpg">'
        , '<span>' + 'name1' + '</span>'
        , '<p>' + 555 + '</p>'
        , '</li>'
        , '</ul>'
        , '</div>'
        , '<div class="searchresult searchresultteacher" style="display: none">'
        , '<h5>老师</h5>'
        , '<ul class="layui-layim-list layui-show layim-list-student">'
        , '<li layim-event="chat" data-type="history" data-index="138" class="layim-138 ">'
        , '<img src="http://tbfile.oss-cn-beijing.aliyuncs.com/wxhome/test/tbeqcL08rY5LPlTsi2V5JP_IvM0B_q9xEEUXniIpMX2m8p3s0u0Gcr-KFUUjm6m41554024292183.jpg">'
        , '<span>' + 'name1' + '</span>'
        , '<p>' + 555 + '</p>'
        , '</li>'
        , '</ul>'
        , '</div>'
        , '</div>'
        , '</div>'
    ].join('');

    //换肤模版
    var elemSkinTpl = ['<ul class="layui-layim-skin">'
        , '{{# layui.each(d.skin, function(index, item){ }}'
        , '<li><img layim-event="setSkin" src="{{ item }}"></li>'
        , '{{# }); }}'
        , '<li layim-event="setSkin"><cite>简约</cite></li>'
        , '</ul>'].join('');
    layui.data.getsrcbyptype = function (obj) {
        if (obj.ptype == 1) {
            return 'images/newtong/defaultbaba.png';
        } else if (obj.ptype == 2) {
            return 'images/newtong/defaultmama.png';
        } else {
            return 'images/newtong/defaultbaba.png';
        }
    }
    layui.data.getrolebyptype = function (obj) {
        var commonptype = ["", "爸爸", "妈妈", "爷爷", "奶奶", "姥爷", "姥姥", "叔叔", "阿姨", "哥哥", "姐姐"];
        return commonptype[obj.ptype];
    }

    //聊天主模板 d.data.arrweixin.length
    var elemChatTpl = ['<div class="layim-chat layim-chat-{{d.data.type}}{{d.first ? " layui-show" : ""}}">'
        , '<div class="layui-unselect layim-chat-title">'
        , '<div class="layim-chat-other">'
        , '<img class="layim-{{ d.data.type }}{{ d.data.id }}" src="{{ d.data.avatar }}"><span class="layim-chat-username" layim-event="{{ d.data.type==="group" ? \"groupMembers\" : \"\" }}">{{ d.data.name||"佚名" }} {{d.data.temporary ? "<cite>临时会话</cite>" : ""}} {{# if(d.data.type==="group"){ }} <em class="layim-chat-members"></em><i class="layui-icon">&#xe61a;</i> {{# } }}</span>'
        , '<p class="layim-chat-status" layim-event="groupMembers">'
        , '{{(d.data.arrweixin&&d.data.arrweixin.length?d.data.arrweixin.length+"个家长绑定":"无家长绑定")}}<i class="layui-icon">&#xe61a;</i>'
        /*, '{{# layui.each(d.data.arrweixin, function(index, item){ }}'
        , '<span><img layim-event="openweixin" src="{{ layui.data.getsrcbyptype(item) }}"></span>'
        , '{{# }); }}'*/
        , '</p>'
        , '</div>'
        , '</div>'
        , '<div class="layim-chat-main">'
        , '<ul></ul>'
        , '</div>'
        , '<div class="layim-chat-footer">'
        , '<div class="layui-unselect layim-chat-tool" data-json="{{encodeURIComponent(JSON.stringify(d.data))}}">'
        , '<span class="layui-icon layim-tool-face" title="选择表情" layim-event="face">&#xe60c;</span>'
        , '{{# if(d.base && d.base.uploadImage){ }}'
        , '<span class="layui-icon layim-tool-image" title="上传图片" layim-event="image">&#xe60d;<!--<input type="file" name="file">--></span>'
        , '{{# }; }}'
        , '{{# if(d.base && d.base.uploadFile){ }}'
        , '<span class="layui-icon layim-tool-image" title="发送文件" layim-event="image" data-type="file">&#xe61d;<input type="file" name="file"></span>'
        , '{{# }; }}'
        , '{{# if(d.base && d.base.isAudio){ }}'
        , '<span class="layui-icon layim-tool-audio" title="发送网络音频" layim-event="media" data-type="audio">&#xe6fc;</span>'
        , '{{# }; }}'
        , '{{# if(d.base && d.base.isVideo){ }}'
        , '<span class="layui-icon layim-tool-video" title="发送网络视频" layim-event="media" data-type="video">&#xe6ed;</span>'
        , '{{# }; }}'
        , '{{# layui.each(d.base.tool, function(index, item){ }}'
        , '<span class="layui-icon layim-tool-{{item.alias}}" title="{{item.title}}" layim-event="extend" lay-filter="{{ item.alias }}">{{item.icon}}</span>'
        , '{{# }); }}'
        , '{{# if(d.base && d.base.chatLog){ }}'
        , '<span class="layim-tool-log" layim-event="chatLog"><i class="layui-icon">&#xe60e;</i>聊天记录</span>'
        , '{{# }; }}'
        , '</div>'
        , '<div class="layim-chat-textarea"><textarea></textarea></div>'
        , '<div class="layim-chat-bottom">'
        , '<div class="layim-chat-send">'
        , '{{# if(!d.base.brief){ }}'
        , '<span class="layim-send-close" layim-event="closeThisChat">关闭</span>'
        , '{{# } }}'
        , '<span class="layim-send-btn" layim-event="send">发送</span>'
        , '<span class="layim-send-set" layim-event="setSend" lay-type="show"><em class="layui-edge"></em></span>'
        , '<ul class="layui-anim layim-menu-box">'
        , '<li {{d.local.sendHotKey !== "Ctrl+Enter" ? "class=layim-this" : ""}} layim-event="setSend" lay-type="Enter"><i class="layui-icon">&#xe605;</i>按Enter键发送消息</li>'
        , '<li {{d.local.sendHotKey === "Ctrl+Enter" ? "class=layim-this" : ""}} layim-event="setSend"  lay-type="Ctrl+Enter"><i class="layui-icon">&#xe605;</i>按Ctrl+Enter键发送消息</li>'
        , '</ul>'
        , '</div>'
        , '</div>'
        , '</div>'
        , '</div>'].join('');

    //添加好友群组模版
    var elemAddTpl = ['<div class="layim-add-box">'
        , '<div class="layim-add-img"><img class="layui-circle" src="{{ d.data.avatar }}"><p>{{ d.data.name||"" }}</p></div>'
        , '<div class="layim-add-remark">'
        , '{{# if(d.data.type === "friend" && d.type === "setGroup"){ }}'
        , '<p>选择分组</p>'
        , '{{# } if(d.data.type === "friend"){ }}'
        , '<select class="layui-select" id="LAY_layimGroup">'
        , '{{# layui.each(d.data.group, function(index, item){ }}'
        , '<option value="{{ item.id }}">{{ item.groupname }}</option>'
        , '{{# }); }}'
        , '</select>'
        , '{{# } }}'
        , '{{# if(d.data.type === "group"){ }}'
        , '<p>请输入验证信息</p>'
        , '{{# } if(d.type !== "setGroup"){ }}'
        , '<textarea id="LAY_layimRemark" placeholder="验证信息" class="layui-textarea"></textarea>'
        , '{{# } }}'
        , '</div>'
        , '</div>'].join('');

    //聊天内容列表模版
    var elemChatMain = ['<li {{ (d.isyey||d.mine) ? "class=layim-chat-mine" : "" }} {{# if(d.cid){ }}data-cid="{{d.cid}}"{{# } }}>'
        , '<div class="layim-chat-user"><img src="{{ d.avatar }}"><cite>'
        , '{{# if(d.mine){ }}'
        , '<i>{{ layui.data.date(d.timestamp) }}</i>{{ d.username||d.name||"佚名" }}'
        , '{{# } else { }}'
        , '{{ d.name||"佚名" }}<i>{{ layui.data.date(d.timestamp) }}</i>'
        , '{{# } }}'
        , '</cite></div>'
        , '<div class="layim-chat-text">{{ layui.data.getContentHtml(d) }}</div>'
        , '</li>'].join('');

    var elemChatList = '<li class="layim-{{ d.data.type }}{{ d.data.id }} layim-chatlist-{{ d.data.type }}{{ d.data.id }} layim-this" layim-event="tabChat"><img src="{{ d.data.avatar }}"><span>{{ d.data.name||"佚名" }}</span>{{# if(!d.base.brief){ }}<i class="layui-icon" layim-event="closeChat">&#x1007;</i>{{# } }}</li>';

    //补齐数位
    var digit = function (num) {
        return num < 10 ? '0' + (num | 0) : num;
    };

    //转换时间
    layui.data.date = function (timestamp) {
        var d = timestamp ? new Date(timestamp.substring(0, 19).replace(/-/g, '/')) : new Date();
        return d.getFullYear() + '-' + digit(d.getMonth() + 1) + '-' + digit(d.getDate())
            + ' ' + digit(d.getHours()) + ':' + digit(d.getMinutes()) + ':' + digit(d.getSeconds());
    };
    //表情库
    var faces = function () {
        var alt = ["[微笑]", "[嘻嘻]", "[哈哈]", "[可爱]", "[可怜]", "[挖鼻]", "[吃惊]", "[害羞]", "[挤眼]", "[闭嘴]", "[鄙视]", "[爱你]", "[泪]", "[偷笑]", "[亲亲]", "[生病]", "[太开心]", "[白眼]", "[右哼哼]", "[左哼哼]", "[嘘]", "[衰]", "[委屈]", "[吐]", "[哈欠]", "[抱抱]", "[怒]", "[疑问]", "[馋嘴]", "[拜拜]", "[思考]", "[汗]", "[困]", "[睡]", "[钱]", "[失望]", "[酷]", "[色]", "[哼]", "[鼓掌]", "[晕]", "[悲伤]", "[抓狂]", "[黑线]", "[阴险]", "[怒骂]", "[互粉]", "[心]", "[伤心]", "[猪头]", "[熊猫]", "[兔子]", "[ok]", "[耶]", "[good]", "[NO]", "[赞]", "[来]", "[弱]", "[草泥马]", "[神马]", "[囧]", "[浮云]", "[给力]", "[围观]", "[威武]", "[奥特曼]", "[礼物]", "[钟]", "[话筒]", "[蜡烛]", "[蛋糕]"],
            arr = {};
        layui.each(alt, function (index, item) {
            arr[item] = layui.cache.dir + 'images/face/' + index + '.gif';
        });
        return arr;
    }();
    layui.data.contentEmojiToImg = function (text) {
        return text;
    }
    layui.data.getmsgsummary = function (obj) {
        var strname = "";
        if (obj.fromtype == 1 && obj.ptype) {
            if (obj.ptype == 1) {
                strname = '[' + obj.name + '爸爸]';
            } else if (obj.ptype == 2) {
                strname = '[' + obj.name + '妈妈]';
            } else {
                strname = '[' + obj.name + '家长]';
            }
        } else {//老师说的
            if (obj.username) strname = '[' + obj.username + ']';
        }
        var msgsummry = "";
        if (obj.msgtype == 1) {
            if (obj.objmsgcontent.text.indexOf("<") > -1) {
                var _dom = $('<div>' + obj.objmsgcontent.text + '</div>>');
                msgsummry = _dom.text();
            } else {
                msgsummry = obj.objmsgcontent.text;
            }
        } else if (obj.msgtype == 2) {
            msgsummry = "[图片]";
        } else if (obj.msgtype == 3) {
            msgsummry = "[视频]";
        } else if (obj.msgtype == 11) {
            msgsummry = "[请假]";
        } else if (obj.msgtype == 12) {
            msgsummry = "[取消请假]";
        } else if (obj.msgtype == 15 || obj.msgtype == 16 || obj.msgtype == 17 || obj.msgtype == 18 || obj.msgtype == 19) {
            msgsummry = "[喂药]";
        }
        return strname ? strname + msgsummry : "";
    }
    layui.data.getContentHtml = function (obj) {
        var content = "";
        var objmsgcontent = obj.objmsgcontent;
        if (obj.msgtype == '1') {
            content = layui.data.contentEmojiToImg(objmsgcontent.text);
        } else if (obj.msgtype == '2') {
            var sourceUrl = objdata.ossPrefix + objmsgcontent.url;
            var smallUrl = sourceUrl + '?x-oss-process=image/resize,w_100';
            content = '<div style="position:relative;float:right;"><img class="small" layim-event="viewimage" src="' + smallUrl + '" big="' + sourceUrl + '" /><div class="model" style="position:absolute;top:0;left:0;width:100%;height:100%;background: #c0c0c0;display:none;"></div></div>';
        } else if (obj.msgtype == '3') {
            var thumbUrl = objdata.ossPrefix + objmsgcontent.url + '?x-oss-process=image/resize,w_100';
            var videoUrl = objdata.ossPrefix + objmsgcontent.videourl;
            content = '<div style="position:relative;float:right;"><img class="videothumb" src="' + thumbUrl + '" videourl="' + videoUrl + '" /><div class="model" style="position:absolute;top:0;left:0;width:100%;height:100%;background: #c0c0c0;display:none;"></div></div>';
        } else if (obj.msgtype == '4') {
            var thumbUrl = objdata.ossPrefix + objmsgcontent.url + '?x-oss-process=image/resize,w_100';
            var videoUrl = objdata.ossPrefix + objmsgcontent.videourl;
            content = '<div style="position:relative;float:right;"><img class="videothumb" src="' + thumbUrl + '" videourl="' + videoUrl + '" /><div class="model" style="position:absolute;top:0;left:0;width:100%;height:100%;background: #c0c0c0;display:none;"></div></div>';
        } else if (obj.msgtype == '5') {//TODO 错误的url
            var voiceUrl = '';//objdata.ossPrefix +data.msg.url;
            var voicetype = '';
            if (objmsgcontent.mp3url) {
                voicetype = 'mp3';
                voiceUrl = objdata.ossPrefix + objmsgcontent.mp3url;
            } else {
                voicetype = 'amr';
                voiceUrl = objdata.ossPrefix + objmsgcontent.url;
            }
            content = '<div style="position:relative;float:right;"><img class="voicedefault" src="images/defaultvoice.png" voiceUrl="' + voiceUrl + '" voicetype="' + voicetype + '" />'
                + (objmsgcontent.seconds ? '<span style="position:absolute;right: -29px;top: 5px;">' + objmsgcontent.seconds + '”</span>' : '')
                + '<div class="model" style="position:absolute;top:0;left:0;width:100%;height:100%;background: #c0c0c0;display:none;"></div></div>';


        } else if (obj.msgtype == '11' || obj.msgtype == '12') {
            if (objmsgcontent.description) {
                return objmsgcontent.description;
            } else {
                return obj.msgtype == '11' ? "请假" : "取消请假";
            }
        } else if (obj.msgtype == 15 || obj.msgtype == 16 || obj.msgtype == 17 || obj.msgtype == 18 || obj.msgtype == 19) {
            return objmsgcontent.description;
        } else if (objmsgcontent.description) {
            return objmsgcontent.description;
        } else {
            return "不可识别的消息";
        }
        return content;
    };
    //转换内容
    layui.data.content = function (content) {
        //支持的html标签
        var html = function (end) {
            return new RegExp('\\n*\\[' + (end || '') + '(code|pre|div|span|p|table|thead|th|tbody|tr|td|ul|li|ol|li|dl|dt|dd|h2|h3|h4|h5)([\\s\\S]*?)\\]\\n*', 'g');
        };
        content = (content || '').replace(/&(?!#?[a-zA-Z0-9]+;)/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/'/g, '&#39;').replace(/"/g, '&quot;') //XSS
            .replace(/@(\S+)(\s+?|$)/g, '@<a href="javascript:;">$1</a>$2') //转义@

            .replace(/face\[([^\s\[\]]+?)\]/g, function (face) {  //转义表情
                var alt = face.replace(/^face/g, '');
                return '<img alt="' + alt + '" title="' + alt + '" src="' + faces[alt] + '">';
            }).replace(/img\[([^\s]+?)\]/g, function (img) {  //转义图片
                return '<img class="layui-layim-photos" src="' + img.replace(/(^img\[)|(\]$)/g, '') + '">';
            }).replace(/file\([\s\S]+?\)\[[\s\S]*?\]/g, function (str) { //转义文件
                var href = (str.match(/file\(([\s\S]+?)\)\[/) || [])[1];
                var text = (str.match(/\)\[([\s\S]*?)\]/) || [])[1];
                if (!href) return str;
                return '<a class="layui-layim-file" href="' + href + '" download target="_blank"><i class="layui-icon">&#xe61e;</i><cite>' + (text || href) + '</cite></a>';
            }).replace(/audio\[([^\s]+?)\]/g, function (audio) {  //转义音频
                return '<div class="layui-unselect layui-layim-audio" layim-event="playAudio" data-src="' + audio.replace(/(^audio\[)|(\]$)/g, '') + '"><i class="layui-icon">&#xe652;</i><p>音频消息</p></div>';
            }).replace(/video\[([^\s]+?)\]/g, function (video) {  //转义音频
                return '<div class="layui-unselect layui-layim-video" layim-event="playVideo" data-src="' + video.replace(/(^video\[)|(\]$)/g, '') + '"><i class="layui-icon">&#xe652;</i></div>';
            }).replace(/a\([\s\S]+?\)\[[\s\S]*?\]/g, function (str) { //转义链接
                var href = (str.match(/a\(([\s\S]+?)\)\[/) || [])[1];
                var text = (str.match(/\)\[([\s\S]*?)\]/) || [])[1];
                if (!href) return str;
                return '<a href="' + href + '" target="_blank">' + (text || href) + '</a>';
            }).replace(html(), '\<$1 $2\>').replace(html('/'), '\</$1\>') //转移HTML代码
            .replace(/\n/g, '<br>') //转义换行
        return content;
    };

    //Ajax
    var post = function (options, callback, tips) {
        options = options || {};
        return $.ajax({
            url: options.url
            , type: options.type || 'get'
            , data: options.data
            , dataType: options.dataType || 'json'
            , cache: false
            , success: function (res) {
                res.code == 0
                    ? callback && callback(res.data || {})
                    : layer.msg(res.msg || ((tips || 'Error') + ': LAYIM_NOT_GET_DATA'), {
                    time: 5000
                });
            }, error: function (err, msg) {
                window.console && console.log && console.error('LAYIM_DATE_ERROR：' + msg);
            }
        });
    };

    //处理初始化信息
    var cache = {message: {}, chat: []};
    var init = function (options) {
        var init = options.init || {}
        var mine = init.mine || {}
            , local = layui.data('layim')[mine.id] || {}
            , obj = {
            base: options
            , local: local
            , mine: mine
            , history: local.history || {}
        };
        var create = function (data) {
            var mine = data.mine || {};
            var local = layui.data('layim')[mine.id] || {};
            var obj = {
                base: options //基础配置信息
                , local: local //本地数据
                , mine: mine //我的用户信息
                , friend: data.friend || [] //联系人信息
                , group: data.group || [] //群组信息
                , history: data.history || [] //历史会话信息
            };
            cache = $.extend(cache, obj);
            popim(laytpl(elemTpl).render(obj));

            //判断是否有展开的classno
            if (local.openclass) {
                $('[layim-event="spread"][classno="' + local.openclass + '"]').trigger('click');
            }
            layui.each(call.ready, function (index, item) {
                item && item(obj);
            });
        };
        cache = $.extend(cache, obj);
        if (options.brief) {
            return layui.each(call.ready, function (index, item) {
                item && item(obj);
            });
        }
        init.url ? post(init, create, 'INIT') : create(init);
    };

    //显示主面板
    var layimMain;
    var popim = function (content) {
        return layer.open({
            type: 1
            , area: ['260px', '520px']
            , skin: 'layui-box layui-layim'
            , title: '&#8203;'
            , offset: 'rb'
            , id: 'layui-layim'
            , shade: false
            , anim: 2
            , resize: false
            , content: content
            , success: function (layero, index) {
                layimMain = layero;
                layer.style(index, {
                    zIndex: '111111111'
                })
                if (cache.base.bottom) {//如果有底部距离
                    $(window).resize(function () {
                        resizeBottom();
                    });
                    function resizeBottom() {
                        if (!layero.is(":hidden")) {
                            layero.css('top', layero.offset().top - cache.base.bottom - 10);
                            layero.css('left', layero.offset().left - 160);
                        }
                    }

                    resizeBottom();
                }
                setSkin(layero);
                //设置历史记录数据
                var strhtml = makeListHistory(cache.history);
                $(".layim-list-history").html(strhtml);

                if (cache.base.right) {
                    layero.css('margin-left', '-' + cache.base.right);
                }

                //按最新会话重新排列
                var arr = [], historyElem = layero.find('.layim-list-history');
                historyElem.find('li').each(function () {
                    arr.push($(this).prop('outerHTML'))
                });
                if (arr.length > 0) {
                    arr.reverse();
                    historyElem.html(arr.join(''));
                }

                banRightMenu();
                events.sign();
            }
            , cancel: function (index) {
                popmin();
                var local = layui.data('layim')[cache.mine.id] || {};
                local.close = true;
                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });
                return false;
            }
        });
    };

    //屏蔽主面板右键菜单
    var banRightMenu = function () {
        layimMain.on('contextmenu', function (event) {
            event.cancelBubble = true
            event.returnValue = false;
            return false;
        });

        var hide = function () {
            layer.closeAll('tips');
        };

        //自定义历史会话右键菜单
        layimMain.find('.layim-list-history').on('contextmenu', 'li', function (e) {
            var othis = $(this);
            var html = '<ul data-id="' + othis[0].id + '" data-index="' + othis.data('index') + '"><li layim-event="menuHistory" data-type="one">移除该会话</li><li layim-event="menuHistory" data-type="all">清空全部会话列表</li></ul>';

            if (othis.hasClass('layim-null')) return;

            layer.tips(html, this, {
                tips: 1
                , time: 0
                , anim: 5
                , fixed: true
                , skin: 'layui-box layui-layim-contextmenu'
                , success: function (layero) {
                    var stopmp = function (e) {
                        stope(e);
                    };
                    layero.off('mousedown', stopmp).on('mousedown', stopmp);
                }
            });
            $(document).off('mousedown', hide).on('mousedown', hide);
            $(window).off('resize', hide).on('resize', hide);

        });
    }

    //主面板最小化状态
    var popmin = function (content) {
        $("#btnopenim").data("isopen", false);
        if (layimMain) {
            layimMain.hide();
            layui.each(call.layimMainClose, function (index, item) {
                item && item();
            });
        }
    };

    //显示聊天面板
    var layimChat, layimMin, chatIndex, To = {};
    var popchat = function (data, othis) {
        data = data || {};

        var chat = $('#layui-layim-chat'), render = {
            data: data
            , base: cache.base
            , local: cache.local
        };

        if (!data.id) {
            return layer.msg('非法用户');
        }

        if (chat[0]) {
            var list = layimChat.find('.layim-chat-list');
            var listThat = list.find('.layim-chatlist-' + data.type + data.id);
            var hasFull = layimChat.find('.layui-layer-max').hasClass('layui-layer-maxmin');
            var chatBox = chat.children('.layim-chat-box');

            //如果是最小化，则还原窗口
            if (layimChat.css('display') === 'none') {
                layimChat.show();
            }

            if (layimMin) {
                layer.close(layimMin.attr('times'));
            }

            //如果出现多个聊天面板
            if (list.find('li').length === 1 && !listThat[0]) {
                hasFull || layimChat.css('width', 800);
                list.css({
                    height: layimChat.height()
                }).show();
                chatBox.css('margin-left', '200px');
            }

            //打开的是非当前聊天面板，则新增面板
            if (!listThat[0]) {
                list.append(laytpl(elemChatList).render(render));
                var cbox = $(laytpl(elemChatTpl).render(render));
                chatBox.append(cbox);
                syncGray(data);
                resizeChat();
                changeChat(list.find('.layim-chatlist-' + data.type + data.id));
                viewChatlog(othis, cbox);
            } else {
                changeChat(list.find('.layim-chatlist-' + data.type + data.id));
            }

            setHistory(data);
            hotkeySend();

            return chatIndex;
        }

        render.first = !0;

        var index = chatIndex = layer.open({
            type: 1
            , area: '600px'
            , skin: 'layui-box layui-layim-chat'
            , id: 'layui-layim-chat'
            , title: '&#8203;'
            , shade: false
            , maxmin: true
            , offset: data.offset || 'auto'
            , anim: data.anim || 0
            , closeBtn: cache.base.brief ? false : 1
            , content: laytpl('<ul class="layui-unselect layim-chat-list">' + elemChatList + '</ul><div class="layim-chat-box">' + elemChatTpl + '</div>').render(render)
            , success: function (layero, index) {
                layimChat = layero;

                layero.css({
                    'min-width': '500px'
                    , 'min-height': '420px'
                });
                syncGray(data);

                typeof data.success === 'function' && data.success(layero);

                hotkeySend();
                setSkin(layero);
                setHistory(data);

                viewChatlog(othis, layero);
                showOffMessage();

                //聊天窗口的切换监听
                layui.each(call.chatChange, function (index, item) {
                    item && item(thisChat());
                });
                layer.style(index, {
                    zIndex: 888888888
                });
            }
            , full: function (layero) {
                layer.style(index, {
                    width: '100%'
                    , height: '100%'
                }, true);
                resizeChat();
            }
            , resizing: resizeChat
            , restore: resizeChat
            , min: function () {
                setChatMin();
                return false;
            }
            , end: function () {
                layer.closeAll('tips');
                layimChat = null;
                layui.each(call.chatClose, function (index, item) {
                    item && item();
                });
            }
        });
        return index;
    };

    //同步置灰状态
    var syncGray = function (data) {
        $('.layim-' + data.type + data.id).each(function () {
            if ($(this).hasClass('layim-list-gray')) {
                layui.layim.setFriendStatus(data.id, 'offline');
            }
        });
    };

    //重置聊天窗口大小
    var resizeChat = function () {
        var list = layimChat.find('.layim-chat-list')
            , chatMain = layimChat.find('.layim-chat-main')
            , chatHeight = layimChat.height();
        list.css({
            height: chatHeight
        });
        chatMain.css({
            height: chatHeight - 20 - 80 - 158
        })
    };

    //设置聊天窗口最小化 & 新消息提醒
    var setChatMin = function (newMsg) {
        var thatChat = newMsg || thisChat().data, base = layui.layim.cache().base;
        if (layimChat && !newMsg) {
            layimChat.hide();
        }
        layui.each(call.chatMin, function (index, item) {
            item && item();
        });
        /*layer.close(setChatMin.index);
        setChatMin.index = layer.open({
            type: 1
            , title: false
            , skin: 'layui-box layui-layim-min'
            , shade: false
            , closeBtn: false
            , anim: thatChat.anim || 2
            , offset: 'b'
            , move: '#layui-layim-min'
            , resize: false
            , area: ['182px', '50px']
            , content: '<img id="layui-layim-min" src="' + thatChat.avatar + '"><span>' + thatChat.name + '</span>'
            , success: function (layero, index) {
                if (!newMsg) layimMin = layero;

                if (base.minRight) {
                    layer.style(index, {
                        left: $(window).width() - layero.outerWidth() - parseFloat(base.minRight)
                    });
                }
                if (cache.base.bottom) {//如果有底部距离
                    $(window).resize(function () {
                        resizeBottom();
                    });
                    function resizeBottom() {
                        layero.css('top', layero.offset().top - cache.base.bottom);
                    }

                    resizeBottom();
                }
                layero.find('.layui-layer-content span').on('click', function () {
                    layer.close(index);
                    newMsg ? layui.each(cache.chat, function (i, item) {
                        popchat(item);
                    }) : layimChat.show();
                    if (newMsg) {
                        cache.chat = [];
                        chatListMore();
                    }
                });
                layero.find('.layui-layer-content img').on('click', function (e) {
                    stope(e);
                });
            }
        });*/
    };

    //打开添加好友、群组面板、好友分组面板
    var popAdd = function (data, type) {
        data = data || {};
        layer.close(popAdd.index);
        return popAdd.index = layer.open({
            type: 1
            , area: '430px'
            , title: {
                friend: '添加好友'
                , group: '加入群组'
            }[data.type] || ''
            , shade: false
            , resize: false
            , btn: type ? ['确认', '取消'] : ['发送申请', '关闭']
            , content: laytpl(elemAddTpl).render({
                data: {
                    name: data.username || data.groupname
                    , avatar: data.avatar
                    , group: data.group || parent.layui.layim.cache().friend || []
                    , type: data.type
                }
                , type: type
            })
            , yes: function (index, layero) {
                var groupElem = layero.find('#LAY_layimGroup')
                    , remarkElem = layero.find('#LAY_layimRemark')
                if (type) {
                    data.submit && data.submit(groupElem.val(), index);
                } else {
                    data.submit && data.submit(groupElem.val(), remarkElem.val(), index);
                }
            }
        });
    };

    //切换聊天
    var changeChat = function (elem, del) {
        elem = elem || $('.layim-chat-list .' + THIS);
        var index = elem.index() === -1 ? 0 : elem.index();
        var str = '.layim-chat', cont = layimChat.find(str).eq(index);
        var hasFull = layimChat.find('.layui-layer-max').hasClass('layui-layer-maxmin');

        if (del) {

            //如果关闭的是当前聊天，则切换聊天焦点
            if (elem.hasClass(THIS)) {
                changeChat(index === 0 ? elem.next() : elem.prev());
            }

            var length = layimChat.find(str).length;

            //关闭聊天界面
            if (length === 1) {
                return layer.close(chatIndex);
            }

            elem.remove();
            cont.remove();

            //只剩下1个列表，隐藏左侧区块
            if (length === 2) {
                layimChat.find('.layim-chat-list').hide();
                if (!hasFull) {
                    layimChat.css('width', '600px');
                }
                layimChat.find('.layim-chat-box').css('margin-left', 0);
            }

            return false;
        }

        elem.addClass(THIS).siblings().removeClass(THIS);
        cont.addClass(SHOW).siblings(str).removeClass(SHOW);
        cont.find('textarea').focus();

        //聊天窗口的切换监听
        layui.each(call.chatChange, function (index, item) {
            item && item(thisChat());
        });
        showOffMessage();
    };

    //展示存在队列中的消息
    var showOffMessage = function () {
        var thatChat = thisChat();
        var message = cache.message[thatChat.data.type + thatChat.data.id];
        if (message) {
            //展现后，删除队列中消息
            delete cache.message[thatChat.data.type + thatChat.data.id];
        }
    };

    //获取当前聊天面板
    var thisChat = LAYIM.prototype.thisChat = function () {
        if (!layimChat) return;
        var index = $('.layim-chat-list .' + THIS).index();
        var cont = layimChat.find('.layim-chat').eq(index);
        var to = JSON.parse(decodeURIComponent(cont.find('.layim-chat-tool').data('json')));
        return {
            elem: cont
            , data: to
            , textarea: cont.find('textarea')
        };
    };

    //记录初始背景
    var setSkin = function (layero) {
        var local = layui.data('layim')[cache.mine.id] || {}
            , skin = local.skin;
        layero.css({
            'background-image': skin ? 'url(' + skin + ')' : function () {
                return cache.base.initSkin
                    ? 'url(' + (layui.cache.dir + 'css/modules/layim/skin/' + cache.base.initSkin) + ')'
                    : 'none';
            }()
        });
    };

    //记录历史会话
    var setHistory = function (data) {
        return;
        //1.查看cache.history里有没有
        var has = false;
        if (cache.history) {
            for (var i = 0; i < cache.history.length; i++) {
                if (cache.history[i].id == data.id) {
                    has = true;
                }
            }
        }
        if (has)return;
        //2.没有则增加
        cache.history.unshift(data);
        if (!layimMain) return;

        var historyElem = layimMain.find('.layim-list-history')

        historyElem.find('.layim-null').remove();


        /*debugger
        var local = layui.data('layim')[cache.mine.id] || {};
        var obj = {}, history = local.history || {};
        var is = history[data.type + data.id];
;

        data.historyTime = new Date().getTime();
        history[data.type + data.id] = data;

        local.history = history;

        layui.data('layim', {
            key: cache.mine.id
            , value: local
        });

        if (is) return;

        obj[data.type + data.id] = data;

        var historyList = laytpl(listTpl({
            type: 'history'
            , item: 'd.data'
        })).render({data: obj});

        historyElem.prepend(historyList);
        historyElem.find('.layim-null').remove();*/
    };
    var sendImageMessage = function (arr) {
        var data = {
            username: cache.mine ? cache.mine.username : '访客'
            , avatar: cache.mine ? cache.mine.avatar : (layui.cache.dir + 'css/pc/layim/skin/logo.jpg')
            , id: cache.mine ? cache.mine.id : null
            , mine: true
        };
        var thatChat = thisChat(), ul = thatChat.elem.find('.layim-chat-main ul');
        for (var i = 0; i < arr.length; i++) {
            data.msgtype = 2;
            data.objmsgcontent = {
                url: arr[i].url
            };
            ul.append(laytpl(elemChatMain).render(data));
            var param = {
                mine: data
                , to: thatChat.data
            }, message = {
                fromtype: 2//老师发的
                , username: param.mine.username
                , avatar: param.mine.avatar
                , id: param.to.id
                , name: param.to.name
                , type: param.to.type || 'friend'
                , msgtype: data.msgtype
                , objmsgcontent: data.objmsgcontent
                , timestamp: new Date().getTime()
                , mine: true
            };
            changeRecentHtml(message);

            layui.each(call.sendMessage, function (index, item) {
                item && item(param);
            });
            chatListMore();
        }
    }
    //发送消息
    var sendTextMessage = function () {
        var data = {
            username: cache.mine ? cache.mine.username : '访客'
            , avatar: cache.mine ? cache.mine.avatar : (layui.cache.dir + 'css/pc/layim/skin/logo.jpg')
            , id: cache.mine ? cache.mine.id : null
            , mine: true
        };
        var thatChat = thisChat(), ul = thatChat.elem.find('.layim-chat-main ul');
        var maxLength = cache.base.maxLength || 3000;
        var val = thatChat.textarea.val();
        if (val.replace(/\s/g, '') !== '') {

            if (val.length > maxLength) {
                return layer.msg('内容最长不能超过' + maxLength + '个字符')
            }
            data.msgtype = 1;
            data.objmsgcontent = {
                text: val
            };
            ul.append(laytpl(elemChatMain).render(data));

            var param = {
                mine: data
                , to: thatChat.data
            }, message = {
                fromtype: 2//老师发的
                , username: param.mine.username
                , avatar: param.mine.avatar
                , id: param.to.id
                , name: param.to.name
                , type: param.to.type || 'friend'
                , msgtype: data.msgtype
                , objmsgcontent: data.objmsgcontent
                , timestamp: new Date().getTime()
                , mine: true
            };
            changeRecentHtml(message);

            layui.each(call.sendMessage, function (index, item) {
                item && item(param);
            });
        }
        chatListMore();
        thatChat.textarea.val('').focus();
    };

    //桌面消息提醒
    var notice = function (data) {
        data = data || {};
        if (window.Notification) {
            if (Notification.permission === 'granted') {
                var notification = new Notification(data.title || '', {
                    body: data.content || ''
                    , icon: data.avatar || 'http://tp2.sinaimg.cn/5488749285/50/5719808192/1'
                });
            } else {
                Notification.requestPermission();
            }
        }
    };

    //消息声音提醒
    var voice = function () {
        if (device.ie && device.ie < 9) return;
        var audio = document.createElement("audio");
        audio.src = layui.cache.dir + 'css/modules/layim/voice/' + cache.base.voice;
        audio.play();
    };

    //接受消息
    var messageNew = {};
    var getMessage = function (data) {
        data = data || {};

        var elem = $('.layim-chatlist-' + data.type + data.id);
        var group = {}, index = elem.index();

        data.timestamp = data.timestamp || new Date().getTime();
        if (data.fromid == cache.mine.id) {
            data.mine = true;
        }
        messageNew = JSON.parse(JSON.stringify(data));
        changeRecentHtml(messageNew);
        if (cache.base.voice) {
            voice();
        }

        if ((!layimChat && data.objmsgcontent) || index === -1) {
            if (cache.message[data.type + data.id]) {
                cache.message[data.type + data.id].push(data)
            } else {
                cache.message[data.type + data.id] = [data];

                //记录聊天面板队列
                if (data.type === 'friend') {
                    var friend;
                    layui.each(cache.friend, function (index1, item1) {
                        layui.each(item1.list, function (index, item) {
                            if (item.id == data.id) {
                                item.type = 'friend';
                                item.name = item.username;
                                cache.chat.push(item);
                                return friend = true;
                            }
                        });
                        if (friend) return true;
                    });
                    if (!friend) {
                        data.name = data.username;
                        data.temporary = true; //临时会话
                        cache.chat.push(data);
                    }
                } else if (data.type === 'group') {
                    var isgroup;
                    layui.each(cache.group, function (index, item) {
                        if (item.id == data.id) {
                            item.type = 'group';
                            item.name = item.groupname;
                            cache.chat.push(item);
                            return isgroup = true;
                        }
                    });
                    if (!isgroup) {
                        data.name = data.groupname;
                        cache.chat.push(data);
                    }
                } else {
                    data.name = data.name || data.username || data.groupname;
                    cache.chat.push(data);
                }
            }
            if (data.type === 'group') {
                layui.each(cache.group, function (index, item) {
                    if (item.id == data.id) {
                        group.avatar = item.avatar;
                        return true;
                    }
                });
            }
            if (!data.system) {
                /*if (cache.base.notice) {
                    notice({
                        title: '来自 ' + data.username + ' 的消息'
                        , content: data.content
                        , avatar: group.avatar || data.avatar
                    });
                }
                return setChatMin({
                    name: '收到新消息'
                    , avatar: group.avatar || data.avatar
                    , anim: 6
                });*/
            }
        }

        if (!layimChat) return;

        //接受到的消息不在当前Tab
        var thatChat = thisChat();
        if (thatChat.data.type + thatChat.data.id !== data.type + data.id) {
            elem.addClass('layui-anim layer-anim-06');
            setTimeout(function () {
                elem.removeClass('layui-anim layer-anim-06')
            }, 300);
        }

        var cont = layimChat.find('.layim-chat').eq(index);
        var ul = cont.find('.layim-chat-main ul');

        //系统消息
        if (data.system) {
            if (index !== -1) {
                ul.append('<li class="layim-chat-system"><span>' + data.content + '</span></li>');
            }
        } else {
            ul.append(laytpl(elemChatMain).render(data));
        }

        chatListMore();
    };

    //消息盒子的提醒
    var ANIM_MSG = 'layui-anim-loop layer-anim-05', msgbox = function (num) {
        var msgboxElem = layimMain.find('.layim-tool-msgbox');
        msgboxElem.find('span').addClass(ANIM_MSG).html(num);
    };

    //修改最近聊天面板  有则置顶 没有则增加到顶部
    var changeRecentHtml = function (message) {
        var id = message.id;
        message.avatar = objdata.babys[id].avatar
        var _elem = $(".layim-list-history").children("[data-id=" + id + "]");
        var strsummary = layui.data.getmsgsummary(message);
        if (_elem.length) {
            if (message.fromtype == 1) {
                _elem.find('.unreadnum').text(_elem.find('.unreadnum').text() * 1 + 1).show();
            }
            _elem.children('p').html(strsummary);
            $(".layim-list-history").prepend(_elem.clone());
            _elem.remove();
        } else {
            var strhtml = makeOneHistory(message);
            _elem = $(strhtml);
            $(".layim-list-history").prepend(_elem);
        }
    };

    //渲染本地最新聊天记录到相应面板
    var viewChatlog = function (othis, layero) {
        var local = layui.data('layim')[cache.mine.id] || {}
            , thatChat = thisChat(), chatlog = local.chatlog || {}
            , ul = thatChat.elem.find('.layim-chat-main ul');
        cache.base.onFirstOpenChat({
            elem: othis,
            type: thatChat.data.type,
            id: thatChat.data.id
        }, function (re) {
            layui.each(re, function (index, item) {
                ul.append(laytpl(elemChatMain).render(item));
            });
            chatListMore();
        });
    };

    //查看更多记录
    var chatListMore = function () {
        var thatChat = thisChat(), chatMain = thatChat.elem.find('.layim-chat-main');
        var ul = chatMain.find('ul');
        var length = ul.find('li').length;

        if (length >= MAX_ITEM) {
            var first = ul.find('li').eq(0);
            if (!ul.prev().hasClass('layim-chat-system')) {
                ul.before('<div class="layim-chat-system"><span layim-event="chatLog">查看更多记录</span></div>');
            }
            if (length > MAX_ITEM) {
                first.remove();
            }
        }
        chatMain.scrollTop(chatMain[0].scrollHeight + 1000);
        chatMain.find('ul li:last').find('img').load(function () {
            chatMain.scrollTop(chatMain[0].scrollHeight + 1000);
        });
    };

    //快捷键发送
    var hotkeySend = function () {
        var thatChat = thisChat(), textarea = thatChat.textarea;
        textarea.focus();
        textarea.off('keydown').on('keydown', function (e) {
            var local = layui.data('layim')[cache.mine.id] || {};
            var keyCode = e.keyCode;
            if (local.sendHotKey === 'Ctrl+Enter') {
                if (e.ctrlKey && keyCode === 13) {
                    sendTextMessage();
                }
                return;
            }
            if (keyCode === 13) {
                if (e.ctrlKey) {
                    return textarea.val(textarea.val() + '\n');
                }
                if (e.shiftKey) return;
                e.preventDefault();
                sendTextMessage();
            }
        });
    };


    var stope = layui.stope; //组件事件冒泡

    //在焦点处插入内容
    var focusInsert = function (obj, str) {
        var result, val = obj.value;
        obj.focus();
        if (document.selection) { //ie
            result = document.selection.createRange();
            document.selection.empty();
            result.text = str;
        } else {
            result = [val.substring(0, obj.selectionStart), str, val.substr(obj.selectionEnd)];
            obj.focus();
            obj.value = result.join('');
        }
    };

    //事件
    var anim = 'layui-anim-upbit', events = {
        //在线状态
        status: function (othis, e) {
            var hide = function () {
                othis.next().hide().removeClass(anim);
            };
            var type = othis.attr('lay-type');
            if (type === 'show') {
                stope(e);
                othis.next().show().addClass(anim);
                $(document).off('click', hide).on('click', hide);
            } else {
                var prev = othis.parent().prev();
                othis.addClass(THIS).siblings().removeClass(THIS);
                prev.html(othis.find('cite').html());
                prev.removeClass('layim-status-' + (type === 'online' ? 'hide' : 'online')).addClass('layim-status-' + type);
                layui.each(call.online, function (index, item) {
                    item && item(type);
                });
            }
        }

        //编辑签名
        , sign: function () {
            var input = layimMain.find('.layui-layim-remark');
            input.on('change', function () {
                var value = this.value;
                layui.each(call.sign, function (index, item) {
                    item && item(value);
                });
            });
            input.on('keyup', function (e) {
                var keyCode = e.keyCode;
                if (keyCode === 13) {
                    this.blur();
                }
            });
        }

        //大分组切换
        , tab: function (othis) {
            var index, main = '.layim-tab-content';
            var tabs = layimMain.find('.layui-layim-tab>li');
            typeof othis === 'number' ? (index = othis, othis = tabs.eq(index)) : (index = othis.index());
            index > 2 ? tabs.removeClass(THIS) : (events.tab.index = index, othis.addClass(THIS).siblings().removeClass(THIS))
            layimMain.find(main).eq(index).addClass(SHOW).siblings(main).removeClass(SHOW);
            if (index == 2) {
                if (!othis.attr('hasload')) {
                    othis.attr('hasload', true)
                    cache.base.getYeyUserRole(function (arr) {
                        var arrhtml = [];
                        for (var i = 0; i < arr.length; i++) {
                            arrhtml.push('<li>');
                            arrhtml.push('<h5 layim-event="spreadrole" lay-type="false" roleid="' + arr[i].id + '">');
                            arrhtml.push('<i class="layui-icon"></i><span>' + arr[i].rolename + '</span><em>(<cite class="layim-count">' + arr[i].count + '</cite>)</em>');
                            arrhtml.push('</h5>');
                            arrhtml.push('<ul class="layui-layim-list"></ul>');
                            arrhtml.push('</li>');
                        }
                        layimMain.find(main).eq(index).html(arrhtml.join(''));
                    });
                }
            }
        }
        //展开角色分组
        , spreadrole: function (othis) {
            var type = othis.attr('lay-type');
            var spread = type === 'true' ? 'false' : 'true';
            othis.next()[type === 'true' ? 'removeClass' : 'addClass'](SHOW);
            var index = othis.parent().index();
            if (spread == "true") {
                if (!othis.attr("hasload")) {
                    cache.base.getRoleUser(othis.attr('roleid'), function (arruser) {
                        var strhtml = [];
                        if (arruser.length) {
                            for (var i = 0; i < arruser.length; i++) {
                                var headimg = arruser[i].headimg ? objdata.ossPrefix + arruser[i].headimg : 'images/desktop/default.png';
                                strhtml.push('<li layim-event="openuser" data-type="friend" data-index="' + index + '" class="layim-' + arruser[i].id + '"><img src="' + headimg + '"><span>' + arruser[i].truename + '</span><p>' + arruser[i].mobile + '</p><span class="layim-msg-status">new</span></li>')
                            }
                        } else {
                            strhtml.push('<li class="layim-null">角色无用户</li>')
                        }
                        othis.next().html(strhtml.join(''));
                        othis.attr("hasload", true);
                    });
                }
            } else {//取消默认展开的班级
                var local = layui.data('layim')[cache.mine.id] || {};
                delete local.openclass;
                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });
            }
            othis.attr('lay-type', spread);
            othis.find('.layui-icon').html(spread === 'true' ? '&#xe61a;' : '&#xe602;');
        }
        //展开联系人分组
        , spread: function (othis) {
            var type = othis.attr('lay-type');
            var spread = type === 'true' ? 'false' : 'true';
            othis.next()[type === 'true' ? 'removeClass' : 'addClass'](SHOW);
            var index = othis.parent().index();
            if (spread == "true") {
                //记录默认展开的班级
                var local = layui.data('layim')[cache.mine.id] || {};
                local.openclass = othis.attr('classno');
                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });
                if (!othis.attr("hasload")) {
                    cache.base.getClassStu(othis.attr('classno'), function (arrstudent) {
                        var strhtml = [];
                        cache['friend'][index].list = arrstudent;
                        if (arrstudent.length) {
                            for (var i = 0; i < arrstudent.length; i++) {
                                strhtml.push('<li layim-event="chat" data-type="friend" data-index="' + index + '" class="layim-' + arrstudent[i].id + '"><img src="' + arrstudent[i].avatar + '"><span>' + arrstudent[i].username + '</span><p>' + arrstudent[i].summary + '</p><span class="layim-msg-status">new</span></li>')
                            }
                        } else {
                            strhtml.push('<li class="layim-null">班级无宝宝</li>')
                        }
                        othis.next().html(strhtml.join(''));
                        othis.attr("hasload", true);
                    });
                }
            } else {//取消默认展开的班级
                var local = layui.data('layim')[cache.mine.id] || {};
                delete local.openclass;
                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });
            }
            othis.attr('lay-type', spread);
            othis.find('.layui-icon').html(spread === 'true' ? '&#xe61a;' : '&#xe602;');
        }

        //搜索
        , search: function (othis) {
            if ($(".search-content").css('display') == 'none') {
                $(".search-content").show();
                var _input = $(".layui-layim-searchtop").find('input');
                _input.focus();
                _input.on('input propertychange', function () {
                    var val = $(this).val();
                    if (!val) {
                        return $(".searchresultbaby").hide();
                    }
                    cache.base.searchbaby(val, function (arrstudent) {
                        cache.base.objsearchstu = {};
                        if (!arrstudent || !arrstudent.length) {
                            return $(".searchresultbaby").hide();
                        }
                        var strhtml = '';
                        for (var i = 0; i < arrstudent.length; i++) {
                            var objstu = arrstudent[i];
                            cache.base.objsearchstu[objstu.id] = objstu;
                            strhtml += '<li layim-event="chat" data-type="onebaby" data-id="' + objstu.id + '" class="layim-' + objstu.id + '">' +
                                '<img src="' + objstu.avatar + '">' +
                                '<span>' + objstu.username + '</span>' +
                                '<p>' + objstu.summary + '</p>' +
                                '</li>';
                        }
                        $(".searchresultbaby").show();
                        $(".layim-list-student").html(strhtml);
                    })
                })
            } else {
                $(".search-content").hide();
            }
            /*var search = layimMain.find('.layui-layim-search');
            var main = layimMain.find('#layui-layim-search');
            var input = search.find('input'), find = function (e) {
                var val = input.val().replace(/\s/);
                if (val === '') {
                    events.tab(events.tab.index | 0);
                } else {
                    var data = [], friend = cache.friend || [];
                    var group = cache.group || [], html = '';
                    for (var i = 0; i < friend.length; i++) {
                        for (var k = 0; k < (friend[i].list || []).length; k++) {
                            if (friend[i].list[k].username.indexOf(val) !== -1) {
                                friend[i].list[k].type = 'friend';
                                friend[i].list[k].index = i;
                                friend[i].list[k].list = k;
                                data.push(friend[i].list[k]);
                            }
                        }
                    }
                    for (var j = 0; j < group.length; j++) {
                        if (group[j].groupname.indexOf(val) !== -1) {
                            group[j].type = 'group';
                            group[j].index = j;
                            group[j].list = j;
                            data.push(group[j]);
                        }
                    }
                    if (data.length > 0) {
                        for (var l = 0; l < data.length; l++) {
                            html += '<li layim-event="chat" data-type="' + data[l].type + '" data-index="' + data[l].index + '" data-list="' + data[l].list + '"><img src="' + data[l].avatar + '"><span>' + (data[l].username || data[l].groupname || '佚名') + '</span><p>' + (data[l].msgsummry || data[l].sign || '') + '</p></li>';
                        }
                    } else {
                        html = '<li class="layim-null">无搜索结果</li>';
                    }
                    main.html(html);
                    events.tab(3);
                }
            };
            if (!cache.base.isfriend && cache.base.isgroup) {
                events.tab.index = 1;
            } else if (!cache.base.isfriend && !cache.base.isgroup) {
                events.tab.index = 2;
            }
            search.show();
            input.focus();
            input.off('keyup', find).on('keyup', find);*/
        }

        //关闭搜索
        , closeSearch: function (othis) {
            $(".search-content").hide();
        }

        //消息盒子
        , msgbox: function () {
            var msgboxElem = layimMain.find('.layim-tool-msgbox');
            layer.close(events.msgbox.index);
            msgboxElem.find('span').removeClass(ANIM_MSG).html('');
            return events.msgbox.index = layer.open({
                type: 2
                , title: '消息盒子'
                , shade: false
                , maxmin: true
                , area: ['600px', '520px']
                , skin: 'layui-box layui-layer-border'
                , resize: false
                , content: cache.base.msgbox
            });
        }

        //弹出查找页面
        , find: function () {
            layer.close(events.find.index);
            return events.find.index = layer.open({
                type: 2
                , title: '查找'
                , shade: false
                , maxmin: true
                , area: ['1000px', '520px']
                , skin: 'layui-box layui-layer-border'
                , resize: false
                , content: cache.base.find
            });
        }

        //弹出更换背景
        , skin: function () {
            layer.open({
                type: 1
                , title: '更换背景'
                , shade: false
                , area: '222px'
                , skin: 'layui-box layui-layer-border'
                , id: 'layui-layim-skin'
                , zIndex: 66666666
                , resize: false
                , content: laytpl(elemSkinTpl).render({
                    skin: cache.base.skin
                })
            });
        }

        //关于
        , about: function () {
            layer.alert('版本： ' + v + '<br>版权所有：<a href="http://layim.layui.com" target="_blank">layim.layui.com</a>', {
                title: '关于 LayIM'
                , shade: false
            });
        }

        //生成换肤
        , setSkin: function (othis) {
            var src = othis.attr('src');
            var local = layui.data('layim')[cache.mine.id] || {};
            local.skin = src;
            if (!src) delete local.skin;
            layui.data('layim', {
                key: cache.mine.id
                , value: local
            });
            try {
                layimMain.css({
                    'background-image': src ? 'url(' + src + ')' : 'none'
                });
                layimChat.css({
                    'background-image': src ? 'url(' + src + ')' : 'none'
                });
            } catch (e) {
            }
            layui.each(call.setSkin, function (index, item) {
                var filename = (src || '').replace(layui.cache.dir + 'css/modules/layim/skin/', '');
                item && item(filename, src);
            });
        }
        //弹出聊天面板
        , chat: function (othis) {
            var local = layui.data('layim')[cache.mine.id] || {};
            var type = othis.data('type'), index = othis.data('index');
            var list = othis.attr('data-list') || othis.index(), data = {};
            if (type !== 'history') {
                data.type = type;
            }
            if (type === 'friend') {
                data = cache[type][index].list[list];
            } else if (type === 'group') {
                data = cache[type][list];
            } else if (type === 'history') {
                var id = othis.data('id');
                for (var i = 0; i < cache[type].length; i++) {
                    if (id == cache[type][i].id) {
                        data = cache[type][i];
                        break;
                    }
                }
                data.type = 'friend';
            } else if (type === 'onebaby') {
                data = cache.base.objsearchstu[othis.data('id')];
                data.type = 'friend';
            }
            data.name = data.name || data.username || data.groupname;
            popchat(data, othis);
        }

        //切换聊天
        , tabChat: function (othis) {
            changeChat(othis);
        }

        //关闭聊天列表
        , closeChat: function (othis, e) {
            changeChat(othis.parent(), 1);
            stope(e);
        }, closeThisChat: function () {
            changeChat(null, 1);
        }
        //展开宝宝老师群组成员
        , groupMembers: function (othis, e) {
            var icon = othis.find('.layui-icon'), hide = function () {
                icon.html('&#xe61a;');
                othis.data('down', null);
                layer.close(events.groupMembers.index);
            }, stopmp = function (e) {
                stope(e)
            };

            if (othis.data('down')) {
                hide();
            } else {
                //cache.mine
                var thatChat = thisChat();
                var strhtml = '';
                for (var i = 0; i < thatChat.data.arrweixin.length; i++) {
                    var objone = thatChat.data.arrweixin[i];
                    strhtml += '<li><a href="javascript:;"><img src="' + layui.data.getsrcbyptype(objone) + '"><cite>' + layui.data.getrolebyptype(objone) + '</cite></a></li>';
                }

                icon.html('&#xe619;');
                othis.data('down', true);
                events.groupMembers.index = layer.tips('<ul class="layim-members-list">' + strhtml + '</ul>', othis, {
                    tips: 3
                    , time: 0
                    , anim: false
                    , fixed: true
                    , skin: 'layui-box layui-layim-members'
                    , success: function (layero, index) {
                        layer.style(index, {
                            zIndex: 888888890
                        });
                        var members = cache.base.members || {}
                            , ul = layero.find('.layim-members-list'), li = '', membersCache = {}
                            , hasFull = layimChat.find('.layui-layer-max').hasClass('layui-layer-maxmin')
                            , listNone = layimChat.find('.layim-chat-list').css('display') === 'none';
                        if (hasFull) {
                            ul.css({
                                width: $(window).width() - 22 - (listNone || 200)
                            });
                        }
                        /* members.data = $.extend(members.data, {
                             id: thatChat.data.id
                         });
                         post(members, function (res) {
                             layui.each(res.list, function (index, item) {
                                 li += '<li data-uid="' + item.id + '"><a href="javascript:;"><img src="' + item.avatar + '"><cite>' + item.username + '</cite></a></li>';
                                 membersCache[item.id] = item;
                             });
                             ul.html(li);

                             //获取群员
                             othis.find('.layim-chat-members').html(res.members || (res.list || []).length + '人');

                             //私聊
                             ul.find('li').on('click', function () {
                                 var uid = $(this).data('uid'), info = membersCache[uid]
                                 popchat({
                                     name: info.username
                                     , type: 'friend'
                                     , avatar: info.avatar
                                     , id: info.id
                                 });
                                 hide();
                             });

                             layui.each(call.members, function (index, item) {
                                 item && item(res);
                             });
                         });*/
                        layero.on('mousedown', function (e) {
                            stope(e);
                        });
                    }
                });
                $(document).off('mousedown', hide).on('mousedown', hide);
                $(window).off('resize', hide).on('resize', hide);
                othis.off('mousedown', stopmp).on('mousedown', stopmp);
            }
        }

        //发送聊天内容
        , send: function () {
            sendTextMessage();
        }

        //设置发送聊天快捷键
        , setSend: function (othis, e) {
            var box = events.setSend.box = othis.siblings('.layim-menu-box')
                , type = othis.attr('lay-type');

            if (type === 'show') {
                stope(e);
                box.show().addClass(anim);
                $(document).off('click', events.setSendHide).on('click', events.setSendHide);
            } else {
                othis.addClass(THIS).siblings().removeClass(THIS);
                var local = layui.data('layim')[cache.mine.id] || {};
                local.sendHotKey = type;
                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });
                events.setSendHide(e, othis.parent());
            }
        }, setSendHide: function (e, box) {
            (box || events.setSend.box).hide().removeClass(anim);
        }

        //表情
        , face: function (othis, e) {
            var content = '', thatChat = thisChat();

            for (var key in faces) {
                content += '<li title="' + key + '"><img src="' + faces[key] + '"></li>';
            }
            content = '<ul class="layui-clear layim-face-list">' + content + '</ul>';

            events.face.index = layer.tips(content, othis, {
                tips: 1
                , time: 0
                , fixed: true
                , zIndex: 888888889
                , skin: 'layui-box layui-layim-face'
                , success: function (layero, index) {
                    layer.style(index, {
                        zIndex: 888888889
                    });
                    layero.find('.layim-face-list>li').on('mousedown', function (e) {
                        stope(e);
                    }).on('click', function () {
                        focusInsert(thatChat.textarea[0], this.title + ' ');
                        layer.close(events.face.index);
                    });
                }
            });

            $(document).off('mousedown', events.faceHide).on('mousedown', events.faceHide);
            $(window).off('resize', events.faceHide).on('resize', events.faceHide);
            stope(e);

        }, faceHide: function () {
            layer.close(events.face.index);
        }
        , imgtoolHide: function () {
            layer.close(events.image.index);
        }
        , viewimage: function (othis) {
            layer.close(popchat.photosIndex);
            layer.photos({
                photos: {
                    data: [{
                        "alt": "大图模式",
                        "src": othis.attr('big')
                    }]
                }
                , shade: 0.01
                , closeBtn: 2
                , anim: 0
                , resize: false
                , success: function (layero, index) {
                    popchat.photosIndex = index;
                }
            });
        }
        //图片或一般文件
        , image: function (othis, e) {
            var content =
                '<div id="divimselimgtool">' +
                '<div style="margin: 5px;" id="divimimgpiker">本地上传</div>' +
                '<div style="margin: 5px;">图库中选择</div>' +
                '</div>';
            events.image.index = layer.tips(content, othis, {
                tips: 1
                , time: 0
                , fixed: true
                , skin: 'layui-box layui-layim-imgtool'
                , success: function (layero, index) {
                    layer.style(index, {
                        zIndex: 888888890
                    });
                    dynamicLoadCss("plugin/ueditor/third-party/webuploader/webuploader.css");
                    dynamicLoadJs("plugin/ueditor/third-party/webuploader/webuploader.js", function () {
                        console.log(WebUploader)
                        var uploader = WebUploader.create({// 初始化Web Uploader
                            // auto : true,// 自动上传。
                            swf: $.projectpath + '/plugin/ueditor/third-party/webuploader/Uploader.swf',// swf文件路径
                            server: objdata.upconfig.upfile_endpoint,//$.projectpath + '/ueditorController?action=uploadimage',// 文件接收服务端。
                            pick: '#divimimgpiker',// 内部根据当前运行是创建，可能是input元素，也可能是flash.
                            accept: {// 只允许选择文件，可选。
                                title: 'Images',
                                extensions: 'gif,jpg,jpeg,bmp,png',
                                mimeTypes: 'image/jpg,image/jpeg,image/png,image/gif'
                            },
                            duplicate: true
                        });
                        objdata.imguploader = uploader;
                        // 文件上传过程中创建进度条实时显示。
                        uploader.on('uploadProgress', function (file, percentage) {
                            console.log(percentage);
                        });
                        uploader.on('uploadBeforeSend', function (obj, data, headers) {
                            //TODO   如果同一个页面上传多次  需要处理签名逻辑   不用每次都签名
                            $.sm(function (re, err) {
                                if (re) {
                                    objdata.upexpire = re.expire;
                                    objdata.osssignature = {
                                        'key': re.dir,
                                        'policy': re.policy,
                                        'OSSAccessKeyId': re.accessid,
                                        'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                                        'signature': re.signature
                                    };
                                }
                            }, ['osssignature', objdata.upconfig.upfile_defaltdir], "", "", {
                                async: false
                            });
                            //赋值参数
                            data = $.extend(data, objdata.osssignature);
                            //设置文件路径
                            data.key = data.key + "/" + calculate_object_name(data.name, objdata.upfile_nametype);
                            obj.filepath = data.key;
                            //file.path = data.key;
                            headers['Access-Control-Allow-Origin'] = "*";
                        });
                        function calculate_object_name(filename, type) {
                            if (type && type == 'local_name') {
                                return "${filename}";
                            } else {//随机生成文件名
                                var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
                                var maxPos = chars.length;
                                var random_string = '';
                                for (var i = 0; i < 10; i++) {
                                    random_string += chars.charAt(Math.floor(Math.random() * maxPos));
                                }
                                var pos = filename.lastIndexOf('.')
                                var suffix = ''
                                if (pos != -1) {
                                    suffix = filename.substring(pos);
                                }
                                return random_string + new Date().getTime() + suffix;
                            }
                        }

                        uploader.on('filesQueued', function (files) {
                            objdata.arrtoupfiles = [];
                            for (var i = 0; i < files.length; i++) {
                                objdata.arrtoupfiles[i] = {
                                    id: files[i].id
                                }
                            }
                            uploader.upload();
                        });
                        // 文件上传成功，给item添加成功class, 用样式标记上传成功。
                        uploader.on('uploadSuccess', function (file, status, b) {
                            for (var i = 0; i < objdata.arrtoupfiles.length; i++) {
                                if (objdata.arrtoupfiles[i].id == file.id) {
                                    objdata.arrtoupfiles[i].name = file.name;
                                    objdata.arrtoupfiles[i].url = file.blocks[0].filepath;
                                    break;
                                }
                            }
                        });
                        // 文件上传失败，现实上传出错。
                        uploader.on('uploadError', function (file) {
                            alert('上传失败');
                        });
                        // 完成上传完了，成功或者失败，先删除进度条。
                        uploader.on('uploadComplete', function (file, a, b) {
                        });
                        //所有文件上传结束
                        uploader.on('uploadFinished', function (files) {
                            sendImageMessage(objdata.arrtoupfiles);
                        });
                    })
                    layero.find('#divimselimgtool>div').on('mousedown', function (e) {
                        stope(e);
                    }).on('click', function () {
                        if ($(this).index() == 1) {
                            layer.msg("功能建设中")
                        }
                        layer.close(events.image.index);
                    });
                }
            });
            $(document).off('mousedown', events.imgtoolHide).on('mousedown', events.imgtoolHide);
            $(window).off('resize', events.imgtoolHide).on('resize', events.imgtoolHide);
            stope(e);
            /*var thatChat = thisChat();
            layer.open({
                type: 1,
                title: false,
                closeBtn: 0,
                shadeClose: true,
                skin: 'yourclass',
                content: '自定义HTML内容'
            });
            layer.tab({
                area: ['600px', '300px'],
                tab: [{
                    title: '上传图片',
                    content: '内容1'
                }, {
                    title: '相册中选择',
                    content: '内容3'
                }]
            });*/
            /*var type = othis.data('type') || 'images', api = {
                images: 'uploadImage'
                , file: 'uploadFile'
            }, thatChat = thisChat(), conf = cache.base[api[type]] || {};
            $.sm(function (re, err) {
                if (re) {
                    objdata.upexpire = re.expire;
                    objdata.osssignature = {
                        'key': re.dir,
                        'policy': re.policy,
                        'OSSAccessKeyId': re.accessid,
                        'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                        'signature': re.signature
                    };
                }
                layui.upload.render({
                    //url: conf.url || ''
                    url: ossPrefix
                    , data:{

                    }
                    , method: conf.type
                    , elem: othis.find('input')[0]
                    , accept: type
                    , done: function (res) {
                        if (res.code == 0) {
                            res.data = res.data || {};
                            if (type === 'images') {
                                focusInsert(thatChat.textarea[0], 'img[' + (res.data.src || '') + ']');
                            } else if (type === 'file') {
                                focusInsert(thatChat.textarea[0], 'file(' + (res.data.src || '') + ')[' + (res.data.name || '下载文件') + ']');
                            }
                            sendMessage();
                        } else {
                            layer.msg(res.msg || '上传失败');
                        }
                    }
                });
            }, ['osssignature', objdata.upconfig.upfile_defaltdir], "", "", {
                async: false
            });*/
        }

        //音频和视频
        , media: function (othis) {
            var type = othis.data('type'), text = {
                audio: '音频'
                , video: '视频'
            }, thatChat = thisChat()

            layer.prompt({
                title: '请输入网络' + text[type] + '地址'
                , shade: false
                , offset: [
                    othis.offset().top - $(window).scrollTop() - 158 + 'px'
                    , othis.offset().left + 'px'
                ]
            }, function (src, index) {
                focusInsert(thatChat.textarea[0], type + '[' + src + ']');
                sendTextMessage();
                layer.close(index);
            });
        }

        //扩展工具栏
        , extend: function (othis) {
            var filter = othis.attr('lay-filter')
                , thatChat = thisChat();

            layui.each(call['tool(' + filter + ')'], function (index, item) {
                item && item.call(othis, function (content) {
                    focusInsert(thatChat.textarea[0], content);
                }, sendTextMessage, thatChat);
            });
        }

        //播放音频
        , playAudio: function (othis) {
            var audioData = othis.data('audio')
                , audio = audioData || document.createElement('audio')
                , pause = function () {
                audio.pause();
                othis.removeAttr('status');
                othis.find('i').html('&#xe652;');
            };
            if (othis.data('error')) {
                return layer.msg('播放音频源异常');
            }
            if (!audio.play) {
                return layer.msg('您的浏览器不支持audio');
            }
            if (othis.attr('status')) {
                pause();
            } else {
                audioData || (audio.src = othis.data('src'));
                audio.play();
                othis.attr('status', 'pause');
                othis.data('audio', audio);
                othis.find('i').html('&#xe651;');
                //播放结束
                audio.onended = function () {
                    pause();
                };
                //播放异常
                audio.onerror = function () {
                    layer.msg('播放音频源异常');
                    othis.data('error', true);
                    pause();
                };
            }
        }

        //播放视频
        , playVideo: function (othis) {
            var videoData = othis.data('src')
                , video = document.createElement('video');
            if (!video.play) {
                return layer.msg('您的浏览器不支持video');
            }
            layer.close(events.playVideo.index);
            events.playVideo.index = layer.open({
                type: 1
                , title: '播放视频'
                , area: ['460px', '300px']
                , maxmin: true
                , shade: false
                , content: '<div style="background-color: #000; height: 100%;"><video style="position: absolute; width: 100%; height: 100%;" src="' + videoData + '" loop="loop" autoplay="autoplay"></video></div>'
            });
        }

        //聊天记录
        , chatLog: function (othis) {
            var thatChat = thisChat();
            if (!cache.base.chatLog) {
                return layer.msg('未开启更多聊天记录');
            }
            layer.close(events.chatLog.index);
            return events.chatLog.index = layer.open({
                type: 2
                , maxmin: true
                , title: '与 ' + thatChat.data.name + ' 的聊天记录'
                , area: ['450px', '100%']
                , shade: false
                , offset: 'rb'
                , skin: 'layui-box'
                , anim: 2
                , id: 'layui-layim-chatlog'
                , content: cache.base.chatLog + '?id=' + thatChat.data.id + '&type=' + thatChat.data.type
                , success: function (layero, index) {
                    layer.style(index, {
                        zIndex: '111111112'
                    })
                }
            });
        }
        , searchrecord: function (othis) {//聊天记录
            layer.msg("功能建设中")
            return;
            layer.close(events.chatLog.index);
            return events.chatLog.index = layer.open({
                type: 2
                , maxmin: true
                , title: '查找聊天记录'
                , area: ['450px', '100%']
                , shade: false
                , offset: 'rb'
                , skin: 'layui-box'
                , anim: 2
                , id: 'layui-layim-chatlog'
                , content: cache.base.chatLog + '?type=all'
                , success: function (layero, index) {
                    layer.style(index, {
                        zIndex: '111111112'
                    })
                }
            });
        }

        //历史会话右键菜单操作
        , menuHistory: function (othis, e) {
            var local = layui.data('layim')[cache.mine.id] || {};
            var parent = othis.parent(), type = othis.data('type');
            var hisElem = layimMain.find('.layim-list-history');
            var none = '<li class="layim-null">暂无历史会话</li>'

            if (type === 'one') {
                var history = local.history;
                delete history[parent.data('index')];

                local.history = history;

                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });

                //删除 DOM
                $('.layim-list-history li.layim-' + parent.data('index')).remove();

                if (hisElem.find('li').length === 0) {
                    hisElem.html(none);
                }

            } else if (type === 'all') {
                delete local.history;
                layui.data('layim', {
                    key: cache.mine.id
                    , value: local
                });
                hisElem.html(none);
            }

            layer.closeAll('tips');
        }

    };

    //暴露接口
    exports('layim', new LAYIM());

}).addcss(
    'modules/layim/layim.css?v=3.9.1'
    , 'skinlayimcss'
);
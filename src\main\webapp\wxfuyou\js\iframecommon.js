
var objiframecom = {
    isfirst: true,
    prevtitle: "",
    prevhash: "",
    arrwin: [],
    objwin: {},
    curpage: 0,
    pernum: 20,
    curzindex: 10,
    curhash: "",
    isload: true,
    haswxconfig: false,
    jsApiList: ['checkJsApi', 'getNetworkType', 'previewImage', 'getLocation', 'chooseImage', 'uploadImage', 'startRecord', 'stopRecord', 'onVoiceRecordEnd', 'uploadVoice', 'playVoice', 'pauseVoice', 'stopVoice', 'onVoicePlayEnd', 'getLocalImgData', 'scanQRCode', 'hideAllNonBaseMenuItem', 'showAllNonBaseMenuItem']
};
function setOnPageBack(backevent) {
    objiframecom.arrwin[objiframecom.arrwin.length - 1].onPageBack = backevent;
}

function removeOnPageBack() {
    objiframecom.arrwin[objiframecom.arrwin.length - 1].onPageBack = null;
}

function initBottom() {
    console.log("initBottom");
    // var html = [];
    // var arrpage = ['openverifylist'];
    // var arricon = [''];
    // var title = ['开学核验'];
    // var hasload = [0, 0, 0, 0];
    // var argindex = Arg('index');
    // var curindex = '';
    // if (argindex || (0 == argindex)) {
    //     curindex = argindex;
    // } else {
    //     curindex = 0;
    // }
    // console.log(curindex);
    // var _footer_m = $("#footer_m");
    // var _frames = $("#frames");
    // for (var i = 0; i < arrpage.length; i++) {
    //     var src = '';
    //     if (i == curindex) {
    //         objiframecom.arrwin.push({
    //             tabbar: true,
    //             key: "",
    //             title: title[i]
    //         });
    //     }
    //     html.push('<iframe id="frame' + arrpage[i] + '" src="' + src + '" class="oneFrame" style="' + (i == curindex ? 'display:block;' : '') + '"></iframe>');
    // }
    //
    // _frames.html(html.join(''));
    // _footer_m.children().click(function () {
    //     var _this = $(this);
    //     if (!_this.hasClass('weui-bar__item_on')) {//不是当前页
    //         var index = _this.index();
    //         localStorage.setItem("index", index);
    //         if (!flag) {
    //             document.title = title[index];
    //         }
    //
    //         if (objiframecom.arrwin && objiframecom.arrwin.length) {
    //             objiframecom.arrwin[0].title = title[index] || '';
    //         }
    //
    //         $(".oneFrame").hide().eq(index).css('display', 'block');
    //         if (!hasload[index]) {
    //             $(".oneFrame").eq(index).prop('src', arrpage[index] + '.html?v=' + (Arg('testv') || Arg('v')) + (Arg("type") ? "&type=" + Arg("type") : ""));
    //             hasload[index] = 1;
    //         }
    //         _footer_m.children().each(function () {
    //             var _index = $(this).index();
    //             if (index == _index) {
    //                 $(this).find('img').prop('src', 'images/index/' + arricon[_index] + '_HL.png');
    //                 $(this).addClass('weui-bar__item_on');
    //             } else {
    //                 $(this).find('img').prop('src', 'images/index/' + arricon[_index] + '.png');
    //                 $(this).removeClass('weui-bar__item_on');
    //             }
    //         });
    //     }
    // }).eq(curindex).trigger('click');
    objiframecom.arrwin.push({
        tabbar: true,
        key: "",
        title: "开学核验"
    });
    var flag = Arg('flag');
    // if (flag) {
    //     setTimeout(function () {
    //         _frames.show();
    //         _footer_m.parent().show();
    //     }, 1000);
    // } else {
    //     _frames.show();
    //     _footer_m.parent().show();
    // }
    // if (flag) {
    //     var objarg = Arg.all();
    //     var src = flag + '.html?v=' + Arg('v');
    //     for (var key in objarg) {
    //         if (key && key != "index" && key != "flag" && key != "v")
    //             src += "&" + key + "=" + objarg[key];
    //     }
    //     openwin(flag, src, objarg.title);
    // }
    if (flag) {
        if (flag == 'wechat') {
            var yguid = Arg('yguid');
            var stuno = Arg('stuno');
            if (yguid && stuno) {
                parent.openwin('homechat', 'homechat.html?v=' + Arg("v") + "&index=2&yguid=" + yguid + "&stuno=" + stuno, "家长消息");
            }
        } else {
            var objarg = Arg.all();
            var src = flag + '.html?v=' + (Arg('testv') || Arg('v'));
            for (var key in objarg) {
                if (key && key != "index" && key != "flag" && key != "v")
                    src += "&" + key + "=" + objarg[key];
            }
            openwin(flag, src, objarg.title);

        }
    }
}

function backEvent(obj) {
    if (objiframecom.curhash) {
        closewin(obj);
    }
    var objto = objiframecom.arrwin[objiframecom.arrwin.length - 1];
    if (objto.tabbar) {
        /* configwx(function () {
             wx.showAllNonBaseMenuItem();
         })*/
    }
    document.title = objto.title || '';
}
/**
 * 打开聊天页面
 * @param yguid 幼儿园id
 * @param stuno 学生编号
 * @param fromPageHash 来源页面
 */
function initBabyChat(yguid, stuno, fromPageHash) {
    objiframecom.curtalkbabystuno = stuno;
    pushHash({
        key: 'wechat',
        title: "聊天"
    });
    //读取页面重置为0
    objiframecom.curpage = 0;
    objiframecom.curzindex += 1;
    $("#pageChat").css("zIndex", objiframecom.curzindex).addClass('slideIn').show();
    setTimeout(function () {
        _content.css('opacity', '1');
    }, 500);
    objiframecom.prevtitle = document.title;
    document.title = "家长沟通";
    startBabyChat(yguid, stuno, fromPageHash);
}

/**
 * 初始化socket
 */
function initSocketOn() {
    if (socket != null) {
        socket.on("msg", function (data, fn) {
            if (fn) {
                fn(true); // 消息接收成功
            }
            if (data.t == 'talk' && data.type == "tb_parenttalk") {
                if (data.msg && data.msg.data) {
                    //如果当前聊天宝宝界面打开
                    var _framechat = $("#framehomechat");
                    if (_framechat.length)
                        _framechat[0].contentWindow.onMsg && _framechat[0].contentWindow.onMsg(data.yguid, data.msg);
                    //如果message页面打开
                    var _framemessage = $("#framemessage");
                    if (_framemessage.length)
                        _framemessage[0].contentWindow.onMsg && _framemessage[0].contentWindow.onMsg(data.yguid, data.msg);
                    //处理外层数目
                    changeMsgNum(1);
                }
            } else if (data.t == 'askleavesocket' || data.t == 'dianming' || data.t == 'homeshuaka' || data.t == 'deferredleave') {// 请假通知 点名通知
                var _framechuqin = $("#framechuqin");
                if (_framechuqin.length)
                    _framechuqin[0].contentWindow.socketOn && _framechuqin[0].contentWindow.socketOn(data);
                var _frameaskleave = $("#frameaskleave");
                if (_frameaskleave.length)
                    _frameaskleave[0].contentWindow.socketOn && _frameaskleave[0].contentWindow.socketOn(data);
            } else if (data.t == 'homereadmsg') {
                var obj = {};
                if (typeof data.msg == "string") {
                    obj = JSON.parse(data.msg);
                } else {
                    obj = data.msg;
                }
                var _framechat = $("#framehomechat");
                if (_framechat.length)
                    _framechat[0].contentWindow.onMsgRead && _framechat[0].contentWindow.onMsgRead(data.yguid, obj);
                if (obj.arrclassmsgid && obj.arrclassmsgid.length) {
                    var _frameclassmsg = $("#frameclassmessage");
                    if (_frameclassmsg.length) {
                        _frameclassmsg[0].contentWindow.onMsgRead && _frameclassmsg[0].contentWindow.onMsgRead(data.yguid, obj);
                    }
                }
            } else if (data.t == 'yzpmsg') {
                //首页
                var _recruitchat = $("#framerecruitchat");
                if (_recruitchat.length)
                    _recruitchat[0].contentWindow.onMsg && _recruitchat[0].contentWindow.onMsg(data);
                //消息页
                var _recruitindex = $("#framerecruitindex");
                if (_recruitindex.length)
                    _recruitindex[0].contentWindow.onMsg && _recruitindex[0].contentWindow.onMsg(data);
            }
        });
    }
}

/**
 * 查询未读数目
 */
function getUnReadNum() {
    $.sm(function (re, err, obj) {
        if (!err) {
            makeUreadNum(obj.msgNum, obj.noticeNum);
        } else {
            makeUreadNum(0, 0);
        }
    }, ['wxyey.index.getUnReadNum']);
}

/**
 * 设置未读数目
 * @param msgNum
 * @param noticeNum
 */
function makeUreadNum(msgNum, noticeNum) {
    msgNum = msgNum || 0;
    noticeNum = noticeNum || 0;
    if (msgNum > 0) {
        _messagenum.html(msgNum > 99 ? '99+' : msgNum).attr('num', msgNum).show();
    } else {
        _messagenum.html(0).attr('num', 0).hide();
    }
    if (noticeNum > 0) {
        _noticenum.html(noticeNum > 99 ? '99+' : noticeNum).attr('num', noticeNum).show();
    } else {
        _noticenum.html(0).attr('num', 0).hide();
    }
}

/*修改消息未读数目*/
function changeMsgNum(num) {
    var oldnum = _messagenum.attr('num') * 1;
    var intnum = oldnum ? (oldnum + num) : num;
    if (intnum < 0) intnum = 0;
    if (intnum > 0) {
        _messagenum.show().html(intnum > 99 ? '99+' : intnum).attr("num", intnum);
    } else {
        _messagenum.hide().html(0).attr("num", 0);
    }
}

function changeNoticeNum(num) {
    var oldnum = _noticenum.attr('num') * 1;
    var intnum = oldnum ? (oldnum + num) : num;
    if (intnum < 0) intnum = 0;
    if (intnum > 0) {
        _noticenum.show().html(intnum > 99 ? '99+' : intnum).attr("num", intnum);
    } else {
        _noticenum.hide().html(0).attr("num", 0);
    }
}

/**
 * {
		key:key,
		title:title
	}
 * @param obj
 */
function pushHash(obj) {
    objiframecom.arrwin.push(obj);
    window.history.pushState(null, obj.title, "#" + obj.key);
}

function openwin(key, src, title) {
    if (title) document.title = title || "";
    pushHash({
        key: key,
        title: title
    });
    //修改objwin
    objiframecom.objwin[key] = {
        wintype: 'win',
        key: key
    };
    if (!src) {
        src = key + '.html?v=' + (Arg('testv') || Arg('v'));
    }
    var str =
        '<div class="oneopenpage" style="border:none;z-index:' + objiframecom.curzindex + '">\
            <div class="oneopenpage_overlay"></div>\
            <iframe class="oneopenpage_frame" id="frame' + key + '" src="" name="' + key + '"></iframe>\
        </div>';
    var _obj = $(str);
    objiframecom.curzindex += 1;
    if (!title) {
        setTimeout(function () {
            var win = $('#frame' + key).length ? $('#frame' + key)[0].contentWindow : null;
            if (win && win.document && win.document.title) {
                objiframecom.arrwin[objiframecom.arrwin.length - 1].title = win.document.title;
                document.title = win.document.title;
            }
        }, 2000);
    }
    $('body').append(_obj);
    $('#frames').children().css('pointer-events', 'none');
    $('frames').each(function () {
        if (!$(this).parent().is(':visible')) {
            $(this).css('pointer-events', 'none');
        }
    });
    _obj.show();
    _obj.width();
    _obj.addClass('oneopenpage--visible');
    var _frame = _obj.find('.oneopenpage_frame');
    _frame.width();
    _frame.transitionEnd(function () {
        _frame.prop('src', src);
    });
    /*configwx(function () {
        wx.hideAllNonBaseMenuItem();
    })*/
}

function getprevwin() {
    var obj = objiframecom.arrwin[objiframecom.arrwin.length - 2];
    if (obj && obj.key) {
        var win = $("#frame" + obj.key)[0].contentWindow;
        return win;
    }
    return null;
}

function closewin(obj) {
    if (obj && obj.wintype) {
        if (obj.wintype == 'layer') {
            closelayer(obj);
        } else if (obj.wintype == 'popup') {
            closepopupevent(obj);
        }
    } else {
        $("#frame" + obj.key).parent().remove();
    }
    if ($('.oneopenpage--visible').length == 0) {
        $('#frames').children().css('pointer-events', 'auto');
        $('frames').each(function () {
            if (!$(this).parent().is(':visible')) {
                $(this).css('pointer-events', 'auto');
            }
        });
    }
}

function pageback(num) {
    num = num || 1;
    for (var i = 0; i < num; i++) {
        history.back();
    }
}

//在 我的信息页面更新完 切换关联
function refreshOther(index) {
    $(".oneFrame").each(function () {
        var _this = $(this);
        if (_this.index() != index && _this.prop('src')) {
            if (_this[0] && _this[0].contentWindow)
                _this[0].contentWindow.location.reload();
        }
    });
}

function getStrShowTime(oldstrtime) {
    if (oldstrtime) {
        var curMo = objiframecommon.moment(oldstrtime || "");
        var nowMo = objiframecommon.moment();
        if (curMo.year() == nowMo.year()) {
            if (curMo.month() == nowMo.month()) {
                if (curMo.dayOfYear() == nowMo.dayOfYear()) {
                    return curMo.format("HH:mm");
                } else if (curMo.dayOfYear() == nowMo.dayOfYear() - 1) {
                    return curMo.format("昨天 HH:mm");
                } else {
                    return curMo.format("MM-DD");
                }
            } else {
                return curMo.format("YYYY-MM-DD");
            }
        } else {
            return curMo.format("YYYY-MM-DD");
        }
    } else {
        var curMo = objiframecommon.moment(oldstrtime);
        return curMo.format("HH:mm");
    }
}

function changeTextToUrl(inputText) {
    var replacedText, replacePattern1, replacePattern2, replacePattern3;
    var originalText = inputText;
    //URLs starting with http://, https://, file:// or ftp://
    replacePattern1 = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
    //URLs starting with "www." (without // before it, or it'd re-link the ones done above).
    replacePattern2 = /(^|[^\/f])(www\.[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
    //Change email addresses to mailto:: links.
    replacePattern3 = /(([a-zA-Z0-9\-\_\.])+@[a-zA-Z\_]+?(\.[a-zA-Z]{2,6})+)/gi;
    //If there are hrefs in the original text, let's split
    // the text up and only work on the parts that don't have urls yet.
    var count = originalText.match(/<a href/g) || [];
    if (count.length > 0) {
        var combinedReplacedText;
        //Keep delimiter when splitting
        var splitInput = originalText.split(/(<\/a>)/g);

        for (i = 0; i < splitInput.length; i++) {
            if (splitInput[i].match(/<a href/g) == null) {
                splitInput[i] = splitInput[i].replace(replacePattern1, '<a href="$1" target="_blank">$1</a>').replace(replacePattern2, '$1<a href="http://$2" target="_blank">$2</a>').replace(replacePattern3, '<a href="mailto:$1">$1</a>');
            }
        }
        combinedReplacedText = splitInput.join('');
        return combinedReplacedText;
    } else {
        replacedText = inputText.replace(replacePattern1, '<a href="$1" target="_blank">$1</a>');
        replacedText = replacedText.replace(replacePattern2, '$1<a href="http://$2" target="_blank">$2</a>');
        replacedText = replacedText.replace(replacePattern3, '<a href="mailto:$1">$1</a>');
        return replacedText;
    }
}

/**
 * 文本框根据输入内容自适应高度
 * @param                {HTMLElement}        输入框元素
 * @param                {Number}                设置光标与输入框保持的距离(默认0)
 * @param                {Number}                设置最大高度(可选)
 */
function autoTextarea(elem, extra, maxHeight) {
    extra = extra || 0;
    var isFirefox = !!document.getBoxObjectFor || 'mozInnerScreenX' in window,
        isOpera = !!window.opera && !!window.opera.toString().indexOf('Opera'),
        addEvent = function (type, callback) {
            elem.addEventListener ?
                elem.addEventListener(type, callback, false) :
                elem.attachEvent('on' + type, callback);
        },
        getStyle = elem.currentStyle ? function (name) {
            var val = elem.currentStyle[name];

            if (name === 'height' && val.search(/px/i) !== 1) {
                var rect = elem.getBoundingClientRect();
                return rect.bottom - rect.top -
                    parseFloat(getStyle('paddingTop')) -
                    parseFloat(getStyle('paddingBottom')) + 'px';
            }
            return val;
        } : function (name) {
            return getComputedStyle(elem, null)[name];
        },
        minHeight = parseFloat(getStyle('height'));
    objiframecom.minHeight = minHeight;

    elem.style.resize = 'none';

    var change = function () {
        var scrollTop, height,
            padding = 0,
            style = elem.style;

        if (elem._length === elem.value.length) return;
        elem._length = elem.value.length;

        if (!isFirefox && !isOpera) {
            padding = parseInt(getStyle('paddingTop')) + parseInt(getStyle('paddingBottom'));
        }
        scrollTop = document.body.scrollTop || document.documentElement.scrollTop;

        elem.style.height = minHeight + 'px';
        if (elem.scrollHeight > minHeight) {
            if (maxHeight && elem.scrollHeight > maxHeight) {
                height = maxHeight - padding;
                style.overflowY = 'auto';
            } else {
                height = elem.scrollHeight - padding;
                style.overflowY = 'hidden';
            }
            style.height = height + extra + 'px';
            scrollTop += parseInt(style.height) - elem.currHeight;
            document.body.scrollTop = scrollTop;
            document.documentElement.scrollTop = scrollTop;
            elem.currHeight = parseInt(style.height);
        }
    };

    addEvent('propertychange', change);
    addEvent('input', change);
    addEvent('focus', change);
    change();
};

function getStrDate(d) {
    var strSeparator = "-"; //日期分隔符
    if (typeof d == "string") {
        d = d.split(" ")[0];
        var arr = d.split(strSeparator);
        d = new Date(arr[0], arr[1] - 1, arr[2]);
    }
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
};

//根据key获取指定的win对象，以及此对象相对于当前窗口的位置（num，用于跳转使用）
function getfixedwin(key) {
    var num = 2;
    while (true) {
        var win = null;
        var obj = objiframecom.arrwin[objiframecom.arrwin.length - num];
        if (obj && obj.key) {
            win = $("#frame" + obj.key)[0].contentWindow;
            // win = parent.frames[obj.key]
        }
        if (obj.key == key) {
            var result = {};
            result.win = win;
            result.num = num - 1;
            return result;
        }
        if (num == 6) {
            return null;
        }
        num++;
    }
}
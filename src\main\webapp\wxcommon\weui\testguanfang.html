<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <title>测试</title>
    <link rel="stylesheet" type="text/css" href="weui.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.bootcss.com/jquery-weui/1.2.0/css/jquery-weui.css">
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <style>
        body {
            background: #F3F4F6;
        }

        #selSex {
            color: #dddddd;
        }

        #selSex .current {
            color: #000000;
        }
    </style>
</head>
<body>
<section class="addbodydiv">
    <div style="height:100px;" id="div1">
        啊啊啊啊啊啊啊啊啊
    </div>
    <div>

        <input type="button" value="点击弹出" id="btn1">
    </div>
</section>
<script src="https://cdn.bootcss.com/jquery-weui/1.2.0/js/jquery-weui.js"></script>
<script>
    $(function () {
        var _div1 = $("#div1");
        _div1.datetimePicker({
            inputChange: false,//滑动时,input值不改变
            min: '2018-03-29',
            max: '2018-04-02',
            onPick: function (picker) {
                var values = picker.value;
                console.log(values)
            }
        });
    })
</script>
</body>
</html>

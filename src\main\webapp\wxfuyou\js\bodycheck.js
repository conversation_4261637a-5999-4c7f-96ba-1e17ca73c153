/*
 新增日期: 2014.07.29
 作 者:
 內容摘要:　体检
 */
var objdata = {
    curbid: null,
    preheight: 0,
    prewidth: 0,
    acdata: [], //allcheckdata
    aclen: 0, //体检记录的条数
    recordsid: 0, //体检总表的id,当前宝宝的那一条体检记录
    arrtjset: ["注意饮食的规律与营养搭配的合理，注意睡眠的充足；保持孩子适量的体育锻炼与户外运动，不要久坐。保持身高、体重协调发展。", "饮食中应以低脂肪、低碳水化合物、低热量为原则，少吃甜食；适量减少食量，先从主食开始；务必加强体育锻炼、积极参加户外运动，改善饮食习惯和加强运动相结合的健康方式减肥。", "请注意控制体重。在保持饮食的规律与营养搭配的合理情况下，适量减少猪肉、花生、油炸食物等含油脂较多的食物；加强体育锻炼、积极参加户外运动来控制体内脂肪含量，控制体重在正常范围以内。", "请注意增重、改善体质。有效的体育锻炼会强身健体、增大食量，饮食中多增加肉类、蛋类等蛋白质成分较多的食物，辅食多吃些香蕉、葡萄、橘子等含糖较多的水果；同时注意保持良好的睡眠质量。", "请注意保持；在坚持体育锻炼情况下，请注意饮食的规律与营养搭配的合理，保证作息习惯规律，睡眠充足；保持孩子身心健康全面发展。", "请注意保持；在坚持体育锻炼情况下，请注意饮食的规律与营养搭配的合理，保证作息习惯规律，睡眠充足；保持孩子身心健康全面发展。", "请注意增强；饮食上保证蔬菜、水果、高蛋白类食物（肉类、蛋类等）的摄入，然后加强跑步、打球等体育锻炼，保证充足睡眠；使孩子身体素质得到更快的提升。", "请注意改善体质；饮食上保证蔬菜、水果、高蛋白类食物（肉类、蛋类等）的摄入，然后制定体育锻炼计划，坚持户外运动，保证充足睡眠；注重身高、体重、身体素质的协调发展。"], //正常、肥胖、超重、营养不良、优秀级、良好级、及格级、不及格
    currindex: 0, //currcordindex，当前体检索引
    arrdate: [], //成长记录日期数组
    evalType: 0, //评价类型    0身高别体重, 1年龄别身高, 2年龄别体重
    standard: 0, //体检标准
    standName: { //标准名称
        "0": "*参照2006年WHO儿童生长标准制定",
        "1": "*参照2015年儿童保健体格生长评价标准制定"
    },
    series: {}, //缓存series标准
    objfypg: {
        1: "不会简单叙说事情经过",
        2: "不会知道自己的性别",
        3: "不会用筷子吃饭",
        4: "不会单脚跳"
    },
    objhuanbname: {
        2: "肺炎",
        3: "腹泻",
        4: "外伤"
    },
    objguidance: {
        1: "合理膳食",
        2: "生长发育",
        3: "疾病预防",
        4: "预防伤害",
        5: "口腔保健"
    },
    objxbcontent: {
        1: "外观异常",
        2: "心动过速",
        3: "心动过缓",
        4: "心脏杂音",
        5: "心率不齐",
        6: "呼吸音粗糙",
        7: "干啰音",
        8: "湿啰音"
    },
    objfbcontent: {
        1: "外观异常",
        2: "肝脏肿大",
        3: "脾脏肿大",
        4: "有压痛",
        5: "有包块"
    },
    ageContent: {
        "1.06":{
            'sljc':'',//视力检查
            'ywjc':'',//眼位检查
            'qgjc':'',//屈光检查
            'dyzgywsy':'',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'其他',v:1}],//转诊建议
            'jkzd':["指导家长注意观察儿童有无歪头视物、视物距离过近等行为。","保证充足睡眠和营养。","至少每半年带儿童接受一次眼保健和视力检查。","家长给儿童阅读绘本，减少近距离用眼时间。","建议儿童禁用手机、电脑等视屏类电子产品。","户外活动不少2小时/天。","避免儿童玩尖锐物、接触强酸强碱等洗涤剂。","教育、帮助儿童经常洗手，不揉眼睛，不带患传染性眼病儿童到人群聚集场所活动。","注意观察儿童眼病有无异常，若发现异常及时就医。"],
            'jkzd2':[{k:"儿童远视储备量不足，需进一步排查发生近视风险，并改变不良用眼行为，定期检查。",v:32},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"其他指导",v:1}],//健康指导
        },
        "2":{
            'sljc':'',//视力检查
            'ywjc':'1',//眼位检查
            'qgjc':'1',//屈光检查
            'dyzgywsy':'1',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'眼位检查异常',v:16},{k:'屈光筛查异常',v:32},{k:'接受专项检查',v:64},{k:'单眼遮盖厌恶试验异常',v:128},{k:'其他',v:1}],//转诊建议
            'jkzd':["指导家长注意观察儿童有无歪头视物、视物距离过近等行为。","保证充足睡眠和营养。","至少每半年带儿童接受一次眼保健和视力检查。","家长给儿童阅读绘本，减少近距离用眼时间。","建议儿童禁用手机、电脑等视屏类电子产品。","户外活动不少2小时/天。","避免儿童玩尖锐物、接触强酸强碱等洗涤剂。","教育、帮助儿童经常洗手，不揉眼睛，不带患传染性眼病儿童到人群聚集场所活动。","注意观察儿童眼病有无异常，若发现异常及时就医。"],
            'jkzd2':[{k:"儿童远视储备量不足，需进一步排查发生近视风险，并改变不良用眼行为，定期检查。",v:32},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"带儿童到县级妇幼保健机构或具备条件的县级医疗机构做红光反射检查、眼位检查、单眼遮盖厌恶试验。",v:64},
                {k:"其他指导",v:1}],//健康指导
        },
        "2.06":{
            'sljc':'',//视力检查
            'ywjc':'',//眼位检查
            'qgjc':'',//屈光检查
            'dyzgywsy':'',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'其他',v:1}],//转诊建议
            'jkzd':["指导家长注意观察儿童有无歪头视物、视物距离过近等行为。","保证充足睡眠和营养。","至少每半年带儿童接受一次眼保健和视力检查。","家长给儿童阅读绘本，减少近距离用眼时间。","建议儿童禁用手机、电脑等视屏类电子产品。","户外活动不少2小时/天。","避免儿童玩尖锐物、接触强酸强碱等洗涤剂。","教育、帮助儿童经常洗手，不揉眼睛，不带患传染性眼病儿童到人群聚集场所活动。","注意观察儿童眼病有无异常，若发现异常及时就医。"],
            'jkzd2':[{k:"儿童远视储备量不足，需进一步排查发生近视风险，并改变不良用眼行为，定期检查。",v:32},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"带儿童到县级妇幼保健机构或具备条件的县级医疗机构做红光反射检查、眼位检查、单眼遮盖厌恶试验。",v:64},
                {k:"其他指导",v:1}],//健康指导
        },
        "3":{
            'sljc':'',//视力检查
            'ywjc':'1',//眼位检查
            'qgjc':'1',//屈光检查
            'dyzgywsy':'1',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'眼位检查异常',v:16},{k:'屈光筛查异常',v:32},{k:'接受专项检查',v:64},{k:'单眼遮盖厌恶试验异常',v:128},{k:'其他',v:1}],//转诊建议
            'jkzd':["指导家长注意观察儿童有无歪头视物、视物距离过近等行为。","保证充足睡眠和营养。","至少每半年带儿童接受一次眼保健和视力检查。","家长给儿童阅读绘本，减少近距离用眼时间。","建议儿童禁用手机、电脑等视屏类电子产品。","户外活动不少2小时/天。","避免儿童玩尖锐物、接触强酸强碱等洗涤剂。","教育、帮助儿童经常洗手，不揉眼睛，不带患传染性眼病儿童到人群聚集场所活动。","注意观察儿童眼病有无异常，若发现异常及时就医。"],
            'jkzd2':[{k:"儿童远视储备量不足，需进一步排查发生近视风险，并改变不良用眼行为，定期检查。",v:32},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"带儿童到县级妇幼保健机构或具备条件的县级医疗机构做红光反射检查、眼位检查、单眼遮盖厌恶试验。",v:64}
            ],//健康指导
        },
        "4":{
            'sljc':'1',//视力检查
            'ywjc':'1',//眼位检查
            'qgjc':'1',//屈光检查
            'dyzgywsy':'',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'视力检查异常',v:8},{k:'眼位检查异常',v:16},{k:'屈光筛查异常',v:32},{k:'接受专项检查',v:64},{k:'其他',v:1}],//转诊建议
            'jkzd':["至少每年带儿童进行一次眼保健和视力检查。","培养良好用眼习惯，科学护眼和防控近视。","避免接触和使用电子视屏类电子产品。","减少近距离用眼时间。","增加户外活动，每天2小时以上在室外活动“目”浴阳光。","读写和握笔姿势正确。","保证儿童充足睡眠和营养。","注意观察儿童视物有无异常，一旦发现异常，要到正规医疗机构进行医学验光，并遵医嘱正确矫正。"],
            'jkzd2':[{k:"儿童远视储备量不足。需进一步检查并改变不良用眼行为，定期检查",v:2},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"科学护眼和防控近视。",v:8},
                {k:"带儿童到县级妇幼保健机构或具备条件的县级医疗机构做眼位检查、屈光筛查。",v:16},
                {k:"其他指导",v:1}],//健康指导
        },
        "5":{
            'sljc':'1',//视力检查
            'ywjc':'1',//眼位检查
            'qgjc':'1',//屈光检查
            'dyzgywsy':'',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'视力检查异常',v:8},{k:'眼位检查异常',v:16},{k:'屈光筛查异常',v:32},{k:'接受专项检查',v:64},{k:'其他',v:1}],//转诊建议
            'jkzd':["至少每年带儿童进行一次眼保健和视力检查。","培养良好用眼习惯，科学护眼和防控近视。","避免接触和使用电子视屏类电子产品。","减少近距离用眼时间。","增加户外活动，每天2小时以上在室外活动“目”浴阳光。","读写和握笔姿势正确。","保证儿童充足睡眠和营养。","注意观察儿童视物有无异常，一旦发现异常，要到正规医疗机构进行医学验光，并遵医嘱正确矫正。"],
            'jkzd2':[{k:"儿童远视储备量不足。需进一步检查并改变不良用眼行为，定期检查",v:2},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"科学护眼和防控近视。",v:8},
                {k:"带儿童到县级妇幼保健机构或具备条件的县级医疗机构做眼位检查、屈光筛查。",v:16},
                {k:"其他指导",v:1}],//健康指导
        },
        "6":{
            'sljc':'1',//视力检查
            'ywjc':'1',//眼位检查
            'qgjc':'1',//屈光检查
            'dyzgywsy':'',//单眼遮盖厌恶试验
            'zzjy':[{k:'眼外观检查异常',v:2},{k:'视物行为异常',v:4},{k:'视力检查异常',v:8},{k:'眼位检查异常',v:16},{k:'屈光筛查异常',v:32},{k:'接受专项检查',v:64},{k:'其他',v:1}],//转诊建议
            'jkzd':["至少每年带儿童进行一次眼保健和视力检查。","培养良好用眼习惯，科学护眼和防控近视。","避免接触和使用电子视屏类电子产品。","减少近距离用眼时间。","增加户外活动，每天2小时以上在室外活动“目”浴阳光。","读写和握笔姿势正确。","保证儿童充足睡眠和营养。","注意观察儿童视物有无异常，一旦发现异常，要到正规医疗机构进行医学验光，并遵医嘱正确矫正。"],
            'jkzd2':[{k:"儿童远视储备量不足。需进一步检查并改变不良用眼行为，定期检查",v:2},
                {k:"若儿童存在斜视、弱视等眼病及危险因素，到眼科检查。",v:4},
                {k:"科学护眼和防控近视。",v:8},
                {k:"带儿童到县级妇幼保健机构或具备条件的县级医疗机构做眼位检查、屈光筛查。",v:16},
                {k:"其他指导",v:1}],//健康指导
        }
    }
};
var arrstand = {
    "ante": "通过体育锻炼能够提高关节的灵活性，改善关节周围软组织的功能以及肌肉、韧带、肌腱的伸展性。柔韧性的提高，对增强身体的协调能力，更好地发挥力量、速度等素质，提高技能和技术，防止运动创伤等都有积极的作用。",
    "fat": "合理喂养，调整饮食结构，避免幼儿挑食、偏食，避免暴饮暴食，选择高蛋白、低热量、高维生素、低脂肪的食物和饮品，注意粗细搭配，均衡饮食，增加各类蔬菜的食用。增加每天运动时间及强度。",
    "fine": "根据幼儿的生长发育和体育活动的规律，以多种形式：如体育游戏，户外活动，散步，户外小型器械练习等，保证幼儿每天有1-2个小时的锻炼时间，让幼儿进行多方位的锻炼，促进幼儿运动能力的发展与提高。",
    "health": "生活有规律，保持良好的睡眠质量。均衡膳食，合理喂养。正确刷牙，预防龋齿。培养良好的用眼卫生习惯。加强户外活动。",
    "malnu": "合理喂养，调整饮食结构，避免幼儿挑食、偏食及吃单一食物，选择高蛋白、高热量、高维生素、低脂肪的食物和饮品，注意粗细搭配，肉、鱼、奶类、豆制品及各类蔬菜均要安排食用。有效的体育锻炼会增加进食量。注意保持良好的睡眠质量。",
    "pinxue": "生活有规律，做适合幼儿的适量运动。合理喂养，调整饮食结构，避免幼儿挑食、偏食及吃单一食物，选择高蛋白、高维生素、低脂肪、高铁含量的食物和饮品，注意粗细搭配，肉、鱼、奶类、豆制品及各类蔬菜均要安排食用。",
    "pinxuefat": "合理喂养，调整饮食结构，避免幼儿挑食、偏食及吃单一食物，选择高蛋白、低热量、高维生素、低脂肪、高铁含量的食物和饮品，注意粗细搭配，肉、鱼、奶类、豆制品及各类蔬菜均要安排食用，增加各类蔬菜的食用。生活有规律，做适合幼儿的运动，保持良好的睡眠质量。",
    "pinxuemalnu": "生活有规律，做适合幼儿的适量运动。合理喂养，调整饮食结构，避免幼儿挑食、偏食及吃单一食物，选择高蛋白、高维生素、低脂肪、高铁含量的食物和饮品，注意粗细搭配，肉、鱼、奶类、豆制品及各类蔬菜均要安排食用。注意保持良好的睡眠质量。",
    "quchi": "幼儿早晚均需刷牙，正确选择合适的牙刷和牙膏，掌握正确刷牙方法。减少每天吃甜食及饮用碳酸饮品的频率，预防龋病的发生。定期进行口腔检查。",
    "sbalancebeam": "发展平衡能力有利于提高运动器官的功能和前庭器官的机能，提高适应复杂环境的能力和自我保护的能力，平衡能力可以通过静态的平衡活动和动态的平衡活动来提高。",
    "sdjump": "通过跳跃的练习以及与之相关的游戏使幼儿掌握跳跃正确方法和姿势，发展跳跃能力，提高幼儿身体的协调性、灵活性、和持久性。",
    "shili": "及时带幼儿到医院进行详细检查。培养良好的用眼卫生习惯，改变目前用眼不良习惯，训练孩子正确的读、写姿势。减少电子产品的使用。合理营养，平衡膳食，加强户外活动。",
    "sjump": "提高立定跳远成绩，力量是基础，特别要提高膝、踝、髋三个关节的协调用力及爆发用力的能力。",
    "srun": "通过练习和游戏使幼儿掌握减速、降重心、转身迅速，而提高灵敏素质和速度。",
    "ssoftball": "投掷训练能加强幼儿的上肢、腰部、腹部等肌肉力量，使上肢关节、韧带得到锻炼，并需要幼儿投掷出手角度与身体用力的协调性相配合。"
};
require.config({
    baseUrl: '../', //指定js文件的基路径
    paths: {
        jquery: 'sys/jquery',
        system: 'sys/system',
        common: 'sys/common',
        echarts: "plugin/echarts3.4/echarts.common.min",
        layer: "plugin/layer_mobile/layer",
        'jquery-weui': 'wxcommon/weui/jquery-weui'  // 新增 jQuery WeUI 模块路径
    },
    shim: {  // 新增 shim 配置，确保 jquery-weui 依赖于 jquery
        'jquery-weui': {
            deps: ['jquery'],
            exports: '$'
        }
    },
    waitSeconds: 0
});
require(['jquery', 'jquery-weui', 'system', 'common', 'echarts', 'layer'], function (j, jweui, s, c, e, l) {
    // 验证 jQuery WeUI 模块加载状态
    console.log('jQuery WeUI 模块加载验证:', {
        alert: typeof $.alert,
        toast: typeof $.toast,
        photoBrowser: typeof $.photoBrowser,
        modal: typeof $.modal
    });

    window.echarts = e;
    if(Arg("token")){
        localStorage.setItem("token", Arg("token")); //存储token
    }
    initEvent();
    objdata.checkid = Arg("checkid");
    objdata.read_id = Arg("read_id");
    objdata.stuno = Arg("stuno");
    objdata.yeyid = Arg("yeyid");
    objdata.cguid = Arg("cguid");
    console.log(objdata.checkid);
    console.log(objdata.stuno);
    initMyinfo(objdata.stuno);
    initListener();
});

/**
 * 初始化页面事件
 */
function initEvent() {
    //身高别体重、身高别年龄、体重别年龄
    $(".sp_eval").off('click').on('click', function () {
        var $this = $(this);
        if (!$this.hasClass("current")) {
            $this.addClass("current").siblings().removeClass("current");
            objdata.evalType = $this.index();
            //切换图表类型显示
            drawEchart();
        }
    });

    $("#bmiask").click(function () {
        $("#bmialter").show();

    });
    $("#yesbmi").click(function () {
        $("#bmialter").hide();
    });

    $(".out-container").off("click", ".checklist").on("click", ".checklist", function () {
        var $this = $(this);
        var arrImg = $this.find("img").map(function () {
            var url = $(this).data("img");
            if(!url.endsWith(".pdf")){
                return url;
            }
        }).get();
        var pb = $.photoBrowser({
            /*items: [
                "https://cdty.fy114.com.cn:801/userfiles/chengdu/fuyou/userimg/1702621306859031675.png",
                "images/report_nanhai.png"
            ]*/
            items:arrImg
        });
        pb.open();
    }).off("click", ".pdf").on("click", ".pdf", function (e) {
        e.stopPropagation();
        var url = $(this).data("img");
        // window.open(url, '_blank');
        window.open('../plugin/pdfjs/web/viewer.html?file=' + url, '_blank');
    }).off("click", ".waiguandetail").on("click", ".waiguandetail", function (e) {
        var waiguandetail = getyanwgDetail(objdata.curobj,$(this).data("type"));
        $.alert(waiguandetail || '暂无详细信息', '眼外观异常详情');
    });

    // 预约挂号按钮点击事件
    $("#appointmentBtn").off('click').on('click', function() {
        // 检查是否在微信小程序环境中
        if (typeof wx !== 'undefined' && wx.miniProgram) {
            // 跳转到小程序预约挂号页面
            wx.miniProgram.reLaunch({
                url: '/pages/yygh/selectdepartment?fromtype=baogao&fromtable=check_child_result_read&fromid='+Arg("read_id"),
                success: function(res) {
                    console.log('跳转预约挂号页面成功', res);
                },
                fail: function(err) {
                    console.log('跳转预约挂号页面失败', err);
                    // 如果跳转失败，可以显示提示信息
                    $.toast('跳转失败，请稍后重试', 'text');
                }
            });
        } else {
            // 如果不在小程序环境中，显示提示
            $.toast('请在小程序中使用此功能', 'text');
        }
    });
}



/**
 * 初始化我的宝宝信息
 */
function initMyinfo(stuno) {
    $.sm(function (re, err, obj) {
        if (obj) {
            var arrtemimg = ['images/yeimg1.jpg', 'images/yeimg2.jpg', 'images/yeimg3.jpg'];
            var info = obj,
                stuno = info.stuno,
                img = (info.photopath ? ossPrefix + info.photopath + ossParam : (info.sex === "男" ? "images/img_boy.png" : "images/img_girl.png")),
                stuname = info.stuname;
            objdata.curbid = stuno;
            objdata.curname = info.stuname;
            objdata.sex = info.sex;
            var yeyname = info.yeyname,
                classno = info.classno,
                claname = info.claname,
                sex = info.sex,
                ptype = info.ptype,
                birthday = info.birthday && info.birthday.substr(0, 10),
                t1 = (birthday && new Date(birthday.replace(/-/g, "/"))) ? new Date(birthday.replace(/-/g, "/")) : new Date('2012-08-19'),
                t2 = new Date(),
                age = GetAge(t1, t2);
            objdata.curt1 = t1;
            objdata.curobjbabyinfo = {
                stuname: stuname,
                sex: sex,
                age: age,
                classno: classno,
                claname: claname,
                ptype: ptype
            };

            $('#yeyname').html(yeyname);
            $('#claname').html(claname);
            $('#curstuname').html(stuname);
            $('#stuname').html(stuname);
            $('#curage').html(convertAge(age));
            $('#headimg').prop("src", (img ? img : arrtemimg[i])).show();
            $('#curimg').prop("src", (img ? img : arrtemimg[i])).show();
            $('#xiaoren').prop("src", (objdata.sex == "女" ? "images/report_nvhai.png" : "images/report_nanhai.png"));

            initCheck(info);
            drawEchart();
        } else {
            myAlert('没有关联宝宝请先关联');
        }
    }, ['xcx.cpmch.getStuChildResult', objdata.cguid, objdata.checkid, objdata.yeyid, stuno]);
};

function initbmiTpl(curobj) {
    if(!curobj.rday){return}
    $.sm(function (re, err,obj) {
        if(obj && obj.p97){
            $("#bmitpl").text("<" + obj.p97);
        }
        console.log(obj);
    }, ["check.getbmitpl", JSON.stringify({normal: 1, birthday: curobj.birthday, rday: curobj.rday,sex:curobj.sex})]);
}

function convertAge(age) {
    var arrage = age.split(".");
    return arrage[0] + "岁" + (parseInt(arrage[1] || 0)) + "个月";
}

/**
 * 初始化体检信息
 * @param curstuno
 */
function initCheck(curobj) {
    console.log("curobj", curobj);
    objdata.curobj = curobj;
    if (curobj.id) {
        objdata.curonedata = curobj;
        var curacdata = curobj || [],
            recordsid = curacdata.id,
            curtjname = curacdata.checkname,
            curtjtime = curacdata.rday;
        objdata.recordsid = recordsid;
        //显示顶部的信息
        $("#curtjname").html(curtjname);
        $("#tjdate").html(curtjtime);
        $("#curtjage").html(convertAge(curobj.age + ""));

        initCheckData(curobj);

        $('#divhasdata').show();
        $('#nodata').hide();

        //处理图表
        drawEchart();

        //控制体检项目 显示与隐藏
        var recordneck = curobj.recordneck?curobj.recordneck.split(","):[];
        for (var i = 0; i < recordneck.length; i++) {
            var oned = recordneck[i];
            $("#div" + oned).show();
            $(".div" + oned).show();
        }

        //bmi标准范围
        initbmiTpl(curobj);
    } else {
        $('#divhasdata').hide();
        $('#nodata').show();
    }
}

/**
 * 初始化体检数据
 */
function initCheckData(obj) {
    // 改为直接获取check_child_result 表数据值
    var tcdata = obj;
    //无身高、体重信息时 不显示下面的体检的数据
    if (!tcdata.hight && !tcdata.weight) {
        $(".divweightheight").each(function () {
            $(this).hide();
        });
    } else {
        $('.partdata').show();
        $("#nodata2").hide();
    }

    //人形图赋值
    $(".heightnum").text((tcdata.hight || "") + "cm");
    $(".weightnum").text((tcdata.weight || "") + "kg");
    if (objdata.preheight && tcdata.hight && (tcdata.hight - objdata.preheight != 0)) {//有身高增长数据
        $("#inhight").text((tcdata.hight - objdata.preheight).toFixed(1) + "cm");
    } else {
        $("#inhight").parent("div").hide();
    }
    if (objdata.prewidth && tcdata.weight && (tcdata.weight - objdata.prewidth != 0)) {//有体重增长数据
        $("#inweight").text((tcdata.weight - objdata.prewidth).toFixed(2) + "kg");
    } else {
        $("#inweight").parent("div").hide();
    }

    var arrdata = []; //根据体检情况的 建议项数组
    if (tcdata.wufat != "" && tcdata.malnu != "") {
        if (tcdata.wufat != "正常") {
            arrdata.push(tcdata.wufat);
        }
    }
    if (tcdata.hematin != "") {
        if (tcdata.hematindivision != "正常" && tcdata.hematindivision != "")
            arrdata.push("贫血");
    }
    if (tcdata.eyecheckdate != "") {
        if (tcdata.islow == 1)
            arrdata.push("视力低常");
    }
    if (tcdata.car1 > 0||tcdata.kouqiang==2) {
        arrdata.push("龋齿");
    }

    //读取成长记录
    // chengzhangdata();

    //身高体重部分渲染视图
    renderHw();
    //五官部分渲染视图
    renderWg();
    //内科部分渲染试图
    // renderNk();
    //其他体检项目
    renderOther();

    //口腔数据
    renderKq(tcdata);

    // // 体检建议 1.先显示自己的体检建议 2.再显示CHDC端的 (如果有的话)
    //1.先显示自己的体检建议
    var suginfo = '暂无',
        suggInfo = "";//自己设置的的体检建议
    if (suggInfo) {// && $.parseJSON(re1[4][0])
        var suggest = suggInfo && $.parseJSON(suggInfo);
        arrstand = suggest;
    }
    //体检建议1
    $('#tijianjianyi1').html(getTjpjInfo(arrdata));

    //体检建议2 医院建议
    $("#tijianjianyi2").html("");
    $("#yiyuaninfo").html("");

    //身高体重部分渲染
    function renderHw() {
        var starObj = {
            "下": '<i class="iconfont icon_star current"></i> <i class="iconfont icon_star"></i><i class="iconfont icon_star"></i><i class="iconfont icon_star"></i><i class="iconfont icon_star"></i>',
            "中下": '<i class="iconfont icon_star current"></i> <i class="iconfont icon_star current"></i><i class="iconfont icon_star"></i><i class="iconfont icon_star"></i><i class="iconfont icon_star"></i>',
            "中": '<i class="iconfont icon_star current"></i> <i class="iconfont icon_star current"></i><i class="iconfont icon_star current"></i><i class="iconfont icon_star"></i><i class="iconfont icon_star"></i>',
            "中上": '<i class="iconfont icon_star current"></i> <i class="iconfont icon_star current"></i><i class="iconfont icon_star current"></i><i class="iconfont icon_star current"></i><i class="iconfont icon_star"></i>',
            "上": '<i class="iconfont icon_star current"></i> <i class="iconfont icon_star current"></i><i class="iconfont icon_star current"></i><i class="iconfont icon_star current"></i><i class="iconfont icon_star current"></i>'
        };
        //new
        $("#fwheight").text((tcdata.ahstandard && tcdata.ahstandard) || "无");//身高范围
        $("#tjheight").text((tcdata.hight) || "无");//身高
        $("#fwweight").text((tcdata.awstandard && tcdata.awstandard) || "无");//体重范围
        $("#tjweight").text((tcdata.weight && tcdata.weight) || "无");//体重
        if (objdata.preheight && tcdata.hight && (tcdata.hight - objdata.preheight != 0)) {//有身高增长数据
            $("#intjheight").text((tcdata.hight - objdata.preheight).toFixed(1));
        } else {
            $("#intjheight").text("无").prev().removeClass();
        }
        if (objdata.prewidth && tcdata.weight && (tcdata.weight - objdata.prewidth != 0)) {//有体重增长数据
            $("#intjweight").text((tcdata.weight - objdata.prewidth).toFixed(2));
        } else {
            $("#intjweight").text("无").prev("i").hide();
        }

        //年龄别身高
        if (tcdata.agehe) {
            $("#agehe").html(starObj[decodeURIComponent(tcdata.agehe)]).next("div").text(decodeURIComponent(tcdata.agehe));
        } else {
            $("#agehe").html("无").next("div").text("无");
        }
        //年龄别体重
        if (tcdata.agewe) {
            $("#agewe").html(starObj[decodeURIComponent(tcdata.agewe)]).next("div").text(decodeURIComponent(tcdata.agewe));
        } else {
            $("#agewe").html("无").next("div").text("无");
        }
        //身高别体重
        if (tcdata.hewe) {
            $("#hewe").html(starObj[decodeURIComponent(tcdata.hewe)]).next("div").text(decodeURIComponent(tcdata.hewe));
        } else {
            $("#hewe").html("无").next("div").text("无");
        }
        //BMI 值
        if (tcdata.bmi) {
            $("#bmidiv").text(tcdata.bmi);
        } else {
            $("#bmidiv").text("无");
        }
        //五分法 当做评价 2019.4.26需求更改的
        var wufenfa = $(".wufenfa");
        for (var i = 0; i < wufenfa.length; i++) {
            var obj1 = wufenfa[i];
            var $obj1 = $(obj1);
            if ($obj1.text() == tcdata.wufat) {
                $obj1.prev("img").attr("src", "images/normals_icon_HL.png").parent().addClass("current").siblings().removeClass("current").find("img").attr("src", "images/normals_icon.png");
                break;
            }
            $obj1.parent().siblings().removeClass("current").find("img").attr("src", "images/normals_icon.png");
        }

    }

    //    五官部分渲染
    function renderWg() {
        // new 视力部分
        if (tcdata.eyecheckdate) {
            changeClass(((tcdata.lrank == "正常" && tcdata.leye) || (!tcdata.lrank || !tcdata.leye)), $("#lefteye"), ((!tcdata.lrank || !tcdata.leye) ? "(--)" : tcdata.lrank == "正常" ? "左眼正常" : "左眼异常")); //正常或未登记都显示正常
            changeClass((tcdata.rrank == "正常" && tcdata.reye || (!tcdata.rrank || !tcdata.reye)), $("#righteye"), ((!tcdata.rrank || !tcdata.reye) ? "(--)" : tcdata.rrank == "正常" ? "右眼正常" : "右眼异常"));
        }
        $("#eyediv").empty();
        var shayanObj = {"-1": "--", "0": "-", "1": "+", "2": "++", "3": "+++"};
        var eyeStr = '<h6 class="weui-flex" style="align-items: center;margin: 10px 16px 5px 0;font-size: 15px;"><span class="eyemark-img weui-flex_center" style="margin-right: 5px;"><img src="images/eyeleft_icon2.png" style="width: 14px;height: 8px;"></span>视力：</h6>';
        eyeStr += '<div style="margin: 7px 0;">';
        var haseye = false;
        if (tcdata.eyecheckdate) {
            eyeStr += ' <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">左</span>\
                    <div class="check-text">视力：' + tcdata.leye + '<span style="color: ' + ((tcdata.lrank == "正常" && tcdata.leye) ? "#00d36d;" : "#ff9232;") + '">（' + (!tcdata.leye ? "--" : tcdata.lrank) + '）</span></div>\
                </div>';
            haseye = true;
        }
        if (tcdata.islchl) {//左右眼 沙眼
            // eyeStr += '<div class="check-cell weui-flex">\
            //         <span class="check-name weui-flex">' + (haseye ? "" : '<img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">左') + '</span>\
            //         <div class="check-text">沙眼：(' + shayanObj[tcdata.islchl] + ')</div>\
            //     </div>';
        }
        eyeStr += ' </div><div style="margin: 7px 0;">';
        if (tcdata.eyecheckdate) {
            eyeStr += '<div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">右</span>\
                    <div class="check-text">视力：' + tcdata.reye + '<span style="color: ' + ((tcdata.rrank == "正常" && tcdata.reye) ? "#00d36d;" : "#ff9232;") + '">（' + (!tcdata.reye ? "--" : tcdata.rrank) + '）</span></div>\
                </div>';
        }
        if (tcdata.isrchl) {
            // eyeStr += ' <div class="check-cell weui-flex">\
            //         <span class="check-name weui-flex">' + (haseye ? "" : '<img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">右') + '</span>\
            //         <div class="check-text">沙眼：(' + shayanObj[tcdata.isrchl] + ')</div>\
            //     </div>';
        }
        eyeStr += '</div>';
        // $("#eyediv").html(eyeStr);//回显数据

        //屈光度
        if (tcdata.eyedioptercheckdate) {
            var qgdStr = '<h6 class="weui-flex" style="align-items: center;margin: 10px 16px 5px 0;font-size: 15px;"><span class="eyemark-img weui-flex_center" style="margin-right: 5px;"><img src="images/eyeleft_icon2.png" style="width: 14px;height: 8px;"></span>屈光度（视力）：' + tcdata.eyediopterrank + '</h6>\
               <div style="margin: 7px 0;">\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">左</span>\
                    <div class="check-text">左眼S：' + tcdata.lseye + '<span style="color: ' + (tcdata.lsrank == "正常" ? "#00d36d;" : "#ff9232;") + '">（' + (tcdata.lsrank == "" ? "--" : tcdata.lsrank) + '）</span></div>\
                </div>\
                <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"></span>\
                    <div class="check-text">左眼C：' + tcdata.lceye + '<span style="color: ' + (tcdata.lcrank == "正常" ? "#00d36d;" : "#ff9232;") + '">（' + (tcdata.lcrank == "" ? "--" : tcdata.lcrank) + '）</span></div>\
                </div>\
                </div>\
                <div style="margin: 7px 0;">\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">右</span>\
                    <div class="check-text">右眼S：' + tcdata.rseye + '<span style="color: ' + (tcdata.rsrank == "正常" ? "#00d36d;" : "#ff9232;") + '">（' + (tcdata.rsrank == "" ? "--" : tcdata.rsrank) + '）</span></div>\
                </div>\
                <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"></span>\
                    <div class="check-text">右眼C：' + tcdata.rceye + '<span style="color: ' + (tcdata.rcrank == "正常" ? "#00d36d;" : "#ff9232;") + '">（' + (tcdata.rcrank == "" ? "--" : tcdata.rcrank) + '）</span></div>\
                </div>\
                </div>';
            //回显数据
            $("#qgddiv").html(qgdStr);
        } else {
            $("#qgddiv").empty();
        }

        //眼科部分
        if (tcdata.yan_ckdate) {
            changeClass((tcdata.lrank == "正常" && tcdata.yan_l || (!tcdata.lrank || !tcdata.yan_l)), $("#lefteye"), ((!tcdata.lrank || !tcdata.yan_l) ? "(--)" : tcdata.lrank == "正常" ? "左眼正常" : "左眼异常")); //正常或未登记都显示正常
            changeClass((tcdata.rrank == "正常" && tcdata.yan_r || (!tcdata.rrank || !tcdata.yan_r)), $("#righteye"), ((!tcdata.rrank || !tcdata.yan_r) ? "(--)" : tcdata.rrank == "正常" ? "右眼正常" : "右眼异常"));
        }
        $("#divyanke").empty();
        var shayanObj = {"-1": "--", "0": "-", "1": "+", "2": "++", "3": "+++"};
        var eyeStr = '<h6 class="weui-flex" style="align-items: center;margin: 10px 16px 5px 0;font-size: 15px;"><span class="eyemark-img weui-flex_center" style="margin-right: 5px;"><img src="images/eyeleft_icon2.png" style="width: 14px;height: 8px;"></span>眼科：</h6>';
        var yan_stateObj = {"1": "未检查", "2": "正常", "3": "异常"}
        var yan_scjgObj = {1:"未见异常",2:"异常",3:"未检查",4:"可疑屈光不正"}
        eyeStr += '<div style="margin: 7px 0;"><div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">总体情况：' + (yan_stateObj[tcdata.yan_state] || "") + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">备注：' + (tcdata.yan_content || "") + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">屈光筛查结果：' + (tcdata.yan_ckdate && yan_scjgObj[tcdata.yan_scjg] || "") + '</div></div>'
        //视物行为观察,眼位检查
        var yan_swxwgcObj = {"1": "未检查", "2": "正常", "3": "异常"};
        var ywjcObj = {"1": "未检查", "2": "正常", "3": "异常"};
        eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">视物行为观察：' + (yan_swxwgcObj[tcdata.yan_swxwgc] || "") + '</div></div>' +
            '<div style="display:' + (!tcdata.yan_swxwgcdetail ? "none" : "") + '" class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">备注：' + (tcdata.yan_swxwgcdetail) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">眼位检查：' + (ywjcObj[tcdata.ywjc] || "") + '</div></div>' +
            '<div style="display:' + (!tcdata.ywjcdetail ? "none" : "") + '" class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">其他：' + (tcdata.ywjcdetail) + '</div></div></div></div></div>';


        eyeStr += '<div style="margin: 7px 0;">';
        var haseye = false;
        var slresObj={"1": "未检查", "2": "正常", "3": "视力低常"};//1未检查 2正常 3视力低常
        if (tcdata.yan_ckdate && tcdata.age >= 4) {
            eyeStr += ' <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">左</span>\
                    <div class="check-text">视力：' + tcdata.yan_l + '<span style="color: ' + ((tcdata.lrank == "正常" && tcdata.yan_l) ? "#00d36d;" : "#ff9232;") + '">（' + (!tcdata.yan_l ? "--" : tcdata.lrank) + '）</span></div>\
                </div></div>';
            eyeStr+='<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">左眼视力结果：' + (slresObj[tcdata.slres_l]?slresObj[tcdata.slres_l]+"；"+tcdata.slres_lldetail:"") + '</div></div>'
            haseye = true;
        }
        if (tcdata.islchl) {//左右眼 沙眼
            // eyeStr += '<div class="check-cell weui-flex">\
            //         <span class="check-name weui-flex">' + (haseye ? "" : '<img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">左') + '</span>\
            //         <div class="check-text">沙眼：(' + shayanObj[tcdata.islchl] + ')</div>\
            //     </div>';
        }
        var yan_wgr_detail_L = getyanwgDetail(tcdata, 'l');
        eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">左SE：' + showSE_DS_DC(tcdata.yan_lse) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">左DS：' + showSE_DS_DC(tcdata.yan_lds) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">左DC：' + showSE_DS_DC(tcdata.yan_ldc) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">左A：' + showSE_DS_DC(tcdata.yan_la) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">眼外观：' + (tcdata.yan_ckdate && tcdata.yan == 1 ? '正常' : tcdata.yan_ckdate && tcdata.yan == 2 ? '异常' + (yan_wgr_detail_L?'<button class="detail-btn waiguandetail" data-type="l">详细</button>':'') : '') + '</div></div>'

        //单眼遮盖厌恶试验
        var dyzgywsy_lObj = {"1": "未检查", "2": "正常", "3": "异常"};
        if (tcdata.age <4) {//4岁及以上不显示
            eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">单眼遮盖厌恶试验：' + (dyzgywsy_lObj[tcdata.dyzgywsy_l] || "") + '</div></div>';
        }
        //左屈光结果
        var qgjg_lObj = {"1": "未检查", "2": "正常", "3": "可疑屈光不正"};
        eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">左屈光结果：' + (qgjg_lObj[tcdata.qgjg_l]||"") + '</div></div>';
        eyeStr += '<div class="check-cell weui-flex" style="display: ' + (!tcdata.qgjg_ldetail ? "none" : "") + '"><span class="check-name weui-flex"></span><div class="check-text">左屈光结果-其他：' + (tcdata.qgjg_ldetail) + '</div></div>';

        eyeStr += ' </div><div style="margin: 7px 0;">';
        if (tcdata.yan_ckdate && tcdata.age >= 4) {
            eyeStr += '<div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">右</span>\
                    <div class="check-text">视力：' + tcdata.yan_r + '<span style="color: ' + ((tcdata.rrank == "正常" && tcdata.yan_r) ? "#00d36d;" : "#ff9232;") + '">（' + (!tcdata.yan_r ? "--" : tcdata.rrank) + '）</span></div>\
                </div>';
            eyeStr+='<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">右眼视力结果：' + (slresObj[tcdata.slres_r]?slresObj[tcdata.slres_r]+"；"+tcdata.slres_rldetail:"") + '</div></div>'    
        }
        if (tcdata.isrchl) {
            // eyeStr += ' <div class="check-cell weui-flex">\
            //         <span class="check-name weui-flex">' + (haseye ? "" : '<img src="images/eyeleft_icon2.png" style="width: 20px;height: 12px;margin-right: 5px;">右') + '</span>\
            //         <div class="check-text">沙眼：(' + shayanObj[tcdata.isrchl] + ')</div>\
            //     </div>';
        }
        var yan_wgr_detail_R = getyanwgDetail(tcdata, 'r');
        eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">右SE：' + showSE_DS_DC(tcdata.yan_rse) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">右DS：' + showSE_DS_DC(tcdata.yan_rds) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">右DC：' + showSE_DS_DC(tcdata.yan_rdc) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">右A：' + showSE_DS_DC(tcdata.yan_ra) + '</div></div>' +
            '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">眼外观：' + (tcdata.yan_ckdate && tcdata.yan_wgr == 1 ? '正常' : tcdata.yan_ckdate && tcdata.yan_wgr == 2 ? '异常' + (yan_wgr_detail_R?'<button class="detail-btn waiguandetail" data-type="r">详细</button>':'') : '') + '</div></div>';

        //单眼遮盖厌恶试验
        var dyzgywsy_lObj = {"1": "未检查", "2": "正常", "3": "异常"};
        if (tcdata.age <4) {//4岁及以上不显示
            eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">单眼遮盖厌恶试验：' + (dyzgywsy_lObj[tcdata.dyzgywsy_r] || "") + '</div></div>';
        }

        //右屈光结果
        var qgjg_lObj = {"1": "未检查", "2": "正常", "3": "可疑屈光不正"};
        eyeStr += '<div class="check-cell weui-flex"><span class="check-name weui-flex"></span><div class="check-text">右屈光结果：' + (qgjg_lObj[tcdata.qgjg_r]||"") + '</div></div>';
        eyeStr += '<div class="check-cell weui-flex" style="display: ' + (!tcdata.qgjg_rdetail ? "none" : "") + '"><span class="check-name weui-flex"></span><div class="check-text">右屈光结果-其他：' + (tcdata.qgjg_rdetail) + '</div></div>';


        eyeStr += '<div class="abdomen-list">\n' +
            '                        <div class="weui-flex">\n' +
            '                            <span style="width: 30%;">【转诊建议】</span>\n' +
            '                            <div class="phystat-cell"><span id="yan_zhuanz_state1">无</span><span id="yan_zhuanz_state2">有</span></div>\n' +
            '                        </div>\n' +
            '                        <div class="abdomenr-txt divzhuanz" style="display: none;">\n' +
            '                            <p>\n' +
            '                                <img src="images/sel-agreetxt.png">原因：<label id="yan_zhuanz_yuanyin" style="word-wrap: break-word;"></label>\n' +
            '                            </p>\n' +
            '                            <p>\n' +
            '                                <img src="images/sel-agreetxt.png">机构及科室：<label id="yan_zhuanz_jgks"></label>\n' +
            '                            </p>\n' +
            '                        </div>\n' +
            '   </div>';

        //健康指导
        eyeStr += '<div class="abdomen-list">\n' +
            '                        <div class="weui-flex">\n' +
            '                            <span style="width: 30%;">【健康指导】</span>\n' +
            '                            <div class="phystat-cell"><span id="jkzd1">无</span><span id="jkzd2">有</span></div>\n' +
            '                        </div>\n' +
            '                        <div class="abdomenr-txt divzhuanz" style="display: none;">\n' +
            '                            <p>\n' +
            '                                <img src="images/sel-agreetxt.png">普遍性指导：<label id="jkzddetail1"></label>\n' +
            '                            </p>\n' +
            '                            <p>\n' +
            '                                <img src="images/sel-agreetxt.png">针对性指导：<label id="jkzddetail2"></label>\n' +
            '                            </p>\n' +
            '                        </div>\n' +
            '   </div>';

        $("#divyanke").html(eyeStr);//回显数据


        //听力部分
        var conArr = ["通过", "未通过", "--", "其他"];
        var llis_showObj = {1: '未见异常', 2: '异常', 3: '其他'};
        if (tcdata.ischeck == "1") {
            //new 听力部分
            changeClass((conArr[tcdata.llis - 1] == "通过" || !conArr[tcdata.llis - 1]), $("#lefter"), tcdata.llis == 1 ? "左耳正常" : tcdata.llis == 2 ? "左耳异常" : "--");
            changeClass((conArr[tcdata.rlis - 1] == "通过" || !conArr[tcdata.rlis - 1]), $("#righter"), tcdata.rlis == 1 ? "右耳正常" : tcdata.rlis == 2 ? "右耳异常" : "--");

            var earStr = '<h6 class="weui-flex" style="align-items: center;margin: 10px 16px 5px 0;font-size: 15px;"><span class="earmark-img weui-flex_center" style="margin-right: 5px;"><img src="images/earright_icon2.png" style="width: 10px;height: 13px;"></span>听力：</h6>\
                    <div style="margin: 7px 0;">\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex" style="color: #ffa939;"><img src="images/earleft_icon2.png" style="width: 12px;height: 17px;margin-right: 5px;">左</span>\
                    <div class="check-text">听力：<span style="font-size: 10px;color: ' + (tcdata.llis == 1 ? "#00d36d;" : "#ff7474;") + '">' + (conArr[tcdata.llis - 1] || "") + '</span></div>\
                    </div>\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex" style="color: #ffa939;"><img src="images/earright_icon2.png" style="width: 12px;height: 17px;margin-right: 5px;">右</span>\
                    <div class="check-text">听力：<span style="font-size: 10px;color: ' + (tcdata.rlis == 1 ? "#00d36d;" : "#ff7474;") + '">' + (conArr[tcdata.rlis - 1] || "") + '</span></div>\
                    </div>\
                    <div class="check-cell weui-flex">\
                    <div class="check-text" style="margin-left: 80px;margin-top: 5px;"><div>备注：</div><span style="font-size: 10px; width: 75%;  ">' + (tcdata.llis_beizhu||"") + '</span></div>\
                    </div>\
                    </div> <div style="margin: 7px 0;">\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex" style="color: #ffa939;"><img src="images/earleft_icon2.png" style="width: 12px;height: 17px;margin-right: 5px;">左</span>\
                    <div style="display:' + (!tcdata.lis_ckdate ? "none" : "") + '" class="check-text">耳：<span style="font-size: 10px;color: ' + (tcdata.llis_show == 1 ? "#00d36d;" : "#ff7474;") + '">(' + (llis_showObj[tcdata.llis_show] || "") + ')</span></div>\
                    </div>\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex" style="color: #ffa939;"><img src="images/earright_icon2.png" style="width: 12px;height: 17px;margin-right: 5px;">右</span>\
                    <div style="display:' + (!tcdata.lis_ckdate ? "none" : "") + '" class="check-text">耳：<span style="font-size: 10px;color: ' + (tcdata.rlis_show == 1 ? "#00d36d;" : "#ff7474;") + '">(' + (llis_showObj[tcdata.rlis_show] || "") + ')</span></div>\
                    </div>\
                    <div class="check-cell weui-flex">\
                    <div style="margin-left: 80px;margin-top: 5px;display:' + (!tcdata.lis_ckdate ? "none" : "") + '" class="check-text"><div >备注：</div><span style="font-size: 10px; width: 75%;">'+(tcdata.llis_show_beizhu||"")+'</span></div>\
                    </div>\
                    </div>';

            earStr += '<div class="abdomen-list">\n' +
                '                        <div class="weui-flex">\n' +
                '                            <span style="width: 30%;">【转诊建议】</span>\n' +
                '                            <div class="phystat-cell"><span id="lis_zhuanz_state1">无</span><span id="lis_zhuanz_state2">有</span></div>\n' +
                '                        </div>\n' +
                '                        <div class="abdomenr-txt divzhuanz" style="display: none;">\n' +
                '                            <p>\n' +
                '                                <img src="images/sel-agreetxt.png">原因：<label id="lis_zhuanz_yuanyin"></label>\n' +
                '                            </p>\n' +
                '                            <p>\n' +
                '                                <img src="images/sel-agreetxt.png">机构及科室：<label id="lis_zhuanz_jgks"></label>\n' +
                '                            </p>\n' +
                '                        </div>\n' +
                '   </div>';

            //回显数据
            $("#divlisten").html(earStr);

        } else {
            //new 听力部分
            changeClass((conArr[tcdata.llis - 1] == "通过" || conArr[tcdata.llis - 1] == "--" || !conArr[tcdata.llis - 1]), $("#lefter"), tcdata.llis == 1 ? "左耳正常" : tcdata.llis == 2 ? "左耳异常" : "--");
            changeClass((conArr[tcdata.rlis - 1] == "通过" || conArr[tcdata.rlis - 1] == "--" || !conArr[tcdata.rlis - 1]), $("#righter"), tcdata.rlis == 1 ? "右耳正常" : tcdata.rlis == 2 ? "右耳异常" : "--");
            $("#divlisten").empty();
        }


        //checked,car1,car2,car3,检出颗数 矫治颗数 新龋颗数
        //龋齿部分
        if (tcdata.checked == '1' && tcdata.car) {
            var teethArr = tcdata.car.split("");//幼儿牙齿检查情况，共20颗。0表示不是龋齿，1表示龋齿，2表示矫，3表示拔，4表示缺。以字符串的形式存储检查结果:"11111111111123411111"
            var toothSpan = $(".divtooth").children('span');
            for (var i = 0; i < toothSpan.length; i++) {
                var onetooth = $(toothSpan[i]);
                if (teethArr[i] == "1") {
                    onetooth.addClass("qu").text("龋");
                } else if (teethArr[i] == "2") {
                    onetooth.addClass("jiao").text("补");
                } else if (teethArr[i] == "3") {
                    onetooth.addClass("ba").text("拔");
                } else if (teethArr[i] == "4") {
                    onetooth.addClass("que").text("失");
                } else {
                    onetooth.removeClass().text("");
                }
            }

            //new 牙齿部分
            var kouqiangObj = getQuchiNum(tcdata.kouqiang_content);
            var toothStr = '<h6 class="weui-flex" style="align-items: center;margin: 10px 16px 5px 0;font-size: 15px;"><span class="toothmark-img weui-flex_center" style="margin-right: 5px;"><img src="images/tooth_icon1.png" style="width: 11px;height: 11px;"></span>口腔：</h6>\
                    <div style="margin: 7px 0;">\
                    <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/tooth_icon1.png" style="width: 15px;height: 15px;margin-right: 6px;"></span>\
                    <div class="check-text">龋失补数：<span style="color: #13b9eb;">(' + (tcdata.car1 || 0) + ')</span></div>\
                </div>\
                <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/tooth_icon3.png" style="width: 15px;height: 15px;margin-right: 6px;"></span>\
                    <div class="check-text">龋齿数：<span style="color: #13b9eb;">(' + (kouqiangObj.n1 || 0) + ')</span></div>\
                </div>\
                <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/tooth_icon3.png" style="width: 15px;height: 15px;margin-right: 6px;"></span>\
                    <div class="check-text">牙齿总数：<span style="color: #13b9eb;">(' + (tcdata.carnum || 0) + ')</span></div>\
                </div>\
                </div>';
            toothStr += '<div class="abdomen-list">\n' +
                '                        <div class="weui-flex">\n' +
                '                            <span style="width: 30%;">【转诊建议】</span>\n' +
                '                            <div class="phystat-cell"><span id="car_zhuanz_state1">无</span><span id="car_zhuanz_state2">有</span></div>\n' +
                '                        </div>\n' +
                '                        <div class="abdomenr-txt divzhuanz" style="display: none;">\n' +
                '                            <p>\n' +
                '                                <img src="images/sel-agreetxt.png">原因：<label id="car_zhuanz_yuanyin"></label>\n' +
                '                            </p>\n' +
                '                            <p>\n' +
                '                                <img src="images/sel-agreetxt.png">机构及科室：<label id="car_zhuanz_jgks"></label>\n' +
                '                            </p>\n' +
                '                        </div>\n' +
                '   </div>';
            //回显数据
            $("#toothdiv").html(toothStr);
        }

        //口腔部分
        if (tcdata.kouqiang_car) {
            var teethArr = tcdata.kouqiang_car.split("");//幼儿牙齿检查情况，共20颗。0表示不是龋齿，1表示龋齿，2表示矫，3表示拔，4表示缺。以字符串的形式存储检查结果:"11111111111123411111"
            var toothSpan = $(".divtooth2").children('span');
            for (var i = 0; i < toothSpan.length; i++) {
                var onetooth = $(toothSpan[i]);
                if (teethArr[i] == "1") {
                    onetooth.addClass("qu").text("龋");
                } else if (teethArr[i] == "2") {
                    onetooth.addClass("jiao").text("补");
                } else if (teethArr[i] == "3") {
                    onetooth.addClass("ba").text("拔");
                } else if (teethArr[i] == "4") {
                    onetooth.addClass("que").text("失");
                } else {
                    onetooth.removeClass().text("");
                }
            }

            // 牙齿部分
            var toothStr = '<h6 class="weui-flex" style="align-items: center;margin: 10px 16px 5px 0;font-size: 15px;"><span class="toothmark-img weui-flex_center" style="margin-right: 5px;"><img src="images/tooth_icon1.png" style="width: 11px;height: 11px;"></span>龋齿：</h6>\
                    <div style="margin: 7px 0;">\
                <div class="check-cell weui-flex">\
                    <span class="check-name weui-flex"><img src="images/tooth_icon3.png" style="width: 15px;height: 15px;margin-right: 6px;"></span>\
                    <div class="check-text">龋齿数：<span style="color: #13b9eb;">(' + (tcdata.car3 || 0) + ')</span></div>\
                </div>\
                </div>';

            toothStr += '<div class="abdomen-list">\n' +
                '                        <div class="weui-flex">\n' +
                '                            <span style="width: 30%;">【转诊建议】</span>\n' +
                '                            <div class="phystat-cell"><span id="car_zhuanz_state1">无</span><span id="car_zhuanz_state2">有</span></div>\n' +
                '                        </div>\n' +
                '                        <div class="abdomenr-txt divzhuanz" style="display: none;">\n' +
                '                            <p>\n' +
                '                                <img src="images/sel-agreetxt.png">原因：<label id="car_zhuanz_yuanyin"></label>\n' +
                '                            </p>\n' +
                '                            <p>\n' +
                '                                <img src="images/sel-agreetxt.png">机构及科室：<label id="car_zhuanz_jgks"></label>\n' +
                '                            </p>\n' +
                '                        </div>\n' +
                '   </div>';
            //回显数据
            $("#toothdiv2").html(toothStr);
        }

    }

    //其他部分渲染
    function renderOther() {
        //tw ,twpj,hematin,hematindivision
        //33 头围 34 头围评价  35 血色素值 36 贫血诊断结果 37 佝偻病 38 面色 39 皮肤 40四肢 41 步态 42 年龄别身高范围标准
        if (tcdata.hematin) {
            $("#xuess").text(tcdata.hematin?tcdata.hematin:(tcdata.hematin_isck==2?"未检查":"无")); //血红蛋白
            $("#xuesspj").text(tcdata.hematin_isck==2?"":(tcdata.hematindivision || "无")); //hematindivision 贫血诊断结果
            $("#xuess_bz").text(tcdata.hematin_beizhu || "无"); //hematindivision 贫血诊断结果
            // $("#divmatin").show();
            $("#divcenr").show().parent().parent("div").show();
        }
        //头围
        if (tcdata.tw) {
            $("#tw").text(tcdata.tw || "无");
            $("#twpj").text(tcdata.twpj || "无");
            $("#divtw").show();
            $("#divcenr").show().parent().parent("div").show();
        }
        //face,skin,limbs,butai ;面色，皮肤，四肢，步态
        var zhengyichangArr = ['<span class="phy-greentxt">未见异常</span><span>异常</span>', '<span>未见异常</span><span class="phy-redtxt">异常</span>'];
        var headmianse = $("#headmianse");
        var headpifu = $("#headpifu");
        var headsizhi = $("#headsizhi");
        var headbutai = $("#headbutai");

        //面色
        var htmlother = '<span>红润</span><span>黄染</span><span>紫绀</span><span>其他</span>';
        $("#mianse").empty();
        $("#mianse").html(htmlother);
        var mianseEle = $("#mianse").children();
        for (var i = 0; i < mianseEle.length; i++) {
            var obj = $(mianseEle[i]);
            if (obj.text() == tcdata.face) {
                obj.addClass("phy-redtxt").siblings().removeClass("phy-redtxt");
                break;
            }
        }
        changeClass(("红润" == tcdata.face || !tcdata.face), headmianse, ("红润" == tcdata.face || !tcdata.face) ? "面色正常" : "面色异常");

        // 皮肤
        var pifustate = tcdata.skin ? tcdata.skin : 0;
        $("#pifu").html(zhengyichangArr[pifustate]);
        changeClass(pifustate == "0", headpifu, pifustate == "0" ? "皮肤正常" : "皮肤异常");

        // 四肢
        var sizhistate = tcdata.limbs ? tcdata.limbs : 0;
        $("#sizhi").html(zhengyichangArr[sizhistate]);
        changeClass(sizhistate == "0", headsizhi, sizhistate == "0" ? "四肢正常" : "四肢异常");

        // 步态
        var butaistate = tcdata.butai ? tcdata.butai : 0;
        $("#butai").html(zhengyichangArr[butaistate]);
        changeClass(butaistate == "0", headbutai, butaistate == "0" ? "步态正常" : "步态异常");

        //备注
        if (tcdata.remark) {
            $("#premark").text(tcdata.remark);
            $("#divremark").show();
        }
    }

    if (obj.xiongbu) {
        if (obj.xiongbu == 1 && obj.xf_ckdate) {
            $("#labxiongbu1").addClass("phy-greentxt");
        } else if(obj.xiongbu == 2 && obj.xf_ckdate){
            $("#labxiongbu2").addClass("phy-redtxt");
            if (obj.xiongbu_beizhu) {
                $("#labxiongbu_beizhu").html(obj.xiongbu_beizhu);
            }
            if (obj.xiongbu_content) {
                var arrhtml = [];
                var arrfypg = obj.xiongbu_content.split(',');
                for (var i = 0; i < arrfypg.length; i++) {
                    arrhtml.push('<p><img src="images/sel-agreetxt.png">' + objdata.objxbcontent[arrfypg[i]] + '</p>');
                }
                $("#labxiongbu_content").html(arrhtml.join(''));
            }
        }
    }
    if (obj.abdomen_status) {
        if (obj.abdomen_status == 1 && obj.xf_ckdate) {
            $("#lababdomen_status1").addClass("phy-greentxt");
        } else if(obj.abdomen_status == 2 && obj.xf_ckdate){
            $("#lababdomen_status2").addClass("phy-redtxt");
            if (obj.fubu_beizhu) {
                $("#labfubu_beizhu").html(obj.fubu_beizhu);
            }
            if (obj.fubu_content) {
                var arrhtml = [];
                var arrfypg = obj.fubu_content.split(',');
                for (var i = 0; i < arrfypg.length; i++) {
                    arrhtml.push('<p><img src="images/sel-agreetxt.png">' + objdata.objfbcontent[arrfypg[i]] + '</p>');
                }
                $("#labfubu_content").html(arrhtml.join(''));
            }
        }
    }
    if (obj.fypg_state) {
        if (obj.fypg_state == 1) {
            $("#labfypg_state1").addClass("phy-greentxt");
        }else if (obj.fypg_state == 0) {
            $("#labfypg_state0").addClass("phy-greentxt");
        } else {
            $("#labfypg_state2").addClass("phy-redtxt");
            if (obj.fypg_content) {
                var arrhtml = [];
                var arrfypg = obj.fypg_content.split(',');
                for (var i = 0; i < arrfypg.length; i++) {
                    arrhtml.push('<p><img src="images/sel-agreetxt.png">' + objdata.objfypg[arrfypg[i]] + '</p>');
                }
                $("#divfypg_content").html(arrhtml.join(''));
            }
        }
    }

    if (obj.huanb_state) {
        if (obj.huanb_state == 1) {
            $("#labhuanb_state1").addClass("phy-greentxt");
        } else {
            $("#labhuanb_state2").addClass("phy-redtxt");
            if (obj.huanb_content) {
                var arrhtml = [];
                var objhuanb = JSON.parse(obj.huanb_content.replaceAll("'", "\""));
                for (var item in objhuanb) {
                    if (item == 5 && objhuanb[item]) {
                        arrhtml.push('<p><img src="images/sel-agreetxt.png">' + objhuanb[item] + '</p>');
                    } else {
                        arrhtml.push('<p><img src="images/sel-agreetxt.png">' + objdata.objhuanbname[item] + objhuanb[item] + '次</p>');
                    }
                }
                $("#divhuanb_content").html(arrhtml.join(''));
            }
        }
    }
    if (obj.hw_zhuanz_state && obj.rday) {
        if (obj.hw_zhuanz_state == 1) {
            $("#hw_zhuanz_state1").addClass("phy-greentxt");
        } else if(obj.hw_zhuanz_state == 2) {
            $("#hw_zhuanz_state2").addClass("phy-redtxt");
            $("#hw_zhuanz_yuanyin").parents(".divzhuanz").show();
            $("#hw_zhuanz_yuanyin").html(obj.hw_zhuanz_yuanyin);
            $("#hw_zhuanz_jgks").html(obj.hw_zhuanz_jgks);
        }
    }
    if (obj.yan_zhuanz_state && obj.yan_ckdate) {
        if (obj.yan_zhuanz_state == 1) {
            $("#yan_zhuanz_state1").addClass("phy-greentxt");
        } else if(obj.yan_zhuanz_state == 2) {
            $("#yan_zhuanz_state2").addClass("phy-redtxt");
            $("#yan_zhuanz_yuanyin").parents(".divzhuanz").show();
            var yan_zhuanz_yuanyinArr = obj.yan_zhuanz_yydetail?JSON.parse(obj.yan_zhuanz_yydetail):[obj.yan_zhuanz_yuanyin];
            $("#yan_zhuanz_yuanyin").html(yan_zhuanz_yuanyinArr.join(','));
            $("#yan_zhuanz_jgks").html(obj.yan_zhuanz_jgks);
        }
    }
    if (obj.jkzd_state && obj.yan_ckdate) {
        if (obj.jkzd_state == 1) {
            $("#jkzd1").addClass("phy-greentxt");
        } else if(obj.jkzd_state == 2) {
            var intage=initAge(obj.age);
            var jkzdArr1=objdata.ageContent[intage].jkzd.map(function(item,index) {
                return '<p>' +(index+1)+'. '+ item + '</p>';
            });
            var jkzdArr2 = obj.jkzddetail ? JSON.parse(obj.jkzddetail) : [];
            var jkzdArr2 = jkzdArr2.map(function(item,index) {
                return '<p>' +(index+1)+'. '+ item + '</p>';
            });
            $("#jkzd2").addClass("phy-redtxt");
            $("#jkzddetail1").parents(".divzhuanz").show();
            $("#jkzddetail1").html(jkzdArr1.join(''));//普遍性指导
            $("#jkzddetail2").html(jkzdArr2.join(''));//针对性指导
        }
    }
    if (obj.lis_zhuanz_state && obj.lis_ckdate) {
        if (obj.lis_zhuanz_state == 1) {
            $("#lis_zhuanz_state1").addClass("phy-greentxt");
        } else if(obj.lis_zhuanz_state == 2) {
            $("#lis_zhuanz_state2").addClass("phy-redtxt");
            $("#lis_zhuanz_yuanyin").parents(".divzhuanz").show();
            $("#lis_zhuanz_yuanyin").html(obj.lis_zhuanz_yuanyin);
            $("#lis_zhuanz_jgks").html(obj.lis_zhuanz_jgks);
        }
    }
    if (obj.hematin_zhuanz_state && obj.hematin_ckdate) {
        if (obj.hematin_zhuanz_state == 1) {
            $("#hematin_zhuanz_state1").addClass("phy-greentxt");
        } else if(obj.hematin_zhuanz_state == 2) {
            $("#hematin_zhuanz_state2").addClass("phy-redtxt");
            $("#hematin_zhuanz_yuanyin").parents(".divzhuanz").show();
            $("#hematin_zhuanz_yuanyin").html(obj.hematin_zhuanz_yuanyin);
            $("#hematin_zhuanz_jgks").html(obj.hematin_zhuanz_jgks);
        }
    }
    if (obj.car_zhuanz_state && obj.kq_ckdate) {
        if (obj.car_zhuanz_state == 1) {
            $("#car_zhuanz_state1").addClass("phy-greentxt");
        } else if(obj.car_zhuanz_state == 2) {
            $("#car_zhuanz_state2").addClass("phy-redtxt");
            $("#car_zhuanz_yuanyin").parents(".divzhuanz").show();
            $("#car_zhuanz_yuanyin").html(obj.car_zhuanz_yuanyin);
            $("#car_zhuanz_jgks").html(obj.car_zhuanz_jgks);
        }
    }
    if (obj.xf_zhuanz_state && obj.xf_ckdate) {
        if (obj.xf_zhuanz_state == 1) {
            $("#labxf_zhuanz_state1").addClass("phy-greentxt");
        } else if(obj.xf_zhuanz_state == 2) {
            $("#labxf_zhuanz_state2").addClass("phy-redtxt");
            $("#divzhuanz").show();
            $("#labxf_zhuanz_yuanyin").html(obj.xf_zhuanz_yuanyin);
            $("#labxf_zhuanz_jgks").html(obj.xf_zhuanz_jgks);
        }
    }

    if (obj.guidance) {
        var arrhtml = [];
        var arrguidance = obj.guidance.split(',');
        for (var i = 0; i < arrguidance.length; i++) {
            if (arrguidance[i] == 6) {
                if (obj.guidance_other) {
                    arrhtml.push('<p><img src="images/sel-agreetxt.png">' + obj.guidance_other + '</p>');
                }
            } else {
                arrhtml.push('<p><img src="images/sel-agreetxt.png">' + objdata.objguidance[arrguidance[i]] + '</p>');
            }
        }

        $("#divguidance").html(arrhtml.join('')).parent("div").show();
    }

    // 体检套餐 收费项目
    initPackageDetail(obj);

    $("#labnext_check_date").html(obj.next_check_date);
    $("#labcheck_doctor").html(obj.check_doctor);
}

//体检套餐 收费项目
function initPackageDetail(obj) {
    $.sm(function (re, err) {
        if (err) {
            return $.toast("体检套餐加载失败" + err,'forbidden');
        }
        var feeresult = obj.feeitem ? JSON.parse(obj.feeitem) : {};

        var arrpackage = JSON.parse(obj.packagearr || '[]');
        //如果个人体检结果中有体检套餐及体检项信息，此种情况，说明是从其他区县发布来的，需要从体检结果中取体检套餐和体检项数据
        if(arrpackage.length > 0 && arrpackage[0].package_detail){
            for (var i = 0; i < arrpackage.length; i++) {
                arrpackage[i].name = arrpackage[i].package_name;
            }
        } else {
            arrpackage = re || [];
        }

        for (var j = 0; j < arrpackage.length; j++) {
            var packagedata = arrpackage[j];
            var packageid = packagedata.package_id;
            var packagename = packagedata.name;
            var arritem = packagedata.package_detail ? JSON.parse(packagedata.package_detail) : [];
            for (var i = 0; i < arritem.length; i++) {
                var arrhtml = [];
                var content = "";
                var arrimg = [];
                if (feeresult[packageid + '_' + arritem[i].id]) {
                    content = feeresult[packageid + '_' + arritem[i].id].content;
                    content = $.toCharCode(content).replaceAll("\n", "<br/>");
                    arrimg = feeresult[packageid + '_' + arritem[i].id].arrimg;
                }

                arrhtml.push('<div style="margin: 7px 15px; background: #ffffff; padding: 10px 0; position: relative;">');
                arrhtml.push('        <img src="images/link_icon2.png" style="width: 10px; height: 30px; position: absolute; left: 60px; top: -18px;">');
                arrhtml.push('        <img src="images/link_icon2.png" style="width: 10px; height: 30px; position: absolute; right: 60px; top: -18px;">');
                arrhtml.push('        <h5 class="type-title weui-flex"><img src="images/weiliang_ico.png">' + packagename + '-' + arritem[i].name + '</h5>');
                arrhtml.push('        <div class="checklist-div" style="padding: 10px 15px; font-size: 13px;">');
                arrhtml.push('            <div class="weui-cell">');
                arrhtml.push('                <div class="weui-cell__bd" style="flex: none; width:65px; "><p>检查单:</p></div>');
                arrhtml.push('                <div class="weui-cell__ft" style="text-align:left;">');
                arrhtml.push('                    <div class="checklist">');
                for (var k = 0; k < arrimg.length; k++) {
                    if(arrimg[k].endsWith(".pdf")){
                        arrhtml.push('                        <img class="pdf" data-img="' + ossPrefix + arrimg[k] + '" src="images/icon_pdf.png">');
                    } else {
                        arrhtml.push('                        <img data-img="' + ossPrefix + arrimg[k] + '" src="' + ossPrefix + arrimg[k] + '">');
                    }
                }
                arrhtml.push('                    </div>');
                arrhtml.push('                </div>');
                arrhtml.push('            </div>');
                arrhtml.push('            <div class="weui-cell">');
                arrhtml.push('                <div class="weui-cell__bd" style="flex: none; width:65px; "><p>检查结果:</p></div>');
                arrhtml.push('                <div class="weui-cell__ft" style="text-align:left;color: #444444;">' + content + '');
                arrhtml.push('                </div>');
                arrhtml.push('            </div>');
                arrhtml.push('        </div>');
                arrhtml.push('    </div>');

                $("#package").append(arrhtml.join(''));
            }
        }


    }, ["yypt.getcheckpackagedetail", objdata.cguid, objdata.checkid, $.msgwhere({packids:$.msgpJoin(obj.package_id?obj.package_id.split(","):[0])})]);
}

function chengzhangdata() {
    //读取宝宝成长记录
    var arrpm = [],
        arrdate = objdata.arrdate = [];//日期数组，排序用
    objdata.stuRecord = [];
    arrpm.push(["growthrecord.getMidicalRecord", objdata.curbid ? objdata.curbid : 'null']); //体检记录
    arrpm.push(["growthrecord.getGrowthRecord", objdata.curbid ? objdata.curbid : 'null']);  //成长记录
    $.sm(function (re1, err1) {
        if (err1) {
            $.toast(err1,'forbidden');
        } else if (re1) {
            var i = 0,
                j = 0;
            for (; i < re1[0].length; i++) {//处理体检记录中的
                var arr = re1[0][i];
                if (!objdata.stuRecord[arr[5]]) {
                    arrdate.push(arr[5]);
                }
                objdata.stuRecord[arr[5]] = {
                    type: 1,
                    id: arr[0],
                    stunum: arr[1],
                    stuname: arr[2],
                    sex: arr[3],
                    birthday: arr[4],
                    rday: arr[5],
                    height: arr[6],
                    weight: arr[7],
                    agehe: arr[8],
                    agewe: arr[9],
                    hewe: arr[10],
                    percentagehe: arr[11],
                    percentagewe: arr[12],
                    percenthewe: arr[13],
                    typename: arr[14],
                    fromopenid: ""
                };
            }
            for (; j < re1[1].length; j++) {//处理成长记录中的
                var arr = re1[1][j];
                if (!objdata.stuRecord[arr[5]]) {
                    arrdate.push(arr[5]);
                }
                //,fromtype,status
                //判断添加类型
                var type = 3;
                if (arr[14] > 5) {
                    type = 3;
                } else {
                    type = 2;
                }
                objdata.stuRecord[arr[5]] = {
                    type: type,
                    id: arr[0],
                    stunum: arr[1],
                    stuname: arr[2],
                    sex: arr[3],
                    birthday: arr[4],
                    rday: arr[5],
                    height: arr[6],
                    weight: arr[7],
                    agehe: arr[8],
                    agewe: arr[9],
                    hewe: arr[10],
                    percentagehe: arr[11],
                    percentagewe: arr[12],
                    percenthewe: arr[13],
                    typename: arr[16],
                    fromopenid: arr[17]
                };
            }
            //遍历学生
            eachDateRecord();
        }
    }, arrpm);
}

/**
 * @des 遍历学生成长记录数据拼接html
 */
function eachDateRecord() {
    var _index = 1,//序号
        arrHtml = [], //页面html
        //日期排序
        arrdate = objdata.arrdate.sort(function (a, b) {
            return (new Date(b).getTime() - new Date(a).getTime());
        });
    //处理图表
    drawEchart();
}

function changeClass(bool, element, tipsText) {
    if (bool) {//未见异常
        element.show().addClass("greenphy").removeClass("redphy").find("p").text(tipsText);
    } else {//异常
        element.show().addClass("redphy").removeClass("greenphy").find("p").text(tipsText);
    }
}

/**
 * 功能：根据出生日期dtBeginTime 得到格式（2.01）的年龄
 * 参数都是Date()类型new Date('2016-8-19')
 * @param dtBeginTime
 * @param nowDate
 * @returns {String}
 */
function GetAge(dtBeginTime, nowDate) {
    if (!dtBeginTime) return "";
    var nowYear = nowDate.getFullYear();
    var birYear = dtBeginTime.getFullYear();
    var nowMonth = nowDate.getMonth() + 1;
    var birMonth = dtBeginTime.getMonth() + 1;
    var nowDate = nowDate.getDate();
    var birDate = dtBeginTime.getDate();
    var ageMonth;
    var ageYear;
    if (birMonth > nowMonth) {
        nowYear -= 1;
        ageMonth = 12 - birMonth + nowMonth;
    } else
        ageMonth = nowMonth - birMonth;
    ageYear = nowYear - birYear;
    if (nowDate < birDate)
        ageMonth--;
    if (ageMonth < 0) {
        ageYear--;
        ageMonth = 11;
    }
    if (parseInt(ageMonth) == 0)
        return ageYear + ".00";
    else {
        if (ageMonth < 10)
            ageMonth = "0" + ageMonth;
        return ageYear + "." + ageMonth;
    }
}

/**
 * @des 图表显示
 */
function drawEchart() {
    var height = 300;
    $('.chart-con').height(height);
    $('.div_chart').html('').height(height);
    $(".div_pj").height(height - 100);
    var main = $(".div_chart")[0]
        , stand = objdata.MedicalStand //处理标准信息
        , splitnumber = 0
        , ytitle = ""
        , xtitle = ""
        , title = ""
        , axisLabel = {} //y轴坐标显示
        , series = returnSeries();//series数组
    myChart = echarts.init(main);
    //处理分隔位数、xy轴名称显示
    xmin = series[0].data[0][0];
    xmax = series[0].data[series[0].data.length - 1][0];

    ymin = Math.ceil((series[0].data[0][1] - 5));
    if (ymin <= 0) {
        ymin = 0;
    }
    ymax = Math.ceil((series[series.length - 1].data[series[0].data.length - 1][1] + 5));
    if (objdata.evalType == 0) {
        splitnumber = parseInt((series[0].data[series[0].data.length - 1][0] - series[0].data[0][0]) / 10);
        xtitle = "身高(cm)";
        ytitle = "体重(kg)";
        title = "按身高测体重(W/H)(" + objdata.sex + ")";
    } else {
        axisLabel = {
            margin: 10,
            formatter: function (value) {
                //				var age = value % 12;
                //				var intage = parseInt(value/12);
                //				age = age / 12;
                ////				if(age == 0){
                ////					return parseInt(value/12) + "岁";
                ////				}else{
                ////					return value;
                ////				}
                //				return parseFloat(intage + age).toFixed(1);
                return value;
            },
            textStyle: {
                color: function (value, index) {
                    return value % 12 == 0 ? '#ADADAD' : '#ccc';
                }
            }
        };
        splitnumber = parseInt((series[0].data[series[0].data.length - 1][0] - series[0].data[0][0]) / 12);
        if (objdata.evalType == 1) {
            xtitle = "年(月)龄";
            ytitle = "身高(cm)";
            title = "按年龄测身高(H/Y)(" + objdata.sex + ")";
        } else {
            xtitle = "年(月)龄";
            ytitle = "体重(kg)";
            title = "按年龄测体重(W/Y)(" + objdata.sex + ")";
        }
    }
    //获取最大的五个点数据
    arrmax = [];
    for (var i = 0; i < series.length; i++) {
        if (i != 2) arrmax.push(series[i].data[series[i].data.length - 1][1]);
    }
    //拼接实际数据数组
    series.push(getGrowthSeries());
    //处理echart option
    option = {
        title: {
            text: title,
            x: 'center',
            y: 'bottom',
            textStyle: {
                fontWeight: 'bolder',
                color: '#1CA89D'
            },
            left: '10%'
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: '#1da89e',
            borderColor: '#1da89e',
            borderRadius: 8,
            borderWidth: 2,
            textStyle: {
                color: 'white',
                decoration: 'none',
                fontFamily: 'Verdana, sans-serif',
                fontSize: 15,
                fontStyle: 'italic',
                fontWeight: 'bold'
            },
            formatter: function (params, ticket, callback) {
                var res;
                if (params.seriesName === "trueRecord") {//实际测量值
                    var sDate = params.data.name //日期属性
                        , value = params.value
                        , obj = objdata.stuRecord[sDate];
                    res = '日期：' + sDate + '<br />'
                        + '年龄：' + GetAge(new Date(obj.birthday.substr(0, 10)), new Date(obj.rday.substr(0, 10))) + '<br />'
                        + '身高：' + obj.height + '' + '<br />'
                        + '体重：' + obj.weight + '' + '<br />'
                        + 'W/H：' + (objdata.standard == 0 ? obj.hewe : obj.percenthewe) + '<br />'
                        + 'H/Y：' + (objdata.standard == 0 ? obj.agehe : obj.percentagehe) + '<br />'
                        + 'W/Y：' + (objdata.standard == 0 ? obj.agewe : obj.percentagewe) + '<br />';
                } else {
                    res = params.seriesName + '<br/>' + (params.value || "");
                }
                return res;
            }
        },
        //	    legend: {
        //	    	show: false
        //	    },
        xAxis: {
            type: 'value',
            boundaryGap: false,
            splitLine: {
                lineStyle: {
                    width: 1,
                    type: 'solid',
                    color: '#ccc'
                }
            },
            name: xtitle,
            //	        nameLocation: 'middle',
            //	        nameRotate : 45,
            nameGap: 2,
            axisLabel: axisLabel,
            min: xmin,
            max: xmax,
            splitNumber: splitnumber
        },
        calculable: true,
        yAxis: [{
            min: ymin,
            max: ymax,
            type: 'value',
            postion: 'left',
            splitLine: {
                lineStyle: {
                    width: 1,
                    type: 'solid',
                    color: '#ccc'
                }
            },
            name: ytitle
        }],
        grid: {
            top: 40,
            right: '19%',
            left: '10%'
        },
        series: series
    };
    myChart.setOption(option);
    //刻度显示
    resetPjHtml();
    //重置页面
    $(window).off('resize').on('resize', function () {
        var height = 300;
        $('.chart-con').height(height);
        $('.div_chart').height(height);
        $(".div_pj").height(height - 100);
        myChart.resize();
        resetPjHtml();
    });
}

/**
 * @des 返回生成图表所需标准series
 */
function returnSeries() {
    var stand = objdata.MedicalStand //处理标准信息
        , arrName = [] //series名称数组
        , arrColor = [] //线颜色
        , series = [];//option数组
    //判断已经缓存过，直接返回缓存内容
    if (objdata.series[objdata.standard] && objdata.series[objdata.standard][objdata.evalType]) {
        return $.extend([], objdata.series[objdata.standard] && objdata.series[objdata.standard][objdata.evalType]);
    }
    if (objdata.standard == 0) {//世界卫生组织
        arrName = ['M-2SD', 'M-1SD', 'M', 'M+1SD', 'M+2SD'];
        arrColor = ["#ff153c", "#ffd400", "#23db7b", "#ffd400", "#ff153c"];
        series = [];
        for (var i = 0; i < arrName.length; i++) {
            series.push({
                name: arrName[i],
                type: 'line',
                symbol: 'circle',
                symbolSize: '1',
                itemStyle: {
                    normal: {
                        color: arrColor[i]
                    }
                },
                smooth: true,
                data: []
            });
        }
        if (objdata.evalType == 0) {//身高别体重
            stand = medicalstandard["hwle"]["1"];
            for (var o in stand) {
                if (o.indexOf(objdata.sex) > -1 && o.substring(o.length - 1, o.length) == "2") {//暂时采用立位标准显示
                    //a_女_110.7_1
                    var height = o.replace("a_" + objdata.sex + "_", "").replace("_2", "");//身高数值
                    if (height.indexOf('0.0') >= 0 || height.indexOf('5.0') >= 0) {
                        series[0].data.push([height, stand[o][1]]);
                        series[1].data.push([height, stand[o][2]]);
                        series[2].data.push([height, stand[o][3]]);
                        series[3].data.push([height, stand[o][4]]);
                        series[4].data.push([height, stand[o][5]]);
                    }
                }
            }
        } else if (objdata.evalType == 1 || objdata.evalType == 2) {//年龄别身高、体重
            stand = medicalstandard["agehw"]["1"];
            for (var o in stand) {
                if (o.indexOf(objdata.sex) > -1) {//男女
                    var age = o.replace("a_", "").replace("_" + objdata.sex, "");//身高数值
                    if (age.indexOf('.00') >= 0 || age.indexOf('.06') >= 0) {
                        //						age = parseFloat(age.replace('.06', '.50'));
                        age = getMonthAge(age);
                        if (objdata.evalType == 1) {//年龄别身高
                            series[0].data.push([age, stand[o][1]]);
                            series[1].data.push([age, stand[o][2]]);
                            series[2].data.push([age, stand[o][3]]);
                            series[3].data.push([age, stand[o][4]]);
                            series[4].data.push([age, stand[o][5]]);
                        } else {//年龄别体重
                            series[0].data.push([age, stand[o][8]]);
                            series[1].data.push([age, stand[o][9]]);
                            series[2].data.push([age, stand[o][10]]);
                            series[3].data.push([age, stand[o][11]]);
                            series[4].data.push([age, stand[o][12]]);
                        }
                    }
                }
            }
        }
    } else if (objdata.standard == 1) {//百分位法
        arrName = ['P3', 'P10', 'P50', 'P80', 'P97'];
        arrColor = ["#ff153c", "#ffd400", "#23db7b", "#ffd400", "#ff153c"];
        series = [];
        for (var i = 0; i < arrName.length; i++) {
            series.push({
                name: arrName[i],
                type: 'line',
                symbol: 'circle',
                symbolSize: '1',
                itemStyle: {
                    normal: {
                        color: arrColor[i]
                    }
                },
                smooth: true,
                data: []
            });
        }
        if (objdata.evalType == 0) {
            stand = medicalstandard["hwle"]["3"];
            for (var o in stand) {
                if (o.indexOf(objdata.sex) > -1 && o.substring(o.length - 1, o.length) == "2") {//暂时采用立位标准显示
                    //a_女_110.7_1
                    var height = o.replace("a_" + objdata.sex + "_", "").replace("_2", "");//身高数值
                    if (height.indexOf('0.0') >= 0 || height.indexOf('5.0') >= 0) {
                        series[0].data.push([height, stand[o][0]]);
                        series[1].data.push([height, stand[o][1]]);
                        series[2].data.push([height, stand[o][3]]);
                        series[3].data.push([height, stand[o][4]]);
                        series[4].data.push([height, stand[o][5]]);
                        //						series[5].data.push([height, stand[o][5]]);
                    }
                }
            }
        } else if (objdata.evalType == 1 || objdata.evalType == 2) {
            stand = medicalstandard["agehw"]["3"];
            for (var o in stand) {
                if (o.indexOf(objdata.sex) > -1) {//男女
                    var age = o.replace("a_", "").replace("_" + objdata.sex, "");//身高数值
                    if (age.indexOf('.00') >= 0 || age.indexOf('.06') >= 0) {
                        //						age = parseFloat(age.replace('.06', '.50'));//0.06相当于0.5岁
                        age = getMonthAge(age);
                        if (objdata.evalType == 1) {//年龄别身高
                            series[0].data.push([age, stand[o][0]]);
                            series[1].data.push([age, stand[o][1]]);
                            series[2].data.push([age, stand[o][3]]);
                            series[3].data.push([age, stand[o][4]]);
                            series[4].data.push([age, stand[o][5]]);
                            //							series[5].data.push([age, stand[o][5]]);
                        } else {//年龄别体重
                            series[0].data.push([age, stand[o][6]]);
                            series[1].data.push([age, stand[o][7]]);
                            series[2].data.push([age, stand[o][9]]);
                            series[3].data.push([age, stand[o][10]]);
                            series[4].data.push([age, stand[o][11]]);
                            //							series[5].data.push([age, stand[o][11]]);
                        }
                    }
                }
            }
        }

    }
    //处理point点数据
    for (var i = 0; i < series.length; i++) {
        var data = series[i].data[series[i].data.length - 1];
        series[i].markPoint = {
            data: [
                {
                    xAxis: data[0], yAxis: data[1],
                    symbol: 'circle',
                    symbolSize: 4,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                formatter: "{a}",
                                position: 'right',
                                textStyle: {
                                    color: '#161616',
                                    fontWeight: 'bold'
                                }
                            }
                        }
                    }
                }
            ]
        };
    }
    //缓存到变量中
    if (!objdata.series[objdata.standard]) {
        objdata.series[objdata.standard] = {};
    }
    objdata.series[objdata.standard][objdata.evalType] = $.extend([], series);
    return series;
}

/**
 * @des 获取成长记录数据series
 */
function getGrowthSeries() {
    //	var datafrom = 1;
    var _index = 1,//序号
        series = {
            name: 'trueRecord',
            type: 'line',
            symbolSize: '8',
            data: [],
            itemStyle: {
                normal: {
                    color: "blue"
                }
            }
        }, //series对象
        i = 0,
        //日期排序
        arrdate = objdata.arrdate.sort(function (a, b) {
            return (new Date(b).getTime() - new Date(a).getTime());
        });
    for (; i < arrdate.length; i++) {
        if (objdata.stuRecord[arrdate[i]]) {
            var obj = objdata.stuRecord[arrdate[i]];
            //			if(datafrom != "" && datafrom != obj.type) continue; //数据来源筛选
            if (objdata.evalType == 0) {
                series.data.push({
                    value: [obj.height, obj.weight],
                    name: arrdate[i]
                });
            } else if (objdata.evalType == 1 || objdata.evalType == 2) {
                var age = GetAge(new Date(obj.birthday.substr(0, 10)), new Date(obj.rday.substr(0, 10)));//月转换 例：0.06转0.5
                //				var arrge = age.split('.');
                //				age = parseInt(arrge[0]) + (parseInt(arrge[1]) / 12);
                age = getMonthAge(age);
                if (objdata.evalType == 1) {//年龄别身高、体重
                    series.data.push({
                        value: [age, obj.height],
                        name: arrdate[i]
                    });
                } else {
                    series.data.push({
                        value: [age, obj.weight],
                        name: arrdate[i]
                    });
                }
            }
        }
    }
    return series;
}

/**
 * 通过最大值处理各个评价区间显示
 */
function resetPjHtml() {
    var arrtitle = [],
        arrhtml = [],
        count = ymax - ymin,
        total = 0,
        j = 0,
        divpj = $('.div_pj'),
        dheight = parseFloat(divpj.height());

    if (objdata.standard == 0) {//世界卫生组织
        arrtitle = ["上", "中上", "中", "中下", "下"];
    } else if (objdata.standard == 1) {
        if (objdata.evalType == 0) {
            arrtitle = ["过胖", "正常偏胖", "中等", "正常偏瘦", "消瘦"];
        } else if (objdata.evalType == 1) {
            arrtitle = ["过高", "正常偏高", "中等", "正常偏矮", "生长迟缓"];
        } else if (objdata.evalType == 2) {
            arrtitle = ["过重", "正常偏重", "中等", "正常偏轻", "体重低下"];
        }
    }

    for (var i = arrmax.length - 1; i >= 0; i--) {
        var height = 0
            , strborder = "";
        if (i == arrmax.length - 1) {
            height = ((ymax - arrmax[i]) / count * 100);
            strborder = ' style="border-top:1px solid #ccc;"';
        } else {
            height = ((arrmax[i + 1] - arrmax[i]) / count * 100);
        }
        height = parseFloat(parseFloat(height).toFixed(2));
        arrhtml.push('<div class="div-item" style="height:' + height + '%"><div ' + strborder + ' class="div-border"></div><div class="div-title" style="line-height:' + parseInt(height * dheight / 100) + 'px">' + arrtitle[j] + '</div></div>');
        total += height;
        j++;
    }
    arrhtml.push('<div class="div-item" style="height:' + (100 - total) + '%"><div class="div-border"></div><div class="div-title" style="line-height:' + parseInt(dheight * (100 - total) / 100) + 'px">' + arrtitle[j] + '</div></div>');
    divpj.html(arrhtml.join(''));
}

/**
 * @des 通过年龄得到月龄
 */
function getMonthAge(age) {
    var arr = age.split('.');
    return parseInt(arr[0]) * 12 + parseInt(arr[1]);
}


/**
 * 获取评价 建议
 */
function getTjpjInfo(arr) {
    var arrhtml = [];
    if (arr.length == 0) {
        return '<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["health"] + '</p>';
    }
    //视力低常
    if ($.inArray("视力低常", arr) >= 0) {
        arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["shili"] + '</p>');
    }
    //龋齿
    if ($.inArray("龋齿", arr) >= 0) {
        arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["quchi"] + '</p>');
    }
    //贫血及营养状况
    var ismalnu = false,
        isfat = false;
    if ($.inArray("消瘦", arr) >= 0 || $.inArray("低体重", arr) >= 0 || $.inArray("生长迟缓", arr) >= 0) {
        ismalnu = true;
    } else if ($.inArray("超重", arr) >= 0 || $.inArray("肥胖", arr) >= 0) {
        isfat = true;
    }
    if ($.inArray("贫血", arr) >= 0) {
        if (ismalnu) {
            //营养不良贫血
            arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["pinxuemalnu"] + '</p>');
        } else if (isfat) {
            //肥胖超重加贫血
            arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["pinxuefat"] + '</p>');
        } else {
            //贫血
            arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["pinxue"] + '</p>');
        }
    } else {
        if (ismalnu) {
            arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["malnu"] + '</p>');
        }
        if (isfat) {
            arrhtml.push('<p style="font-size: 14px;color: #858379;text-indent:2em;">' + arrstand["fat"] + '</p>');
        }
    }
    return arrhtml.join('');
}

//口腔数据
function renderKq(tcdata) {
    var kqstyle={"1":"phy-greentxt","2":"phy-redtxt"}
    $(".kq"+tcdata.kouqiang).addClass(kqstyle[tcdata.kouqiang]);

    var kqcontentObj = tcdata.kouqiang_content ? JSON.parse(tcdata.kouqiang_content) : {};
    for (var k in kqcontentObj) {
        $("#kqcontent" + k).show();
        var jianyiArr = kqcontentObj[k]['jy']||[];
        for (var i = 0; i < jianyiArr.length; i++) {
            var onejy = jianyiArr[i];
            $("#kqcontent" + k + "_" + onejy).show();
            $(".kqcontent" + k + "_" + onejy).show();
        }
        if(kqcontentObj[k]['n1']){
            $("#kqcontent" + k + "_n1").text(kqcontentObj[k]['n1']);
        }
        if(kqcontentObj[k]['n2']){
            $("#kqcontent" + k + "_n2").text(kqcontentObj[k]['n2']);
        }
        if(kqcontentObj[k]['n3']){
            $("#kqcontent" + k + "_n3").text(kqcontentObj[k]['n3']);
        }
        if(kqcontentObj[k]['n4']){
            $("#kqcontent" + k + "_n4").text(kqcontentObj[k]['n4']);
        }
    }

    if(kqcontentObj['1']&&kqcontentObj['1']['other']){
        $("#kqcontent1_other").text(kqcontentObj['1']['other']);
    }

}

function showSE_DS_DC(vals) {
    if(vals==undefined){
        return "";
    }
    return vals <= 0 ? vals + "" : "+"+vals;
}

function initAge(age){
    var ageKey='';
    if(age<2){
        ageKey="1.06";//18月龄
    }else if(age>=2 && age<2.06){
        ageKey="2";//24月龄
    }else if(age>=2.06 && age<3){
        ageKey="2.06";//30月龄
    }else if(age>=3 && age<4){
        ageKey="3";//36月龄
    }else if(age>=4 && age<5){
        ageKey="4";// 4岁
    }else if(age>=5 && age<6){
        ageKey="5";//5岁
    }else if(age>=6 && age<7){
        ageKey="6";//6岁
    }else if(age>=7){
        ageKey="6";//6岁
    }
    return ageKey;
}

function getyanwgDetail(data,type){
    var ycArr=[];
    if(data['yanjian_'+type+'detail']){
        var detailArr=JSON.parse(data['yanjian_'+type+'detail']);
        ycArr.push('<p style="text-align: left;margin-top: 5px;">眼睑:'+detailArr.join("；")+'</p>')
    }if(data['yanqiu_'+type+'detail']){
        var detailArr=JSON.parse(data['yanqiu_'+type+'detail']);
        ycArr.push('<p style="text-align: left;margin-top: 5px;">眼球:'+detailArr.join("；")+'</p>')
    }if(data['jiemo_'+type+'detail']){
        var detailArr=JSON.parse(data['jiemo_'+type+'detail']);
        ycArr.push('<p style="text-align: left;margin-top: 5px;">结膜:'+detailArr.join("；")+'</p>')
    }if(data['jiaomo_'+type+'detail']){
        var detailArr=JSON.parse(data['jiaomo_'+type+'detail']);
        ycArr.push('<p style="text-align: left;margin-top: 5px;">角膜:'+detailArr.join("；")+'</p>')
    }if(data['tongkong_'+type+'detail']){
        var detailArr=JSON.parse(data['tongkong_'+type+'detail']);
        ycArr.push('<p style="text-align: left;margin-top: 5px;">瞳孔:'+detailArr.join("；")+'</p>')
    }
    return ycArr.join("");
}

function getQuchiNum(kouqiang_content){
    var n1='',n2='',n3='',n4='';
    var kqcontentObj = kouqiang_content ? JSON.parse(kouqiang_content) : {};
    for (var k in kqcontentObj) {
        if(k!=5){
            continue;
        }
        if(kqcontentObj[k]['n1']){//龋齿
           n1=kqcontentObj[k]['n1'];
        }
        if(kqcontentObj[k]['n2']){
            n2=kqcontentObj[k]['n2'];
        }
        if(kqcontentObj[k]['n3']){
            n3=kqcontentObj[k]['n3'];
        }
        if(kqcontentObj[k]['n4']){
            n4=kqcontentObj[k]['n4'];
        }
    }

    return {n1,n2,n3,n4}
}


// 检测是否在微信小程序环境中
function isInMiniProgram() {
    return window.__wxjs_environment === 'miniprogram' ||
        (typeof wx !== 'undefined' && wx.miniProgram);
}

// 向小程序发送消息的函数
function sendMessageToMiniProgram(data) {
    console.log('尝试发送消息:', data);

    if (isInMiniProgram()) {
        try {
            wx.miniProgram.postMessage({
                data: data
            });
            console.log('消息发送成功:', data);
        } catch (error) {
            console.error('消息发送失败:', error);
        }
    } else {
        console.log('不在小程序环境中，无法发送消息');
        console.log('当前环境:', window.__wxjs_environment);
        console.log('wx对象:', typeof wx);
    }
}

// 初始化监听器
function initListener() {

    console.log('=== 初始化监听器 ===');
    console.log('当前环境:', window.__wxjs_environment);
    console.log('wx 对象:', typeof wx);
    console.log('是否在小程序中:', isInMiniProgram());

    let isScrolledToBottom = false;
    let maxProgress = 0;
    let messageBuffer = []; // 消息缓冲区
    
    // 发送缓冲消息的函数
    function flushMessages() {
        if (messageBuffer.length > 0) {
            // 只发送最重要的消息
            const importantMessages = messageBuffer.filter(msg => 
                msg.type === 'scrollToBottom' || 
                msg.type === 'pageUnload' ||
                (msg.type === 'readingProgress' && msg.progress % 20 === 0) // 每20%发送一次
            );
            
            importantMessages.forEach(msg => {
                sendMessageToMiniProgram(msg);
            });
            
            messageBuffer = [];
        }
    }
    
    window.addEventListener('scroll', function () {
        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(function () {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            const progress = Math.round((scrollTop + windowHeight) / documentHeight * 100);
            
            maxProgress = Math.max(maxProgress, progress);
            
            // 只在关键进度点发送消息
            if (progress % 20 === 0 && progress > maxProgress - 20) {
                const message = {
                    type: 'readingProgress',
                    progress: progress,
                    maxProgress: maxProgress,
                    timestamp: new Date().getTime()
                };
                
                messageBuffer.push(message);
                sendMessageToMiniProgram(message);
            }
            
            // 滚动到底部
            if (scrollTop + windowHeight >= documentHeight - 50 && !isScrolledToBottom) {
                isScrolledToBottom = true;
                const message = {
                    type: 'scrollToBottom',
                    maxProgress: maxProgress,
                    timestamp: new Date().getTime()
                };
                
                messageBuffer.push(message);
                sendMessageToMiniProgram(message);
                
                // 立即尝试刷新消息（通过页面状态变化）
                document.title = document.title + ' '; // 触发页面变化
            }
        }, 200);
    });
    
    // 页面即将离开时发送所有缓冲消息
    window.addEventListener('beforeunload', function () {
        sendMessageToMiniProgram({
            type: 'pageUnload',
            maxProgress: maxProgress,
            isCompleted: isScrolledToBottom,
            timestamp: new Date().getTime()
        });
        
        flushMessages();
    });
}



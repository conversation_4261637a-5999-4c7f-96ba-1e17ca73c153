<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <title>测试</title>
    <link rel="stylesheet" type="text/css" href="weui.min.css">
    <link rel="stylesheet" type="text/css" href="jquery-weui.min.css">
    <script type="text/javascript" src="http://ptlib.tb-n.com/lib/sys/jquery.js"></script>
    <style>
        .weui-picker-calendar {
            height: auto;
        }
        .picker-calendar-day{
            position: relative;background: #C9E1FE;margin-left: 1px;margin-bottom:1px;
            display: -webkit-box;display: -webkit-flex;display: flex;align-items:center;justify-content:center;color: #333333;font-size: 13px;
        }
        /*第一列的最左边*/
        .picker-calendar-day:first-child{
            margin-left: 0;
        }
        /*行底部灰色边框*/
        .picker-calendar-row:after,.picker-calendar-week-days:after{
            height:0;
        }
        /*今天*/
        .picker-calendar-day.picker-calendar-day-today span {
            background: #ffffff;
            color: #1BBA70;
        }
        .picker-calendar-day.picker-calendar-day-selected span {
            color: red;
        }
    </style>
</head>
<body>
<section class="addbodydiv">
    <div id="divcalendar" style="height: 13rem"></div>
    <script>
        $(function () {
            debugger
            var _calendar = $("#divcalendar");
            _calendar.calendar({
                toolbar: false,
                weekHeader: false,
                container: "#divcalendar",
                onMonthYearChangeEnd: function (p, currentYear, currentMonth) {
                    setMonthShow(p);
                },
                onOpen: function (p) {
                    setMonthShow(p);
                },
                onDayClick: function (p, dayContainer, year, month, day) {

                }
            });
            function setMonthShow(p) {
                console.log(p.currentYear + " " + p.currentMonth);
                
            }
        })
    </script>
</section>
<script src="jquery-weui.js?v=22"></script>
</body>
</html>

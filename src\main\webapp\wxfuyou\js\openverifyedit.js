require.config({
    baseUrl: '../',//指定js文件的基路径
    paths: {
        system: 'sys/system_zepto',
        moment:'plugin/js/moment',
        common: 'wxcommon/common',
        // commongroup: 'wxcommon/commongroup',
        chdccommon: 'wxchdc/js/chdccommon',
        // iframecommon: 'wxchdc/js/iframecommon',
        open: 'wxchdc/js/openverifyjson'
    },
    waitSeconds: 0
});
var objdata = {
    yeyname:""
    , truename: ""
    , curyeyname: ""//地区名称
    , verifyid: 0//核验id
};
require(['system','common', 'chdccommon','moment','open'], function (s, c, p, moment) {
    // objdata.moment = moment;
    objdata.curmoment = moment();
    //获取用户信息
    selectEmployeeInfo(function (obj) {
        if (!obj) {
            console.log("没有obj");
            $("#divunsub").show().siblings().hide();
            return;
        }
        window.objinfo = obj;
        console.log(JSON.stringify(obj));
        objdata.openid = obj.openid;
        if (!obj.mobile) {
            parent.layer.open({
                content: "没有绑定手机号，请绑定后使用",
                btn: '确定',
                yes: function () {
                    var args = Arg.all();
                    parent.location.href = "bind.html?v=" + Arg('v') + "&frompage=openverifyedit" + "&flag=" + args['flag'] + (Arg("id") ? '&id=' + Arg("id") : "");
                }
            });
            return;
        }
        console.log(obj.mobile);
        console.log("objinfo.areacode:" + objinfo.areacode);
        if (objinfo.areacode) {
        } else {
            parent.layer.msg("请先添加地区用户！");
            return;
        }
        objdata.areacode = objinfo.areacode;//地区编码
        objdata.curyeyname = Arg("areaname") || "";//地区名称
        objdata.truename = objinfo.truename;//真实姓名
        if (objdata.openid) {
        } else {
            location.href = "openidnotfind.html";
        }
        $('body').on('click', '#back', function () {
            history.back();
        });
        //处理当前页面逻辑
        objopenverify.initEvent();
        objopenverify.initData();
    });
});

var objopenverify = {
    /**
     * 注册事件
     */
    initEvent: function () {
        $("body").on('click','input',function(){//结果
            var txtname = $(this).prop('name');
            if(txtname.indexOf('result') == 0){
                var v = $(this).val();//值
                var f1 = txtname.replace('result', '');
                objopenverify.savesingle('code' + f1, f1, txtname, v);
            }
        }).on('blur', 'textarea', function () {//备注
            var id = $(this).prop('id');
            if(id.indexOf('remark') == 0){
                var v = $.trim($(this).val());//值
                var f1 = id.replace('remark', '');
                objopenverify.savesingle('code' + f1, f1, id, v, true);
            }
        });
        //初始化页面
        if(!$.isEmptyObject(objverify)){
            var xmnum = 1, arrhtml = [], znum = 0;//xmnum项目序号
            for (var n in objverify) {
                var objitem = objverify[n];
                arrhtml.push('<div class="set-radio-style3" style="margin-bottom: 10px;background: #ffffff;"><h5 class="obj-title">项目' + xmnum + '：' + objitem.name + '</h5>');
                for (var o in objitem) {
                    if(o != 'name'){
                        arrhtml.push('<div class="verify-cell codeverify" code="' + n + "_" + o + '"><h6>' + o + "." + objitem[o].name + '</h6>\
                            <div class="verify-txt"><p>核验要点：'  + objitem[o].content + '</p><p>核验方法：' + objitem[o].way + '</p></div>\
                            <div class="verify-form">\
                                <div class="weui-flex" style="margin-top: 10px;">核验结果：\
                                    <label class="weui-flex_center"><input type="radio" name="result' + n + "_" + o + '" value="1">是</label>\
                                    <label class="weui-flex_center red-radio" style="margin-left: 20px;"><input type="radio" name="result' + n + "_" + o + '" value="2">否</label>\
                                </div>\
                                <div class="weui-flex" style="margin-top: 3px;">备注：\
                                <textarea name="" id="remark' + n + "_" + o + '" cols="30" rows="10" placeholder="请输入" class="weui-flex__item" maxlength="200"></textarea>\
                        </div></div></div>');
                        znum++;
                    }
                }
                arrhtml.push('</div>');
                xmnum++;
            }
            $("#divconment").html(arrhtml.join(""));
            $('.codeverify').eq(znum - 1).css('margin-bottom', '100px');
        }
    },
    //初始化开学条件核验数据
    initData: function () {
        if(parent.objdata.objyeydata && parent.objdata.objyeydata[Arg('yid')]){
            $.showLoading();
            var objyeyinfo = parent.objdata.objyeydata[Arg('yid')];
            $.sm(function (re, err) {
                $.hideLoading();
                if (err) {
                    parent.layer.msg(err);
                } else if(re && re[0]){
                    if(re[0] && re[0][0]){//核验信息
                        var nums = 0, yesnum = 0;//nums全部项，yesnum选择是的项
                        var item = re[0][0];
                        var p = $("#divconment");
                        for (var cd in item) {
                            if(cd.indexOf('result') == 0){//结果
                                p.find('input[name="' + cd + '"][value="' + item[cd] + '"]').attr('checked', true);
                            }else if(cd.indexOf('remark') == 0){//备注
                                p.find('#' + cd).val(item[cd]);
                            }else if(cd == "id"){
                                objdata.verifyid = item[cd];//核验id
                            }
                        }
                    }
                    // if(re[1] && re[1][0]){//政府端区域code
                    //     objdata.areacode = re[1][0][0];
                    // }
                    if(re[1] && re[1][0]){//集团园id
                        objdata.groupid = re[1][0][0];
                    }
                }
                //保存
                $("#btnsave").click(function () {
                    objopenverify.savedata();
                });
            }, [["openverify.seljson", Arg('yid')], ['openverify.getgroupid', Arg('yid')]]);
        }else{
            return parent.layer.msg("未找到幼儿园，无法核验！");
        }
    },
    savedata: function () {//保存
        $.showLoading('存储中...');
        var field = [], vals = [], flag = 0;
        $('.codeverify').each(function () {
            var _this = $(this);
            var code = _this.attr('code');
            var result = _this.find("input[name='result" + code + "']:checked").val(), remark = $.trim(_this.find("#remark" + code).val());
            if(!result){
                var arrcode = code.split('_');
                // _this.scroll();
                parent.layer.msg("项目" + arrcode[0] + "的第" + arrcode[1] + "项未选，请选择！");
                flag = 1;
                return false;
            }
            field.push('code' + code);
            vals.push("'" + code + "'");
            field.push('result' + code);
            vals.push(result);
            field.push('remark' + code);
            vals.push("'" + remark + "'");
        });
        if(flag == 1){
            $.hideLoading('存储中...');
            return;
        }
        if(field.length > 0){
            $.sm(function (re, err) {
                $.hideLoading('存储中...');
                if(err){
                    parent.layer.msg("保存失败！");
                }else {
                    parent.layer.msg("保存成功！");
                    objdata.verifyid = re;
                    var win = parent.$("#frame" + Arg('frompage'))[0].contentWindow;
                    win.objopenverify.initData();
                    parent.objopenverify.initData();
                    history.back();
                }
            }, ['openverifyedit.save', Arg('yid'), objdata.groupid || 'null', objdata.areacode || '', objdata.curyeyname, objdata.truename, 'c', field.join(","), vals.join(","), objdata.verifyid || 0]);
        }else{
            parent.layer.msg("没有要保存的数据！");
            $.hideLoading('存储中...');
        }
    },
    //单项保存
    savesingle: function (f1, code, f, v, str) {//f字段，v值，str标识是字符串类型
        $.showLoading('存储中...');
        $.sm(function (re, err) {
            if(err){
                parent.layer.msg("保存失败！");
            }else {
                objdata.verifyid = re;
                var win = parent.$("#frame" + Arg('frompage'))[0].contentWindow;
                win.objopenverify.initData();
                parent.objopenverify.initData();
                $.hideLoading('存储中...');
                // parent.layer.msg("保存成功！");
                // var win = parent.$("#frame" + Arg('frompage'))[0].contentWindow;
                // win.objopenverify.initData();
                // history.back();
            }
        }, ['openverifyedit.savesingle', f1, "'" + code + "'", f, str ? "'" + v + "'" : v, objdata.verifyid || 0, Arg('yid')]);
    }
};
var gzhtype = 'wxhome';
//配置微信api
function configwx(resolve) {
    if(!parent.objdata.haswxconfig){
        $.sm(function (re, err) {
            if (re) {
                var wxinfo = JSON.parse(re);
                wx.config({
                    debug: false,
                    appId: wxinfo['appId'],
                    timestamp: wxinfo['timestamp'],
                    nonceStr: wxinfo['nonceStr'],
                    signature: wxinfo['signature'],
                    jsApiList: parent.objdata.jsApiList
                });
                resolve();
            } else {
                layer.msg(err);
            }
        }, ["wx_jssdk_config", "wx_home", parent.location.href.split('#')[0]]);
    } else {
        resolve();
    }
}
//查询个人信息
function selectMyInfo(cb) {
    $.sm(function (reo,erro) {
        $.sm(function (re,err) {
            if (re && re.length) {
                var objinfo = re[0];
                objinfo.stuno = objinfo.curbid;
                var arrinfo = objinfo.babyjson && $.parseJSON(objinfo.babyjson) || [];
                var arryey = objinfo.yeyinfo && $.parseJSON(objinfo.yeyinfo) || [];
                var yinfolenth = arryey.length;//宝宝数目
                for (var j = 0; j < yinfolenth; j++) {// 幼儿园的json可能不存在yeyinfo里的yey信息  不存在就合并
                    var yyguid = arryey[j].yguid;
                    var has = false;
                    for (var k = 0; k < arrinfo.length; k++) {
                        var info = arrinfo[k];
                        var byguid = info.yguid;
                        if (byguid == yyguid) {
                            has = true;
                            break;
                        }
                    }
                    if (!has) {
                        arrinfo.push(arryey[j]);
                    }
                }
                objinfo.arrinfo = arrinfo;
                if (arrinfo.length) {//可能没有宝宝
                    if (!objinfo.curyguid && arrinfo.length) {
                        //异常情况
                        objinfo.dbyguidwrong = true;//
                    }
                    var hascur = false;
                    for (var i = 0; i < arrinfo.length; i++) {
                        var info = arrinfo[i],
                            curmark = iscurbaby(objinfo.curyguid,objinfo.curbid,info);
                        if (curmark) {
                            hascur = true;
                            objinfo.objyey = {
                                yeyid:info.yid,
                                yeyname:info.yeyname,
                                yguid:info.yguid,
                            }
                            if (objinfo.curbid) {
                                var birthday = info.birthday && info.birthday.substr(0,10),
                                    t1 = (birthday && new Date(birthday.replace(/-/g,"/")))?new Date(birthday.replace(/-/g,"/")):new Date('2012-08-19'),
                                    age = GetAge(t1,new Date(),'text'),
                                    agenum = GetAge(t1,new Date(),'num'),
                                    img = info.photopath?ossPrefix + info.photopath + ossParam2:(info.sex === "男"?"images/boy.png":"images/girl.png");
                                if (info.claname && info.claname.indexOf("班") == -1) {
                                    info.claname = info.claname + "班";
                                }
                                objinfo.objbaby = {
                                    yeyid:info.yid,
                                    yguid:info.yguid,
                                    yeyname:info.yeyname,
                                    photopath:info.photopath,
                                    headimg:img,
                                    stuno:objinfo.curbid,
                                    stuname:info.stuname,
                                    sex:info.sex,
                                    age:age,
                                    agenum:agenum,
                                    classno:info.classno,
                                    claname:info.claname,
                                    ptype:info.ptype,
                                    intime:info.intime
                                }
                            }
                        }
                    }
                    if (!hascur && objinfo.curyguid) {//修复guid和babyid不一致的问题
                        for (var i = 0; i < arrinfo.length; i++) {
                            var info = arrinfo[i],
                                curmark = iscurbaby(objinfo.curyguid,0,info);
                            if (curmark) {
                                hascur = true;
                                objinfo.objyey = {
                                    yeyid:info.yid,
                                    yeyname:info.yeyname,
                                    yguid:info.yguid,
                                }
                            }
                        }
                    }
                }
                cb(objinfo);
            } else {
                cb(null,err);
            }
        },['wxhome.myjsoninfo']);
    },["wxhome.freshbabys"]);
}
/**
 * 获取任意时间的宝宝详细信息（包括班级 入园时间 离园时间 状态）
 * @param stuno
 * @param objtime
 * {
 *      type:'time',//year(本年的9-1) time schoolyear(根据学校的学年获取)
 * }
 * @param cb
 */
function getbabydetail(stuno,objtime,cb) {
    var strdate = 'now()';
    if (objtime) {
        if (objtime.type == 'year' && objtime.year) {
            strdate = "'" + objtime.year + '-09-01' + "'";
        } else if (objtime.type == 'time' && objtime.time) {
            strdate = "'" + objtime.time + "'";
        }
    }
    $.sm(function (re,err,obj) {
        cb && cb(obj);
    },['wxhome.getbabydetail',stuno,strdate])
}
//获取个人信息（供其他页面通用）
function getMyInfo(cb) {
    if (parent.objinfo) {
        cb(parent.objinfo);
    } else {
        selectMyInfo(function (objinfo,err) {
            cb(objinfo,err);
        });
    }
}
/**
 * 是否当前宝宝
 * @param curyguid
 * @param curstuno
 * @param curinfo
 * @returns {Boolean}
 */
function iscurbaby(curyguid,curstuno,curinfo) {
    if (curstuno) {
        if (curinfo && curyguid && curstuno) {
            if (curinfo.yguid == curyguid && curinfo.stuno == curstuno) {
                return true;
            }
        }
    } else {
        if (curinfo && curyguid && curinfo.yguid == curyguid) {
            return true;
        }
    }
    return false;
}
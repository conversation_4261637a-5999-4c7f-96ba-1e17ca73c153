// $(function () {
//     document.body.addEventListener('touchmove', function (e) {
//         e.preventDefault() // 阻止默认的处理方式(阻止下拉滑动的效果)
//     }, { passive: false }) // passive 参数不能省略，用来兼容ios和android
// })
var commonconfig = {};
var commonptype = ["", "爸爸", "妈妈", "爷爷", "奶奶", "姥爷", "姥姥", "叔叔", "阿姨", "哥哥", "姐姐"];
var arrweekdaystr = "日一二三四五六".split("");
commonconfig.objill = {
    1: '其他',
    2: '感冒',
    4: '咳嗽',
    8: '发热',
    16: '肠胃不适',
    32: '皮疹',
    64: '腹泻',
    128: '黄疸',
    256: '结膜红肿',
    512: '呕吐',
    1024: '牙疼'
};
commonconfig.objthings = {
    1: '其他',
    2: '休息',
    4: '辅导班',
    8: '寒暑假',
    16: '旅游'
};
//1 托班、2 小班、3 中班、4 大班、5 学前
commonconfig.arrgrade = ["", "托班", "小班", "中班", "大班", "学前", "混龄"];
commonconfig.objgrade = {
    1: "托班",
    2: "小班",
    3: "中班",
    4: "大班",
    5: "学前",
    6: "混龄"
};
function getgradebyno(no) {
    return commonconfig.objgrade[no] || "";
}
function getDateByStr(strdate) {
    strdate = strdate.split(" ")[0];
    var arr = strdate.split("-");
    return new Date(arr[0], arr[1] - 1, arr[2]);
}
function getTimeByStr(strdate) {
    strdate = strdate.substr(0, 19).replace(/-/g, '/');
    return new Date(strdate);
}
function getStrHtml(strValue) {
    if (strValue)
        return strValue.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>').replace(/\s/g, ' ');
    else
        return strValue;
}
function getStrVal(strHtml) {
    if (strHtml)
        return strHtml.replace(/<br>/g, '\n');
    else
        return strHtml;
}
function getpreday(date, num) {
    var d = getpredaydate(date, num);
    return getStrByDate(d);
}
function getpredaydate(date, num) {
    if (!num) num = 1;
    var currentDate = typeof date == 'string' ? getDateByStr(date) : date;
    var d = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - num);
    return d;
}
/**
 * Created by 郭玉峰 on 2017/11/16.
 */
/**
 * 功能：根据出生日期dtBeginTime 得到格式（2.01）的年龄
 * 参数都是Date()类型new Date('2016-8-19')
 * @param dtBeginTime
 * @param nowDate
 * @param flag  num text 默认为num
 * @returns {String}
 */
function GetAge(dtBeginTime, nowDate, flag) {
    if (!dtBeginTime) return "";
    if (typeof dtBeginTime == "string") {
        dtBeginTime = new Date(dtBeginTime.split(" ")[0].replace(/-/g, "/"));
    }
    var nowYear = nowDate.getFullYear();
    var birYear = dtBeginTime.getFullYear();
    var nowMonth = nowDate.getMonth() + 1;
    var birMonth = dtBeginTime.getMonth() + 1;
    var nowDay = nowDate.getDate();
    var birDate = dtBeginTime.getDate();
    var ageMonth = 0;
    var ageYear = 0;
    if (birMonth > nowMonth) {
        nowYear -= 1;
        ageMonth = 12 - birMonth + nowMonth;
    } else
        ageMonth = nowMonth - birMonth;
    ageYear = nowYear - birYear;
    if (nowDay < birDate)
        ageMonth--;
    if (ageMonth < 0) {
        ageYear--;
        ageMonth = 11;
    }
    if (!flag || flag == 'num') {
        if (parseInt(ageMonth) == 0)
            return ageYear + ".00";
        else {
            if (ageMonth < 10)
                ageMonth = "0" + ageMonth;
            return ageYear + "." + ageMonth;
        }
    } else {
        if (parseInt(ageMonth) == 0)
            return ageYear + "岁";
        else {
            return ageYear + "岁" + ageMonth + "个月";
        }
    }
}
/**
 * 获取家庭成员信息
 * @param type
 */
function getHomeMeb(type) {
    return commonptype[type] || '家长';
}
/**
 * 第一版家庭头像
 * @param ptype
 * @return {*}
 */
function getHomeMebImg1(ptype) {
    var touxiang = '';
    if (ptype == 1 || ptype == 7) {
        touxiang = 'img_baba.png';
    } else if (ptype == 2 || ptype == 8) {
        touxiang = 'img_mama.png';
    } else if (ptype == 3 || ptype == 5) {
        touxiang = 'img_.png';
    } else if (ptype == 4 || ptype == 6) {
        touxiang = 'chat4.png';
    } else if (ptype == 9) {
        touxiang = 'tea_img1.png';
    } else if (ptype == 10) {
        touxiang = 'tea_img3.png';
    }
    return touxiang;
}
/**
 * 第二版家庭头像
 * @param ptype
 * @return {*}
 */
function getHomeMebImg(type) {
    if (type) {
        if (type == 1) {//爸爸
            return 'images/img_baba.png';
        } else if (type == 2) {//妈妈
            return 'images/img_mama.png';
        } else if (type == 3) {//爷爷
            return 'images/img_laonan.png';
        } else if (type == 4) {//奶奶
            return 'images/img_laonv.png';
        } else if (type == 5) {//姥爷
            return 'images/img_laonan.png';
        } else if (type == 6) {//姥姥
            return 'images/img_laonv.png';
        } else if (type == 7) {//叔叔
            return 'images/img_zhongnan.png';
        } else if (type == 8) {//阿姨
            return 'images/img_zhongnv.png';
        } else if (type == 9) {//哥哥
            return 'images/img_qingnan.png';
        } else if (type == 10) {//姐姐
            return 'images/img_qingnv.png';
        } else {
            return 'images/img_teachernv.png';
        }
    } else {
        return 'images/img_teachernv.png';
    }
}
//设置宝宝头像
function setBabyImg(_img, obj) {
    if (obj.headimg) {
        _img.prop('src', obj.headimg).error(function () {
            if (obj.sex == '男') {
                _img.prop("src", "images/img_boy.png");
            } else {
                _img.prop("src", "images/img_girl.png");
            }
        })
    } else {
        if (!obj.sex || obj.sex == '男') {
            _img.prop("src", "images/img_boy.png");
        } else {
            _img.prop("src", "images/img_girl.png");
        }
    }
}
function getPhotoPath(fromtype, sex, photopath) {
    if (fromtype > 20) {
        if (photopath) {
            photopath = ossPrefix + photopath + "?x-oss-process=image/resize,m_fill,h_50,w_50";
        } else {
            if (sex == "男") {
                photopath = "images/img_boy.png";
            } else {
                photopath = "images/img_girl.png";
            }
        }
    } else {
        photopath = 'images/img_teachernv.png';
    }
    return photopath;
}
//用于fromtype==1代表家长的模式
function getPhotoPath2(fromtype, sex, photopath) {
    if (fromtype == 1 || fromtype > 20) {
        if (photopath) {
            photopath = ossPrefix + photopath + "?x-oss-process=image/resize,m_fill,h_50,w_50";
        } else {
            if (sex == "男") {
                photopath = "images/img_boy.png";
            } else {
                photopath = "images/img_girl.png";
            }
        }
    } else {
        photopath = 'images/img_teachernv.png';
    }
    return photopath;
}
/**
 * 根据大小得到压缩比率 使图片大约在300k
 * @param size
 * @param type noother 没有其他参数 hasother 有其他参数
 */
function getImgRatio(size, type) {
    var ratio = "";
    var maxsize = 400;
    if (size) {
        if (size <= maxsize * 1000) {
            return 100;
        } else {
            ratio = parseInt(maxsize * 1000 * 100 / size);
            return ratio;
        }
    } else {
        return 100;
    }
}
//?x-oss-process=image/resize,w_500
function getImgOSSParam(objmedia, winwidth) {
    if (objmedia.w < winwidth * 2) {
        return "";
    } else {
        return "?x-oss-process=image/resize,w_" + winwidth * 2;
    }
}

function changeTextToUrl(inputText) {
    var replacedText, replacePattern1, replacePattern2, replacePattern3;
    var originalText = inputText;
    //URLs starting with http://, https://, file:// or ftp://
    replacePattern1 = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
    //URLs starting with "www." (without // before it, or it'd re-link the ones done above).
    replacePattern2 = /(^|[^\/f])(www\.[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
    //Change email addresses to mailto:: links.
    replacePattern3 = /(([a-zA-Z0-9\-\_\.])+@[a-zA-Z\_]+?(\.[a-zA-Z]{2,6})+)/gi;
    //If there are hrefs in the original text, let's split
    // the text up and only work on the parts that don't have urls yet.
    var count = originalText.match(/<a href/g) || [];
    if (count.length > 0) {
        var combinedReplacedText;
        //Keep delimiter when splitting
        var splitInput = originalText.split(/(<\/a>)/g);

        for (i = 0; i < splitInput.length; i++) {
            if (splitInput[i].match(/<a href/g) == null) {
                splitInput[i] = splitInput[i].replace(replacePattern1, '<a href="$1" target="_blank">$1</a>').replace(replacePattern2, '$1<a href="http://$2" target="_blank">$2</a>').replace(replacePattern3, '<a href="mailto:$1">$1</a>');
            }
        }
        combinedReplacedText = splitInput.join('');
        return combinedReplacedText;
    } else {
        replacedText = inputText.replace(replacePattern1, '<a href="$1" target="_blank">$1</a>');
        replacedText = replacedText.replace(replacePattern2, '$1<a href="http://$2" target="_blank">$2</a>');
        replacedText = replacedText.replace(replacePattern3, '<a href="mailto:$1">$1</a>');
        return replacedText;
    }
}
/**
 * 文本框根据输入内容自适应高度
 * @param                {HTMLElement}        输入框元素
 * @param                {Number}                设置光标与输入框保持的距离(默认0)
 * @param                {Number}                设置最大高度(可选)
 */
function autoTextarea(elem, extra, maxHeight) {
    extra = extra || 0;
    var isFirefox = !!document.getBoxObjectFor || 'mozInnerScreenX' in window,
        isOpera = !!window.opera && !!window.opera.toString().indexOf('Opera'),
        addEvent = function (type, callback) {
            elem.addEventListener ?
                elem.addEventListener(type, callback, false) :
                elem.attachEvent('on' + type, callback);
        },
        getStyle = elem.currentStyle ? function (name) {
            var val = elem.currentStyle[name];

            if (name === 'height' && val.search(/px/i) !== 1) {
                var rect = elem.getBoundingClientRect();
                return rect.bottom - rect.top -
                    parseFloat(getStyle('paddingTop')) -
                    parseFloat(getStyle('paddingBottom')) + 'px';
            }
            return val;
        } : function (name) {
            return getComputedStyle(elem, null)[name];
        },
        minHeight = parseFloat(getStyle('height'));
    objdata.minHeight = minHeight;

    elem.style.resize = 'none';

    var change = function () {
        var scrollTop, height,
            padding = 0,
            style = elem.style;

        if (elem._length === elem.value.length) return;
        elem._length = elem.value.length;

        if (!isFirefox && !isOpera) {
            padding = parseInt(getStyle('paddingTop')) + parseInt(getStyle('paddingBottom'));
        }
        scrollTop = document.body.scrollTop || document.documentElement.scrollTop;

        elem.style.height = minHeight + 'px';
        if (elem.scrollHeight > minHeight) {
            if (maxHeight && elem.scrollHeight > maxHeight) {
                height = maxHeight - padding;
                style.overflowY = 'auto';
            } else {
                height = elem.scrollHeight - padding;
                style.overflowY = 'hidden';
            }
            style.height = height + extra + 'px';
            scrollTop += parseInt(style.height) - elem.currHeight;
            document.body.scrollTop = scrollTop;
            document.documentElement.scrollTop = scrollTop;
            elem.currHeight = parseInt(style.height);
        }
    };

    addEvent('propertychange', change);
    addEvent('input', change);
    addEvent('focus', change);
    change();
    return change;
};
/**
 * 获取时间间隔
 * @param date
 * @return {Number}
 */
function getDays(date) {
    var strSeparator = "-"; //日期分隔符
    if (typeof date == "string") {
        date = date.split(" ")[0];
        var arr = date.split(strSeparator);
        date = new Date(arr[0], arr[1] - 1, arr[2]);
    }
    var now = new Date();
    var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    var iDays = parseInt((date - today ) / 1000 / 60 / 60 / 24)//把相差的毫秒数转换为天数
    return iDays;
}
function getMonday(date) {
    var d = getMondayDate(date);
    return getStrByDate(d);
}
function getMondayDate(date) {
    if (typeof date == "string") {
        date = date.split(" ")[0];
        var arr = date.split("-");
        date = new Date(arr[0], arr[1] - 1, arr[2]);
    }
    var idendity = date.getDay();  //返回值0-6 ,分别表示这个礼拜的星期日到星期六
    var arr = [6, 0, 1, 2, 3, 4, 5];
    var d = new Date(date.getFullYear(), date.getMonth(), date.getDate() - arr[idendity]);
    return d;
}
function getDateStr(dateString) {
    if (dateString.trim() == "")return false;
    dateString = dateString.substring(0, 10);
    var r = dateString.match(/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/);
    if (r != null) {
        return dateString;
    }
}
/*
 功能：得到日期字符串
 */
function getStrByDate(d) {
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
}
function getDayFlag(date) {
    var iDays = getDays(date);
    if (iDays == 0) {
        return "今天";
    } else if (iDays == 1) {
        return "明天";
    } else if (iDays == 2) {
        return "后天";
    } else if (iDays == -1) {
        return "昨天";
    }
    return "";
}
/*字符串格式的日期*/
function getStrDate(d) {
    var strSeparator = "-"; //日期分隔符
    if (typeof d == "string") {
        d = d.split(" ")[0];
        var arr = d.split(strSeparator);
        d = new Date(arr[0], arr[1] - 1, arr[2]);
    }
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate());
};
function getStrTime(d) {
    return d.getFullYear() + "-" + (((d.getMonth() + 1) < 10) ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1)) + "-" + ((d.getDate() < 10) ? "0" + d.getDate() : d.getDate()) + " " + ((d.getHours() < 10) ? "0" + d.getHours() : d.getHours()) + ":" + ((d.getMinutes() < 10) ? "0" + d.getMinutes() : d.getMinutes()) + ":" + ((d.getSeconds() < 10) ? "0" + d.getSeconds() : d.getSeconds());
};
function getStrChaTime(strtime1, strtime2) {
    var date1 = getTimeByStr(strtime1);
    var date2 = getTimeByStr(strtime2);
    var seconds = (date1 - date2) / 1000;
    if (seconds < 0) {
        seconds = -seconds;
    }
    return getStrChaTimeBySecond(seconds);
}
//获取相差时间   格式1天2小时5分10秒
function getStrChaTimeBySecond(seconds) {
    var days = parseInt(seconds / 24 / 3600);
    var hours = parseInt((seconds - days * 24 * 3600) / 3600);
    var minutes = parseInt((seconds - days * 24 * 3600 - hours * 3600) / 60)
    //var seconds = parseInt(time - days * 24 * 3600 - hours * 3600 - minutes *60);
    var costinfo = '';
    if (days < 0 || hours < 0 || minutes < 0) {
        days = 0;
        hours = 0;
        minutes = 0;
        //seconds = 0;
    }
    costinfo += days > 0 ? (days + '天') : '';
    costinfo += hours > 0 ? (hours + '小时') : '';
    costinfo += minutes > 0 ? (minutes + '分钟') : '';
    return costinfo || '0分';
}
//获取微信格式的时间
function getStrShowTime(oldstrtime) {
    if (oldstrtime) {
        var curMo = null;
        if (typeof oldstrtime == 'string') {
            oldstrtime = oldstrtime.substring(0, 19).replace(/-/g, '/');
            curMo = moment(new Date(oldstrtime));
        } else {
            curMo = moment(oldstrtime);
        }
        var nowMo = moment();
        if (curMo.year() == nowMo.year()) {
            if (curMo.month() == nowMo.month()) {
                if (curMo.dayOfYear() == nowMo.dayOfYear()) {
                    return curMo.format("HH:mm");
                } else if (curMo.dayOfYear() == nowMo.dayOfYear() - 1) {
                    return curMo.format("昨天 HH:mm");
                } else {
                    return curMo.format("MM-DD");
                }
            } else {
                return curMo.format("YYYY-MM-DD");
            }
        } else {
            return curMo.format("YYYY-MM-DD");
        }
    } else {
        var curMo = moment(oldstrtime);
        return curMo.format("HH:mm");
    }
}
function renderTemp(template, model) {
    var reg = /\{\{\w+\}\}/g;
    template = template.replace(reg, function (regStr) {
        var reg2 = /\{\{(\w+)\}\}/g,
            key = reg2.exec(regStr)[1];
        return model[key];
    });
    return template;
};
//打开窗口
function openlayer(key, obj) {
    if (!window.layer)return;
    var config = obj;
    var ctype = config.ctype;
    if (ctype) {
        config.type = 1;
        if (ctype == 'page' && config.content) {//整页弹框
            config.content = '<iframe id="frame' + key + '" src="' + config.content + '" style="width:100%;height:100%;border:none;position: absolute;top: 0;left: 0;"></iframe>';
        } else if (ctype == 'iframe' && config.content) {//部分弹框iframe
            config.style = config.style || "width:90%;height:90%";
            config.content = '<iframe id="frame' + key + '" src="' + config.content + '" style="width:100%;height:100%;border:none;"></iframe>';
        } else if (ctype == 'html') {

        }
    }
    config.end = function () {//判断您是点击后退 还是关闭
        var objwin = parent.objdata.arrwin[parent.objdata.arrwin.length - 1];
        if (objwin && objwin.layerindex == index) {
            history.back();
        }
    }
    var index = layer.open(config);
    parent.pushHash({
        key: key,
        wintype: 'layer',
        winname: window.name,
        layerindex: index
    });
}
function closelayer(obj) {
    if (!obj.winname) {//index
        layer.close(obj.layerindex);
    } else {
        $('iframe[name=' + obj.winname + ']')[0].contentWindow.layer.close(obj.layerindex);
    }
}
//确定本页面支持jquery-weui
function openPopup(key, _dom) {
    _dom.popup();
    _dom.find('.weui-popup__modal').on('close', function () {
        var objwin = parent.objdata.arrwin[parent.objdata.arrwin.length - 1];
        if (objwin && objwin.wintype == 'popup') {
            history.back();
        }
    })
    parent.pushHash({
        key: key,
        wintype: 'popup',
        winname: window.name
    });
}
function closepopup() {
    history.back();
}
function closepopupevent(obj) {
    if (!obj.winname) {//index
        $.closePopup();
    } else {
        $('iframe[name=' + obj.winname + ']')[0].contentWindow.$.closePopup();
    }

}

function getLocationRad(d) {
    return d * Math.PI / 180.0; //经纬度转换成三角函数中度分表形式。
}
//计算距离，参数分别为第一点的纬度，经度；第二点的纬度，经度
function getPointDistance(lng1, lat1, lng2, lat2) {
    var radLat1 = getLocationRad(lat1);
    var radLat2 = getLocationRad(lat2);
    var a = radLat1 - radLat2;
    var b = getLocationRad(lng1) - getLocationRad(lng2);
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    s = s * 6378.137; // 地球半径，千米;
    s = Math.round(s * 10000) / 10000; //输出为公里
    s = Math.round(s * 1000) / 1; //单位修改为米,取整
    //s=s.toFixed(4);
    return s;
}
function getzhongwenweek(strdate) {
    var date = getDateByStr(strdate);
    var day = date.getDay();
    return arrweekdaystr[day];
}
//格式化比率 0 返回 0 如果是整数 返回整数 小数 取2位小数
function getFormatBiLv(num, total) {
    if (!total || !num)return 0;
    var lv = num / total * 100;
    lv = lv.toFixed(2);//得到两位小数
    lv = lv.toString().replace(".00", "") * 1;
    return lv;
}

// 导出页面html(table)到Excel
// strHtml:[可选] 要导出的html
// 使用示例：$("#divtab").exportExcel("","");
$.fn.exportExcel = function (eid, strHtml, type) {
    type = type ? type : 1;
    var obj = this.get(0);
    var obj_id = $(this).attr("id");
    try {
        if (ExcellentExport) {
            var a = document.createElement("a");
            a.download = eid + ".xls";
            a.onclick = function () {
                return ExcellentExport.excel(this, obj_id, eid);
            }
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            return;
        }
    } catch (err) {
        Console.log("excel error！");
    }
    strHtml = (strHtml) ? strHtml : (obj.tagName == "TABLE" ? obj.outerHTML : obj.innerHTML);
    var htmlvalue = "";
    if (type == "1") {
        htmlvalue = "index.stexcel" + "%15" + eid + "%15" + $.EncodeSpChar(strHtml.replace(new RegExp('"', "g"), "'"));//.replace(new RegExp('"', "g"), "'")
    } else {
        htmlvalue = "common.tabletoexcel" + "%15" + type + "%15" + obj.outerHTML.replace(new RegExp('&nbsp;', "g"), "").replace(new RegExp('"', "g"), "'") + "%15" + "%15";//.replace(new RegExp('"', "g"), "'")
    }
    var cid = obj.id;
    if ($("#edivOutexportExcel" + cid)[0])
        $("div").remove("#edivOutexportExcel" + cid);
    var arrstr = [];
    arrstr.push('<div id="edivOutexportExcel' + cid + '" style="width: 0px;height: 0px;display:none;">');
    arrstr.push('<form id="form' + cid + '" action="' + $.smurl + '" accept-charset="UTF-8" method="post" enctype="application/x-www-form-urlencoded" target="formFrame' + cid + '">');
    arrstr.push('<input id="Password' + cid + '" name="arr" type="hidden" value="' + htmlvalue + '" style="display:none;" />');
    arrstr.push('</form>');
    arrstr.push('<iframe name="formFrame' + cid + '" id="formFrame' + cid + '" style="width: 0px;height: 0px;display:none;"></iframe>');
    arrstr.push('</div>');
    $(document.body).append(arrstr.join(''));
    $('#form' + cid)[0].submit();
};
/**
 * 生成excel对象
 * @type {{version, excel, csv}}
 */
var ExcellentExport = function () {
    var e = function (e, t, n) {
            t = t || "", n = n || 512;
            var o = window.atob(e), r = [], c = void 0;
            for (c = 0; c < o.length; c += n) {
                var i = o.slice(c, c + n), l = new Array(i.length), a = void 0;
                for (a = 0; a < i.length; a += 1)l[a] = i.charCodeAt(a);
                var f = new window.Uint8Array(l);
                r.push(f)
            }
            return new window.Blob(r, {type: t})
        },
        t = {excel: '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e</head><body>{table}</body></html>'},
        n = ",", o = "\r\n", r = function (e) {
            return window.btoa(window.unescape(encodeURIComponent(e)))
        }, c = function (e, t) {
            return e.replace(new RegExp("{(\\w+)}", "g"), function (e, n) {
                return t[n]
            })
        }, i = function (e) {
            return e.nodeType ? e : document.getElementById(e)
        }, l = function (e) {
            var t = e, o = -1 !== e.indexOf(n) || -1 !== e.indexOf("\r") || -1 !== e.indexOf("\n"),
                r = -1 !== e.indexOf('"');
            return r && (t = t.replace(/"/g, '""')), (o || r) && (t = '"' + t + '"'), t
        }, a = function (e) {
            var t = "", r = void 0, c = void 0, i = void 0, a = void 0;
            for (r = 0; r < e.rows.length; r += 1) {
                for (i = e.rows[r], c = 0; c < i.cells.length; c += 1)a = i.cells[c], t = t + (c ? n : "") + l(a.textContent.trim());
                t += o
            }
            return t
        }, f = function (t, n, o, r) {
            var c = void 0;
            return window.navigator.msSaveBlob ? (c = e(n, o), window.navigator.msSaveBlob(c, r), !1) : (window.URL.createObjectURL ? (c = e(n, o), t.href = window.URL.createObjectURL(c)) : (t.download = r, t.href = "data:" + o + ";base64," + n), !0)
        };
    return {
        version: function () {
            return "2.0.3"
        }, excel: function (e, n, o) {
            n = i(n);
            var l = {worksheet: o || "Worksheet", table: n.innerHTML}, a = r(c(t.excel, l));
            return f(e, a, "application/vnd.ms-excel", "export.xls")
        }, csv: function (e, t, c, l) {
            void 0 !== c && c && (n = c), void 0 !== l && l && (o = l), t = i(t);
            var u = "\ufeff" + a(t), s = r(u);
            return f(e, s, "application/csv", "export.csv")
        }
    }
}();
(/iphone|ipod|ipad/i.test(navigator.appVersion)) && document.addEventListener('blur', function (e) {
    document.body.scrollIntoView(false)
}, true)

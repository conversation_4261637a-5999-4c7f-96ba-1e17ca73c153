define(function () {
    return {
        "isout": [
            {
                title: "否",
                value: "0"
            }, {
                title: "是",
                value: "1"
            }
        ],
        "credentialstype": [
            {
                title: "居民身份证",
                value: "1"
            }, {
                title: "香港特区护照/身份证明",
                value: "4"
            }, {
                title: "澳门特区护照/身份证明",
                value: "5"
            }, {
                title: "台湾居民来往大陆通行证",
                value: "6"
            }, {
                title: "境外永久居住证",
                value: "7"
            }, {
                title: "其他",
                value: "3"
            }
        ],
        "xuexing": [['请选择', ''], ['未知血型', 'unknown'], ['A血型', 'A'], ['B血型', 'B'], ['AB血型', 'AB'], ['O血型', 'O'], ['RH阳性血型', 'RHN'], ['RH阴性血型', 'RHV'], ['HLA血型', 'HLA'], ['未定血型', 'undefinedxx'], ['其他血型', 'other']],//血型
        "intype": ["日托", "全托", "混托"],
        "szs": ["安徽", "北京", "重庆", "福建", "甘肃", "广东", "广西", "贵州", "河北", "河南", "黑龙江", "湖北", "湖南", "海南", "吉林", "江西", "江苏", "辽宁", "宁夏", "内蒙古", "青海", "上海", "陕西", "山东", "山西", "四川", "天津", "新疆", "西藏", "云南", "浙江", "台湾", "香港", "澳门"],
        "nationality": ["中国", "香港", "澳门", "台湾", "阿富汗", "阿尔巴尼亚", "阿尔及利亚", "美属萨摩亚", "安道尔", "安哥拉", "安圭拉", "南极洲", "安提瓜和巴布达", "阿根廷", "亚美尼亚", "阿鲁巴", "澳大利亚", "奥地利", "阿塞拜疆", "巴哈马", "巴林", "孟加拉国", "巴巴多斯", "白俄罗斯", "比利时", "伯利兹", "贝宁", "百慕大", "不丹", "玻利维亚", "波黑", "博茨瓦纳", "布维岛", "巴西", "英属印度洋领土", "文莱", "保加利亚", "布基纳法索", "布隆迪", "柬埔寨", "喀麦隆", "加拿大", "佛得角", "开曼群岛", "中非", "乍得", "智利", "圣诞岛", "科科斯(基林)群岛", "哥伦比亚", "科摩罗", "刚果（布）", "刚果（金）", "库克群岛", "哥斯达黎加", "科特迪瓦", "克罗地亚", "古巴", "塞浦路斯", "捷克", "丹麦", "吉布提", "多米尼克", "多米尼加共和国", "东帝汶", "厄瓜多尔", "埃及", "萨尔瓦多", "赤道几内亚", "厄立特里亚", "爱沙尼亚", "埃塞俄比亚", "福克兰群岛(马尔维纳斯)", "法罗群岛", "斐济", "芬兰", "法国", "法属圭亚那", "法属波利尼西亚", "法属南部领土", "加蓬", "冈比亚", "格鲁吉亚", "德国", "加纳", "直布罗陀", "希腊", "格陵兰", "格林纳达", "瓜德罗普", "关岛", "危地马拉", "几内亚", "几内亚比绍", "圭亚那", "海地", "赫德岛和麦克唐纳岛", "洪都拉斯", "匈牙利", "冰岛", "印度", "印度尼西亚", "伊朗", "伊拉克", "爱尔兰", "以色列", "意大利", "牙买加", "日本", "约旦", "哈萨克斯坦", "肯尼亚", "基里巴斯", "朝鲜", "韩国", "科威特", "吉尔吉斯斯坦", "老挝", "拉脱维亚", "黎巴嫩", "莱索托", "利比里亚", "利比亚", "列支敦士登", "立陶宛", "卢森堡", "前南马其顿", "马达加斯加", "马拉维", "马来西亚", "马尔代夫", "马里", "马耳他", "马绍尔群岛", "马提尼克", "毛里塔尼亚", "毛里求斯", "马约特", "墨西哥", "密克罗尼西亚联邦", "摩尔多瓦", "摩纳哥", "蒙古", "蒙特塞拉特", "摩洛哥", "莫桑比克", "缅甸", "纳米比亚", "瑙鲁", "尼泊尔", "荷兰", "荷属安的列斯", "新喀里多尼亚", "新西兰", "尼加拉瓜", "尼日尔", "尼日利亚", "纽埃", "诺福克岛", "北马里亚纳", "挪威", "阿曼", "巴基斯坦", "帕劳", "巴勒斯坦", "巴拿马", "巴布亚新几内亚", "巴拉圭", "秘鲁", "菲律宾", "皮特凯恩群岛", "波兰", "葡萄牙", "波多黎各", "卡塔尔", "留尼汪", "罗马尼亚", "俄罗斯联邦", "卢旺达", "圣赫勒拿", "圣基茨和尼维斯", "圣卢西亚", "圣皮埃尔和密克隆", "圣文森特和格林纳丁斯", "萨摩亚", "圣马力诺", "圣多美和普林西比", "沙特阿拉伯", "塞内加尔", "塞舌尔", "塞拉利昂", "新加坡", "斯洛伐克", "斯洛文尼亚", "所罗门群岛", "索马里", "南非", "南乔治亚岛和南桑德韦奇岛", "西班牙", "斯里兰卡", "苏丹", "苏里南", "斯瓦尔巴群岛", "斯威士兰", "瑞典", "瑞士", "叙利亚", "塔吉克斯坦", "坦桑尼亚", "泰国", "多哥", "托克劳", "汤加", "特立尼达和多巴哥", "突尼斯", "土耳其", "土库曼斯坦", "特克斯科斯群岛", "图瓦卢", "乌干达", "乌克兰", "阿联酋", "英国", "美国", "美国本土外小岛屿", "乌拉圭", "乌兹别克斯坦", "瓦努阿图", "梵蒂冈", "委内瑞拉", "越南", "英属维尔京群岛", "美属维尔京群岛", "瓦利斯和富图纳", "西撒哈拉", "也门", "南斯拉夫", "赞比亚", "津巴布韦"],
        "locationtype": ["城镇户口", "农村户口"],
        getValueBykey: function (type, key) {
            var obj = {};
            for (var i = 0; i < this[type].length; i++) {
                if (key == this[type][i].value)
                    return this[type][i].title
            }
            return "";
        },
        getWeUiArrObj: function (key) {
            var arr = [];
            if (this[key]) {
                for (var i = 0; i < this[key].length; i++) {
                    arr.push({
                        title: this[key][i][0],
                        value: this[key][i][1]
                    })
                }
            }
            return arr;
        },
        "nation": ["汉族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族", "布依族", "朝鲜族", "满族", "侗族", "瑶族", "白族", "土家族", "哈尼族", "哈萨克族", "傣族", "黎族", "僳僳族", "佤族", "畲族", "高山族", "拉祜族", "水族", "东乡族", "纳西族", "景颇族", "柯尔克孜族", "土族", "达斡尔族", "仫佬族", "羌族", "布朗族", "撒拉族", "毛南族", "仡佬族", "锡伯族", "阿昌族", "普米族", "塔吉克族", "怒族", "乌孜别克族", "俄罗斯族", "鄂温克族", "德昂族", "保安族", "裕固族", "京族", "塔塔尔族", "独龙族", "鄂伦春族", "赫哲族", "门巴族", "珞巴族", "基诺族", "外籍", "其他"],
        "localtion": ["本片", "本片外区", "本市外区", "外埠", "外籍", "港澳台"],
        "sex": ["男", "女"],
        guardianrelation: [["父亲", '1', '男'], ["母亲", '2', '女'], ['继父或养父', '4', '男'], ['继母或养母', '5', '女'], ['祖父', '6', '男'], ['祖母', '7', '女'], ['外祖父', '8', '男'], ['外祖母', '9', '女'], ['兄', '10', '男'], ['姐姐', '11', '女'], ['其他亲属', '3', ''], ['非亲属', '12', '']],
        guardianeducation:[['请选择',''],['高中及以下','高中及以下'],['中等学历','中等学历'],['大专','大专'],['本科','本科'],['硕士','硕士'],['博士','博士']]//学历（监护人）
        , hongkmactaiw: [['无','无'],['香港','香港'],['澳门','澳门'],['台湾','台湾'],['海外华侨','海外华侨'],['香港同胞亲属','香港同胞亲属'],['澳门同胞亲属','澳门同胞亲属'],['台湾同胞亲属','台湾同胞亲属'],['侨眷','侨眷'],['归侨','归侨'],['归侨子女归国留学人员','归侨子女归国留学人员'],['非华裔中国人','非华裔中国人'],['外籍华裔人','外籍华裔人'],['外国人','外国人'],['其他','其他']]//港澳台侨胞 无,香港,澳门,台湾,海外华侨,香港同胞亲属,澳门同胞亲属,台湾同胞亲属,侨眷,归侨,归侨子女归国留学人员,非华裔中国人,外籍华裔人,外国人,其他
        , studyway: [['住校', '住校'],['走读','走读']]
        , health: [['健康或良好', '1'],['一般或较弱', '2'],['有慢性病', '3'],['有生理缺陷', '4'],['残疾', '5']]//基础信息  健康状况 健康或良好,一般或较弱,有慢性病,有生理缺陷,残疾
        , isleft: [['否', '0'],['单亲外出','1'],['双亲外出','2']]//是否留守儿童
        , cjerttypearr: [["无", 0], ["视力残疾", "1"], ["听力残疾", "2"], ["言语残疾", "3"], ["肢体残疾", "4"], ["智力残疾", "5"], ["精神残疾", "6"], ["多重残疾", "7"], ["其他残疾", "8"]]
    };
});
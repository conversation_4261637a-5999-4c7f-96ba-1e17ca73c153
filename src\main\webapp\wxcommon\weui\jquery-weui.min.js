!function(t){"use strict";t.fn.transitionEnd=function(t){function e(o){if(o.target===this)for(t.call(this,o),n=0;n<i.length;n++)a.off(i[n],e)}var n,i=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],a=this;if(t)for(n=0;n<i.length;n++)a.on(i[n],e);return this},t.support={touch:!!("ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch)},t.touchEvents={start:t.support.touch?"touchstart":"mousedown",move:t.support.touch?"touchmove":"mousemove",end:t.support.touch?"touchend":"mouseup"},t.getTouchPosition=function(t){return"touchstart"===(t=t.originalEvent||t).type||"touchmove"===t.type||"touchend"===t.type?{x:t.targetTouches[0].pageX,y:t.targetTouches[0].pageY}:{x:t.pageX,y:t.pageY}},t.fn.scrollHeight=function(){return this[0].scrollHeight},t.fn.transform=function(t){for(var e=0;e<this.length;e++){var n=this[e].style;n.webkitTransform=n.MsTransform=n.msTransform=n.MozTransform=n.OTransform=n.transform=t}return this},t.fn.transition=function(t){"string"!=typeof t&&(t+="ms");for(var e=0;e<this.length;e++){var n=this[e].style;n.webkitTransitionDuration=n.MsTransitionDuration=n.msTransitionDuration=n.MozTransitionDuration=n.OTransitionDuration=n.transitionDuration=t}return this},t.getTranslate=function(t,e){var n,i,a,o;return void 0===e&&(e="x"),a=window.getComputedStyle(t,null),window.WebKitCSSMatrix?o=new WebKitCSSMatrix("none"===a.webkitTransform?"":a.webkitTransform):n=(o=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===e&&(i=window.WebKitCSSMatrix?o.m41:16===n.length?parseFloat(n[12]):parseFloat(n[4])),"y"===e&&(i=window.WebKitCSSMatrix?o.m42:16===n.length?parseFloat(n[13]):parseFloat(n[5])),i||0},t.requestAnimationFrame=function(t){return window.requestAnimationFrame?window.requestAnimationFrame(t):window.webkitRequestAnimationFrame?window.webkitRequestAnimationFrame(t):window.mozRequestAnimationFrame?window.mozRequestAnimationFrame(t):window.setTimeout(t,1e3/60)},t.cancelAnimationFrame=function(t){return window.cancelAnimationFrame?window.cancelAnimationFrame(t):window.webkitCancelAnimationFrame?window.webkitCancelAnimationFrame(t):window.mozCancelAnimationFrame?window.mozCancelAnimationFrame(t):window.clearTimeout(t)},t.fn.join=function(t){return this.toArray().join(t)}}($),function(t){"use strict";t.Template7=t.t7=function(){function t(t){return"[object Array]"===Object.prototype.toString.apply(t)}function e(t){return"function"==typeof t}function n(t){var e,n,i,a=t.replace(/[{}#}]/g,"").split(" "),o=[];for(n=0;n<a.length;n++){var r=a[n];if(0===n)o.push(r);else if(0===r.indexOf('"'))if(2===r.match(/"/g).length)o.push(r);else{for(e=0,i=n+1;i<a.length;i++)if(r+=" "+a[i],a[i].indexOf('"')>=0){e=i,o.push(r);break}e&&(n=e)}else if(r.indexOf("=")>0){var s=r.split("="),c=s[0],l=s[1];if(2!==l.match(/"/g).length){for(e=0,i=n+1;i<a.length;i++)if(l+=" "+a[i],a[i].indexOf('"')>=0){e=i;break}e&&(n=e)}var p=[c,l.replace(/"/g,"")];o.push(p)}else o.push(r)}return o}function i(e){var i,a,o=[];if(!e)return[];var r=e.split(/({{[^{^}]*}})/);for(i=0;i<r.length;i++){var s=r[i];if(""!==s)if(s.indexOf("{{")<0)o.push({type:"plain",content:s});else{if(s.indexOf("{/")>=0)continue;if(s.indexOf("{#")<0&&s.indexOf(" ")<0&&s.indexOf("else")<0){o.push({type:"variable",contextName:s.replace(/[{}]/g,"")});continue}var c=n(s),l=c[0],p=[],u={};for(a=1;a<c.length;a++){var d=c[a];t(d)?u[d[0]]="false"!==d[1]&&d[1]:p.push(d)}if(s.indexOf("{#")>=0){var h,f="",m="",v=0,g=!1,w=!1,y=0;for(a=i+1;a<r.length;a++)if(r[a].indexOf("{{#")>=0&&y++,r[a].indexOf("{{/")>=0&&y--,r[a].indexOf("{{#"+l)>=0)f+=r[a],w&&(m+=r[a]),v++;else if(r[a].indexOf("{{/"+l)>=0){if(!(v>0)){h=a,g=!0;break}v--,f+=r[a],w&&(m+=r[a])}else r[a].indexOf("else")>=0&&0===y?w=!0:(w||(f+=r[a]),w&&(m+=r[a]));g&&(h&&(i=h),o.push({type:"helper",helperName:l,contextName:p,content:f,inverseContent:m,hash:u}))}else s.indexOf(" ")>0&&o.push({type:"helper",helperName:l,contextName:p,hash:u})}}return o}var a=function(t){function e(t,e){return t.content?r(t.content,e):function(){return""}}function n(t,e){return t.inverseContent?r(t.inverseContent,e):function(){return""}}function a(t,e){var n,i,a=0;if(0===t.indexOf("../")){a=t.split("../").length-1;var o=e.split("_")[1]-a;e="ctx_"+(o>=1?o:1),i=t.split("../")[a].split(".")}else 0===t.indexOf("@global")?(e="$.Template7.global",i=t.split("@global.")[1].split(".")):0===t.indexOf("@root")?(e="ctx_1",i=t.split("@root.")[1].split(".")):i=t.split(".");n=e;for(var r=0;r<i.length;r++){var s=i[r];0===s.indexOf("@")?r>0?n+="[(data && data."+s.replace("@","")+")]":n="(data && data."+t.replace("@","")+")":isFinite(s)?n+="["+s+"]":0===s.indexOf("this")?n=s.replace("this",e):n+="."+s}return n}function o(t,e){for(var n=[],i=0;i<t.length;i++)0===t[i].indexOf('"')?n.push(t[i]):n.push(a(t[i],e));return n.join(", ")}function r(t,r){if(r=r||1,"string"!=typeof(t=t||s.template))throw new Error("Template7: Template must be a string");var c=i(t);if(0===c.length)return function(){return""};var l="ctx_"+r,p="(function ("+l+", data) {\n";1===r&&(p+="function isArray(arr){return Object.prototype.toString.apply(arr) === '[object Array]';}\n",p+="function isFunction(func){return (typeof func === 'function');}\n",p+='function c(val, ctx) {if (typeof val !== "undefined") {if (isFunction(val)) {return val.call(ctx);} else return val;} else return "";}\n'),p+="var r = '';\n";var u;for(u=0;u<c.length;u++){var d=c[u];if("plain"!==d.type){var h,f;if("variable"===d.type&&(p+="r += c("+(h=a(d.contextName,l))+", "+l+");"),"helper"===d.type)if(d.helperName in s.helpers)f=o(d.contextName,l),p+="r += ($.Template7.helpers."+d.helperName+").call("+l+", "+(f&&f+", ")+"{hash:"+JSON.stringify(d.hash)+", data: data || {}, fn: "+e(d,r+1)+", inverse: "+n(d,r+1)+", root: ctx_1});";else{if(d.contextName.length>0)throw new Error('Template7: Missing helper: "'+d.helperName+'"');p+="if ("+(h=a(d.helperName,l))+") {",p+="if (isArray("+h+")) {",p+="r += ($.Template7.helpers.each).call("+l+", "+h+", {hash:"+JSON.stringify(d.hash)+", data: data || {}, fn: "+e(d,r+1)+", inverse: "+n(d,r+1)+", root: ctx_1});",p+="}else {",p+="r += ($.Template7.helpers.with).call("+l+", "+h+", {hash:"+JSON.stringify(d.hash)+", data: data || {}, fn: "+e(d,r+1)+", inverse: "+n(d,r+1)+", root: ctx_1});",p+="}}"}}else p+="r +='"+d.content.replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/'/g,"\\'")+"';"}return p+="\nreturn r;})",eval.call(window,p)}var s=this;s.template=t,s.compile=function(t){return s.compiled||(s.compiled=r(t)),s.compiled}},o=function(t,e){if(2===arguments.length){var n=new a(t),i=n.compile()(e);return n=null,i}return new a(t)};return o.registerHelper=function(t,e){a.prototype.helpers[t]=e},o.unregisterHelper=function(t){a.prototype.helpers[t]=void 0,delete a.prototype.helpers[t]},o.compile=function(t,e){return new a(t,e).compile()},o.options=(a.prototype={options:{},helpers:{if:function(t,n){return e(t)&&(t=t.call(this)),t?n.fn(this,n.data):n.inverse(this,n.data)},unless:function(t,n){return e(t)&&(t=t.call(this)),t?n.inverse(this,n.data):n.fn(this,n.data)},each:function(n,i){var a="",o=0;if(e(n)&&(n=n.call(this)),t(n)){for(i.hash.reverse&&(n=n.reverse()),o=0;o<n.length;o++)a+=i.fn(n[o],{first:0===o,last:o===n.length-1,index:o});i.hash.reverse&&(n=n.reverse())}else for(var r in n)o++,a+=i.fn(n[r],{key:r});return o>0?a:i.inverse(this)},with:function(t,n){return e(t)&&(t=t.call(this)),n.fn(t)},join:function(t,n){return e(t)&&(t=t.call(this)),t.join(n.hash.delimiter||n.hash.delimeter)},js:function(t,e){var n;return n=t.indexOf("return")>=0?"(function(){"+t+"})":"(function(){return ("+t+")})",eval.call(this,n).call(this)},js_compare:function(t,e){var n;return n=t.indexOf("return")>=0?"(function(){"+t+"})":"(function(){return ("+t+")})",eval.call(this,n).call(this)?e.fn(this,e.data):e.inverse(this,e.data)}}}).options,o.helpers=a.prototype.helpers,o}()}($),function(t){"use strict";var e;t.modal=function(n,i){var a=(n=t.extend({},e,n)).buttons,o=a.map(function(t,e){return'<a href="javascript:;" class="weui-dialog__btn '+(t.className||"")+'">'+t.text+"</a>"}).join(""),r='<div class="weui-dialog"><div class="weui-dialog__hd"><strong class="weui-dialog__title">'+n.title+"</strong></div>"+(n.text?'<div class="weui-dialog__bd">'+n.text+"</div>":"")+'<div class="weui-dialog__ft">'+o+"</div></div>",s=t.openModal(r,i);return s.find(".weui-dialog__btn").each(function(e,i){t(i).click(function(){n.autoClose&&t.closeModal(),a[e].onClick&&a[e].onClick.call(s)})}),s},t.openModal=function(e,n){var i=t("<div class='weui-mask'></div>").appendTo(document.body);i.show();var a=t(e).appendTo(document.body);return n&&a.transitionEnd(function(){n.call(a)}),a.show(),i.addClass("weui-mask--visible"),a.addClass("weui-dialog--visible"),a},t.closeModal=function(){t(".weui-mask--visible").removeClass("weui-mask--visible").transitionEnd(function(){t(this).remove()}),t(".weui-dialog--visible").removeClass("weui-dialog--visible").transitionEnd(function(){t(this).remove()})},t.alert=function(n,i,a){var o;return"object"==typeof n?o=n:("function"==typeof i&&(a=arguments[1],i=void 0),o={text:n,title:i,onOK:a}),t.modal({text:o.text,title:o.title,buttons:[{text:e.buttonOK,className:"primary",onClick:o.onOK}]})},t.confirm=function(n,i,a,o){var r;return"object"==typeof n?r=n:("function"==typeof i&&(o=arguments[2],a=arguments[1],i=void 0),r={text:n,title:i,onOK:a,onCancel:o}),t.modal({text:r.text,title:r.title,buttons:[{text:e.buttonCancel,className:"default",onClick:r.onCancel},{text:e.buttonOK,className:"primary",onClick:r.onOK}]})},t.prompt=function(n,i,a,o,r){var s;"object"==typeof n?s=n:("function"==typeof i&&(r=arguments[3],o=arguments[2],a=arguments[1],i=void 0),s={text:n,title:i,input:r,onOK:a,onCancel:o,empty:!1});var c=t.modal({text:'<p class="weui-prompt-text">'+(s.text||"")+'</p><input type="text" class="weui-input weui-prompt-input" id="weui-prompt-input" value="'+(s.input||"")+'" />',title:s.title,autoClose:s.autoClose,buttons:[{text:e.buttonCancel,className:"default",onClick:function(){t.closeModal(),s.onCancel&&s.onCancel.call(c)}},{text:e.buttonOK,className:"primary",onClick:function(){var e=t("#weui-prompt-input").val();if(!s.empty&&(""===e||null===e))return c.find(".weui-prompt-input").focus()[0].select(),!1;t.closeModal(),s.onOK&&s.onOK.call(c,e)}}]},function(){this.find(".weui-prompt-input").focus()[0].select()});return c},t.login=function(n,i,a,o,r,s){var c;"object"==typeof n?c=n:("function"==typeof i&&(s=arguments[4],r=arguments[3],o=arguments[2],a=arguments[1],i=void 0),c={text:n,title:i,username:r,password:s,onOK:a,onCancel:o});var l=t.modal({text:'<p class="weui-prompt-text">'+(c.text||"")+'</p><input type="text" class="weui-input weui-prompt-input" id="weui-prompt-username" value="'+(c.username||"")+'" placeholder="输入用户名" /><input type="password" class="weui-input weui-prompt-input" id="weui-prompt-password" value="'+(c.password||"")+'" placeholder="输入密码" />',title:c.title,autoClose:!1,buttons:[{text:e.buttonCancel,className:"default",onClick:function(){t.closeModal(),c.onCancel&&c.onCancel.call(l)}},{text:e.buttonOK,className:"primary",onClick:function(){var e=t("#weui-prompt-username").val(),n=t("#weui-prompt-password").val();return c.empty||""!==e&&null!==e?c.empty||""!==n&&null!==n?(t.closeModal(),void(c.onOK&&c.onOK.call(l,e,n))):(l.find("#weui-prompt-password").focus()[0].select(),!1):(l.find("#weui-prompt-username").focus()[0].select(),!1)}}]},function(){this.find("#weui-prompt-username").focus()[0].select()});return l},e=t.modal.prototype.defaults={title:"提示",text:void 0,buttonOK:"确定",buttonCancel:"取消",buttons:[{text:"确定",className:"primary"}],autoClose:!0}}($),function(t){"use strict";var e=function(e,n,i){if(n=n||"",!i)t("<div class='weui-mask_transparent'></div>").appendTo(document.body);var a=t('<div class="weui-toast '+n+'">'+e+"</div>").appendTo(document.body);a.addClass("weui-toast--visible"),a.show()},n=function(e){t(".weui-mask_transparent").remove(),t(".weui-toast--visible").removeClass("weui-toast--visible").transitionEnd(function(){var n=t(this);n.remove(),e&&e(n)})};t.msg=function(e,n,i){function a(){t(".weui-toast--visible").remove(),i&&i(t(".weui-toast--visible"))}"function"==typeof(n=n||2e3)&&(i=n);var o=t('<div style="position:absolute;top:0;bottom:0;width:100%;display: table;height: 100%;text-align: center;" class="weui-toast--visible"><div style="display:table-cell;vertical-align:middle;text-align:center;"><div class="weui-toast weui-toast--text" style="position:static;transform:inherit;-webkit-transform:inherit;display:inline-block;max-width:80%;margin-top:100px;opacity: 1;visibility: visible"><i class="weui-icon-success-no-circle weui-icon_toast"></i><p class="weui-toast_content">'+e+"</p></div></div></div>").appendTo(document.body);o.addClass("weui-toast--visible"),o.show(),t(".weui-toast--visible").click(function(){a()}),setTimeout(function(){a()},n)},t.toast=function(t,a,o){"function"==typeof a&&(o=a);var r,s="weui-icon-success-no-circle",c=i.duration;"cancel"==a?(r="weui-toast_cancel",s="weui-icon-cancel"):"forbidden"==a?(r="weui-toast--forbidden",s="weui-icon-warn"):"text"==a?r="weui-toast--text":"number"==typeof a&&(c=a),e('<i class="'+s+' weui-icon_toast"></i><p class="weui-toast_content">'+(t||"已经完成")+"</p>",r),setTimeout(function(){n(o)},c)},t.showLoading=function(t){var n='<div class="weui_loading">';n+='<i class="weui-loading weui-icon_toast"></i>',n+="</div>",e(n+='<p class="weui-toast_content">'+(t||"数据加载中")+"</p>","weui_loading_toast")},t.hideLoading=function(){n()};var i=t.toast.prototype.defaults={duration:2500}}($),function(t){"use strict";var e=function(e){var n=t("<div class='weui-mask weui-actions_mask'></div>").appendTo(document.body),i=e.actions||[],a=i.map(function(t,e){return'<div class="weui-actionsheet__cell '+(t.className||"")+'">'+t.text+"</div>"}).join(""),o="";e.title&&(o='<div class="weui-actionsheet__title"><p class="weui-actionsheet__title-text">'+e.title+"</p></div>");var r=t('<div class="weui-actionsheet " id="weui-actionsheet">'+o+'<div class="weui-actionsheet__menu">'+a+'</div><div class="weui-actionsheet__action"><div class="weui-actionsheet__cell weui-actionsheet_cancel">取消</div></div></div>').appendTo(document.body);r.find(".weui-actionsheet__menu .weui-actionsheet__cell, .weui-actionsheet__action .weui-actionsheet__cell").each(function(n,a){t(a).click(function(){t.closeActions(),e.onClose&&e.onClose(),i[n]&&i[n].onClick&&i[n].onClick()})}),n.show(),r.show(),n.addClass("weui-mask--visible"),r.addClass("weui-actionsheet_toggle")},n=function(){t(".weui-mask").removeClass("weui-mask--visible").transitionEnd(function(){t(this).remove()}),t(".weui-actionsheet").removeClass("weui-actionsheet_toggle").transitionEnd(function(){t(this).remove()})};t.actions=function(n){n=t.extend({},i,n),e(n)},t.closeActions=function(){n()},t(document).on("click",".weui-actions_mask",function(){t.closeActions()});var i=t.actions.prototype.defaults={title:void 0,onClose:void 0}}($),function(t){"use strict";var e=function(n,i){"function"==typeof i&&(i={onRefresh:i}),"string"==typeof i&&(i=void 0),this.opt=t.extend(e.defaults,i||{}),this.container=t(n),this.attachEvents()};e.defaults={distance:50,onRefresh:void 0,onPull:void 0},e.prototype.touchStart=function(e){if(!this.container.hasClass("refreshing")){var n=t.getTouchPosition(e);this.start=n,this.diffX=this.diffY=0}},e.prototype.touchMove=function(e){if(!this.container.hasClass("refreshing")){if(!this.start)return!1;if(!(this.container.scrollTop()>0)){var n=t.getTouchPosition(e);if(this.diffX=n.x-this.start.x,this.diffY=n.y-this.start.y,Math.abs(this.diffX)>Math.abs(this.diffY))return!0;this.diffY<0||(this.container.addClass("touching"),e.preventDefault(),e.stopPropagation(),this.diffY=Math.pow(this.diffY,.75),this.container.css("transform","translate3d(0, "+this.diffY+"px, 0)"),this.triggerPull(this.diffY))}}},e.prototype.touchEnd=function(){this.start=!1,this.diffY<=0||this.container.hasClass("refreshing")||(this.container.removeClass("touching"),this.container.removeClass("pull-down pull-up"),this.container.css("transform",""),Math.abs(this.diffY)<=this.opt.distance||this.triggerPullToRefresh())},e.prototype.triggerPullToRefresh=function(){this.triggerPull(this.opt.distance),this.container.removeClass("pull-up").addClass("refreshing"),this.opt.onRefresh&&this.opt.onRefresh.call(this),this.container.trigger("pull-to-refresh")},e.prototype.triggerPull=function(t){t<this.opt.distance?this.container.removeClass("pull-up").addClass("pull-down"):this.container.removeClass("pull-down").addClass("pull-up"),this.opt.onPull&&this.opt.onPull.call(this,Math.floor(t/this.opt.distance*100)),this.container.trigger("pull")},e.prototype.pullToRefreshDone=function(){this.container.removeClass("refreshing")},e.prototype.attachEvents=function(){var e=this.container;e.addClass("weui-pull-to-refresh"),e.on(t.touchEvents.start,t.proxy(this.touchStart,this)),e.on(t.touchEvents.move,t.proxy(this.touchMove,this)),e.on(t.touchEvents.end,t.proxy(this.touchEnd,this))};var n=function(e){t(e).removeClass("refreshing")};t.fn.pullToRefresh=function(n){return this.each(function(){var i=t(this),a=i.data("ptr");a||i.data("ptr",a=new e(this,n)),"string"==typeof n&&a[n].call(a)})},t.fn.pullToRefreshDone=function(){return this.each(function(){n(this)})}}($),function(t){"use strict";var e=function(e){var n,i=e[0].tagName.toUpperCase();n="BODY"===i||"HTML"===i?e.scrollTop()||t(window).scrollTop():e.scrollTop();var a=e.scrollHeight()-(t(window).height()+n);return console.log(a),a},n=function(e,n){this.container=t(e),this.container.data("infinite",this),this.distance=n||50,this.attachEvents()};n.prototype.scroll=function(){this.container;this._check()},n.prototype.attachEvents=function(e){var n=this.container;("BODY"===n[0].tagName.toUpperCase()?t(document):n)[e?"off":"on"]("scroll",t.proxy(this.scroll,this))},n.prototype.detachEvents=function(t){this.attachEvents(!0)},n.prototype._check=function(){var t=e(this.container);Math.abs(t)<=this.distance&&this.container.trigger("infinite")};t.fn.infinite=function(t){return this.each(function(){new n(this,t)})},t.fn.destroyInfinite=function(){return this.each(function(){var e=t(this).data("infinite");e&&e.detachEvents&&e.detachEvents()})}}($),function(t){"use strict";var e="weui-bar__item--on",n=function(n){var i=t(n);if(!i.hasClass(e)){var a=i.attr("href");/^#/.test(a)&&(i.parent().find("."+e).removeClass(e),i.addClass(e),i.parents(".weui-tab").find(".weui-tab__bd").find(".weui-tab__bd-item--active").removeClass("weui-tab__bd-item--active"),t(a).addClass("weui-tab__bd-item--active"))}};t.showTab=n,t(document).on("click",".weui-navbar__item, .weui-tabbar__item",function(i){var a=t(i.currentTarget),o=a.attr("href");a.hasClass(e)||/^#/.test(o)&&(i.preventDefault(),n(a))})}($),function(t){"use strict";t(document).on("click touchstart",".weui-search-bar__label",function(e){t(e.target).parents(".weui-search-bar").addClass("weui-search-bar_focusing").find("input").focus()}).on("click",".weui-search-bar__cancel-btn",function(e){t(e.target).parents(".weui-search-bar").removeClass("weui-search-bar_focusing").find(".weui-search-bar__input").val("").blur()}).on("click",".weui-icon-clear",function(e){t(e.target).parents(".weui-search-bar").find(".weui-search-bar__input").val("").focus()})}($),function(t){"use strict";var e={},n=navigator.userAgent,i=n.match(/(Android);?[\s\/]+([\d.]+)?/),a=n.match(/(iPad).*OS\s([\d_]+)/),o=n.match(/(iPod)(.*OS\s([\d_]+))?/),r=!a&&n.match(/(iPhone\sOS)\s([\d_]+)/);if(e.ios=e.android=e.iphone=e.ipad=e.androidChrome=!1,i&&(e.os="android",e.osVersion=i[2],e.android=!0,e.androidChrome=n.toLowerCase().indexOf("chrome")>=0),(a||r||o)&&(e.os="ios",e.ios=!0),r&&!o&&(e.osVersion=r[2].replace(/_/g,"."),e.iphone=!0),a&&(e.osVersion=a[2].replace(/_/g,"."),e.ipad=!0),o&&(e.osVersion=o[3]?o[3].replace(/_/g,"."):null,e.iphone=!0),e.ios&&e.osVersion&&n.indexOf("Version/")>=0&&"10"===e.osVersion.split(".")[0]&&(e.osVersion=n.toLowerCase().split("version/")[1].split(" ")[0]),e.webView=(r||a||o)&&n.match(/.*AppleWebKit(?!.*Safari)/i),e.os&&"ios"===e.os){var s=e.osVersion.split(".");e.minimalUi=!e.webView&&(o||r)&&(1*s[0]==7?1*s[1]>=1:1*s[0]>7)&&t('meta[name="viewport"]').length>0&&t('meta[name="viewport"]').attr("content").indexOf("minimal-ui")>=0}var c=t(window).width(),l=t(window).height();e.statusBar=!1,e.webView&&c*l==screen.width*screen.height?e.statusBar=!0:e.statusBar=!1;var p=[];if(e.pixelRatio=window.devicePixelRatio||1,p.push("pixel-ratio-"+Math.floor(e.pixelRatio)),e.pixelRatio>=2&&p.push("retina"),e.os&&(p.push(e.os,e.os+"-"+e.osVersion.split(".")[0],e.os+"-"+e.osVersion.replace(/\./g,"-")),"ios"===e.os))for(var u=parseInt(e.osVersion.split(".")[0],10)-1;u>=6;u--)p.push("ios-gt-"+u);e.statusBar?p.push("with-statusbar-overlay"):t("html").removeClass("with-statusbar-overlay"),p.length>0&&t("html").addClass(p.join(" ")),t.device=e}($),function(t){"use strict";var e=function(e){function n(){var e=!1;return c.params.convertToPopover||c.params.onlyInPopover?(!c.inline&&c.params.input&&(c.params.onlyInPopover?e=!0:t.device.ios?e=!!t.device.ipad:t(window).width()>=768&&(e=!0)),e):e}function i(){return!!(c.opened&&c.container&&c.container.length>0&&c.container.parents(".popover").length>0)}function a(){if(c.opened)for(var t=0;t<c.cols.length;t++)c.cols[t].divider||(c.cols[t].calcSize(),c.cols[t].setValue(c.cols[t].value,0,!1))}function o(t){if(t.preventDefault(),!c.opened&&(c.open(),c.params.scrollToInput&&!n())){var e=c.input.parents(".content");if(0===e.length)return;var i,a=parseInt(e.css("padding-top"),10),o=parseInt(e.css("padding-bottom"),10),r=e[0].offsetHeight-a-c.container.height(),s=e[0].scrollHeight-a-c.container.height(),l=c.input.offset().top-a+c.input[0].offsetHeight;if(l>r){var p=e.scrollTop()+l-r;p+r>s&&(i=p+r-s+o,r===s&&(i=c.container.height()),e.css({"padding-bottom":i+"px"})),e.scrollTop(p,300)}}}function r(e){i()||(c.input&&c.input.length>0?e.target===c.input[0]||t(e.target).parents(".mypickerouter").length||t(e.target).hasClass("mypickerouter")||0!==t(e.target).parents(".weui-picker-modal").length||c.close():0===t(e.target).parents(".weui-picker-modal").length&&c.close())}function s(){c.opened=!1,c.input&&c.input.length>0&&c.input.parents(".page-content").css({"padding-bottom":""}),c.params.onClose&&c.params.onClose(c),c.container.find(".picker-items-col").each(function(){c.destroyPickerCol(this)})}var c=this,l={updateValuesOnMomentum:!1,updateValuesOnTouchmove:!0,rotateEffect:!1,momentumRatio:7,freeMode:!1,scrollToInput:!0,inputReadOnly:!0,toolbar:!0,toolbarCloseText:"完成",title:"请选择",toolbarTemplate:'<div class="toolbar">          <div class="toolbar-inner">          <a href="javascript:;" class="picker-button close-picker" btntype="cancel" style="left: 0;right: auto">取消</a>          <a href="javascript:;" class="picker-button close-picker" btntype="pick" style="right: 0">{{closeText}}</a>          <h1 class="title">{{title}}</h1>          </div>          </div>'};e=e||{};for(var p in l)void 0===e[p]&&(e[p]=l[p]);c.params=e,c.cols=[],c.initialized=!1,c.inline=!!c.params.container;var u=t.device.ios||navigator.userAgent.toLowerCase().indexOf("safari")>=0&&navigator.userAgent.toLowerCase().indexOf("chrome")<0&&!t.device.android;return c.setValue=function(t,e){for(var n=0,i=0;i<c.cols.length;i++)c.cols[i]&&!c.cols[i].divider&&t&&t[n]&&(c.cols[i].setValue(t[n],e),n++)},c.updateValue=function(){for(var e=[],n=[],i=0;i<c.cols.length;i++)c.cols[i].divider||(e.push(c.cols[i].value),n.push(c.cols[i].displayValue));e.indexOf(void 0)>=0||(c.value=e,c.displayValue=n,c.params.onChange&&c.params.onChange(c,c.value,c.displayValue),0!=c.params.inputChange&&c.input&&c.input.length>0&&(t(c.input).val(c.params.formatValue?c.params.formatValue(c,c.value,c.displayValue):c.value.join(" ")),t(c.input).trigger("change")))},c.initPickerCol=function(e,n){function i(){w=t.requestAnimationFrame(function(){d.updateItems(void 0,void 0,0),i()})}function a(e){if(!k&&!y){e.preventDefault(),y=!0;var n=t.getTouchPosition(e);x=b=n.y,C=(new Date).getTime(),H=!0,M=D=t.getTranslate(d.wrapper[0],"y")}}function o(e){if(y){e.preventDefault(),H=!1;var n=t.getTouchPosition(e);b=n.y,k||(t.cancelAnimationFrame(w),k=!0,M=D=t.getTranslate(d.wrapper[0],"y"),d.wrapper.transition(0)),e.preventDefault(),_=void 0,(D=M+(b-x))<v&&(D=v-Math.pow(v-D,.8),_="min"),D>g&&(D=g+Math.pow(D-g,.8),_="max"),d.wrapper.transform("translate3d(0,"+D+"px,0)"),d.updateItems(void 0,D,0,c.params.updateValuesOnTouchmove),O=D-E||D,P=(new Date).getTime(),E=D}}function r(e){if(y&&k){y=k=!1,d.wrapper.transition(""),_&&("min"===_?d.wrapper.transform("translate3d(0,"+v+"px,0)"):d.wrapper.transform("translate3d(0,"+g+"px,0)"));var n;(T=(new Date).getTime())-C>300?n=D:(Math.abs(O/(T-P)),n=D+O*c.params.momentumRatio),n=Math.max(Math.min(n,g),v);var a=-Math.floor((n-g)/f);c.params.freeMode||(n=-a*f+g),d.wrapper.transform("translate3d(0,"+parseInt(n,10)+"px,0)"),d.updateItems(a,n,"",!0),c.params.updateValuesOnMomentum&&(i(),d.wrapper.transitionEnd(function(){t.cancelAnimationFrame(w)})),setTimeout(function(){H=!0},100)}else y=k=!1}function s(e){if(H){t.cancelAnimationFrame(w);var n=t(this).attr("data-picker-value");d.setValue(n)}}var l=t(e),p=l.index(),d=c.cols[p];if(!d.divider){d.container=l,d.wrapper=d.container.find(".picker-items-col-wrapper"),d.items=d.wrapper.find(".picker-item");var h,f,m,v,g;d.replaceValues=function(t,e){d.destroyEvents(),d.values=t,d.displayValues=e;var n=c.columnHTML(d,!0);d.wrapper.html(n),d.items=d.wrapper.find(".picker-item"),d.calcSize(),d.setValue(d.values[0]||"",0,!0),d.initEvents()},d.calcSize=function(){if(d.values.length){c.params.rotateEffect&&(d.container.removeClass("picker-items-col-absolute"),d.width||d.container.css({width:""}));var e,n;e=0,n=d.container[0].offsetHeight,h=d.wrapper[0].offsetHeight,f=d.items[0].offsetHeight,m=f*d.items.length,v=n/2-m+f/2,g=n/2-f/2,d.width&&(e=d.width,parseInt(e,10)===e&&(e+="px"),d.container.css({width:e})),c.params.rotateEffect&&(d.width||(d.items.each(function(){var n=t(this);n.css({width:"auto"}),e=Math.max(e,n[0].offsetWidth),n.css({width:""})}),d.container.css({width:e+2+"px"})),d.container.addClass("picker-items-col-absolute"))}},d.calcSize(),d.wrapper.transform("translate3d(0,"+g+"px,0)").transition(0);var w;d.setValue=function(e,n,a){void 0===n&&(n="");var o=d.wrapper.find('.picker-item[data-picker-value="'+e+'"]').index();if(void 0!==o&&-1!==o){var r=-o*f+g;d.wrapper.transition(n),d.wrapper.transform("translate3d(0,"+r+"px,0)"),c.params.updateValuesOnMomentum&&d.activeIndex&&d.activeIndex!==o&&(t.cancelAnimationFrame(w),d.wrapper.transitionEnd(function(){t.cancelAnimationFrame(w)}),i()),d.updateItems(o,r,n,a)}else d.value=d.displayValue=e},d.updateItems=function(e,n,i,a){void 0===n&&(n=t.getTranslate(d.wrapper[0],"y")),void 0===e&&(e=-Math.round((n-g)/f)),e<0&&(e=0),e>=d.items.length&&(e=d.items.length-1);var o=d.activeIndex;d.activeIndex=e,d.wrapper.find(".picker-selected").removeClass("picker-selected"),c.params.rotateEffect&&d.items.transition(i);var r=d.items.eq(e).addClass("picker-selected").transform("");if((a||void 0===a)&&(d.value=r.attr("data-picker-value"),d.displayValue=d.displayValues?d.displayValues[e]:d.value,void 0!=o&&o!==e&&(d.onChange&&d.onChange(c,d.value,d.displayValue),c.updateValue())),c.params.rotateEffect){Math.floor((n-g)/f);d.items.each(function(){var e=t(this),i=(e.index()*f-(g-n))/f,a=Math.ceil(d.height/f/2)+1,o=-18*i;o>180&&(o=180),o<-180&&(o=-180),Math.abs(i)>a?e.addClass("picker-item-far"):e.removeClass("picker-item-far"),e.transform("translate3d(0, "+(-n+g)+"px, "+(u?-110:0)+"px) rotateX("+o+"deg)")})}},n&&d.updateItems(0,g,0);var y,k,x,b,C,T,M,_,D,E,O,P,H=!0;d.initEvents=function(e){var n=e?"off":"on";d.container[n](t.touchEvents.start,a),d.container[n](t.touchEvents.move,o),d.container[n](t.touchEvents.end,r),d.items[n]("click",s)},d.destroyEvents=function(){d.initEvents(!0)},d.container[0].f7DestroyPickerCol=function(){d.destroyEvents()},d.initEvents()}},c.destroyPickerCol=function(e){"f7DestroyPickerCol"in(e=t(e))[0]&&e[0].f7DestroyPickerCol()},t(window).on("resize",a),c.columnHTML=function(t,e){var n="",i="";if(t.divider)i+='<div class="picker-items-col picker-items-col-divider '+(t.textAlign?"picker-items-col-"+t.textAlign:"")+" "+(t.cssClass||"")+'">'+t.content+"</div>";else{for(var a=0;a<t.values.length;a++)n+='<div class="picker-item" data-picker-value="'+t.values[a]+'">'+(t.displayValues?t.displayValues[a]:t.values[a])+"</div>";i+='<div class="picker-items-col '+(t.textAlign?"picker-items-col-"+t.textAlign:"")+" "+(t.cssClass||"")+'"><div class="picker-items-col-wrapper">'+n+"</div></div>"}return e?n:i},c.layout=function(){var t,e="";c.cols=[];var n="";for(t=0;t<c.params.cols.length;t++){var i=c.params.cols[t];n+=c.columnHTML(c.params.cols[t]),c.cols.push(i)}e='<div class="'+("weui-picker-modal picker-columns "+(c.params.cssClass||"")+(c.params.rotateEffect?" picker-3d":"")+(1===c.params.cols.length?" picker-columns-single":""))+'">'+(c.params.toolbar?c.params.toolbarTemplate.replace(/{{closeText}}/g,c.params.toolbarCloseText).replace(/{{title}}/g,c.params.title).replace(/{{btnType}}/g,c.params.toolbarBtnType):"")+'<div class="picker-modal-inner picker-items">'+n+'<div class="picker-center-highlight"></div></div></div>',c.pickerHTML=e},c.params.input&&(c.input=t(c.params.input),c.input.length>0&&(c.params.inputReadOnly&&c.input.prop("readOnly",!0),c.inline||c.input.on("click",o),c.params.inputReadOnly&&c.input.on("focus mousedown",function(t){t.preventDefault()}))),c.inline||t("html").on("click",r),c.opened=!1,c.open=function(){var e=n();c.opened||(c.layout(),e?(c.pickerHTML='<div class="popover popover-picker-columns"><div class="popover-inner">'+c.pickerHTML+"</div></div>",c.popover=t.popover(c.pickerHTML,c.params.input,!0),c.container=t(c.popover).find(".weui-picker-modal"),t(c.popover).on("close",function(){s()})):c.inline?(c.container=t(c.pickerHTML),c.container.addClass("picker-modal-inline"),t(c.params.container).append(c.container)):(c.container=t(t.openPicker(c.pickerHTML)),t(c.container).on("close",function(){s()}).on("pick",function(){0==c.params.inputChange&&t(c.input).val(c.params.formatValue?c.params.formatValue(c,c.value,c.displayValue):c.value.join(" ")),c.params.onPick&&c.params.onPick(c,c.value,c.displayValue)})),c.container[0].f7Picker=c,c.container.find(".picker-items-col").each(function(){var t=!0;(!c.initialized&&c.params.value||c.initialized&&c.value)&&(t=!1),c.initPickerCol(this,t)}),c.initialized?c.value&&c.setValue(c.value,0):c.params.value&&(c.value=c.params.value,c.setValue(c.params.value,0))),c.opened=!0,c.initialized=!0,c.params.onOpen&&c.params.onOpen(c)},c.close=function(e){if(c.opened&&!c.inline)return i()?void t.closePicker(c.popover):void t.closePicker(c.container)},c.destroy=function(){c.close(),c.params.input&&c.input.length>0&&(c.input.off("click focus",o),t(c.input).data("picker",null)),t("html").off("click",r),t(window).off("resize",a)},c.inline&&c.open(),c};t(document).on("click",".close-picker",function(){var e=t(this).parents(".weui-picker-modal.weui-picker-modal-visible");e.length>0&&t.closePicker(e,null,t(this).attr("btntype"))}),t.openPicker=function(e,n,i){"function"==typeof n&&(i=n,n=void 0);var a=t("<div class='weui-picker-container "+(n||"")+"'></div>").appendTo(document.body);a.show(),a.addClass("weui-picker-container-visible");var o=t(e).appendTo(a);return o.width(),o.addClass("weui-picker-modal-visible"),i&&a.on("close",i),o},t.updatePicker=function(e){var n=t(".weui-picker-container-visible");if(!n[0])return!1;n.html("");var i=t(e).appendTo(n);return i.addClass("weui-picker-modal-visible"),i},t.closePicker=function(e,n,i){"function"==typeof e&&(n=e);var a=e||t(".weui-picker-modal-visible");i&&("pick"==i?a.trigger("pick"):a.trigger("cancel")),a.removeClass("weui-picker-modal-visible").transitionEnd(function(){t(this).parent().remove(),n&&n()}).trigger("close")},t.fn.picker=function(n){var i=arguments;return this.each(function(){if(this){var a=t(this),o=a.data("picker");if(!o){n=t.extend({input:this},n||{});var r=a.val();void 0===n.value&&""!==r&&(n.value=n.cols&&n.cols.length>1?r.split(" "):[r]);var s=t.extend({input:this},n);o=new e(s),a.data("picker",o)}"string"==typeof n&&o[n].apply(o,Array.prototype.slice.call(i,1))}})}}($),function(t){"use strict";var e,n=[],i=function(e,i){this.config=i,this.data={values:"",titles:"",origins:[],length:0},this.$input=t(e),this.$input.prop("readOnly",!0),this.initConfig(),i=this.config,this.$input.click(t.proxy(this.open,this)),n.push(this)};i.prototype.initConfig=function(){this.config=t.extend({},e,this.config);var n=this.config;n.items&&n.items.length&&(n.items=n.items.map(function(t,e){return"string"==typeof t?{title:t,value:t}:t}),this.tpl=t.t7.compile("<div class='weui-picker-modal weui-select-modal'>"+n.toolbarTemplate+(n.multi?n.checkboxTemplate:n.radioTemplate)+"</div>"),void 0!==n.input&&this.$input.val(n.input),this.parseInitValue(),this._init=!0)},i.prototype.updateInputValue=function(t,e){var n,i;this.config.multi?(n=t.join(this.config.split),i=e.join(this.config.split)):(n=t[0],i=e[0]);var a=[];this.config.items.forEach(function(e){t.each(function(t,n){e.value==n&&a.push(e)})}),this.$input.val(i).data("values",n),this.$input.attr("value",i).attr("data-values",n);var o={values:n,titles:i,valuesArray:t,titlesArray:e,origins:a,length:a.length};this.data=o,this.$input.trigger("change",o),this.config.onChange&&this.config.onChange.call(this,o)},i.prototype.parseInitValue=function(){var t=this.$input.val(),e=this.config.items;if(this._init||void 0!==t&&null!=t&&""!==t)for(var n=this.config.multi?t.split(this.config.split):[t],i=0;i<e.length;i++){e[i].checked=!1;for(var a=0;a<n.length;a++)e[i].title===n[a]&&(e[i].checked=!0)}},i.prototype._bind=function(e){var n=this,i=this.config;e.on("change",function(t){i.autoClose&&!i.multi&&e.find(".do-select").trigger("click")}).on("click",".close-select",function(){var e=t(this).parents(".weui-picker-modal.weui-picker-modal-visible");t.closePicker(e,function(){n.onClose()})}).on("click",".do-select",function(){var i=e.find("input:checked"),a=i.map(function(){return t(this).val()}),o=i.map(function(){return t(this).data("title")});n.updateInputValue(a,o);var r=t(this).parents(".weui-picker-modal.weui-picker-modal-visible");n.doselect(r)})},i.prototype.update=function(e){this.config=t.extend({},this.config,e),this.initConfig(),this._open&&this._bind(t.updatePicker(this.getHTML()))},i.prototype.open=function(e,i){if(!this._open){for(var a=0;a<n.length;a++){var o=n[a];if(o!==this&&(o._open&&!o.doselect()))return!1}this.parseInitValue();var r=this.config,s=this.dialog=t.openPicker(this.getHTML());this._bind(s),this._open=!0,r.onOpen&&r.onOpen(this)}},i.prototype.doselect=function(e,n,i){if("function"==typeof e&&(i=n,n=e,e=null),!this._open)return!1;var a=this,o=this.config.beforeClose;if(!i){if(o&&"function"==typeof o&&!1===o.call(this,this.data.values,this.data.titles))return!1;if(this.config.multi){if(void 0!==this.config.min&&this.data.length<this.config.min)return t.toast("请至少选择"+this.config.min+"个","text"),!1;if(void 0!==this.config.max&&this.data.length>this.config.max)return t.toast("最多只能选择"+this.config.max+"个","text"),!1}}return e?t.closePicker(e,function(){a.onClose(),n&&n()}):t.closePicker(function(){a.onClose(),n&&n()}),!0},i.prototype.onClose=function(){this._open=!1,this.config.onClose&&this.config.onClose(this)},i.prototype.getHTML=function(t){var e=this.config;return this.tpl({items:e.items,title:e.title,closeText:e.closeText})},t.fn.select=function(e,n){return this.each(function(){var a=t(this);a.data("weui-select")||a.data("weui-select",new i(this,e));var o=a.data("weui-select");return"string"==typeof e&&o[e].call(o,n),o})},e=t.fn.select.prototype.defaults={items:[],input:void 0,title:"请选择",multi:!1,closeText:"确定",autoClose:!0,onChange:void 0,beforeClose:void 0,onClose:void 0,onOpen:void 0,split:",",min:void 0,max:void 0,toolbarTemplate:'<div class="toolbar">      <div class="toolbar-inner">      <a href="javascript:;" class="picker-button close-select" style="left: 0">取消</a>      <h1 class="title">{{title}}</h1>      <a href="javascript:;" class="picker-button do-select" style="right: 0">{{closeText}}</a>      </div>      </div>',radioTemplate:'<div class="weui-cells weui-cells_radio">        {{#items}}        <label class="weui-cell weui-check_label" for="weui-select-id-{{this.title}}">          <div class="weui-cell__bd weui-cell_primary">            <p>{{this.title}}</p>          </div>          <div class="weui-cell__ft">            <input type="radio" class="weui-check" name="weui-select" id="weui-select-id-{{this.title}}" value="{{this.value}}" {{#if this.checked}}checked="checked"{{/if}} data-title="{{this.title}}">            <span class="weui-icon-checked"></span>          </div>        </label>        {{/items}}      </div>',checkboxTemplate:'<div class="weui-cells weui-cells_checkbox">        {{#items}}        <label class="weui-cell weui-check_label" for="weui-select-id-{{this.title}}">          <div class="weui-cell__bd weui-cell_primary">            <p>{{this.title}}</p>          </div>          <div class="weui-cell__ft">            <input type="checkbox" class="weui-check" name="weui-select" id="weui-select-id-{{this.title}}" value="{{this.value}}" {{#if this.checked}}checked="checked"{{/if}} data-title="{{this.title}}" >            <span class="weui-icon-checked"></span>          </div>        </label>        {{/items}}      </div>'}}($),function(t){"use strict";var e,n=!1,i=function(t,e){var t=new Date(t),e=new Date(e);return t.getFullYear()===e.getFullYear()&&t.getMonth()===e.getMonth()&&t.getDate()===e.getDate()},a=function(a){function o(){var e=!1;return u.params.convertToPopover||u.params.onlyInPopover?(!u.inline&&u.params.input&&(u.params.onlyInPopover?e=!0:t.device.ios?e=!!t.device.ipad:t(window).width()>=768&&(e=!0)),e):e}function r(){return!!(u.opened&&u.container&&u.container.length>0&&u.container.parents(".popover").length>0)}function s(t){var e=(t=new Date(t)).getFullYear(),n=t.getMonth(),i=n+1,a=t.getDate(),o=t.getDay();return u.params.dateFormat.replace(/yyyy/g,e).replace(/yy/g,(e+"").substring(2)).replace(/mm/g,i<10?"0"+i:i).replace(/m/g,i).replace(/MM/g,u.params.monthNames[n]).replace(/M/g,u.params.monthNamesShort[n]).replace(/dd/g,a<10?"0"+a:a).replace(/d/g,a).replace(/DD/g,u.params.dayNames[o]).replace(/D/g,u.params.dayNamesShort[o])}function c(t){if(t.preventDefault(),!u.opened&&(u.open(),u.params.scrollToInput&&!o())){var e=u.input.parents(".page-content");if(0===e.length)return;var n,i=parseInt(e.css("padding-top"),10),a=parseInt(e.css("padding-bottom"),10),r=e[0].offsetHeight-i-u.container.height(),s=e[0].scrollHeight-i-u.container.height(),c=u.input.offset().top-i+u.input[0].offsetHeight;if(c>r){var l=e.scrollTop()+c-r;l+r>s&&(n=l+r-s+a,r===s&&(n=u.container.height()),e.css({"padding-bottom":n+"px"})),e.scrollTop(l,300)}}}function l(e){r()||(u.input&&u.input.length>0?e.target===u.input[0]||t(e.target).parents(".btncalender").length||t(e.target).hasClass("btncalender")||0!==t(e.target).parents(".weui-picker-modal").length||u.close():0===t(e.target).parents(".weui-picker-modal").length&&u.close())}function p(){u.opened=!1,u.input&&u.input.length>0&&u.input.parents(".page-content").css({"padding-bottom":""}),u.params.onClose&&u.params.onClose(u),u.destroyCalendarEvents()}var u=this;a=a||{};for(var d in e)void 0===a[d]&&(a[d]=e[d]);u.params=a,u.initialized=!1,u.inline=!!u.params.container,u.isH="horizontal"===u.params.direction;var h=u.isH&&n?-1:1;return u.animating=!1,u.addValue=function(t){if(u.params.multiple){u.value||(u.value=[]);for(var e,n=0;n<u.value.length;n++)i(t,u.value[n])&&(e=n);void 0===e?u.value.push(t):u.value.splice(e,1),u.updateValue()}else u.value=[t],u.updateValue()},u.setValue=function(t){var e=new Date(t[0]);u.setYearMonth(e.getFullYear(),e.getMonth()),u.addValue(+e)},u.updateValue=function(){u.wrapper.find(".picker-calendar-day-selected").removeClass("picker-calendar-day-selected");var e,n;for(e=0;e<u.value.length;e++){var i=new Date(u.value[e]);u.wrapper.find('.picker-calendar-day[data-date="'+i.getFullYear()+"-"+i.getMonth()+"-"+i.getDate()+'"]').addClass("picker-calendar-day-selected")}if(u.params.onChange&&u.params.onChange(u,u.value.map(s),u.value.map(function(t){return+new Date("string"==typeof t?t.split(/\D/).filter(function(t){return!!t}).join("-"):t)})),u.input&&u.input.length>0){if(u.params.formatValue)n=u.params.formatValue(u,u.value);else{for(n=[],e=0;e<u.value.length;e++)n.push(s(u.value[e]));n=n.join(", ")}t(u.input).val(n),t(u.input).trigger("change")}},u.initCalendarEvents=function(){function e(e){if(!s&&!r){r=!0;var n=t.getTouchPosition(e);c=d=n.x,l=d=n.y,f=(new Date).getTime(),k=0,C=!0,b=void 0,v=g=u.monthsTranslate}}function i(e){if(r){var n=t.getTouchPosition(e);p=n.x,d=n.y,void 0===b&&(b=!!(b||Math.abs(d-l)>Math.abs(p-c))),u.isH&&b?r=!1:(e.preventDefault(),u.animating?r=!1:(C=!1,s||(s=!0,w=u.wrapper[0].offsetWidth,y=u.wrapper[0].offsetHeight,u.wrapper.transition(0)),e.preventDefault(),x=u.isH?p-c:d-l,k=x/(u.isH?w:y),g=100*(u.monthsTranslate*h+k),u.wrapper.transform("translate3d("+(u.isH?g:0)+"%, "+(u.isH?0:g)+"%, 0)")))}}function a(t){r&&s?(r=s=!1,(m=(new Date).getTime())-f<300?Math.abs(x)<10?u.resetMonth():x>=10?n?u.nextMonth():u.prevMonth():n?u.prevMonth():u.nextMonth():k<=-.5?n?u.prevMonth():u.nextMonth():k>=.5?n?u.nextMonth():u.prevMonth():u.resetMonth(),setTimeout(function(){C=!0},100)):r=s=!1}function o(e){if(C){var n=t(e.target).parents(".picker-calendar-day");if(0===n.length&&t(e.target).hasClass("picker-calendar-day")&&(n=t(e.target)),0!==n.length&&!(u.params.onBeforeDayClick&&0==u.params.onBeforeDayClick(u,n[0])||n.hasClass("picker-calendar-day-disabled"))){n.hasClass("picker-calendar-day-next")&&u.nextMonth(),n.hasClass("picker-calendar-day-prev")&&u.prevMonth();var i=n.attr("data-year"),a=n.attr("data-month"),o=n.attr("data-day");u.params.onDayClick&&u.params.onDayClick(u,n[0],i,a,o),u.addValue(new Date(i,a,o).getTime()),u.params.closeOnSelect&&!u.params.multiple&&u.close()}}}var r,s,c,l,p,d,f,m,v,g,w,y,k,x,b,C=!0;u.container.find(".picker-calendar-prev-month").on("click",u.prevMonth),u.container.find(".picker-calendar-next-month").on("click",u.nextMonth),u.container.find(".picker-calendar-prev-year").on("click",u.prevYear),u.container.find(".picker-calendar-next-year").on("click",u.nextYear),u.wrapper.on("click",o),u.params.touchMove&&(u.wrapper.on(t.touchEvents.start,e),u.wrapper.on(t.touchEvents.move,i),u.wrapper.on(t.touchEvents.end,a)),u.container[0].f7DestroyCalendarEvents=function(){u.container.find(".picker-calendar-prev-month").off("click",u.prevMonth),u.container.find(".picker-calendar-next-month").off("click",u.nextMonth),u.container.find(".picker-calendar-prev-year").off("click",u.prevYear),u.container.find(".picker-calendar-next-year").off("click",u.nextYear),u.wrapper.off("click",o),u.params.touchMove&&(u.wrapper.off(t.touchEvents.start,e),u.wrapper.off(t.touchEvents.move,i),u.wrapper.off(t.touchEvents.end,a))}},u.destroyCalendarEvents=function(t){"f7DestroyCalendarEvents"in u.container[0]&&u.container[0].f7DestroyCalendarEvents()},u.daysInMonth=function(t){var e=new Date(t);return new Date(e.getFullYear(),e.getMonth()+1,0).getDate()},u.monthHTML=function(t,e){var n=(t=new Date(t)).getFullYear(),i=t.getMonth();t.getDate();"next"===e&&(t=11===i?new Date(n+1,0):new Date(n,i+1,1)),"prev"===e&&(t=0===i?new Date(n-1,11):new Date(n,i-1,1)),"next"!==e&&"prev"!==e||(i=t.getMonth(),n=t.getFullYear());var a=u.daysInMonth(new Date(t.getFullYear(),t.getMonth()).getTime()-864e6),o=u.daysInMonth(t),r=new Date(t.getFullYear(),t.getMonth()).getDay();0===r&&(r=7);var s,c,l,p=[],d="",h=u.params.firstDay-1+0,f=(new Date).setHours(0,0,0,0),m=u.params.minDate?new Date(u.params.minDate).getTime():null,v=u.params.maxDate?new Date(u.params.maxDate).getTime():null;if(u.value&&u.value.length)for(c=0;c<u.value.length;c++)p.push(new Date(u.value[c]).setHours(0,0,0,0));for(c=1;c<=6;c++){var g="";for(l=1;l<=7;l++){var w=l,y=++h-r,k="";y<0?(y=a+y+1,k+=" picker-calendar-day-prev",s=new Date(i-1<0?n-1:n,i-1<0?11:i-1,y).getTime()):(y+=1)>o?(y-=o,k+=" picker-calendar-day-next",s=new Date(i+1>11?n+1:n,i+1>11?0:i+1,y).getTime()):s=new Date(n,i,y).getTime(),s===f&&(k+=" picker-calendar-day-today"),p.indexOf(s)>=0&&(k+=" picker-calendar-day-selected"),u.params.weekendDays.indexOf(w-1)>=0&&(k+=" picker-calendar-day-weekend"),(m&&s<m||v&&s>v)&&(k+=" picker-calendar-day-disabled");var x=(s=new Date(s)).getFullYear(),b=s.getMonth();g+='<div class="picker-calendar-day'+k+'" data-year="'+x+'" data-month="'+b+'" data-day="'+y+'" data-date="'+x+"-"+b+"-"+y+'" data-weekday="'+s.getDay()+'"><span>'+y+"</span></div>"}d+='<div class="picker-calendar-row">'+g+"</div>"}return d='<div class="picker-calendar-month" data-year="'+n+'" data-month="'+i+'">'+d+"</div>"},u.animating=!1,u.updateCurrentMonthYear=function(t){void 0===t?(u.currentMonth=parseInt(u.months.eq(1).attr("data-month"),10),u.currentYear=parseInt(u.months.eq(1).attr("data-year"),10)):(u.currentMonth=parseInt(u.months.eq("next"===t?u.months.length-1:0).attr("data-month"),10),u.currentYear=parseInt(u.months.eq("next"===t?u.months.length-1:0).attr("data-year"),10)),u.container.find(".current-month-value").text(u.params.monthNames[u.currentMonth]),u.container.find(".current-year-value").text(u.currentYear)},u.onMonthChangeStart=function(t){u.updateCurrentMonthYear(t),u.months.removeClass("picker-calendar-month-current picker-calendar-month-prev picker-calendar-month-next");var e="next"===t?u.months.length-1:0;u.months.eq(e).addClass("picker-calendar-month-current"),u.months.eq("next"===t?e-1:e+1).addClass("next"===t?"picker-calendar-month-prev":"picker-calendar-month-next"),u.params.onMonthYearChangeStart&&u.params.onMonthYearChangeStart(u,u.currentYear,u.currentMonth)},u.onMonthChangeEnd=function(t,e){u.animating=!1;var n,i,a;u.wrapper.find(".picker-calendar-month:not(.picker-calendar-month-prev):not(.picker-calendar-month-current):not(.picker-calendar-month-next)").remove(),void 0===t&&(t="next",e=!0),e?(u.wrapper.find(".picker-calendar-month-next, .picker-calendar-month-prev").remove(),i=u.monthHTML(new Date(u.currentYear,u.currentMonth),"prev"),n=u.monthHTML(new Date(u.currentYear,u.currentMonth),"next")):a=u.monthHTML(new Date(u.currentYear,u.currentMonth),t),("next"===t||e)&&u.wrapper.append(a||n),("prev"===t||e)&&u.wrapper.prepend(a||i),u.months=u.wrapper.find(".picker-calendar-month"),u.setMonthsTranslate(u.monthsTranslate),u.params.onMonthAdd&&u.params.onMonthAdd(u,"next"===t?u.months.eq(u.months.length-1)[0]:u.months.eq(0)[0]),u.params.onMonthYearChangeEnd&&u.params.onMonthYearChangeEnd(u,u.currentYear,u.currentMonth)},u.setMonthsTranslate=function(t){t=t||u.monthsTranslate||0,void 0===u.monthsTranslate&&(u.monthsTranslate=t),u.months.removeClass("picker-calendar-month-current picker-calendar-month-prev picker-calendar-month-next");var e=100*-(t+1)*h,n=100*-t*h,i=100*-(t-1)*h;u.months.eq(0).transform("translate3d("+(u.isH?e:0)+"%, "+(u.isH?0:e)+"%, 0)").addClass("picker-calendar-month-prev"),u.months.eq(1).transform("translate3d("+(u.isH?n:0)+"%, "+(u.isH?0:n)+"%, 0)").addClass("picker-calendar-month-current"),u.months.eq(2).transform("translate3d("+(u.isH?i:0)+"%, "+(u.isH?0:i)+"%, 0)").addClass("picker-calendar-month-next")},u.nextMonth=function(e){void 0!==e&&"object"!=typeof e||(e="",u.params.animate||(e=0));var n=parseInt(u.months.eq(u.months.length-1).attr("data-month"),10),i=parseInt(u.months.eq(u.months.length-1).attr("data-year"),10),a=new Date(i,n).getTime(),o=!u.animating;if(u.params.maxDate&&a>new Date(u.params.maxDate).getTime())return u.resetMonth();if(u.monthsTranslate--,n===u.currentMonth){var r=100*-u.monthsTranslate*h,s=t(u.monthHTML(a,"next")).transform("translate3d("+(u.isH?r:0)+"%, "+(u.isH?0:r)+"%, 0)").addClass("picker-calendar-month-next");u.wrapper.append(s[0]),u.months=u.wrapper.find(".picker-calendar-month"),u.params.onMonthAdd&&u.params.onMonthAdd(u,u.months.eq(u.months.length-1)[0])}u.animating=!0,u.onMonthChangeStart("next");var c=100*u.monthsTranslate*h;u.wrapper.transition(e).transform("translate3d("+(u.isH?c:0)+"%, "+(u.isH?0:c)+"%, 0)"),o&&u.wrapper.transitionEnd(function(){u.onMonthChangeEnd("next")}),u.params.animate||u.onMonthChangeEnd("next")},u.prevMonth=function(e){void 0!==e&&"object"!=typeof e||(e="",u.params.animate||(e=0));var n=parseInt(u.months.eq(0).attr("data-month"),10),i=parseInt(u.months.eq(0).attr("data-year"),10),a=new Date(i,n+1,-1).getTime(),o=!u.animating;if(u.params.minDate&&a<new Date(u.params.minDate).getTime())return u.resetMonth();if(u.monthsTranslate++,n===u.currentMonth){var r=100*-u.monthsTranslate*h,s=t(u.monthHTML(a,"prev")).transform("translate3d("+(u.isH?r:0)+"%, "+(u.isH?0:r)+"%, 0)").addClass("picker-calendar-month-prev");u.wrapper.prepend(s[0]),u.months=u.wrapper.find(".picker-calendar-month"),u.params.onMonthAdd&&u.params.onMonthAdd(u,u.months.eq(0)[0])}u.animating=!0,u.onMonthChangeStart("prev");var c=100*u.monthsTranslate*h;u.wrapper.transition(e).transform("translate3d("+(u.isH?c:0)+"%, "+(u.isH?0:c)+"%, 0)"),o&&u.wrapper.transitionEnd(function(){u.onMonthChangeEnd("prev")}),u.params.animate||u.onMonthChangeEnd("prev")},u.resetMonth=function(t){void 0===t&&(t="");var e=100*u.monthsTranslate*h;u.wrapper.transition(t).transform("translate3d("+(u.isH?e:0)+"%, "+(u.isH?0:e)+"%, 0)")},u.setYearMonth=function(t,e,n){void 0===t&&(t=u.currentYear),void 0===e&&(e=u.currentMonth),void 0!==n&&"object"!=typeof n||(n="",u.params.animate||(n=0));var i;if(i=t<u.currentYear?new Date(t,e+1,-1).getTime():new Date(t,e).getTime(),u.params.maxDate&&i>new Date(u.params.maxDate).getTime())return!1;if(u.params.minDate&&i<new Date(u.params.minDate).getTime())return!1;var a=new Date(u.currentYear,u.currentMonth).getTime(),o=i>a?"next":"prev",r=u.monthHTML(new Date(t,e));u.monthsTranslate=u.monthsTranslate||0;var s,c,l=u.monthsTranslate,p=!u.animating;i>a?(u.monthsTranslate--,u.animating||u.months.eq(u.months.length-1).remove(),u.wrapper.append(r),u.months=u.wrapper.find(".picker-calendar-month"),s=100*-(l-1)*h,u.months.eq(u.months.length-1).transform("translate3d("+(u.isH?s:0)+"%, "+(u.isH?0:s)+"%, 0)").addClass("picker-calendar-month-next")):(u.monthsTranslate++,u.animating||u.months.eq(0).remove(),u.wrapper.prepend(r),u.months=u.wrapper.find(".picker-calendar-month"),s=100*-(l+1)*h,u.months.eq(0).transform("translate3d("+(u.isH?s:0)+"%, "+(u.isH?0:s)+"%, 0)").addClass("picker-calendar-month-prev")),u.params.onMonthAdd&&u.params.onMonthAdd(u,"next"===o?u.months.eq(u.months.length-1)[0]:u.months.eq(0)[0]),u.animating=!0,u.onMonthChangeStart(o),c=100*u.monthsTranslate*h,u.wrapper.transition(n).transform("translate3d("+(u.isH?c:0)+"%, "+(u.isH?0:c)+"%, 0)"),p&&u.wrapper.transitionEnd(function(){u.onMonthChangeEnd(o,!0)}),u.params.animate||u.onMonthChangeEnd(o)},u.nextYear=function(){u.setYearMonth(u.currentYear+1)},u.prevYear=function(){u.setYearMonth(u.currentYear-1)},u.layout=function(){var t,e="",n="",i=u.value&&u.value.length?u.value[0]:(new Date).setHours(0,0,0,0),a='<div class="picker-calendar-months"><div class="picker-calendar-months-wrapper">'+(u.monthHTML(i,"prev")+u.monthHTML(i)+u.monthHTML(i,"next"))+"</div></div>",o="";if(u.params.weekHeader){for(t=0;t<7;t++){var r=t+u.params.firstDay>6?t-7+u.params.firstDay:t+u.params.firstDay,s=u.params.dayNamesShort[r];o+='<div class="picker-calendar-week-day '+(u.params.weekendDays.indexOf(r)>=0?"picker-calendar-week-day-weekend":"")+'"> '+s+"</div>"}o='<div class="picker-calendar-week-days">'+o+"</div>"}n="weui-picker-calendar "+(u.params.cssClass||""),u.inline||(n="weui-picker-modal "+n);var c=u.params.toolbar?u.params.toolbarTemplate.replace(/{{closeText}}/g,u.params.toolbarCloseText).replace(/{{btnType}}/g,u.params.toolbarBtnType):"";u.params.toolbar&&(c=u.params.toolbarTemplate.replace(/{{closeText}}/g,u.params.toolbarCloseText).replace(/{{btnType}}/g,u.params.toolbarBtnType).replace(/{{monthPicker}}/g,u.params.monthPicker?u.params.monthPickerTemplate:"").replace(/{{yearPicker}}/g,u.params.yearPicker?u.params.yearPickerTemplate:"")),e='<div class="'+n+'">'+c+'<div class="picker-modal-inner">'+o+a+"</div></div>",u.pickerHTML=e},u.params.input&&(u.input=t(u.params.input),u.input.length>0&&(u.params.inputReadOnly&&u.input.prop("readOnly",!0),u.inline||u.input.on("click",c),u.params.inputReadOnly&&u.input.on("focus mousedown",function(t){t.preventDefault()}))),u.inline||t(document).on("click touchend",l),u.opened=!1,u.open=function(){var e=o()&&!1,n=!1;u.opened||(u.value||u.params.value&&(u.value=u.params.value,n=!0),u.layout(),e?(u.pickerHTML='<div class="popover popover-picker-calendar"><div class="popover-inner">'+u.pickerHTML+"</div></div>",u.popover=t.popover(u.pickerHTML,u.params.input,!0),u.container=t(u.popover).find(".weui-picker-modal"),t(u.popover).on("close",function(){p()})):u.inline?(u.container=t(u.pickerHTML),u.container.addClass("picker-modal-inline"),t(u.params.container).append(u.container)):(u.container=t(t.openPicker(u.pickerHTML)),t(u.container).on("close",function(){p()})),u.container[0].f7Calendar=u,u.wrapper=u.container.find(".picker-calendar-months-wrapper"),u.months=u.wrapper.find(".picker-calendar-month"),u.updateCurrentMonthYear(),u.monthsTranslate=0,u.setMonthsTranslate(),u.initCalendarEvents(),n&&u.updateValue()),u.opened=!0,u.initialized=!0,u.params.onMonthAdd&&u.months.each(function(){u.params.onMonthAdd(u,this)}),u.params.onOpen&&u.params.onOpen(u)},u.close=function(){if(u.opened&&!u.inline)return u.animating=!1,r()?void t.closePicker(u.popover):void t.closePicker(u.container)},u.destroy=function(){u.close(),u.params.input&&u.input.length>0&&(u.input.off("click focus",c),u.input.data("calendar",null)),t("html").off("click",l)},u.inline&&u.open(),u},o=function(t){return t<10?"0"+t:t};t.fn.calendar=function(e,n){return e=e||{},this.each(function(){var i=t(this);if(i[0]){var r={};e.inner?r.container=i:r.input=i;var s=i.data("calendar");if(!s)if("string"==typeof e);else{if(!e.value&&i.val()&&(e.value=[i.val()]),!e.value&&e.isinittoday){var c=new Date;e.value=[c.getFullYear()+"/"+o(c.getMonth()+1)+"/"+o(c.getDate())]}s=i.data("calendar",new a(t.extend(r,e)))}"string"==typeof e&&s[e].call(s,n)}})},e=t.fn.calendar.prototype.defaults={value:void 0,monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthNamesShort:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],dayNames:["周日","周一","周二","周三","周四","周五","周六"],dayNamesShort:["周日","周一","周二","周三","周四","周五","周六"],firstDay:1,weekendDays:[0,6],multiple:!1,dateFormat:"yyyy/mm/dd",direction:"horizontal",minDate:null,maxDate:null,touchMove:!0,animate:!0,closeOnSelect:!0,monthPicker:!0,monthPickerTemplate:'<div class="picker-calendar-month-picker"><a href="javascript:;" class="link icon-only picker-calendar-prev-month"><i class="icon icon-prev"></i></a><div class="current-month-value"></div><a href="javascript:;" class="link icon-only picker-calendar-next-month"><i class="icon icon-next"></i></a></div>',yearPicker:!0,yearPickerTemplate:'<div class="picker-calendar-year-picker"><a href="javascript:;" class="link icon-only picker-calendar-prev-year"><i class="icon icon-prev"></i></a><span class="current-year-value"></span><a href="javascript:;" class="link icon-only picker-calendar-next-year"><i class="icon icon-next"></i></a></div>',weekHeader:!0,scrollToInput:!0,inputReadOnly:!0,convertToPopover:!0,onlyInPopover:!1,toolbar:!0,toolbarCloseText:"Done",toolbarTemplate:'<div class="toolbar"><div class="toolbar-inner">{{yearPicker}}{{monthPicker}}</div></div>'}}($),function(t){"use strict";var e,n=function(t){return t<10?"0"+t:t},i=function(e,n){this.input=t(e),this.params=n||{},this.initMonthes=n.monthes,this.initYears=n.years;var i=t.extend({},n,this.getConfig());t(this.input).picker(i)};i.prototype={getDays:function(t){for(var e=[],n=1;n<=(t||31);n++)e.push(n<10?"0"+n:n);return e},getDaysByMonthAndYear:function(t,e){var n=new Date(e,parseInt(t)+1-1,1),i=new Date(n-1);return this.getDays(i.getDate())},getConfig:function(){var t,e=new Date,i=this.params,a=this,o={rotateEffect:!1,cssClass:"datetime-picker",value:[e.getFullYear(),n(e.getMonth()+1),n(e.getDate()),n(e.getHours()),n(e.getMinutes())],onChange:function(e,n,o){e.cols;var r=a.getDaysByMonthAndYear(n[1],n[0]),s=n[2];s>r.length&&(s=r.length),e.cols[4].setValue(s);var c=new Date(n[0]+"-"+n[1]+"-"+n[2]),l=!0;i.min&&c<+new Date("function"==typeof i.min?i.min():i.min)&&(e.setValue(t),l=!1),i.max&&c>+new Date("function"==typeof i.max?i.max():i.max)&&(e.setValue(t),l=!1),l&&(t=n),a.params.onChange&&a.params.onChange.apply(this,arguments)},formatValue:function(t,e,n){return a.params.format(t,e,n)},cols:[{values:this.initYears},{divider:!0,content:i.yearSplit},{values:this.initMonthes},{divider:!0,content:i.monthSplit},{values:function(){for(var t=[],e=1;e<=31;e++)t.push(n(e));return t}()}]};if(i.dateSplit&&o.cols.push({divider:!0,content:i.dateSplit}),o.cols.push({divider:!0,content:i.datetimeSplit}),a.params.times){var r=a.params.times();r&&r.length&&(o.cols=o.cols.concat(r))}var s=this.input.val();return s&&(o.value=i.parse(s)),this.params.value&&(this.input.val(this.params.value),o.value=i.parse(this.params.value)),o}},t.fn.datetimePicker=function(n){return n=t.extend({},e,n),this.each(function(){if(this){var e=t(this),a=e.data("datetime");return a||e.data("datetime",new i(this,n)),a}})},e=t.fn.datetimePicker.prototype.defaults={input:void 0,min:void 0,max:void 0,yearSplit:"-",monthSplit:"-",dateSplit:"",datetimeSplit:" ",monthes:"01 02 03 04 05 06 07 08 09 10 11 12".split(" "),years:function(){for(var t=[],e=1950;e<=2030;e++)t.push(e);return t}(),times:function(){return[{values:function(){for(var t=[],e=0;e<24;e++)t.push(n(e));return t}()},{divider:!0,content:":"},{values:function(){for(var t=[],e=0;e<60;e++)t.push(n(e));return t}()}]},format:function(t,e){return t.cols.map(function(t){return t.value||t.content}).join("")},parse:function(t){var e=t.split(this.datetimeSplit),n=e[0].split(/\D/);return e[1]&&(n=n.concat(e[1].split(/:|时|分|秒/))),n.filter(function(t){return!!t})}}}($),function(t){"use strict";t.openPopup=function(e,n){t.closePopup(),(e=t(e)).show(),e.width(),e.addClass("weui-popup__container--visible");var i=e.find(".weui-popup__modal");i.width(),i.transitionEnd(function(){i.trigger("open")})},t.closePopup=function(e,n){(e=t(e||".weui-popup__container--visible")).find(".weui-popup__modal").transitionEnd(function(){t(this).trigger("close"),e.hide(),n&&e.remove()}),e.removeClass("weui-popup__container--visible")},t(document).on("click",".close-popup, .weui-popup__overlay",function(){t.closePopup()}).on("click",".open-popup",function(){t(t(this).data("target")).popup()}).on("click",".weui-popup__container",function(e){t(e.target).hasClass("weui-popup__container")&&t.closePopup()}),t.fn.popup=function(){return this.each(function(){t.openPopup(this)})}}($),function(t){"use strict";var e,n,i,a,o,r,s=function(n){var i=t.getTouchPosition(n);a=i,o=r=0,e.addClass("touching")},c=function(n){if(!a)return!1;n.preventDefault(),n.stopPropagation();var i=t.getTouchPosition(n);o=i.x-a.x,(r=i.y-a.y)>0&&(r=Math.sqrt(r)),e.css("transform","translate3d(0, "+r+"px, 0)")},l=function(){e.removeClass("touching"),e.attr("style",""),r<0&&Math.abs(r)>.38*e.height()&&t.closeNotification(),Math.abs(o)<=1&&Math.abs(r)<=1&&e.trigger("noti-click"),a=!1},p=function(e){e.on(t.touchEvents.start,s),e.on(t.touchEvents.move,c),e.on(t.touchEvents.end,l)};t.notification=t.noti=function(a){a=t.extend({},n,a),(e=t(".weui-notification"))[0]||(e=t('<div class="weui-notification"></div>').appendTo(document.body),p(e)),e.off("noti-click"),a.onClick&&e.on("noti-click",function(){a.onClick(a.data)}),e.html(t.t7.compile(a.tpl)(a)),e.show(),e.addClass("weui-notification--in"),e.data("params",a);var o=function(){i&&(clearTimeout(i),i=null),i=setTimeout(function(){e.hasClass("weui-notification--touching")?o():t.closeNotification()},a.time)};o()},t.closeNotification=function(){if(i&&clearTimeout(i),i=null,t(".weui-notification").removeClass("weui-notification--in").transitionEnd(function(){t(this).remove()})[0]){var e=t(".weui-notification").data("params");e&&e.onClose&&e.onClose(e.data)}},n=t.noti.prototype.defaults={title:void 0,text:void 0,media:void 0,time:4e3,onClick:void 0,onClose:void 0,data:void 0,tpl:'<div class="weui-notification__inner">{{#if media}}<div class="weui-notification__media">{{media}}</div>{{/if}}<div class="weui-notification__content">{{#if title}}<div class="weui-notification__title">{{title}}</div>{{/if}}{{#if text}}<div class="weui-notification__text">{{text}}</div>{{/if}}</div><div class="weui-notification__handle-bar"></div></div>'}}($),function(t){"use strict";var e;t.toptip=function(n,i,a){if(n){"string"==typeof i&&(a=i,i=void 0),i=i||3e3;var o=a?"bg-"+a:"bg-danger",r=t(".weui-toptips").remove();(r=t('<div class="weui-toptips"></div>').appendTo(document.body)).html(n),r[0].className="weui-toptips "+o,clearTimeout(e),r.hasClass("weui-toptips_visible")||(r.show().width(),r.addClass("weui-toptips_visible")),e=setTimeout(function(){r.removeClass("weui-toptips_visible").transitionEnd(function(){r.remove()})},i)}}}($),function(t){"use strict";var e=function(e,n){this.container=t(e),this.handler=this.container.find(".weui-slider__handler"),this.track=this.container.find(".weui-slider__track"),this.value=this.container.find(".weui-slider-box__value"),this.bind(),"function"==typeof n&&(this.callback=n)};e.prototype.bind=function(){this.container.on(t.touchEvents.start,t.proxy(this.touchStart,this)).on(t.touchEvents.end,t.proxy(this.touchEnd,this)),t(document.body).on(t.touchEvents.move,t.proxy(this.touchMove,this))},e.prototype.touchStart=function(e){e.preventDefault(),this.start=t.getTouchPosition(e),this.width=this.container.find(".weui-slider__inner").width(),this.left=parseInt(this.container.find(".weui-slider__handler").css("left")),this.touching=!0},e.prototype.touchMove=function(e){if(!this.touching)return!0;var n=t.getTouchPosition(e).x-this.start.x+this.left,i=parseInt(n/this.width*100);i<0&&(i=0),i>100&&(i=100),this.handler.css("left",i+"%"),this.track.css("width",i+"%"),this.value.text(i),this.callback&&this.callback.call(this,i),this.container.trigger("change",i)},e.prototype.touchEnd=function(t){this.touching=!1},t.fn.slider=function(n){this.each(function(){var i=t(this),a=i.data("slider");if(a)return a;i.data("slider",new e(this,n))})}}($),function(t){"use strict";var e=[],n="swipeout-touching",i=function(n){this.container=t(n),this.mover=this.container.find(">.weui-cell__bd"),this.attachEvents(),e.push(this)};i.prototype.touchStart=function(e){var i=t.getTouchPosition(e);this.container.addClass(n),this.start=i,this.startX=0,this.startTime=+new Date;var a=this.mover.css("transform").match(/-?[\d\.]+/g);a&&a.length&&(this.startX=parseInt(a[4])),this.diffX=this.diffY=0,this._closeOthers(),this.limit=this.container.find(">.weui-cell__ft").width()||68},i.prototype.touchMove=function(e){if(!this.start)return!0;var n=t.getTouchPosition(e);if(this.diffX=n.x-this.start.x,this.diffY=n.y-this.start.y,Math.abs(this.diffX)<Math.abs(this.diffY))return this.close(),this.start=!1,!0;e.preventDefault(),e.stopPropagation();var i=this.diffX+this.startX;i>0&&(i=0),Math.abs(i)>this.limit&&(i=-(Math.pow(-(i+this.limit),.7)+this.limit)),this.mover.css("transform","translate3d("+i+"px, 0, 0)")},i.prototype.touchEnd=function(){if(!this.start)return!0;this.start=!1;var t=this.diffX+this.startX,e=new Date-this.startTime;this.diffX<-5&&e<200?this.open():this.diffX>=0&&e<200?this.close():t>0||-t<=this.limit/2?this.close():this.open()},i.prototype.close=function(){this.container.removeClass(n),this.mover.css("transform","translate3d(0, 0, 0)"),this.container.trigger("swipeout-close")},i.prototype.open=function(){this.container.removeClass(n),this._closeOthers(),this.mover.css("transform","translate3d("+-this.limit+"px, 0, 0)"),this.container.trigger("swipeout-open")},i.prototype.attachEvents=function(){var e=this.mover;e.on(t.touchEvents.start,t.proxy(this.touchStart,this)),e.on(t.touchEvents.move,t.proxy(this.touchMove,this)),e.on(t.touchEvents.end,t.proxy(this.touchEnd,this))},i.prototype._closeOthers=function(){var t=this;e.forEach(function(e){e!==t&&e.close()})};var a=function(t){return new i(t)};t.fn.swipeout=function(e){return this.each(function(){var n=t(this),i=n.data("swipeout")||a(this);n.data("swipeout",i),"string"==typeof e&&i[e]()})},t(".weui-cell_swiped").swipeout()}($);